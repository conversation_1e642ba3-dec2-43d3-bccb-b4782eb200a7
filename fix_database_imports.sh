#!/bin/bash

echo "🔧 Fixing database imports across the codebase..."

# Counter for tracking changes
fixed_count=0

# Fix relative database service imports
echo "Fixing ../database/database.service imports..."
find src -name "*.ts" -type f -exec grep -l "from.*\.\./database/database\.service" {} \; | while read -r file; do
  if sed -i "s|from ['\"]\.\.\/database\/database\.service['\"]|from '@/core/database'|g" "$file"; then
    echo "✅ Fixed: $file"
    ((fixed_count++))
  fi
done

echo "Fixing ../../core/database/database.service imports..."
find src -name "*.ts" -type f -exec grep -l "from.*\.\./\.\./core/database/database\.service" {} \; | while read -r file; do
  if sed -i "s|from ['\"]\.\.\/\.\.\/core\/database\/database\.service['\"]|from '@/core/database'|g" "$file"; then
    echo "✅ Fixed: $file"
    ((fixed_count++))
  fi
done

echo "Fixing ../../../core/database/database.service imports..."
find src -name "*.ts" -type f -exec grep -l "from.*\.\./\.\./\.\./core/database/database\.service" {} \; | while read -r file; do
  if sed -i "s|from ['\"]\.\.\/\.\.\/\.\.\/core\/database\/database\.service['\"]|from '@/core/database'|g" "$file"; then
    echo "✅ Fixed: $file"
    ((fixed_count++))
  fi
done

# Fix entity imports
echo "Fixing entity imports..."
find src -name "*.ts" -type f -exec grep -l "from.*\.\./\.\./core/database/entities" {} \; | while read -r file; do
  if sed -i "s|from ['\"]\.\.\/\.\.\/core\/database\/entities\/[^'\"]*['\"]|from '@/core/database'|g" "$file"; then
    echo "✅ Fixed entities: $file"
    ((fixed_count++))
  fi
done

find src -name "*.ts" -type f -exec grep -l "from.*\.\./\.\./\.\./core/database/entities" {} \; | while read -r file; do
  if sed -i "s|from ['\"]\.\.\/\.\.\/\.\.\/core\/database\/entities\/[^'\"]*['\"]|from '@/core/database'|g" "$file"; then
    echo "✅ Fixed deep entities: $file"
    ((fixed_count++))
  fi
done

# Fix schema imports
echo "Fixing schema imports..."
find src -name "*.ts" -type f -exec grep -l "from.*\.\./\.\./core/database/schema" {} \; | while read -r file; do
  if sed -i "s|from ['\"]\.\.\/\.\.\/core\/database\/schema['\"]|from '@/core/database'|g" "$file"; then
    echo "✅ Fixed schema: $file"
    ((fixed_count++))
  fi
done

# Fix types imports
echo "Fixing types imports..."
find src -name "*.ts" -type f -exec grep -l "from.*\.\./\.\./core/database/types" {} \; | while read -r file; do
  if sed -i "s|from ['\"]\.\.\/\.\.\/core\/database\/types['\"]|from '@/core/database'|g" "$file"; then
    echo "✅ Fixed types: $file"
    ((fixed_count++))
  fi
done

echo "✅ Database import fixes completed!"

# Validate remaining issues
echo "🔍 Checking for remaining issues..."
remaining_issues=$(find src -name "*.ts" -exec grep -l "\.\.\/.*database\.service\|\.\.\/.*database\/entities\|\.\.\/.*database\/schema\|\.\.\/.*database\/types" {} \; 2>/dev/null | wc -l)

if [ $remaining_issues -gt 0 ]; then
    echo "⚠️  Found $remaining_issues files with potential remaining issues:"
    find src -name "*.ts" -exec grep -l "\.\.\/.*database\.service\|\.\.\/.*database\/entities\|\.\.\/.*database\/schema\|\.\.\/.*database\/types" {} \; 2>/dev/null | head -10
    echo "   (showing first 10 files)"
else
    echo "✅ All database imports fixed successfully!"
fi

echo "📊 Summary: Attempted to fix imports in multiple files"