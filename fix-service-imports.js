const fs = require('fs');
const path = require('path');

// Find all TypeScript files with database imports issues
function findServiceFiles(dir) {
  const files = [];
  const items = fs.readdirSync(dir, { withFileTypes: true });
  
  for (const item of items) {
    const fullPath = path.join(dir, item.name);
    if (item.isDirectory() && !item.name.includes('node_modules')) {
      files.push(...findServiceFiles(fullPath));
    } else if (item.isFile() && item.name.endsWith('.service.ts')) {
      files.push(fullPath);
    }
  }
  
  return files;
}

// Fix common import and injection issues
function fixServiceFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let hasChanges = false;
  
  // Skip if file is already using Redis patterns
  if (content.includes('databaseService: DatabaseService') || 
      content.includes('RedisService') ||
      !content.includes('this.db.')) {
    return false;
  }
  
  console.log(`Fixing ${filePath}`);
  
  // Add missing imports
  if (!content.includes('import { DatabaseService }')) {
    content = content.replace(
      /import.*from '@nestjs\/common';/,
      `$&\nimport { DatabaseService } from '../database/database.service';`
    );
    hasChanges = true;
  }
  
  // Fix constructor to inject DatabaseService
  if (content.includes('constructor(') && !content.includes('databaseService: DatabaseService')) {
    // Find the constructor and add DatabaseService injection
    content = content.replace(
      /(constructor\(\s*)/,
      '$1private readonly databaseService: DatabaseService,\n    '
    );
    hasChanges = true;
  }
  
  // Add constructor if none exists
  if (!content.includes('constructor(') && content.includes('this.db.')) {
    const classMatch = content.match(/export class (\w+) {/);
    if (classMatch) {
      const replacement = `export class ${classMatch[1]} {
  constructor(
    private readonly databaseService: DatabaseService,
  ) {}`;
      content = content.replace(`export class ${classMatch[1]} {`, replacement);
      hasChanges = true;
    }
  }
  
  if (hasChanges) {
    fs.writeFileSync(filePath, content);
    return true;
  }
  
  return false;
}

// Main execution
const srcDir = path.join(__dirname, 'src');
const serviceFiles = findServiceFiles(srcDir);

console.log(`Found ${serviceFiles.length} service files`);

let fixedCount = 0;
for (const file of serviceFiles) {
  if (fixServiceFile(file)) {
    fixedCount++;
  }
}

console.log(`Fixed ${fixedCount} service files`);