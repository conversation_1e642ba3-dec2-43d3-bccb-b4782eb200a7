-- Add support tickets table
CREATE TABLE IF NOT EXISTS "support_tickets" (
    "id" VARCHAR(50) PRIMARY KEY DEFAULT gen_random_uuid()::text,
    "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    "ticket_number" VARCHAR(20) UNIQUE NOT NULL,
    "user_id" VARCHAR(50) NOT NULL,
    "title" VARCHAR(255) NOT NULL,
    "description" TEXT NOT NULL,
    "category" VARCHAR(50) NOT NULL,
    "priority" VARCHAR(20) NOT NULL,
    "status" VARCHAR(20) DEFAULT 'open' NOT NULL,
    "assigned_to" VARCHAR(50),
    "guild_id" VARCHAR(50) NOT NULL,
    "estimated_resolution" TIMESTAMP WITH TIME ZONE,
    "resolved_at" TIMESTAMP WITH TIME ZONE,
    "attachments" JSONB,
    "tags" JSONB
);

-- Add ticket responses table
CREATE TABLE IF NOT EXISTS "ticket_responses" (
    "id" VARCHAR(50) PRIMARY KEY DEFAULT gen_random_uuid()::text,
    "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    "ticket_id" VARCHAR(50) NOT NULL,
    "user_id" VARCHAR(50) NOT NULL,
    "username" VARCHAR(100) NOT NULL,
    "message" TEXT NOT NULL,
    "is_staff" BOOLEAN DEFAULT false NOT NULL,
    "is_internal" BOOLEAN DEFAULT false NOT NULL,
    "attachments" JSONB
);

-- Add knowledge base articles table
CREATE TABLE IF NOT EXISTS "knowledge_base_articles" (
    "id" VARCHAR(50) PRIMARY KEY DEFAULT gen_random_uuid()::text,
    "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    "title" VARCHAR(255) NOT NULL,
    "content" TEXT NOT NULL,
    "category" VARCHAR(100) NOT NULL,
    "subcategory" VARCHAR(100) NOT NULL,
    "tags" JSONB,
    "difficulty" VARCHAR(20) NOT NULL,
    "views" INTEGER DEFAULT 0 NOT NULL,
    "helpful" INTEGER DEFAULT 0 NOT NULL,
    "not_helpful" INTEGER DEFAULT 0 NOT NULL,
    "author" VARCHAR(100) NOT NULL,
    "related_articles" JSONB,
    "search_terms" JSONB,
    "is_published" BOOLEAN DEFAULT true NOT NULL
);

-- Add troubleshooting guides table
CREATE TABLE IF NOT EXISTS "troubleshooting_guides" (
    "id" VARCHAR(50) PRIMARY KEY DEFAULT gen_random_uuid()::text,
    "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    "title" VARCHAR(255) NOT NULL,
    "description" TEXT NOT NULL,
    "category" VARCHAR(100) NOT NULL,
    "symptoms" JSONB,
    "solutions" JSONB,
    "prerequisites" JSONB,
    "estimated_time" INTEGER NOT NULL,
    "difficulty" VARCHAR(20) NOT NULL,
    "success_rate" INTEGER DEFAULT 0 NOT NULL,
    "related_guides" JSONB,
    "is_active" BOOLEAN DEFAULT true NOT NULL
);

-- Add system status table
CREATE TABLE IF NOT EXISTS "system_status" (
    "id" VARCHAR(50) PRIMARY KEY DEFAULT gen_random_uuid()::text,
    "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    "service" VARCHAR(100) UNIQUE NOT NULL,
    "status" VARCHAR(20) NOT NULL,
    "last_checked" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    "uptime" INTEGER DEFAULT 100 NOT NULL,
    "response_time" INTEGER DEFAULT 0 NOT NULL,
    "incidents" INTEGER DEFAULT 0 NOT NULL,
    "description" TEXT
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS "support_tickets_user_id_idx" ON "support_tickets" ("user_id");
CREATE INDEX IF NOT EXISTS "support_tickets_guild_id_idx" ON "support_tickets" ("guild_id");
CREATE INDEX IF NOT EXISTS "support_tickets_status_idx" ON "support_tickets" ("status");
CREATE INDEX IF NOT EXISTS "support_tickets_priority_idx" ON "support_tickets" ("priority");
CREATE INDEX IF NOT EXISTS "support_tickets_category_idx" ON "support_tickets" ("category");
CREATE INDEX IF NOT EXISTS "support_tickets_created_at_idx" ON "support_tickets" ("created_at" DESC);

CREATE INDEX IF NOT EXISTS "ticket_responses_ticket_id_idx" ON "ticket_responses" ("ticket_id");
CREATE INDEX IF NOT EXISTS "ticket_responses_user_id_idx" ON "ticket_responses" ("user_id");
CREATE INDEX IF NOT EXISTS "ticket_responses_created_at_idx" ON "ticket_responses" ("created_at" ASC);

CREATE INDEX IF NOT EXISTS "knowledge_base_articles_category_idx" ON "knowledge_base_articles" ("category");
CREATE INDEX IF NOT EXISTS "knowledge_base_articles_subcategory_idx" ON "knowledge_base_articles" ("subcategory");
CREATE INDEX IF NOT EXISTS "knowledge_base_articles_difficulty_idx" ON "knowledge_base_articles" ("difficulty");
CREATE INDEX IF NOT EXISTS "knowledge_base_articles_views_idx" ON "knowledge_base_articles" ("views" DESC);
CREATE INDEX IF NOT EXISTS "knowledge_base_articles_published_idx" ON "knowledge_base_articles" ("is_published");

CREATE INDEX IF NOT EXISTS "troubleshooting_guides_category_idx" ON "troubleshooting_guides" ("category");
CREATE INDEX IF NOT EXISTS "troubleshooting_guides_difficulty_idx" ON "troubleshooting_guides" ("difficulty");
CREATE INDEX IF NOT EXISTS "troubleshooting_guides_success_rate_idx" ON "troubleshooting_guides" ("success_rate" DESC);
CREATE INDEX IF NOT EXISTS "troubleshooting_guides_active_idx" ON "troubleshooting_guides" ("is_active");

CREATE INDEX IF NOT EXISTS "system_status_service_idx" ON "system_status" ("service");
CREATE INDEX IF NOT EXISTS "system_status_status_idx" ON "system_status" ("status");
CREATE INDEX IF NOT EXISTS "system_status_last_checked_idx" ON "system_status" ("last_checked" DESC);

-- Insert sample system status data
INSERT INTO "system_status" ("service", "status", "uptime", "response_time", "incidents", "description") VALUES
    ('Discord API', 'operational', 99, 145, 0, 'All systems operational'),
    ('Database', 'operational', 99, 89, 0, NULL),
    ('Web Dashboard', 'degraded', 98, 520, 1, 'Experiencing slower response times'),
    ('File Storage', 'operational', 99, 67, 0, NULL),
    ('Analytics Service', 'operational', 99, 234, 0, NULL)
ON CONFLICT (service) DO NOTHING;

-- Insert sample knowledge base articles
INSERT INTO "knowledge_base_articles" ("title", "content", "category", "subcategory", "tags", "difficulty", "views", "helpful", "not_helpful", "author", "related_articles", "search_terms") VALUES
    ('How to Set Up Discord Bot Permissions', 'Comprehensive guide on setting up proper permissions for your Discord bot to ensure all features work correctly.', 'Bot Configuration', 'Permissions', '["permissions", "discord", "setup", "bot"]', 'beginner', 1247, 98, 12, 'Support Team', '["kb-2", "kb-3"]', '["permissions", "bot setup", "discord roles", "access control"]'),
    ('Troubleshooting API Rate Limits', 'Learn how to identify and resolve API rate limiting issues that may cause your bot to stop responding.', 'Troubleshooting', 'API Issues', '["api", "rate-limits", "troubleshooting", "performance"]', 'intermediate', 892, 76, 8, 'Technical Team', '["kb-1", "kb-4"]', '["rate limit", "api errors", "429 error", "throttling"]'),
    ('Integration with External Services', 'Step-by-step guide for integrating third-party services and APIs with your Discord bot.', 'Integrations', 'Third-party APIs', '["integrations", "api", "webhooks", "external"]', 'advanced', 654, 58, 5, 'Development Team', '["kb-2", "kb-5"]', '["integration", "api integration", "webhooks", "third-party"]')
ON CONFLICT DO NOTHING;

-- Insert sample troubleshooting guides
INSERT INTO "troubleshooting_guides" ("title", "description", "category", "symptoms", "solutions", "prerequisites", "estimated_time", "difficulty", "success_rate", "related_guides") VALUES
    ('Bot Not Responding to Commands', 'Diagnose and fix issues when your Discord bot stops responding to user commands.', 'Bot Issues', '["Bot online but not responding", "Commands not recognized", "No error messages"]', '[{"id": "step-1", "title": "Check Bot Permissions", "description": "Verify that the bot has the necessary permissions in the Discord server.", "commands": ["/permissions check", "/roles verify"], "expectedResult": "Bot should have appropriate roles and permissions", "commonIssues": ["Missing admin permissions", "Role hierarchy issues"]}, {"id": "step-2", "title": "Verify API Connection", "description": "Check if the bot is properly connected to Discord API.", "commands": ["/status api", "/connection test"], "expectedResult": "API connection should show as healthy", "alternativeSolutions": ["Restart bot process", "Check network connectivity"]}]', '["Bot administrative access", "Server permissions"]', 15, 'easy', 92, '["guide-2", "guide-3"]'),
    ('Resolving Database Connection Issues', 'Fix database connectivity problems that may affect bot functionality.', 'Database', '["Data not saving", "Connection timeouts", "Database errors in logs"]', '[{"id": "step-3", "title": "Check Database Status", "description": "Verify that the database server is running and accessible.", "commands": ["ping database_host", "telnet database_host 5432"], "expectedResult": "Database should be reachable and accepting connections"}, {"id": "step-4", "title": "Verify Connection String", "description": "Ensure the database connection string is correct and credentials are valid.", "expectedResult": "Connection should authenticate successfully", "commonIssues": ["Wrong password", "Expired credentials", "Network restrictions"]}]', '["Database access", "Network connectivity tools"]', 25, 'medium', 87, '["guide-1", "guide-4"]')
ON CONFLICT DO NOTHING;