CREATE TABLE "users" (
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"deleted_at" timestamp with time zone,
	"discord_id" varchar(50) NOT NULL,
	"username" varchar(100) NOT NULL,
	"email" varchar(255),
	"is_active" boolean DEFAULT true NOT NULL,
	"last_activity_at" timestamp with time zone,
	"preferences" jsonb,
	"profile" jsonb,
	"access_token" text,
	"refresh_token" text,
	"token_expires_at" timestamp with time zone,
	"experience" integer DEFAULT 0 NOT NULL,
	"balance" integer DEFAULT 0 NOT NULL,
	"user_id" varchar(50),
	"discriminator" varchar(10),
	"avatar_url" text,
	CONSTRAINT "users_discord_id_unique" UNIQUE("discord_id")
);
--> statement-breakpoint
CREATE TABLE "guilds" (
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"deleted_at" timestamp with time zone,
	"discord_id" varchar(50) NOT NULL,
	"name" varchar(100) NOT NULL,
	"icon" text,
	"is_active" boolean DEFAULT true NOT NULL,
	"settings" jsonb,
	"features" jsonb,
	"last_activity_at" timestamp with time zone,
	"owner_discord_id" varchar(50),
	"welcome_enabled" boolean DEFAULT false NOT NULL,
	"welcome_channel_id" varchar(50),
	"welcome_message" text,
	"welcome_roles" jsonb,
	"starboard_enabled" boolean DEFAULT false NOT NULL,
	"starboard_channel_id" varchar(50),
	"starboard_threshold" integer DEFAULT 3 NOT NULL,
	"guild_id" varchar(50),
	CONSTRAINT "guilds_discord_id_unique" UNIQUE("discord_id")
);
--> statement-breakpoint
CREATE TABLE "sessions" (
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"deleted_at" timestamp with time zone,
	"session_id" varchar(128) NOT NULL,
	"user_id" text NOT NULL,
	"encrypted_data" text,
	"expires_at" timestamp with time zone NOT NULL,
	"ip_address" varchar(45),
	"user_agent" text,
	"device_fingerprint" text,
	"is_revoked" boolean DEFAULT false NOT NULL,
	"last_accessed_at" timestamp with time zone,
	"metadata" jsonb,
	CONSTRAINT "sessions_session_id_unique" UNIQUE("session_id")
);
--> statement-breakpoint
CREATE TABLE "ai_agent_config" (
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"deleted_at" timestamp with time zone,
	"guild_id" text NOT NULL,
	"agent_type" varchar(50) NOT NULL,
	"enabled" boolean DEFAULT true NOT NULL,
	"configuration" jsonb,
	"default_channel_id" text,
	"log_channel_id" text,
	"permissions" jsonb,
	"settings" jsonb,
	"last_used_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "agent_interactions" (
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"deleted_at" timestamp with time zone,
	"user_id" text NOT NULL,
	"agent_type" varchar(50) NOT NULL,
	"interaction_type" varchar(50) NOT NULL,
	"content" text NOT NULL,
	"response" text,
	"status" varchar(20) DEFAULT 'completed' NOT NULL,
	"context" jsonb,
	"metadata" jsonb,
	"channel_id" text,
	"message_id" text,
	"guild_id" text,
	"sentiment_score" integer DEFAULT 0,
	"tags" text[]
);
--> statement-breakpoint
CREATE TABLE "agent_memory" (
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"deleted_at" timestamp with time zone,
	"user_id" text NOT NULL,
	"memory_type" varchar(50) NOT NULL,
	"key" varchar(200) NOT NULL,
	"value" jsonb NOT NULL,
	"context" text,
	"importance" integer DEFAULT 1 NOT NULL,
	"access_count" integer DEFAULT 0 NOT NULL,
	"last_accessed_at" timestamp with time zone,
	"expires_at" timestamp with time zone,
	"tags" text[],
	"metadata" jsonb
);
--> statement-breakpoint
CREATE TABLE "user_relationships" (
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"deleted_at" timestamp with time zone,
	"user_id" text NOT NULL,
	"target_user_id" text NOT NULL,
	"relationship_type" varchar(50) NOT NULL,
	"status" varchar(20) DEFAULT 'pending' NOT NULL,
	"notes" text,
	"metadata" jsonb,
	"established_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "personal_growth_plans" (
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"deleted_at" timestamp with time zone,
	"user_id" text NOT NULL,
	"title" varchar(200) NOT NULL,
	"description" text,
	"status" varchar(20) DEFAULT 'active' NOT NULL,
	"goals" jsonb,
	"milestones" jsonb,
	"start_date" timestamp with time zone,
	"target_completion_date" timestamp with time zone,
	"actual_completion_date" timestamp with time zone,
	"progress_percentage" integer DEFAULT 0 NOT NULL,
	"metadata" jsonb
);
--> statement-breakpoint
CREATE INDEX "users_discord_id_idx" ON "users" USING btree ("discord_id");--> statement-breakpoint
CREATE INDEX "users_created_at_idx" ON "users" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "users_username_idx" ON "users" USING btree ("username");--> statement-breakpoint
CREATE INDEX "guilds_discord_id_idx" ON "guilds" USING btree ("discord_id");--> statement-breakpoint
CREATE INDEX "sessions_session_id_idx" ON "sessions" USING btree ("session_id");--> statement-breakpoint
CREATE INDEX "sessions_user_id_idx" ON "sessions" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "sessions_expires_at_idx" ON "sessions" USING btree ("expires_at");--> statement-breakpoint
CREATE INDEX "ai_agent_config_guild_id_idx" ON "ai_agent_config" USING btree ("guild_id");--> statement-breakpoint
CREATE INDEX "ai_agent_config_agent_type_idx" ON "ai_agent_config" USING btree ("agent_type");--> statement-breakpoint
CREATE INDEX "agent_interactions_user_id_idx" ON "agent_interactions" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "agent_interactions_agent_type_idx" ON "agent_interactions" USING btree ("agent_type");--> statement-breakpoint
CREATE INDEX "agent_interactions_interaction_type_idx" ON "agent_interactions" USING btree ("interaction_type");--> statement-breakpoint
CREATE INDEX "agent_interactions_created_at_idx" ON "agent_interactions" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "agent_memory_user_id_idx" ON "agent_memory" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "agent_memory_memory_type_idx" ON "agent_memory" USING btree ("memory_type");--> statement-breakpoint
CREATE INDEX "agent_memory_created_at_idx" ON "agent_memory" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "user_relationships_user_id_idx" ON "user_relationships" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "user_relationships_target_user_id_idx" ON "user_relationships" USING btree ("target_user_id");--> statement-breakpoint
CREATE INDEX "personal_growth_plans_user_id_idx" ON "personal_growth_plans" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "personal_growth_plans_status_idx" ON "personal_growth_plans" USING btree ("status");