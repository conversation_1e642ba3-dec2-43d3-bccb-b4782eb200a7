-- Migration: Fix missing schema issues for Discord bot
-- This migration addresses the errors found in the logs:
-- 1. Missing ai_agent_config table
-- 2. Missing user_id column in agent_memory table  
-- 3. Missing updated_at column in agent_interactions table
-- 4. Issues with users table id column constraints

-- First, let's create the ai_agent_config table if it doesn't exist
CREATE TABLE IF NOT EXISTS "ai_agent_config" (
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"deleted_at" timestamp with time zone,
	"guild_id" text NOT NULL,
	"agent_type" varchar(50) NOT NULL,
	"enabled" boolean DEFAULT true NOT NULL,
	"configuration" jsonb,
	"default_channel_id" text,
	"log_channel_id" text,
	"permissions" jsonb,
	"settings" jsonb,
	"last_used_at" timestamp with time zone
);

-- Create indexes for ai_agent_config
CREATE INDEX IF NOT EXISTS "ai_agent_config_guild_id_idx" ON "ai_agent_config" USING btree ("guild_id");
CREATE INDEX IF NOT EXISTS "ai_agent_config_agent_type_idx" ON "ai_agent_config" USING btree ("agent_type");

-- Create agent_memory table if it doesn't exist
CREATE TABLE IF NOT EXISTS "agent_memory" (
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"deleted_at" timestamp with time zone,
	"user_id" text NOT NULL,
	"memory_type" varchar(50) NOT NULL,
	"key" varchar(200) NOT NULL,
	"value" jsonb NOT NULL,
	"context" text,
	"importance" integer DEFAULT 1 NOT NULL,
	"access_count" integer DEFAULT 0 NOT NULL,
	"last_accessed_at" timestamp with time zone,
	"expires_at" timestamp with time zone,
	"tags" text[],
	"metadata" jsonb
);

-- Create indexes for agent_memory
CREATE INDEX IF NOT EXISTS "agent_memory_user_id_idx" ON "agent_memory" USING btree ("user_id");
CREATE INDEX IF NOT EXISTS "agent_memory_memory_type_idx" ON "agent_memory" USING btree ("memory_type");
CREATE INDEX IF NOT EXISTS "agent_memory_created_at_idx" ON "agent_memory" USING btree ("created_at");

-- Create agent_interactions table if it doesn't exist
CREATE TABLE IF NOT EXISTS "agent_interactions" (
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"deleted_at" timestamp with time zone,
	"user_id" text NOT NULL,
	"agent_type" varchar(50) NOT NULL,
	"interaction_type" varchar(50) NOT NULL,
	"content" text NOT NULL,
	"response" text,
	"status" varchar(20) DEFAULT 'completed' NOT NULL,
	"context" jsonb,
	"metadata" jsonb,
	"channel_id" text,
	"message_id" text,
	"guild_id" text,
	"sentiment_score" integer DEFAULT 0,
	"tags" text[]
);

-- Create indexes for agent_interactions
CREATE INDEX IF NOT EXISTS "agent_interactions_user_id_idx" ON "agent_interactions" USING btree ("user_id");
CREATE INDEX IF NOT EXISTS "agent_interactions_agent_type_idx" ON "agent_interactions" USING btree ("agent_type");
CREATE INDEX IF NOT EXISTS "agent_interactions_interaction_type_idx" ON "agent_interactions" USING btree ("interaction_type");
CREATE INDEX IF NOT EXISTS "agent_interactions_created_at_idx" ON "agent_interactions" USING btree ("created_at");

-- Fix users table issues - ensure id column is properly configured
-- First, check if the id column exists and has the right constraints
DO $$
BEGIN
    -- Check if id column exists in users table
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'id') THEN
        ALTER TABLE "users" ADD COLUMN "id" serial PRIMARY KEY;
    END IF;
    
    -- Ensure id column has NOT NULL constraint
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'id' AND is_nullable = 'YES') THEN
        ALTER TABLE "users" ALTER COLUMN "id" SET NOT NULL;
    END IF;
    
    -- Ensure id column has default value
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'id' AND column_default IS NULL) THEN
        ALTER TABLE "users" ALTER COLUMN "id" SET DEFAULT nextval('users_id_seq');
    END IF;
END
$$;

-- Ensure users table has discord_id column as unique
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE table_name = 'users' AND constraint_name = 'users_discord_id_unique') THEN
        ALTER TABLE "users" ADD CONSTRAINT "users_discord_id_unique" UNIQUE ("discord_id");
    END IF;
END
$$;

-- Add missing updated_at column to agent_interactions if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agent_interactions' AND column_name = 'updated_at') THEN
        ALTER TABLE "agent_interactions" ADD COLUMN "updated_at" timestamp with time zone DEFAULT now() NOT NULL;
    END IF;
END
$$;

-- Add missing user_id column to agent_memory if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agent_memory' AND column_name = 'user_id') THEN
        ALTER TABLE "agent_memory" ADD COLUMN "user_id" text NOT NULL DEFAULT '';
    END IF;
END
$$;

-- Create default AI agent configurations for common guilds
INSERT INTO "ai_agent_config" (guild_id, agent_type, enabled, configuration, settings) 
VALUES 
('1394355426941730856', 'general', true, '{"channels":["general"],"defaultAgent":"general","personality":{"tone":"friendly","style":"helpful","expertise":["general-knowledge"],"responseLength":"medium"}}', '{"maxResponsesPerHour":50,"cooldownSeconds":30}')
ON CONFLICT (guild_id, agent_type) DO NOTHING;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS "users_discord_id_idx" ON "users" USING btree ("discord_id");
CREATE INDEX IF NOT EXISTS "users_created_at_idx" ON "users" USING btree ("created_at");