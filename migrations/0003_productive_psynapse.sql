CREATE TABLE "deployment_logs" (
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"deleted_at" timestamp with time zone,
	"deployment_id" integer NOT NULL,
	"log_level" varchar(10) NOT NULL,
	"message" text NOT NULL,
	"context" jsonb,
	"timestamp" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "panel_deployments" (
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"deleted_at" timestamp with time zone,
	"channel_id" varchar(50) NOT NULL,
	"guild_id" varchar(50) NOT NULL,
	"panel_id" varchar(100) NOT NULL,
	"message_id" varchar(50),
	"deployment_status" varchar(20) DEFAULT 'active' NOT NULL,
	"last_update_at" timestamp with time zone DEFAULT now() NOT NULL,
	"last_successful_update" timestamp with time zone,
	"panel_version" varchar(50) DEFAULT '1.0.0',
	"content_hash" varchar(64),
	"deployment_config" jsonb,
	"discord_state" jsonb,
	"deployment_metrics" jsonb,
	"error_state" jsonb,
	"is_active" boolean DEFAULT true NOT NULL,
	"needs_cleanup" boolean DEFAULT false NOT NULL,
	"scheduled_update_at" timestamp with time zone,
	"retry_count" integer DEFAULT 0 NOT NULL,
	"max_retries" integer DEFAULT 3 NOT NULL,
	"interaction_count" integer DEFAULT 0 NOT NULL,
	"last_interaction_at" timestamp with time zone,
	"metadata" jsonb
);
--> statement-breakpoint
CREATE TABLE "panel_cleanup_logs" (
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now(),
	"cleanup_session_id" varchar(100) NOT NULL,
	"cleanup_type" varchar(20) NOT NULL,
	"triggered_by" varchar(100),
	"guild_id" varchar(50),
	"channel_id" varchar(50),
	"target_scope" varchar(50),
	"messages_scanned" integer DEFAULT 0,
	"duplicates_found" integer DEFAULT 0,
	"duplicates_removed" integer DEFAULT 0,
	"duplicates_preserved" integer DEFAULT 0,
	"errors_count" integer DEFAULT 0,
	"started_at" timestamp with time zone DEFAULT now(),
	"completed_at" timestamp with time zone,
	"execution_time_ms" integer,
	"cleanup_config" jsonb DEFAULT '{}'::jsonb,
	"results_summary" jsonb DEFAULT '{}'::jsonb,
	"error_details" jsonb DEFAULT '{}'::jsonb,
	"status" varchar(20) DEFAULT 'running'
);
--> statement-breakpoint
CREATE TABLE "panel_health_metrics" (
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now(),
	"guild_id" varchar(50),
	"channel_id" varchar(50),
	"panel_id" varchar(100),
	"active_deployments" integer DEFAULT 0,
	"duplicate_deployments" integer DEFAULT 0,
	"failed_deployments" integer DEFAULT 0,
	"orphaned_messages" integer DEFAULT 0,
	"avg_response_time_ms" integer DEFAULT 0,
	"interaction_success_rate" integer DEFAULT 100,
	"last_successful_interaction" timestamp with time zone,
	"last_failed_interaction" timestamp with time zone,
	"memory_usage_mb" integer DEFAULT 0,
	"cache_hit_rate" integer DEFAULT 0,
	"error_rate_24h" integer DEFAULT 0,
	"cleanups_performed" integer DEFAULT 0,
	"last_cleanup_at" timestamp with time zone,
	"time_since_last_cleanup_hours" integer DEFAULT 0,
	"overall_health_score" integer DEFAULT 100,
	"health_status" varchar(20) DEFAULT 'healthy',
	"metadata" jsonb DEFAULT '{}'::jsonb
);
--> statement-breakpoint
CREATE TABLE "panel_versions" (
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now(),
	"panel_id" varchar(100) NOT NULL,
	"channel_id" varchar(50) NOT NULL,
	"version_number" varchar(20) NOT NULL,
	"content_hash" varchar(64) NOT NULL,
	"panel_content" jsonb NOT NULL,
	"changes_from_previous" jsonb DEFAULT '[]'::jsonb,
	"change_significance" varchar(10) DEFAULT 'patch',
	"deployment_id" integer,
	"generation_time_ms" integer,
	"content_size_bytes" integer,
	"metadata" jsonb DEFAULT '{}'::jsonb,
	"is_current" boolean DEFAULT false
);
--> statement-breakpoint
CREATE INDEX "deployment_logs_deployment_id_idx" ON "deployment_logs" USING btree ("deployment_id");--> statement-breakpoint
CREATE INDEX "deployment_logs_log_level_idx" ON "deployment_logs" USING btree ("log_level");--> statement-breakpoint
CREATE INDEX "deployment_logs_timestamp_idx" ON "deployment_logs" USING btree ("timestamp");--> statement-breakpoint
CREATE INDEX "panel_deployments_channel_id_idx" ON "panel_deployments" USING btree ("channel_id");--> statement-breakpoint
CREATE INDEX "panel_deployments_guild_id_idx" ON "panel_deployments" USING btree ("guild_id");--> statement-breakpoint
CREATE INDEX "panel_deployments_panel_id_idx" ON "panel_deployments" USING btree ("panel_id");--> statement-breakpoint
CREATE INDEX "panel_deployments_message_id_idx" ON "panel_deployments" USING btree ("message_id");--> statement-breakpoint
CREATE INDEX "panel_deployments_status_idx" ON "panel_deployments" USING btree ("deployment_status");--> statement-breakpoint
CREATE INDEX "panel_deployments_last_update_idx" ON "panel_deployments" USING btree ("last_update_at");--> statement-breakpoint
CREATE INDEX "panel_deployments_active_idx" ON "panel_deployments" USING btree ("is_active");--> statement-breakpoint
CREATE INDEX "panel_deployments_cleanup_idx" ON "panel_deployments" USING btree ("needs_cleanup");--> statement-breakpoint
CREATE INDEX "panel_deployments_scheduled_idx" ON "panel_deployments" USING btree ("scheduled_update_at");--> statement-breakpoint
CREATE INDEX "panel_deployments_channel_panel_unique" ON "panel_deployments" USING btree ("channel_id","panel_id");--> statement-breakpoint
CREATE INDEX "panel_deployments_content_hash_idx" ON "panel_deployments" USING btree ("content_hash");--> statement-breakpoint
CREATE INDEX "panel_deployments_interaction_idx" ON "panel_deployments" USING btree ("last_interaction_at");