-- Create Whop integration table
CREATE TABLE IF NOT EXISTS "whop" (
    id VARCHAR(255) PRIMARY KEY,
    enabled BOOLEAN NOT NULL DEFAULT FALSE,
    "companyId" VARCHAR(255),
    "accessPassId" VARCHAR(255),
    "webhookUrl" VARCHAR(500),
    "autoVerifyMembers" BOOLEAN NOT NULL DEFAULT FALSE,
    "syncRoles" BOOLEAN NOT NULL DEFAULT FALSE,
    "premiumRoleId" VARCHAR(255),
    "memberRoleId" VARCHAR(255),
    "requireVerification" BOOLEAN NOT NULL DEFAULT TRUE,
    "welcomeMessage" TEXT,
    "verificationChannel" VARCHAR(255),
    "logChannel" VARCHAR(255),
    "allowedRoles" TEXT[], -- Array of role IDs
    "restrictToChannels" TEXT[], -- Array of channel IDs
    "syncInterval" INTEGER NOT NULL DEFAULT 300, -- 5 minutes in seconds
    "enableWebhooks" BOOLEAN NOT NULL DEFAULT FALSE,
    "customFields" JSONB DEFAULT '[]'::jsonb,
    "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_whop_enabled ON "whop" (enabled);
CREATE INDEX IF NOT EXISTS idx_whop_company_id ON "whop" ("companyId");
CREATE INDEX IF NOT EXISTS idx_whop_access_pass_id ON "whop" ("accessPassId");

-- Add comments for documentation
COMMENT ON TABLE "whop" IS 'Whop integration configuration for Discord guilds';
COMMENT ON COLUMN "whop".id IS 'Discord guild ID';
COMMENT ON COLUMN "whop".enabled IS 'Whether Whop integration is enabled for this guild';
COMMENT ON COLUMN "whop"."companyId" IS 'Whop company ID';
COMMENT ON COLUMN "whop"."accessPassId" IS 'Whop access pass ID to verify against';
COMMENT ON COLUMN "whop"."webhookUrl" IS 'URL for receiving Whop webhooks';
COMMENT ON COLUMN "whop"."autoVerifyMembers" IS 'Automatically verify members when they join';
COMMENT ON COLUMN "whop"."syncRoles" IS 'Automatically assign roles based on membership status';
COMMENT ON COLUMN "whop"."premiumRoleId" IS 'Role ID for premium members';
COMMENT ON COLUMN "whop"."memberRoleId" IS 'Role ID for verified members';
COMMENT ON COLUMN "whop"."requireVerification" IS 'Require users to verify their Whop membership';
COMMENT ON COLUMN "whop"."welcomeMessage" IS 'Message sent when user is verified';
COMMENT ON COLUMN "whop"."verificationChannel" IS 'Channel where users can verify their membership';
COMMENT ON COLUMN "whop"."logChannel" IS 'Channel for logging verification events';
COMMENT ON COLUMN "whop"."allowedRoles" IS 'Array of role IDs that can use Whop features';
COMMENT ON COLUMN "whop"."restrictToChannels" IS 'Array of channel IDs where Whop features are restricted';
COMMENT ON COLUMN "whop"."syncInterval" IS 'How often to sync membership status (in seconds)';
COMMENT ON COLUMN "whop"."enableWebhooks" IS 'Enable webhook notifications from Whop';
COMMENT ON COLUMN "whop"."customFields" IS 'Custom fields configuration as JSON';
COMMENT ON COLUMN "whop"."createdAt" IS 'When the configuration was created';
COMMENT ON COLUMN "whop"."updatedAt" IS 'When the configuration was last updated';
