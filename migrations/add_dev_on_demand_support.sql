-- Migration: Add Dev On Demand System Support
-- Description: Adds necessary database structure for the Dev On Demand marketplace system
-- Date: 2024-01-XX

-- Add dev-on-demand configuration to guilds table
-- This extends the existing preferences JSONB column to support dev-on-demand settings
UPDATE guilds 
SET preferences = COALESCE(preferences, '{}') || '{
  "devOnDemand": {
    "enabled": false,
    "requestChannelId": null,
    "developerRoleId": null,
    "maxActiveRequests": 3,
    "requirePayment": true,
    "autoAssignment": false
  }
}'
WHERE preferences IS NULL OR NOT preferences ? 'devOnDemand';

-- Add dev-on-demand user preferences structure
-- This extends the existing user preferences to support dev requests
UPDATE users 
SET preferences = COALESCE(preferences, '{}') || '{
  "devRequests": [],
  "devProfile": {
    "isClient": false,
    "isDeveloper": false,
    "skills": [],
    "portfolio": null,
    "rating": 0,
    "completedProjects": 0
  }
}'
WHERE preferences IS NULL OR NOT preferences ? 'devRequests';

-- Create index for faster dev request queries
CREATE INDEX IF NOT EXISTS idx_users_dev_requests 
ON users USING GIN ((preferences->'devRequests'));

-- Create index for guild dev-on-demand settings
CREATE INDEX IF NOT EXISTS idx_guilds_dev_on_demand 
ON guilds USING GIN ((preferences->'devOnDemand'));

-- Add comments for documentation
COMMENT ON COLUMN guilds.preferences IS 'Guild configuration including devOnDemand settings: enabled, requestChannelId, developerRoleId, maxActiveRequests, requirePayment, autoAssignment';
COMMENT ON COLUMN users.preferences IS 'User preferences including devRequests array and devProfile: isClient, isDeveloper, skills, portfolio, rating, completedProjects';

-- Example dev request structure (for documentation)
/*
Dev Request Structure in users.preferences.devRequests:
{
  "id": "unique_request_id",
  "clientId": "discord_user_id",
  "clientTag": "username#1234",
  "description": "Project description",
  "budget": "$500",
  "timeline": "2 weeks",
  "skills": ["JavaScript", "Discord.js"],
  "status": "open|assigned|in_progress|completed|cancelled|payment_pending|payment_held",
  "createdAt": "2024-01-01T00:00:00.000Z",
  "developerId": "discord_user_id",
  "developerTag": "developer#5678",
  "assignedAt": "2024-01-01T01:00:00.000Z",
  "channelId": "discord_channel_id",
  "paymentAmount": 500,
  "escrowId": "whop_escrow_id",
  "milestones": [
    {
      "id": "milestone_id",
      "description": "Setup and planning",
      "amount": 166.67,
      "status": "pending|completed|released",
      "completedAt": "2024-01-01T02:00:00.000Z",
      "releasedAt": "2024-01-01T02:05:00.000Z"
    }
  ],
  "paymentStatus": "pending|held|released|refunded"
}
*/

-- Example guild dev-on-demand configuration
/*
Guild DevOnDemand Configuration in guilds.preferences.devOnDemand:
{
  "enabled": true,
  "requestChannelId": "123456789012345678",
  "developerRoleId": "123456789012345679",
  "maxActiveRequests": 3,
  "requirePayment": true,
  "autoAssignment": false,
  "welcomeMessage": "Welcome to our Dev On Demand marketplace!",
  "clientTierRequired": true,
  "developerTierRequired": true,
  "minimumBudget": 50,
  "maximumBudget": 10000,
  "allowedSkills": [
    "JavaScript", "Python", "Discord.js", "React", "Node.js",
    "Database Design", "API Development", "Bot Development"
  ],
  "moderatorRoleId": "123456789012345680",
  "logChannelId": "123456789012345681"
}
*/

-- Verify the migration
SELECT 
  'Guilds with devOnDemand config' as description,
  COUNT(*) as count
FROM guilds 
WHERE preferences ? 'devOnDemand'

UNION ALL

SELECT 
  'Users with devRequests config' as description,
  COUNT(*) as count
FROM users 
WHERE preferences ? 'devRequests';

-- Show sample configuration for verification
SELECT 
  'Sample Guild Config' as type,
  preferences->'devOnDemand' as config
FROM guilds 
WHERE preferences ? 'devOnDemand'
LIMIT 1;

SELECT 
  'Sample User Config' as type,
  preferences->'devRequests' as dev_requests,
  preferences->'devProfile' as dev_profile
FROM users 
WHERE preferences ? 'devRequests'
LIMIT 1;