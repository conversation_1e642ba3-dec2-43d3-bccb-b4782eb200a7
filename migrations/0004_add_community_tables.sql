-- Add community events table
CREATE TABLE IF NOT EXISTS "community_events" (
    "id" VARCHAR(50) PRIMARY KEY DEFAULT gen_random_uuid()::text,
    "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    "title" VARCHAR(255) NOT NULL,
    "description" TEXT NOT NULL,
    "start_date" TIMESTAMP WITH TIME ZONE NOT NULL,
    "end_date" TIMESTAMP WITH TIME ZONE,
    "type" VARCHAR(50) NOT NULL,
    "max_participants" INTEGER,
    "current_participants" INTEGER DEFAULT 0 NOT NULL,
    "tags" JSONB,
    "guild_id" VARCHAR(50) NOT NULL,
    "organizer_id" VARCHAR(50) NOT NULL,
    "is_active" BOOLEAN DEFAULT true NOT NULL
);

-- Add event participants table
CREATE TABLE IF NOT EXISTS "event_participants" (
    "id" VARCHAR(50) PRIMARY KEY DEFAULT gen_random_uuid()::text,
    "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    "event_id" VARCHAR(50) NOT NULL,
    "user_id" VARCHAR(50) NOT NULL,
    "joined_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    "status" VARCHAR(20) DEFAULT 'registered' NOT NULL
);

-- Add community feedback table
CREATE TABLE IF NOT EXISTS "community_feedback" (
    "id" VARCHAR(50) PRIMARY KEY DEFAULT gen_random_uuid()::text,
    "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    "user_id" VARCHAR(50) NOT NULL,
    "type" VARCHAR(50) NOT NULL,
    "title" VARCHAR(255) NOT NULL,
    "description" TEXT NOT NULL,
    "status" VARCHAR(20) DEFAULT 'pending' NOT NULL,
    "votes" INTEGER DEFAULT 0 NOT NULL,
    "guild_id" VARCHAR(50) NOT NULL
);

-- Add leaderboard entries table
CREATE TABLE IF NOT EXISTS "leaderboard_entries" (
    "id" VARCHAR(50) PRIMARY KEY DEFAULT gen_random_uuid()::text,
    "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    "user_id" VARCHAR(50) NOT NULL,
    "guild_id" VARCHAR(50) NOT NULL,
    "points" INTEGER DEFAULT 0 NOT NULL,
    "level" INTEGER DEFAULT 1 NOT NULL,
    "badges" JSONB,
    "monthly_rank" INTEGER,
    "all_time_rank" INTEGER,
    "last_updated" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS "community_events_guild_id_idx" ON "community_events" ("guild_id");
CREATE INDEX IF NOT EXISTS "community_events_start_date_idx" ON "community_events" ("start_date");
CREATE INDEX IF NOT EXISTS "community_events_type_idx" ON "community_events" ("type");

CREATE INDEX IF NOT EXISTS "event_participants_event_id_idx" ON "event_participants" ("event_id");
CREATE INDEX IF NOT EXISTS "event_participants_user_id_idx" ON "event_participants" ("user_id");

CREATE INDEX IF NOT EXISTS "community_feedback_guild_id_idx" ON "community_feedback" ("guild_id");
CREATE INDEX IF NOT EXISTS "community_feedback_user_id_idx" ON "community_feedback" ("user_id");
CREATE INDEX IF NOT EXISTS "community_feedback_type_idx" ON "community_feedback" ("type");

CREATE INDEX IF NOT EXISTS "leaderboard_entries_guild_id_idx" ON "leaderboard_entries" ("guild_id");
CREATE INDEX IF NOT EXISTS "leaderboard_entries_user_id_idx" ON "leaderboard_entries" ("user_id");
CREATE INDEX IF NOT EXISTS "leaderboard_entries_points_idx" ON "leaderboard_entries" ("points" DESC);

-- Add unique constraints to prevent duplicate entries
ALTER TABLE "event_participants" ADD CONSTRAINT IF NOT EXISTS "event_participants_event_user_unique" UNIQUE ("event_id", "user_id");
ALTER TABLE "leaderboard_entries" ADD CONSTRAINT IF NOT EXISTS "leaderboard_entries_user_guild_unique" UNIQUE ("user_id", "guild_id");