-- Migration: Add comprehensive tier system for Dev On Demand community
-- Created: 2025-07-31

-- Community Tiers Table
CREATE TABLE IF NOT EXISTS community_tiers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    guild_id VARCHAR(255) NOT NULL,
    level VARCHAR(50) NOT NULL CHECK (level IN ('free', 'ai_explorer', 'wealth_builder', 'dev_premium', 'enterprise')),
    name VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    monthly_price DECIMAL(10,2) NOT NULL DEFAULT 0,
    yearly_price DECIMAL(10,2),
    features J<PERSON><PERSON><PERSON> NOT NULL DEFAULT '{}',
    limits JSONB NOT NULL DEFAULT '{}',
    discord_role_ids TEXT[] DEFAULT ARRAY[]::TEXT[],
    accessible_channels TEXT[] DEFAULT ARRAY[]::TEXT[],
    restricted_channels TEXT[] DEFAULT ARRAY[]::TEXT[],
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    custom_config JSON<PERSON>,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- User Memberships Table
CREATE TABLE IF NOT EXISTS user_memberships (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(255) NOT NULL,
    guild_id VARCHAR(255) NOT NULL,
    tier_id UUID NOT NULL REFERENCES community_tiers(id) ON DELETE CASCADE,
    whop_subscription_id VARCHAR(255),
    subscription_start_date TIMESTAMP WITH TIME ZONE,
    subscription_end_date TIMESTAMP WITH TIME ZONE,
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'cancelled', 'expired', 'suspended')),
    usage_stats JSONB DEFAULT '{
        "monthlyDevRequests": 0,
        "aiAgentQueries": 0,
        "personalCoachingSessions": 0,
        "lastResetDate": null
    }',
    payment_history JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, guild_id)
);

-- Tier Access Rules Table (for granular channel/feature access control)
CREATE TABLE IF NOT EXISTS tier_access_rules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    guild_id VARCHAR(255) NOT NULL,
    tier_id UUID NOT NULL REFERENCES community_tiers(id) ON DELETE CASCADE,
    resource_type VARCHAR(50) NOT NULL CHECK (resource_type IN ('channel', 'feature', 'command', 'ai_agent')),
    resource_id VARCHAR(255) NOT NULL,
    access_level VARCHAR(50) DEFAULT 'allow' CHECK (access_level IN ('allow', 'deny', 'limit')),
    limit_value INTEGER,
    conditions JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Payment Integration Table (for tracking Whop payments)
CREATE TABLE IF NOT EXISTS tier_payments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(255) NOT NULL,
    guild_id VARCHAR(255) NOT NULL,
    membership_id UUID NOT NULL REFERENCES user_memberships(id) ON DELETE CASCADE,
    whop_payment_id VARCHAR(255),
    payment_method VARCHAR(100),
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(10) DEFAULT 'USD',
    billing_period VARCHAR(20) CHECK (billing_period IN ('monthly', 'yearly', 'one_time')),
    payment_status VARCHAR(50) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'completed', 'failed', 'refunded', 'cancelled')),
    processed_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Usage Tracking Table (for detailed analytics and limit enforcement)
CREATE TABLE IF NOT EXISTS usage_tracking (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(255) NOT NULL,
    guild_id VARCHAR(255) NOT NULL,
    membership_id UUID NOT NULL REFERENCES user_memberships(id) ON DELETE CASCADE,
    feature_type VARCHAR(100) NOT NULL,
    feature_action VARCHAR(100) NOT NULL,
    usage_count INTEGER DEFAULT 1,
    metadata JSONB DEFAULT '{}',
    tracked_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    billing_period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    billing_period_end TIMESTAMP WITH TIME ZONE NOT NULL
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_community_tiers_guild_level ON community_tiers(guild_id, level);
CREATE INDEX IF NOT EXISTS idx_user_memberships_user_guild ON user_memberships(user_id, guild_id);
CREATE INDEX IF NOT EXISTS idx_user_memberships_tier ON user_memberships(tier_id);
CREATE INDEX IF NOT EXISTS idx_user_memberships_whop ON user_memberships(whop_subscription_id) WHERE whop_subscription_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_user_memberships_status ON user_memberships(status);
CREATE INDEX IF NOT EXISTS idx_tier_access_rules_guild_tier ON tier_access_rules(guild_id, tier_id);
CREATE INDEX IF NOT EXISTS idx_tier_access_rules_resource ON tier_access_rules(resource_type, resource_id);
CREATE INDEX IF NOT EXISTS idx_tier_payments_user_guild ON tier_payments(user_id, guild_id);
CREATE INDEX IF NOT EXISTS idx_tier_payments_whop ON tier_payments(whop_payment_id) WHERE whop_payment_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_tier_payments_status ON tier_payments(payment_status);
CREATE INDEX IF NOT EXISTS idx_usage_tracking_user_feature ON usage_tracking(user_id, guild_id, feature_type);
CREATE INDEX IF NOT EXISTS idx_usage_tracking_billing_period ON usage_tracking(billing_period_start, billing_period_end);
CREATE INDEX IF NOT EXISTS idx_usage_tracking_tracked_at ON usage_tracking(tracked_at);

-- Triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_community_tiers_updated_at BEFORE UPDATE ON community_tiers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_memberships_updated_at BEFORE UPDATE ON user_memberships FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tier_access_rules_updated_at BEFORE UPDATE ON tier_access_rules FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tier_payments_updated_at BEFORE UPDATE ON tier_payments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default community tiers for new guilds
-- This will be handled by the TierManagementService.initializeDefaultTiers() method

-- Add helpful views for common queries
CREATE OR REPLACE VIEW user_tier_summary AS
SELECT 
    um.user_id,
    um.guild_id,
    ct.name as tier_name,
    ct.level as tier_level,
    ct.monthly_price,
    um.status as membership_status,
    um.subscription_start_date,
    um.subscription_end_date,
    um.usage_stats,
    CASE 
        WHEN um.subscription_end_date IS NULL THEN true
        WHEN um.subscription_end_date > CURRENT_TIMESTAMP THEN true
        ELSE false
    END as is_active
FROM user_memberships um
JOIN community_tiers ct ON um.tier_id = ct.id
WHERE um.status = 'active';

-- View for tier usage analytics
CREATE OR REPLACE VIEW tier_usage_analytics AS
SELECT 
    ut.guild_id,
    ct.name as tier_name,
    ct.level as tier_level,
    ut.feature_type,
    COUNT(*) as total_usage,
    COUNT(DISTINCT ut.user_id) as unique_users,
    AVG(ut.usage_count) as avg_usage_per_session,
    DATE_TRUNC('day', ut.tracked_at) as usage_date
FROM usage_tracking ut
JOIN user_memberships um ON ut.membership_id = um.id
JOIN community_tiers ct ON um.tier_id = ct.id
GROUP BY ut.guild_id, ct.name, ct.level, ut.feature_type, DATE_TRUNC('day', ut.tracked_at);

COMMENT ON TABLE community_tiers IS 'Defines available membership tiers with features and pricing';
COMMENT ON TABLE user_memberships IS 'Tracks user membership status and usage statistics';
COMMENT ON TABLE tier_access_rules IS 'Fine-grained access control rules for tiers';
COMMENT ON TABLE tier_payments IS 'Payment tracking and integration with Whop';
COMMENT ON TABLE usage_tracking IS 'Detailed usage analytics and limit enforcement';