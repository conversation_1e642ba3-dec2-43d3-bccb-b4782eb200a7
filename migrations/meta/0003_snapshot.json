{"id": "8094bf1d-5563-4496-954a-f0d4042e913f", "prevId": "7959c286-30de-4add-91f6-4cda50f20ed3", "version": "7", "dialect": "postgresql", "tables": {"public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "discord_id": {"name": "discord_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "username": {"name": "username", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "last_activity_at": {"name": "last_activity_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "preferences": {"name": "preferences", "type": "jsonb", "primaryKey": false, "notNull": false}, "profile": {"name": "profile", "type": "jsonb", "primaryKey": false, "notNull": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "token_expires_at": {"name": "token_expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "experience": {"name": "experience", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "balance": {"name": "balance", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "discriminator": {"name": "discriminator", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "avatar_url": {"name": "avatar_url", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"users_discord_id_idx": {"name": "users_discord_id_idx", "columns": [{"expression": "discord_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_created_at_idx": {"name": "users_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_username_idx": {"name": "users_username_idx", "columns": [{"expression": "username", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_discord_id_unique": {"name": "users_discord_id_unique", "nullsNotDistinct": false, "columns": ["discord_id"]}}, "policies": {}, "checkConstraints": {}}, "public.guilds": {"name": "guilds", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "discord_id": {"name": "discord_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "icon": {"name": "icon", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "settings": {"name": "settings", "type": "jsonb", "primaryKey": false, "notNull": false}, "features": {"name": "features", "type": "jsonb", "primaryKey": false, "notNull": false}, "last_activity_at": {"name": "last_activity_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "owner_discord_id": {"name": "owner_discord_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "welcome_enabled": {"name": "welcome_enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "welcome_channel_id": {"name": "welcome_channel_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "welcome_message": {"name": "welcome_message", "type": "text", "primaryKey": false, "notNull": false}, "welcome_roles": {"name": "welcome_roles", "type": "jsonb", "primaryKey": false, "notNull": false}, "starboard_enabled": {"name": "starboard_enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "starboard_channel_id": {"name": "starboard_channel_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "starboard_threshold": {"name": "starboard_threshold", "type": "integer", "primaryKey": false, "notNull": true, "default": 3}, "guild_id": {"name": "guild_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}}, "indexes": {"guilds_discord_id_idx": {"name": "guilds_discord_id_idx", "columns": [{"expression": "discord_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"guilds_discord_id_unique": {"name": "guilds_discord_id_unique", "nullsNotDistinct": false, "columns": ["discord_id"]}}, "policies": {}, "checkConstraints": {}}, "public.sessions": {"name": "sessions", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "session_id": {"name": "session_id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "encrypted_data": {"name": "encrypted_data", "type": "text", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "device_fingerprint": {"name": "device_fingerprint", "type": "text", "primaryKey": false, "notNull": false}, "is_revoked": {"name": "is_revoked", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "last_accessed_at": {"name": "last_accessed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"sessions_session_id_idx": {"name": "sessions_session_id_idx", "columns": [{"expression": "session_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "sessions_user_id_idx": {"name": "sessions_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "sessions_expires_at_idx": {"name": "sessions_expires_at_idx", "columns": [{"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"sessions_session_id_unique": {"name": "sessions_session_id_unique", "nullsNotDistinct": false, "columns": ["session_id"]}}, "policies": {}, "checkConstraints": {}}, "public.ai_agent_config": {"name": "ai_agent_config", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "guild_id": {"name": "guild_id", "type": "text", "primaryKey": false, "notNull": true}, "agent_type": {"name": "agent_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "enabled": {"name": "enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "configuration": {"name": "configuration", "type": "jsonb", "primaryKey": false, "notNull": false}, "default_channel_id": {"name": "default_channel_id", "type": "text", "primaryKey": false, "notNull": false}, "log_channel_id": {"name": "log_channel_id", "type": "text", "primaryKey": false, "notNull": false}, "permissions": {"name": "permissions", "type": "jsonb", "primaryKey": false, "notNull": false}, "settings": {"name": "settings", "type": "jsonb", "primaryKey": false, "notNull": false}, "last_used_at": {"name": "last_used_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"ai_agent_config_guild_id_idx": {"name": "ai_agent_config_guild_id_idx", "columns": [{"expression": "guild_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "ai_agent_config_agent_type_idx": {"name": "ai_agent_config_agent_type_idx", "columns": [{"expression": "agent_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}}, "public.agent_interactions": {"name": "agent_interactions", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "agent_type": {"name": "agent_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "interaction_type": {"name": "interaction_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "response": {"name": "response", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'completed'"}, "context": {"name": "context", "type": "jsonb", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "channel_id": {"name": "channel_id", "type": "text", "primaryKey": false, "notNull": false}, "message_id": {"name": "message_id", "type": "text", "primaryKey": false, "notNull": false}, "guild_id": {"name": "guild_id", "type": "text", "primaryKey": false, "notNull": false}, "sentiment_score": {"name": "sentiment_score", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "tags": {"name": "tags", "type": "text[]", "primaryKey": false, "notNull": false}}, "indexes": {"agent_interactions_user_id_idx": {"name": "agent_interactions_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "agent_interactions_agent_type_idx": {"name": "agent_interactions_agent_type_idx", "columns": [{"expression": "agent_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "agent_interactions_interaction_type_idx": {"name": "agent_interactions_interaction_type_idx", "columns": [{"expression": "interaction_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "agent_interactions_created_at_idx": {"name": "agent_interactions_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}}, "public.agent_memory": {"name": "agent_memory", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "memory_type": {"name": "memory_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "jsonb", "primaryKey": false, "notNull": true}, "context": {"name": "context", "type": "text", "primaryKey": false, "notNull": false}, "importance": {"name": "importance", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "access_count": {"name": "access_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "last_accessed_at": {"name": "last_accessed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "tags": {"name": "tags", "type": "text[]", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"agent_memory_user_id_idx": {"name": "agent_memory_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "agent_memory_memory_type_idx": {"name": "agent_memory_memory_type_idx", "columns": [{"expression": "memory_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "agent_memory_created_at_idx": {"name": "agent_memory_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}}, "public.user_relationships": {"name": "user_relationships", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "target_user_id": {"name": "target_user_id", "type": "text", "primaryKey": false, "notNull": true}, "relationship_type": {"name": "relationship_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'pending'"}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "established_at": {"name": "established_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"user_relationships_user_id_idx": {"name": "user_relationships_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_relationships_target_user_id_idx": {"name": "user_relationships_target_user_id_idx", "columns": [{"expression": "target_user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}}, "public.personal_growth_plans": {"name": "personal_growth_plans", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'active'"}, "goals": {"name": "goals", "type": "jsonb", "primaryKey": false, "notNull": false}, "milestones": {"name": "milestones", "type": "jsonb", "primaryKey": false, "notNull": false}, "start_date": {"name": "start_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "target_completion_date": {"name": "target_completion_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "actual_completion_date": {"name": "actual_completion_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "progress_percentage": {"name": "progress_percentage", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"personal_growth_plans_user_id_idx": {"name": "personal_growth_plans_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "personal_growth_plans_status_idx": {"name": "personal_growth_plans_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}}, "public.ai_chat_sessions": {"name": "ai_chat_sessions", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "guild_id": {"name": "guild_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "agent_type": {"name": "agent_type", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "thread_id": {"name": "thread_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "channel_id": {"name": "channel_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'active'"}, "access_level": {"name": "access_level", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'free'"}, "session_data": {"name": "session_data", "type": "jsonb", "primaryKey": false, "notNull": false}, "message_count": {"name": "message_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "last_message_at": {"name": "last_message_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "archived_at": {"name": "archived_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}}, "public.ai_channel_configs": {"name": "ai_channel_configs", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "guild_id": {"name": "guild_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "ai_channel_id": {"name": "ai_channel_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "panel_message_id": {"name": "panel_message_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "enabled": {"name": "enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "settings": {"name": "settings", "type": "jsonb", "primaryKey": false, "notNull": true}, "last_panel_update": {"name": "last_panel_update", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"ai_channel_configs_guild_id_unique": {"name": "ai_channel_configs_guild_id_unique", "nullsNotDistinct": false, "columns": ["guild_id"]}}, "policies": {}, "checkConstraints": {}}, "public.user_api_keys": {"name": "user_api_keys", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "guild_id": {"name": "guild_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "key_name": {"name": "key_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "encrypted_key": {"name": "encrypted_key", "type": "text", "primaryKey": false, "notNull": true}, "config": {"name": "config", "type": "jsonb", "primaryKey": false, "notNull": true}, "validation": {"name": "validation", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "is_default": {"name": "is_default", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "last_used_at": {"name": "last_used_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}}, "public.content_freshness_tracking": {"name": "content_freshness_tracking", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "content_id": {"name": "content_id", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "source_id": {"name": "source_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "last_checked": {"name": "last_checked", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "last_changed": {"name": "last_changed", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "change_frequency": {"name": "change_frequency", "type": "jsonb", "primaryKey": false, "notNull": false}, "staleness_score": {"name": "staleness_score", "type": "jsonb", "primaryKey": false, "notNull": false}, "refresh_strategy": {"name": "refresh_strategy", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'interval'"}}, "indexes": {"content_freshness_tracking_content_id_idx": {"name": "content_freshness_tracking_content_id_idx", "columns": [{"expression": "content_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "content_freshness_tracking_source_id_idx": {"name": "content_freshness_tracking_source_id_idx", "columns": [{"expression": "source_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "content_freshness_tracking_last_checked_idx": {"name": "content_freshness_tracking_last_checked_idx", "columns": [{"expression": "last_checked", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "content_freshness_tracking_refresh_strategy_idx": {"name": "content_freshness_tracking_refresh_strategy_idx", "columns": [{"expression": "refresh_strategy", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}}, "public.dynamic_content_cache": {"name": "dynamic_content_cache", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "cache_key": {"name": "cache_key", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "source_id": {"name": "source_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "access_count": {"name": "access_count", "type": "jsonb", "primaryKey": false, "notNull": false}, "tags": {"name": "tags", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_stale": {"name": "is_stale", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {"dynamic_content_cache_cache_key_idx": {"name": "dynamic_content_cache_cache_key_idx", "columns": [{"expression": "cache_key", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "dynamic_content_cache_source_id_idx": {"name": "dynamic_content_cache_source_id_idx", "columns": [{"expression": "source_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "dynamic_content_cache_expires_at_idx": {"name": "dynamic_content_cache_expires_at_idx", "columns": [{"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "dynamic_content_cache_is_stale_idx": {"name": "dynamic_content_cache_is_stale_idx", "columns": [{"expression": "is_stale", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"dynamic_content_cache_cache_key_unique": {"name": "dynamic_content_cache_cache_key_unique", "nullsNotDistinct": false, "columns": ["cache_key"]}}, "policies": {}, "checkConstraints": {}}, "public.dynamic_content_sources": {"name": "dynamic_content_sources", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "source_id": {"name": "source_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "config": {"name": "config", "type": "jsonb", "primaryKey": false, "notNull": false}, "refresh_interval": {"name": "refresh_interval", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'3600'"}, "last_fetched": {"name": "last_fetched", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "last_successful": {"name": "last_successful", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "error_count": {"name": "error_count", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "priority": {"name": "priority", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'medium'"}}, "indexes": {"dynamic_content_sources_source_id_idx": {"name": "dynamic_content_sources_source_id_idx", "columns": [{"expression": "source_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "dynamic_content_sources_type_idx": {"name": "dynamic_content_sources_type_idx", "columns": [{"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "dynamic_content_sources_last_fetched_idx": {"name": "dynamic_content_sources_last_fetched_idx", "columns": [{"expression": "last_fetched", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "dynamic_content_sources_priority_idx": {"name": "dynamic_content_sources_priority_idx", "columns": [{"expression": "priority", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "dynamic_content_sources_active_idx": {"name": "dynamic_content_sources_active_idx", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"dynamic_content_sources_source_id_unique": {"name": "dynamic_content_sources_source_id_unique", "nullsNotDistinct": false, "columns": ["source_id"]}}, "policies": {}, "checkConstraints": {}}, "public.panel_content_mappings": {"name": "panel_content_mappings", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "panel_id": {"name": "panel_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "source_id": {"name": "source_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "mapping": {"name": "mapping", "type": "jsonb", "primaryKey": false, "notNull": false}, "priority": {"name": "priority", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'medium'"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}}, "indexes": {"panel_content_mappings_panel_id_idx": {"name": "panel_content_mappings_panel_id_idx", "columns": [{"expression": "panel_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "panel_content_mappings_source_id_idx": {"name": "panel_content_mappings_source_id_idx", "columns": [{"expression": "source_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "panel_content_mappings_priority_idx": {"name": "panel_content_mappings_priority_idx", "columns": [{"expression": "priority", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "panel_content_mappings_active_idx": {"name": "panel_content_mappings_active_idx", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "panel_content_mappings_panel_source_unique": {"name": "panel_content_mappings_panel_source_unique", "columns": [{"expression": "panel_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "source_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}}, "public.ab_test_results": {"name": "ab_test_results", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "test_id": {"name": "test_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "panel_id": {"name": "panel_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "variant": {"name": "variant", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "channel_id": {"name": "channel_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "guild_id": {"name": "guild_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "outcome": {"name": "outcome", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "integer", "primaryKey": false, "notNull": true}, "test_started": {"name": "test_started", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "test_ended": {"name": "test_ended", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"ab_test_results_test_id_idx": {"name": "ab_test_results_test_id_idx", "columns": [{"expression": "test_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "ab_test_results_panel_id_idx": {"name": "ab_test_results_panel_id_idx", "columns": [{"expression": "panel_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "ab_test_results_variant_idx": {"name": "ab_test_results_variant_idx", "columns": [{"expression": "variant", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "ab_test_results_user_id_idx": {"name": "ab_test_results_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "ab_test_results_outcome_idx": {"name": "ab_test_results_outcome_idx", "columns": [{"expression": "outcome", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "ab_test_results_test_started_idx": {"name": "ab_test_results_test_started_idx", "columns": [{"expression": "test_started", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}}, "public.panel_analytics": {"name": "panel_analytics", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "panel_id": {"name": "panel_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "channel_id": {"name": "channel_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "guild_id": {"name": "guild_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "event_type": {"name": "event_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "event_data": {"name": "event_data", "type": "jsonb", "primaryKey": false, "notNull": false}, "timestamp": {"name": "timestamp", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "session_id": {"name": "session_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "ip": {"name": "ip", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"panel_analytics_user_id_idx": {"name": "panel_analytics_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "panel_analytics_panel_id_idx": {"name": "panel_analytics_panel_id_idx", "columns": [{"expression": "panel_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "panel_analytics_channel_id_idx": {"name": "panel_analytics_channel_id_idx", "columns": [{"expression": "channel_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "panel_analytics_guild_id_idx": {"name": "panel_analytics_guild_id_idx", "columns": [{"expression": "guild_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "panel_analytics_event_type_idx": {"name": "panel_analytics_event_type_idx", "columns": [{"expression": "event_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "panel_analytics_timestamp_idx": {"name": "panel_analytics_timestamp_idx", "columns": [{"expression": "timestamp", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "panel_analytics_session_id_idx": {"name": "panel_analytics_session_id_idx", "columns": [{"expression": "session_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}}, "public.panel_performance_metrics": {"name": "panel_performance_metrics", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "panel_id": {"name": "panel_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "channel_id": {"name": "channel_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "guild_id": {"name": "guild_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "date": {"name": "date", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "metrics": {"name": "metrics", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_aggregated": {"name": "is_aggregated", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {"panel_performance_metrics_panel_id_idx": {"name": "panel_performance_metrics_panel_id_idx", "columns": [{"expression": "panel_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "panel_performance_metrics_channel_id_idx": {"name": "panel_performance_metrics_channel_id_idx", "columns": [{"expression": "channel_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "panel_performance_metrics_guild_id_idx": {"name": "panel_performance_metrics_guild_id_idx", "columns": [{"expression": "guild_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "panel_performance_metrics_date_idx": {"name": "panel_performance_metrics_date_idx", "columns": [{"expression": "date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "panel_performance_metrics_aggregated_idx": {"name": "panel_performance_metrics_aggregated_idx", "columns": [{"expression": "is_aggregated", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}}, "public.user_panel_states": {"name": "user_panel_states", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "panel_id": {"name": "panel_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "channel_id": {"name": "channel_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "guild_id": {"name": "guild_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "preferences": {"name": "preferences", "type": "jsonb", "primaryKey": false, "notNull": false}, "progress": {"name": "progress", "type": "jsonb", "primaryKey": false, "notNull": false}, "interaction_history": {"name": "interaction_history", "type": "jsonb", "primaryKey": false, "notNull": false}, "favorites": {"name": "favorites", "type": "jsonb", "primaryKey": false, "notNull": false}, "last_interaction_at": {"name": "last_interaction_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "session_count": {"name": "session_count", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}}, "indexes": {"user_panel_states_user_id_idx": {"name": "user_panel_states_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_panel_states_panel_id_idx": {"name": "user_panel_states_panel_id_idx", "columns": [{"expression": "panel_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_panel_states_channel_id_idx": {"name": "user_panel_states_channel_id_idx", "columns": [{"expression": "channel_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_panel_states_guild_id_idx": {"name": "user_panel_states_guild_id_idx", "columns": [{"expression": "guild_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_panel_states_last_interaction_idx": {"name": "user_panel_states_last_interaction_idx", "columns": [{"expression": "last_interaction_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_panel_states_user_panel_unique": {"name": "user_panel_states_user_panel_unique", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "panel_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "channel_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}}, "public.deployment_logs": {"name": "deployment_logs", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deployment_id": {"name": "deployment_id", "type": "integer", "primaryKey": false, "notNull": true}, "log_level": {"name": "log_level", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true}, "context": {"name": "context", "type": "jsonb", "primaryKey": false, "notNull": false}, "timestamp": {"name": "timestamp", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"deployment_logs_deployment_id_idx": {"name": "deployment_logs_deployment_id_idx", "columns": [{"expression": "deployment_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "deployment_logs_log_level_idx": {"name": "deployment_logs_log_level_idx", "columns": [{"expression": "log_level", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "deployment_logs_timestamp_idx": {"name": "deployment_logs_timestamp_idx", "columns": [{"expression": "timestamp", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}}, "public.panel_deployments": {"name": "panel_deployments", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "channel_id": {"name": "channel_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "guild_id": {"name": "guild_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "panel_id": {"name": "panel_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "message_id": {"name": "message_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "deployment_status": {"name": "deployment_status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'active'"}, "last_update_at": {"name": "last_update_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "last_successful_update": {"name": "last_successful_update", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "panel_version": {"name": "panel_version", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'1.0.0'"}, "content_hash": {"name": "content_hash", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": false}, "deployment_config": {"name": "deployment_config", "type": "jsonb", "primaryKey": false, "notNull": false}, "discord_state": {"name": "discord_state", "type": "jsonb", "primaryKey": false, "notNull": false}, "deployment_metrics": {"name": "deployment_metrics", "type": "jsonb", "primaryKey": false, "notNull": false}, "error_state": {"name": "error_state", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "needs_cleanup": {"name": "needs_cleanup", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "scheduled_update_at": {"name": "scheduled_update_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "retry_count": {"name": "retry_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "max_retries": {"name": "max_retries", "type": "integer", "primaryKey": false, "notNull": true, "default": 3}, "interaction_count": {"name": "interaction_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "last_interaction_at": {"name": "last_interaction_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"panel_deployments_channel_id_idx": {"name": "panel_deployments_channel_id_idx", "columns": [{"expression": "channel_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "panel_deployments_guild_id_idx": {"name": "panel_deployments_guild_id_idx", "columns": [{"expression": "guild_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "panel_deployments_panel_id_idx": {"name": "panel_deployments_panel_id_idx", "columns": [{"expression": "panel_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "panel_deployments_message_id_idx": {"name": "panel_deployments_message_id_idx", "columns": [{"expression": "message_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "panel_deployments_status_idx": {"name": "panel_deployments_status_idx", "columns": [{"expression": "deployment_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "panel_deployments_last_update_idx": {"name": "panel_deployments_last_update_idx", "columns": [{"expression": "last_update_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "panel_deployments_active_idx": {"name": "panel_deployments_active_idx", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "panel_deployments_cleanup_idx": {"name": "panel_deployments_cleanup_idx", "columns": [{"expression": "needs_cleanup", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "panel_deployments_scheduled_idx": {"name": "panel_deployments_scheduled_idx", "columns": [{"expression": "scheduled_update_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "panel_deployments_channel_panel_unique": {"name": "panel_deployments_channel_panel_unique", "columns": [{"expression": "channel_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "panel_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "panel_deployments_content_hash_idx": {"name": "panel_deployments_content_hash_idx", "columns": [{"expression": "content_hash", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "panel_deployments_interaction_idx": {"name": "panel_deployments_interaction_idx", "columns": [{"expression": "last_interaction_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}}, "public.panel_cleanup_logs": {"name": "panel_cleanup_logs", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "cleanup_session_id": {"name": "cleanup_session_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "cleanup_type": {"name": "cleanup_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "triggered_by": {"name": "triggered_by", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "guild_id": {"name": "guild_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "channel_id": {"name": "channel_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "target_scope": {"name": "target_scope", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "messages_scanned": {"name": "messages_scanned", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "duplicates_found": {"name": "duplicates_found", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "duplicates_removed": {"name": "duplicates_removed", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "duplicates_preserved": {"name": "duplicates_preserved", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "errors_count": {"name": "errors_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "started_at": {"name": "started_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "completed_at": {"name": "completed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "execution_time_ms": {"name": "execution_time_ms", "type": "integer", "primaryKey": false, "notNull": false}, "cleanup_config": {"name": "cleanup_config", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "results_summary": {"name": "results_summary", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "error_details": {"name": "error_details", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'running'"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}}, "public.panel_health_metrics": {"name": "panel_health_metrics", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "guild_id": {"name": "guild_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "channel_id": {"name": "channel_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "panel_id": {"name": "panel_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "active_deployments": {"name": "active_deployments", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "duplicate_deployments": {"name": "duplicate_deployments", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "failed_deployments": {"name": "failed_deployments", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "orphaned_messages": {"name": "orphaned_messages", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "avg_response_time_ms": {"name": "avg_response_time_ms", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "interaction_success_rate": {"name": "interaction_success_rate", "type": "integer", "primaryKey": false, "notNull": false, "default": 100}, "last_successful_interaction": {"name": "last_successful_interaction", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "last_failed_interaction": {"name": "last_failed_interaction", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "memory_usage_mb": {"name": "memory_usage_mb", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "cache_hit_rate": {"name": "cache_hit_rate", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "error_rate_24h": {"name": "error_rate_24h", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "cleanups_performed": {"name": "cleanups_performed", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "last_cleanup_at": {"name": "last_cleanup_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "time_since_last_cleanup_hours": {"name": "time_since_last_cleanup_hours", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "overall_health_score": {"name": "overall_health_score", "type": "integer", "primaryKey": false, "notNull": false, "default": 100}, "health_status": {"name": "health_status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'healthy'"}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}}, "public.panel_versions": {"name": "panel_versions", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "panel_id": {"name": "panel_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "channel_id": {"name": "channel_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "version_number": {"name": "version_number", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "content_hash": {"name": "content_hash", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "panel_content": {"name": "panel_content", "type": "jsonb", "primaryKey": false, "notNull": true}, "changes_from_previous": {"name": "changes_from_previous", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "change_significance": {"name": "change_significance", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false, "default": "'patch'"}, "deployment_id": {"name": "deployment_id", "type": "integer", "primaryKey": false, "notNull": false}, "generation_time_ms": {"name": "generation_time_ms", "type": "integer", "primaryKey": false, "notNull": false}, "content_size_bytes": {"name": "content_size_bytes", "type": "integer", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "is_current": {"name": "is_current", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}