{"id": "9483784a-0ff2-4d57-aaaa-98a9a15d5714", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "discord_id": {"name": "discord_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "username": {"name": "username", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "last_activity_at": {"name": "last_activity_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "preferences": {"name": "preferences", "type": "jsonb", "primaryKey": false, "notNull": false}, "profile": {"name": "profile", "type": "jsonb", "primaryKey": false, "notNull": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "token_expires_at": {"name": "token_expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "experience": {"name": "experience", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "balance": {"name": "balance", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "discriminator": {"name": "discriminator", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "avatar_url": {"name": "avatar_url", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"users_discord_id_idx": {"name": "users_discord_id_idx", "columns": [{"expression": "discord_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_created_at_idx": {"name": "users_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_username_idx": {"name": "users_username_idx", "columns": [{"expression": "username", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_discord_id_unique": {"name": "users_discord_id_unique", "nullsNotDistinct": false, "columns": ["discord_id"]}}, "policies": {}, "checkConstraints": {}}, "public.guilds": {"name": "guilds", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "discord_id": {"name": "discord_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "icon": {"name": "icon", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "settings": {"name": "settings", "type": "jsonb", "primaryKey": false, "notNull": false}, "features": {"name": "features", "type": "jsonb", "primaryKey": false, "notNull": false}, "last_activity_at": {"name": "last_activity_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "owner_discord_id": {"name": "owner_discord_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "welcome_enabled": {"name": "welcome_enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "welcome_channel_id": {"name": "welcome_channel_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "welcome_message": {"name": "welcome_message", "type": "text", "primaryKey": false, "notNull": false}, "welcome_roles": {"name": "welcome_roles", "type": "jsonb", "primaryKey": false, "notNull": false}, "starboard_enabled": {"name": "starboard_enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "starboard_channel_id": {"name": "starboard_channel_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "starboard_threshold": {"name": "starboard_threshold", "type": "integer", "primaryKey": false, "notNull": true, "default": 3}, "guild_id": {"name": "guild_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}}, "indexes": {"guilds_discord_id_idx": {"name": "guilds_discord_id_idx", "columns": [{"expression": "discord_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"guilds_discord_id_unique": {"name": "guilds_discord_id_unique", "nullsNotDistinct": false, "columns": ["discord_id"]}}, "policies": {}, "checkConstraints": {}}, "public.sessions": {"name": "sessions", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "session_id": {"name": "session_id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "encrypted_data": {"name": "encrypted_data", "type": "text", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "device_fingerprint": {"name": "device_fingerprint", "type": "text", "primaryKey": false, "notNull": false}, "is_revoked": {"name": "is_revoked", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "last_accessed_at": {"name": "last_accessed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"sessions_session_id_idx": {"name": "sessions_session_id_idx", "columns": [{"expression": "session_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "sessions_user_id_idx": {"name": "sessions_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "sessions_expires_at_idx": {"name": "sessions_expires_at_idx", "columns": [{"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"sessions_session_id_unique": {"name": "sessions_session_id_unique", "nullsNotDistinct": false, "columns": ["session_id"]}}, "policies": {}, "checkConstraints": {}}, "public.ai_agent_config": {"name": "ai_agent_config", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "guild_id": {"name": "guild_id", "type": "text", "primaryKey": false, "notNull": true}, "agent_type": {"name": "agent_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "enabled": {"name": "enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "configuration": {"name": "configuration", "type": "jsonb", "primaryKey": false, "notNull": false}, "default_channel_id": {"name": "default_channel_id", "type": "text", "primaryKey": false, "notNull": false}, "log_channel_id": {"name": "log_channel_id", "type": "text", "primaryKey": false, "notNull": false}, "permissions": {"name": "permissions", "type": "jsonb", "primaryKey": false, "notNull": false}, "settings": {"name": "settings", "type": "jsonb", "primaryKey": false, "notNull": false}, "last_used_at": {"name": "last_used_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"ai_agent_config_guild_id_idx": {"name": "ai_agent_config_guild_id_idx", "columns": [{"expression": "guild_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "ai_agent_config_agent_type_idx": {"name": "ai_agent_config_agent_type_idx", "columns": [{"expression": "agent_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}}, "public.agent_interactions": {"name": "agent_interactions", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "agent_type": {"name": "agent_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "interaction_type": {"name": "interaction_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "response": {"name": "response", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'completed'"}, "context": {"name": "context", "type": "jsonb", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "channel_id": {"name": "channel_id", "type": "text", "primaryKey": false, "notNull": false}, "message_id": {"name": "message_id", "type": "text", "primaryKey": false, "notNull": false}, "guild_id": {"name": "guild_id", "type": "text", "primaryKey": false, "notNull": false}, "sentiment_score": {"name": "sentiment_score", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "tags": {"name": "tags", "type": "text[]", "primaryKey": false, "notNull": false}}, "indexes": {"agent_interactions_user_id_idx": {"name": "agent_interactions_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "agent_interactions_agent_type_idx": {"name": "agent_interactions_agent_type_idx", "columns": [{"expression": "agent_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "agent_interactions_interaction_type_idx": {"name": "agent_interactions_interaction_type_idx", "columns": [{"expression": "interaction_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "agent_interactions_created_at_idx": {"name": "agent_interactions_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}}, "public.agent_memory": {"name": "agent_memory", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "memory_type": {"name": "memory_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "jsonb", "primaryKey": false, "notNull": true}, "context": {"name": "context", "type": "text", "primaryKey": false, "notNull": false}, "importance": {"name": "importance", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "access_count": {"name": "access_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "last_accessed_at": {"name": "last_accessed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "tags": {"name": "tags", "type": "text[]", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"agent_memory_user_id_idx": {"name": "agent_memory_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "agent_memory_memory_type_idx": {"name": "agent_memory_memory_type_idx", "columns": [{"expression": "memory_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "agent_memory_created_at_idx": {"name": "agent_memory_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}}, "public.user_relationships": {"name": "user_relationships", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "target_user_id": {"name": "target_user_id", "type": "text", "primaryKey": false, "notNull": true}, "relationship_type": {"name": "relationship_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'pending'"}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "established_at": {"name": "established_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"user_relationships_user_id_idx": {"name": "user_relationships_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_relationships_target_user_id_idx": {"name": "user_relationships_target_user_id_idx", "columns": [{"expression": "target_user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}}, "public.personal_growth_plans": {"name": "personal_growth_plans", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'active'"}, "goals": {"name": "goals", "type": "jsonb", "primaryKey": false, "notNull": false}, "milestones": {"name": "milestones", "type": "jsonb", "primaryKey": false, "notNull": false}, "start_date": {"name": "start_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "target_completion_date": {"name": "target_completion_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "actual_completion_date": {"name": "actual_completion_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "progress_percentage": {"name": "progress_percentage", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"personal_growth_plans_user_id_idx": {"name": "personal_growth_plans_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "personal_growth_plans_status_idx": {"name": "personal_growth_plans_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}