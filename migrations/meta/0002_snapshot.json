{"id": "7959c286-30de-4add-91f6-4cda50f20ed3", "prevId": "a46de387-7d75-4e69-b471-3fd6e4a8b06f", "version": "7", "dialect": "postgresql", "tables": {"public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "discord_id": {"name": "discord_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "username": {"name": "username", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "last_activity_at": {"name": "last_activity_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "preferences": {"name": "preferences", "type": "jsonb", "primaryKey": false, "notNull": false}, "profile": {"name": "profile", "type": "jsonb", "primaryKey": false, "notNull": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "token_expires_at": {"name": "token_expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "experience": {"name": "experience", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "balance": {"name": "balance", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "discriminator": {"name": "discriminator", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "avatar_url": {"name": "avatar_url", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"users_discord_id_idx": {"name": "users_discord_id_idx", "columns": [{"expression": "discord_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_created_at_idx": {"name": "users_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_username_idx": {"name": "users_username_idx", "columns": [{"expression": "username", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_discord_id_unique": {"name": "users_discord_id_unique", "nullsNotDistinct": false, "columns": ["discord_id"]}}, "policies": {}, "checkConstraints": {}}, "public.guilds": {"name": "guilds", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "discord_id": {"name": "discord_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "icon": {"name": "icon", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "settings": {"name": "settings", "type": "jsonb", "primaryKey": false, "notNull": false}, "features": {"name": "features", "type": "jsonb", "primaryKey": false, "notNull": false}, "last_activity_at": {"name": "last_activity_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "owner_discord_id": {"name": "owner_discord_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "welcome_enabled": {"name": "welcome_enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "welcome_channel_id": {"name": "welcome_channel_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "welcome_message": {"name": "welcome_message", "type": "text", "primaryKey": false, "notNull": false}, "welcome_roles": {"name": "welcome_roles", "type": "jsonb", "primaryKey": false, "notNull": false}, "starboard_enabled": {"name": "starboard_enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "starboard_channel_id": {"name": "starboard_channel_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "starboard_threshold": {"name": "starboard_threshold", "type": "integer", "primaryKey": false, "notNull": true, "default": 3}, "guild_id": {"name": "guild_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}}, "indexes": {"guilds_discord_id_idx": {"name": "guilds_discord_id_idx", "columns": [{"expression": "discord_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"guilds_discord_id_unique": {"name": "guilds_discord_id_unique", "nullsNotDistinct": false, "columns": ["discord_id"]}}, "policies": {}, "checkConstraints": {}}, "public.sessions": {"name": "sessions", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "session_id": {"name": "session_id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "encrypted_data": {"name": "encrypted_data", "type": "text", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "device_fingerprint": {"name": "device_fingerprint", "type": "text", "primaryKey": false, "notNull": false}, "is_revoked": {"name": "is_revoked", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "last_accessed_at": {"name": "last_accessed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"sessions_session_id_idx": {"name": "sessions_session_id_idx", "columns": [{"expression": "session_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "sessions_user_id_idx": {"name": "sessions_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "sessions_expires_at_idx": {"name": "sessions_expires_at_idx", "columns": [{"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"sessions_session_id_unique": {"name": "sessions_session_id_unique", "nullsNotDistinct": false, "columns": ["session_id"]}}, "policies": {}, "checkConstraints": {}}, "public.ai_agent_config": {"name": "ai_agent_config", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "guild_id": {"name": "guild_id", "type": "text", "primaryKey": false, "notNull": true}, "agent_type": {"name": "agent_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "enabled": {"name": "enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "configuration": {"name": "configuration", "type": "jsonb", "primaryKey": false, "notNull": false}, "default_channel_id": {"name": "default_channel_id", "type": "text", "primaryKey": false, "notNull": false}, "log_channel_id": {"name": "log_channel_id", "type": "text", "primaryKey": false, "notNull": false}, "permissions": {"name": "permissions", "type": "jsonb", "primaryKey": false, "notNull": false}, "settings": {"name": "settings", "type": "jsonb", "primaryKey": false, "notNull": false}, "last_used_at": {"name": "last_used_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"ai_agent_config_guild_id_idx": {"name": "ai_agent_config_guild_id_idx", "columns": [{"expression": "guild_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "ai_agent_config_agent_type_idx": {"name": "ai_agent_config_agent_type_idx", "columns": [{"expression": "agent_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}}, "public.agent_interactions": {"name": "agent_interactions", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "agent_type": {"name": "agent_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "interaction_type": {"name": "interaction_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "response": {"name": "response", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'completed'"}, "context": {"name": "context", "type": "jsonb", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "channel_id": {"name": "channel_id", "type": "text", "primaryKey": false, "notNull": false}, "message_id": {"name": "message_id", "type": "text", "primaryKey": false, "notNull": false}, "guild_id": {"name": "guild_id", "type": "text", "primaryKey": false, "notNull": false}, "sentiment_score": {"name": "sentiment_score", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "tags": {"name": "tags", "type": "text[]", "primaryKey": false, "notNull": false}}, "indexes": {"agent_interactions_user_id_idx": {"name": "agent_interactions_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "agent_interactions_agent_type_idx": {"name": "agent_interactions_agent_type_idx", "columns": [{"expression": "agent_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "agent_interactions_interaction_type_idx": {"name": "agent_interactions_interaction_type_idx", "columns": [{"expression": "interaction_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "agent_interactions_created_at_idx": {"name": "agent_interactions_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}}, "public.agent_memory": {"name": "agent_memory", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "memory_type": {"name": "memory_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "jsonb", "primaryKey": false, "notNull": true}, "context": {"name": "context", "type": "text", "primaryKey": false, "notNull": false}, "importance": {"name": "importance", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "access_count": {"name": "access_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "last_accessed_at": {"name": "last_accessed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "tags": {"name": "tags", "type": "text[]", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"agent_memory_user_id_idx": {"name": "agent_memory_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "agent_memory_memory_type_idx": {"name": "agent_memory_memory_type_idx", "columns": [{"expression": "memory_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "agent_memory_created_at_idx": {"name": "agent_memory_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}}, "public.user_relationships": {"name": "user_relationships", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "target_user_id": {"name": "target_user_id", "type": "text", "primaryKey": false, "notNull": true}, "relationship_type": {"name": "relationship_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'pending'"}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "established_at": {"name": "established_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"user_relationships_user_id_idx": {"name": "user_relationships_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_relationships_target_user_id_idx": {"name": "user_relationships_target_user_id_idx", "columns": [{"expression": "target_user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}}, "public.personal_growth_plans": {"name": "personal_growth_plans", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'active'"}, "goals": {"name": "goals", "type": "jsonb", "primaryKey": false, "notNull": false}, "milestones": {"name": "milestones", "type": "jsonb", "primaryKey": false, "notNull": false}, "start_date": {"name": "start_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "target_completion_date": {"name": "target_completion_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "actual_completion_date": {"name": "actual_completion_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "progress_percentage": {"name": "progress_percentage", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"personal_growth_plans_user_id_idx": {"name": "personal_growth_plans_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "personal_growth_plans_status_idx": {"name": "personal_growth_plans_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}}, "public.ai_chat_sessions": {"name": "ai_chat_sessions", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "guild_id": {"name": "guild_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "agent_type": {"name": "agent_type", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "thread_id": {"name": "thread_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "channel_id": {"name": "channel_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'active'"}, "access_level": {"name": "access_level", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'free'"}, "session_data": {"name": "session_data", "type": "jsonb", "primaryKey": false, "notNull": false}, "message_count": {"name": "message_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "last_message_at": {"name": "last_message_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "archived_at": {"name": "archived_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}}, "public.ai_channel_configs": {"name": "ai_channel_configs", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "guild_id": {"name": "guild_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "ai_channel_id": {"name": "ai_channel_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "panel_message_id": {"name": "panel_message_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "enabled": {"name": "enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "settings": {"name": "settings", "type": "jsonb", "primaryKey": false, "notNull": true}, "last_panel_update": {"name": "last_panel_update", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"ai_channel_configs_guild_id_unique": {"name": "ai_channel_configs_guild_id_unique", "nullsNotDistinct": false, "columns": ["guild_id"]}}, "policies": {}, "checkConstraints": {}}, "public.user_api_keys": {"name": "user_api_keys", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "guild_id": {"name": "guild_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "key_name": {"name": "key_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "encrypted_key": {"name": "encrypted_key", "type": "text", "primaryKey": false, "notNull": true}, "config": {"name": "config", "type": "jsonb", "primaryKey": false, "notNull": true}, "validation": {"name": "validation", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "is_default": {"name": "is_default", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "last_used_at": {"name": "last_used_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}}, "public.content_freshness_tracking": {"name": "content_freshness_tracking", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "content_id": {"name": "content_id", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "source_id": {"name": "source_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "last_checked": {"name": "last_checked", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "last_changed": {"name": "last_changed", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "change_frequency": {"name": "change_frequency", "type": "jsonb", "primaryKey": false, "notNull": false}, "staleness_score": {"name": "staleness_score", "type": "jsonb", "primaryKey": false, "notNull": false}, "refresh_strategy": {"name": "refresh_strategy", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'interval'"}}, "indexes": {"content_freshness_tracking_content_id_idx": {"name": "content_freshness_tracking_content_id_idx", "columns": [{"expression": "content_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "content_freshness_tracking_source_id_idx": {"name": "content_freshness_tracking_source_id_idx", "columns": [{"expression": "source_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "content_freshness_tracking_last_checked_idx": {"name": "content_freshness_tracking_last_checked_idx", "columns": [{"expression": "last_checked", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "content_freshness_tracking_refresh_strategy_idx": {"name": "content_freshness_tracking_refresh_strategy_idx", "columns": [{"expression": "refresh_strategy", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}}, "public.dynamic_content_cache": {"name": "dynamic_content_cache", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "cache_key": {"name": "cache_key", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "source_id": {"name": "source_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "access_count": {"name": "access_count", "type": "jsonb", "primaryKey": false, "notNull": false}, "tags": {"name": "tags", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_stale": {"name": "is_stale", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {"dynamic_content_cache_cache_key_idx": {"name": "dynamic_content_cache_cache_key_idx", "columns": [{"expression": "cache_key", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "dynamic_content_cache_source_id_idx": {"name": "dynamic_content_cache_source_id_idx", "columns": [{"expression": "source_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "dynamic_content_cache_expires_at_idx": {"name": "dynamic_content_cache_expires_at_idx", "columns": [{"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "dynamic_content_cache_is_stale_idx": {"name": "dynamic_content_cache_is_stale_idx", "columns": [{"expression": "is_stale", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"dynamic_content_cache_cache_key_unique": {"name": "dynamic_content_cache_cache_key_unique", "nullsNotDistinct": false, "columns": ["cache_key"]}}, "policies": {}, "checkConstraints": {}}, "public.dynamic_content_sources": {"name": "dynamic_content_sources", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "source_id": {"name": "source_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "config": {"name": "config", "type": "jsonb", "primaryKey": false, "notNull": false}, "refresh_interval": {"name": "refresh_interval", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'3600'"}, "last_fetched": {"name": "last_fetched", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "last_successful": {"name": "last_successful", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "error_count": {"name": "error_count", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "priority": {"name": "priority", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'medium'"}}, "indexes": {"dynamic_content_sources_source_id_idx": {"name": "dynamic_content_sources_source_id_idx", "columns": [{"expression": "source_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "dynamic_content_sources_type_idx": {"name": "dynamic_content_sources_type_idx", "columns": [{"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "dynamic_content_sources_last_fetched_idx": {"name": "dynamic_content_sources_last_fetched_idx", "columns": [{"expression": "last_fetched", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "dynamic_content_sources_priority_idx": {"name": "dynamic_content_sources_priority_idx", "columns": [{"expression": "priority", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "dynamic_content_sources_active_idx": {"name": "dynamic_content_sources_active_idx", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"dynamic_content_sources_source_id_unique": {"name": "dynamic_content_sources_source_id_unique", "nullsNotDistinct": false, "columns": ["source_id"]}}, "policies": {}, "checkConstraints": {}}, "public.panel_content_mappings": {"name": "panel_content_mappings", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "panel_id": {"name": "panel_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "source_id": {"name": "source_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "mapping": {"name": "mapping", "type": "jsonb", "primaryKey": false, "notNull": false}, "priority": {"name": "priority", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'medium'"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}}, "indexes": {"panel_content_mappings_panel_id_idx": {"name": "panel_content_mappings_panel_id_idx", "columns": [{"expression": "panel_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "panel_content_mappings_source_id_idx": {"name": "panel_content_mappings_source_id_idx", "columns": [{"expression": "source_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "panel_content_mappings_priority_idx": {"name": "panel_content_mappings_priority_idx", "columns": [{"expression": "priority", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "panel_content_mappings_active_idx": {"name": "panel_content_mappings_active_idx", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "panel_content_mappings_panel_source_unique": {"name": "panel_content_mappings_panel_source_unique", "columns": [{"expression": "panel_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "source_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}}, "public.ab_test_results": {"name": "ab_test_results", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "test_id": {"name": "test_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "panel_id": {"name": "panel_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "variant": {"name": "variant", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "channel_id": {"name": "channel_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "guild_id": {"name": "guild_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "outcome": {"name": "outcome", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "integer", "primaryKey": false, "notNull": true}, "test_started": {"name": "test_started", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "test_ended": {"name": "test_ended", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"ab_test_results_test_id_idx": {"name": "ab_test_results_test_id_idx", "columns": [{"expression": "test_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "ab_test_results_panel_id_idx": {"name": "ab_test_results_panel_id_idx", "columns": [{"expression": "panel_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "ab_test_results_variant_idx": {"name": "ab_test_results_variant_idx", "columns": [{"expression": "variant", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "ab_test_results_user_id_idx": {"name": "ab_test_results_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "ab_test_results_outcome_idx": {"name": "ab_test_results_outcome_idx", "columns": [{"expression": "outcome", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "ab_test_results_test_started_idx": {"name": "ab_test_results_test_started_idx", "columns": [{"expression": "test_started", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}}, "public.panel_analytics": {"name": "panel_analytics", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "panel_id": {"name": "panel_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "channel_id": {"name": "channel_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "guild_id": {"name": "guild_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "event_type": {"name": "event_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "event_data": {"name": "event_data", "type": "jsonb", "primaryKey": false, "notNull": false}, "timestamp": {"name": "timestamp", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "session_id": {"name": "session_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "ip": {"name": "ip", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"panel_analytics_user_id_idx": {"name": "panel_analytics_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "panel_analytics_panel_id_idx": {"name": "panel_analytics_panel_id_idx", "columns": [{"expression": "panel_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "panel_analytics_channel_id_idx": {"name": "panel_analytics_channel_id_idx", "columns": [{"expression": "channel_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "panel_analytics_guild_id_idx": {"name": "panel_analytics_guild_id_idx", "columns": [{"expression": "guild_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "panel_analytics_event_type_idx": {"name": "panel_analytics_event_type_idx", "columns": [{"expression": "event_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "panel_analytics_timestamp_idx": {"name": "panel_analytics_timestamp_idx", "columns": [{"expression": "timestamp", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "panel_analytics_session_id_idx": {"name": "panel_analytics_session_id_idx", "columns": [{"expression": "session_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}}, "public.panel_performance_metrics": {"name": "panel_performance_metrics", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "panel_id": {"name": "panel_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "channel_id": {"name": "channel_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "guild_id": {"name": "guild_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "date": {"name": "date", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "metrics": {"name": "metrics", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_aggregated": {"name": "is_aggregated", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {"panel_performance_metrics_panel_id_idx": {"name": "panel_performance_metrics_panel_id_idx", "columns": [{"expression": "panel_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "panel_performance_metrics_channel_id_idx": {"name": "panel_performance_metrics_channel_id_idx", "columns": [{"expression": "channel_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "panel_performance_metrics_guild_id_idx": {"name": "panel_performance_metrics_guild_id_idx", "columns": [{"expression": "guild_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "panel_performance_metrics_date_idx": {"name": "panel_performance_metrics_date_idx", "columns": [{"expression": "date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "panel_performance_metrics_aggregated_idx": {"name": "panel_performance_metrics_aggregated_idx", "columns": [{"expression": "is_aggregated", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}}, "public.user_panel_states": {"name": "user_panel_states", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "panel_id": {"name": "panel_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "channel_id": {"name": "channel_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "guild_id": {"name": "guild_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "preferences": {"name": "preferences", "type": "jsonb", "primaryKey": false, "notNull": false}, "progress": {"name": "progress", "type": "jsonb", "primaryKey": false, "notNull": false}, "interaction_history": {"name": "interaction_history", "type": "jsonb", "primaryKey": false, "notNull": false}, "favorites": {"name": "favorites", "type": "jsonb", "primaryKey": false, "notNull": false}, "last_interaction_at": {"name": "last_interaction_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "session_count": {"name": "session_count", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}}, "indexes": {"user_panel_states_user_id_idx": {"name": "user_panel_states_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_panel_states_panel_id_idx": {"name": "user_panel_states_panel_id_idx", "columns": [{"expression": "panel_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_panel_states_channel_id_idx": {"name": "user_panel_states_channel_id_idx", "columns": [{"expression": "channel_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_panel_states_guild_id_idx": {"name": "user_panel_states_guild_id_idx", "columns": [{"expression": "guild_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_panel_states_last_interaction_idx": {"name": "user_panel_states_last_interaction_idx", "columns": [{"expression": "last_interaction_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_panel_states_user_panel_unique": {"name": "user_panel_states_user_panel_unique", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "panel_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "channel_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}