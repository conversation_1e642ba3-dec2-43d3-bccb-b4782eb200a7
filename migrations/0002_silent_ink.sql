CREATE TABLE "user_api_keys" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"user_id" varchar(255) NOT NULL,
	"guild_id" varchar(255) NOT NULL,
	"provider" varchar(50) NOT NULL,
	"key_name" varchar(100) NOT NULL,
	"encrypted_key" text NOT NULL,
	"config" jsonb NOT NULL,
	"validation" jsonb,
	"is_active" boolean DEFAULT true NOT NULL,
	"is_default" boolean DEFAULT false NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"last_used_at" timestamp,
	"expires_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "content_freshness_tracking" (
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"deleted_at" timestamp with time zone,
	"content_id" varchar(200) NOT NULL,
	"source_id" varchar(100) NOT NULL,
	"last_checked" timestamp with time zone NOT NULL,
	"last_changed" timestamp with time zone,
	"change_frequency" jsonb,
	"staleness_score" jsonb,
	"refresh_strategy" varchar(50) DEFAULT 'interval' NOT NULL
);
--> statement-breakpoint
CREATE TABLE "dynamic_content_cache" (
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"deleted_at" timestamp with time zone,
	"cache_key" varchar(200) NOT NULL,
	"source_id" varchar(100) NOT NULL,
	"data" jsonb,
	"metadata" jsonb,
	"expires_at" timestamp with time zone NOT NULL,
	"access_count" jsonb,
	"tags" jsonb,
	"is_stale" boolean DEFAULT false NOT NULL,
	CONSTRAINT "dynamic_content_cache_cache_key_unique" UNIQUE("cache_key")
);
--> statement-breakpoint
CREATE TABLE "dynamic_content_sources" (
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"deleted_at" timestamp with time zone,
	"source_id" varchar(100) NOT NULL,
	"name" varchar(200) NOT NULL,
	"type" varchar(50) NOT NULL,
	"config" jsonb,
	"refresh_interval" varchar(20) DEFAULT '3600' NOT NULL,
	"last_fetched" timestamp with time zone,
	"last_successful" timestamp with time zone,
	"error_count" jsonb,
	"is_active" boolean DEFAULT true NOT NULL,
	"priority" varchar(20) DEFAULT 'medium' NOT NULL,
	CONSTRAINT "dynamic_content_sources_source_id_unique" UNIQUE("source_id")
);
--> statement-breakpoint
CREATE TABLE "panel_content_mappings" (
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"deleted_at" timestamp with time zone,
	"panel_id" varchar(100) NOT NULL,
	"source_id" varchar(100) NOT NULL,
	"mapping" jsonb,
	"priority" varchar(20) DEFAULT 'medium' NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL
);
--> statement-breakpoint
CREATE TABLE "ab_test_results" (
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"deleted_at" timestamp with time zone,
	"test_id" varchar(100) NOT NULL,
	"panel_id" varchar(100) NOT NULL,
	"variant" varchar(50) NOT NULL,
	"user_id" varchar(50),
	"channel_id" varchar(50) NOT NULL,
	"guild_id" varchar(50) NOT NULL,
	"outcome" varchar(50) NOT NULL,
	"value" integer NOT NULL,
	"test_started" timestamp with time zone NOT NULL,
	"test_ended" timestamp with time zone,
	"metadata" jsonb
);
--> statement-breakpoint
CREATE TABLE "panel_analytics" (
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"deleted_at" timestamp with time zone,
	"user_id" varchar(50),
	"panel_id" varchar(100) NOT NULL,
	"channel_id" varchar(50) NOT NULL,
	"guild_id" varchar(50) NOT NULL,
	"event_type" varchar(50) NOT NULL,
	"event_data" jsonb,
	"timestamp" timestamp with time zone DEFAULT now() NOT NULL,
	"session_id" varchar(100),
	"ip" varchar(45),
	"user_agent" text,
	"metadata" jsonb
);
--> statement-breakpoint
CREATE TABLE "panel_performance_metrics" (
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"deleted_at" timestamp with time zone,
	"panel_id" varchar(100) NOT NULL,
	"channel_id" varchar(50) NOT NULL,
	"guild_id" varchar(50) NOT NULL,
	"date" timestamp with time zone NOT NULL,
	"metrics" jsonb,
	"is_aggregated" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE "user_panel_states" (
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"deleted_at" timestamp with time zone,
	"user_id" varchar(50) NOT NULL,
	"panel_id" varchar(100) NOT NULL,
	"channel_id" varchar(50) NOT NULL,
	"guild_id" varchar(50) NOT NULL,
	"preferences" jsonb,
	"progress" jsonb,
	"interaction_history" jsonb,
	"favorites" jsonb,
	"last_interaction_at" timestamp with time zone,
	"session_count" jsonb,
	"is_active" boolean DEFAULT true NOT NULL
);
--> statement-breakpoint
CREATE INDEX "content_freshness_tracking_content_id_idx" ON "content_freshness_tracking" USING btree ("content_id");--> statement-breakpoint
CREATE INDEX "content_freshness_tracking_source_id_idx" ON "content_freshness_tracking" USING btree ("source_id");--> statement-breakpoint
CREATE INDEX "content_freshness_tracking_last_checked_idx" ON "content_freshness_tracking" USING btree ("last_checked");--> statement-breakpoint
CREATE INDEX "content_freshness_tracking_refresh_strategy_idx" ON "content_freshness_tracking" USING btree ("refresh_strategy");--> statement-breakpoint
CREATE INDEX "dynamic_content_cache_cache_key_idx" ON "dynamic_content_cache" USING btree ("cache_key");--> statement-breakpoint
CREATE INDEX "dynamic_content_cache_source_id_idx" ON "dynamic_content_cache" USING btree ("source_id");--> statement-breakpoint
CREATE INDEX "dynamic_content_cache_expires_at_idx" ON "dynamic_content_cache" USING btree ("expires_at");--> statement-breakpoint
CREATE INDEX "dynamic_content_cache_is_stale_idx" ON "dynamic_content_cache" USING btree ("is_stale");--> statement-breakpoint
CREATE INDEX "dynamic_content_sources_source_id_idx" ON "dynamic_content_sources" USING btree ("source_id");--> statement-breakpoint
CREATE INDEX "dynamic_content_sources_type_idx" ON "dynamic_content_sources" USING btree ("type");--> statement-breakpoint
CREATE INDEX "dynamic_content_sources_last_fetched_idx" ON "dynamic_content_sources" USING btree ("last_fetched");--> statement-breakpoint
CREATE INDEX "dynamic_content_sources_priority_idx" ON "dynamic_content_sources" USING btree ("priority");--> statement-breakpoint
CREATE INDEX "dynamic_content_sources_active_idx" ON "dynamic_content_sources" USING btree ("is_active");--> statement-breakpoint
CREATE INDEX "panel_content_mappings_panel_id_idx" ON "panel_content_mappings" USING btree ("panel_id");--> statement-breakpoint
CREATE INDEX "panel_content_mappings_source_id_idx" ON "panel_content_mappings" USING btree ("source_id");--> statement-breakpoint
CREATE INDEX "panel_content_mappings_priority_idx" ON "panel_content_mappings" USING btree ("priority");--> statement-breakpoint
CREATE INDEX "panel_content_mappings_active_idx" ON "panel_content_mappings" USING btree ("is_active");--> statement-breakpoint
CREATE INDEX "panel_content_mappings_panel_source_unique" ON "panel_content_mappings" USING btree ("panel_id","source_id");--> statement-breakpoint
CREATE INDEX "ab_test_results_test_id_idx" ON "ab_test_results" USING btree ("test_id");--> statement-breakpoint
CREATE INDEX "ab_test_results_panel_id_idx" ON "ab_test_results" USING btree ("panel_id");--> statement-breakpoint
CREATE INDEX "ab_test_results_variant_idx" ON "ab_test_results" USING btree ("variant");--> statement-breakpoint
CREATE INDEX "ab_test_results_user_id_idx" ON "ab_test_results" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "ab_test_results_outcome_idx" ON "ab_test_results" USING btree ("outcome");--> statement-breakpoint
CREATE INDEX "ab_test_results_test_started_idx" ON "ab_test_results" USING btree ("test_started");--> statement-breakpoint
CREATE INDEX "panel_analytics_user_id_idx" ON "panel_analytics" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "panel_analytics_panel_id_idx" ON "panel_analytics" USING btree ("panel_id");--> statement-breakpoint
CREATE INDEX "panel_analytics_channel_id_idx" ON "panel_analytics" USING btree ("channel_id");--> statement-breakpoint
CREATE INDEX "panel_analytics_guild_id_idx" ON "panel_analytics" USING btree ("guild_id");--> statement-breakpoint
CREATE INDEX "panel_analytics_event_type_idx" ON "panel_analytics" USING btree ("event_type");--> statement-breakpoint
CREATE INDEX "panel_analytics_timestamp_idx" ON "panel_analytics" USING btree ("timestamp");--> statement-breakpoint
CREATE INDEX "panel_analytics_session_id_idx" ON "panel_analytics" USING btree ("session_id");--> statement-breakpoint
CREATE INDEX "panel_performance_metrics_panel_id_idx" ON "panel_performance_metrics" USING btree ("panel_id");--> statement-breakpoint
CREATE INDEX "panel_performance_metrics_channel_id_idx" ON "panel_performance_metrics" USING btree ("channel_id");--> statement-breakpoint
CREATE INDEX "panel_performance_metrics_guild_id_idx" ON "panel_performance_metrics" USING btree ("guild_id");--> statement-breakpoint
CREATE INDEX "panel_performance_metrics_date_idx" ON "panel_performance_metrics" USING btree ("date");--> statement-breakpoint
CREATE INDEX "panel_performance_metrics_aggregated_idx" ON "panel_performance_metrics" USING btree ("is_aggregated");--> statement-breakpoint
CREATE INDEX "user_panel_states_user_id_idx" ON "user_panel_states" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "user_panel_states_panel_id_idx" ON "user_panel_states" USING btree ("panel_id");--> statement-breakpoint
CREATE INDEX "user_panel_states_channel_id_idx" ON "user_panel_states" USING btree ("channel_id");--> statement-breakpoint
CREATE INDEX "user_panel_states_guild_id_idx" ON "user_panel_states" USING btree ("guild_id");--> statement-breakpoint
CREATE INDEX "user_panel_states_last_interaction_idx" ON "user_panel_states" USING btree ("last_interaction_at");--> statement-breakpoint
CREATE INDEX "user_panel_states_user_panel_unique" ON "user_panel_states" USING btree ("user_id","panel_id","channel_id");