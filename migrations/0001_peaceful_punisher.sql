CREATE TABLE "ai_chat_sessions" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"user_id" varchar(255) NOT NULL,
	"guild_id" varchar(255) NOT NULL,
	"agent_type" varchar(100) NOT NULL,
	"thread_id" varchar(255) NOT NULL,
	"channel_id" varchar(255) NOT NULL,
	"status" varchar(50) DEFAULT 'active' NOT NULL,
	"access_level" varchar(50) DEFAULT 'free' NOT NULL,
	"session_data" jsonb,
	"message_count" integer DEFAULT 0,
	"last_message_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"archived_at" timestamp,
	"expires_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "ai_channel_configs" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"guild_id" varchar(255) NOT NULL,
	"ai_channel_id" varchar(255) NOT NULL,
	"panel_message_id" varchar(255),
	"enabled" boolean DEFAULT true NOT NULL,
	"settings" jsonb NOT NULL,
	"last_panel_update" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "ai_channel_configs_guild_id_unique" UNIQUE("guild_id")
);
