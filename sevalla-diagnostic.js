require('dotenv').config();
const { Pool } = require('pg');
const https = require('https');
const http = require('http');

async function runSevallaDiagnostic() {
  console.log('🔍 SEVALLA DEPLOYMENT DIAGNOSTIC TOOL');
  console.log('=' .repeat(60));
  console.log('This tool helps diagnose common issues with Discord bot deployments on Sevalla');
  
  const issues = [];
  const warnings = [];
  const successes = [];
  
  // 1. Environment Variables Check
  console.log('\n📋 CHECKING ENVIRONMENT VARIABLES...');
  
  const requiredEnvVars = [
    'DATABASE_URL',
    'DISCORD_TOKEN', 
    'BOT_CLIENT_ID',
    'BOT_CLIENT_SECRET',
    'WEB_URL',
    'NEXT_PUBLIC_API_ENDPOINT'
  ];
  
  const optionalEnvVars = [
    'INTERNAL_API_ENDPOINT',
    'WHOP_API_KEY',
    'NEXT_PUBLIC_WHOP_APP_ID'
  ];
  
  for (const envVar of requiredEnvVars) {
    if (process.env[envVar]) {
      successes.push(`✅ ${envVar} is set`);
    } else {
      issues.push(`❌ ${envVar} is missing (REQUIRED)`);
    }
  }
  
  for (const envVar of optionalEnvVars) {
    if (process.env[envVar]) {
      successes.push(`✅ ${envVar} is set`);
    } else {
      warnings.push(`⚠️  ${envVar} is not set (optional)`);
    }
  }
  
  // 2. Database Connection Test
  console.log('\n🗄️  TESTING DATABASE CONNECTION...');
  
  if (process.env.DATABASE_URL) {
    try {
      const pool = new Pool({
        connectionString: process.env.DATABASE_URL,
        ssl: { rejectUnauthorized: false },
        connectionTimeoutMillis: 10000
      });
      
      const client = await pool.connect();
      await client.query('SELECT 1');
      client.release();
      await pool.end();
      
      successes.push('✅ Database connection successful');
    } catch (err) {
      issues.push(`❌ Database connection failed: ${err.message}`);
      
      // Specific troubleshooting
      if (err.message.includes('timeout')) {
        issues.push('   💡 Possible cause: Network timeout - check if Neon database region matches Sevalla region');
      }
      if (err.message.includes('authentication')) {
        issues.push('   💡 Possible cause: Invalid credentials in DATABASE_URL');
      }
      if (err.message.includes('SSL')) {
        issues.push('   💡 Possible cause: SSL configuration issue - ensure sslmode=require in DATABASE_URL');
      }
    }
  }
  
  // 3. URL Configuration Check
  console.log('\n🌐 CHECKING URL CONFIGURATION...');
  
  const webUrl = process.env.WEB_URL;
  const apiEndpoint = process.env.NEXT_PUBLIC_API_ENDPOINT;
  
  if (webUrl && webUrl.includes('sevalla.app')) {
    successes.push('✅ WEB_URL appears to be a valid Sevalla URL');
  } else if (webUrl) {
    warnings.push('⚠️  WEB_URL does not appear to be a Sevalla URL');
  }
  
  if (apiEndpoint && apiEndpoint.includes('sevalla.app')) {
    successes.push('✅ API endpoint appears to be a valid Sevalla URL');
  } else if (apiEndpoint) {
    warnings.push('⚠️  API endpoint does not appear to be a Sevalla URL');
  }
  
  // 4. Discord Token Validation
  console.log('\n🤖 VALIDATING DISCORD CONFIGURATION...');
  
  const discordToken = process.env.DISCORD_TOKEN;
  if (discordToken) {
    if (discordToken.length > 50 && discordToken.includes('.')) {
      successes.push('✅ Discord token format appears valid');
    } else {
      issues.push('❌ Discord token format appears invalid');
    }
  }
  
  const clientId = process.env.BOT_CLIENT_ID;
  if (clientId && /^\d{17,19}$/.test(clientId)) {
    successes.push('✅ Bot Client ID format appears valid');
  } else if (clientId) {
    issues.push('❌ Bot Client ID format appears invalid (should be 17-19 digits)');
  }
  
  // 5. Port Configuration
  console.log('\n🔌 CHECKING PORT CONFIGURATION...');
  
  const port = process.env.PORT || '8080';
  if (port === '8080') {
    successes.push('✅ Port is set to 8080 (Sevalla default)');
  } else {
    warnings.push(`⚠️  Port is set to ${port} (Sevalla typically uses 8080)`);
  }
  
  // 6. Node Environment
  console.log('\n⚙️  CHECKING NODE ENVIRONMENT...');
  
  const nodeEnv = process.env.NODE_ENV;
  if (nodeEnv === 'production') {
    successes.push('✅ NODE_ENV is set to production');
  } else {
    warnings.push(`⚠️  NODE_ENV is set to ${nodeEnv || 'undefined'} (should be "production" for Sevalla)`);
  }
  
  // 7. CORS Configuration Check
  console.log('\n🔒 CHECKING CORS CONFIGURATION...');
  
  if (webUrl && apiEndpoint) {
    const webDomain = new URL(webUrl).hostname;
    const apiDomain = new URL(apiEndpoint).hostname;
    
    if (webDomain.endsWith('sevalla.app') && apiDomain.endsWith('sevalla.app')) {
      successes.push('✅ Frontend and backend are both on Sevalla domains');
    } else {
      warnings.push('⚠️  Frontend and backend domains may have CORS issues');
    }
  }
  
  // 8. Common Sevalla Issues Check
  console.log('\n🔧 CHECKING FOR COMMON SEVALLA ISSUES...');
  
  // Check for localhost references
  const envContent = JSON.stringify(process.env);
  if (envContent.includes('localhost') || envContent.includes('127.0.0.1')) {
    issues.push('❌ Found localhost references in environment variables');
    issues.push('   💡 Replace all localhost URLs with your Sevalla deployment URLs');
  } else {
    successes.push('✅ No localhost references found');
  }
  
  // Check for development settings
  if (process.env.ENABLE_ENV_LOGIN === 'true') {
    warnings.push('⚠️  ENABLE_ENV_LOGIN is set to true (should be false for production)');
  } else {
    successes.push('✅ ENABLE_ENV_LOGIN is properly configured for production');
  }
  
  // 9. Generate Report
  console.log('\n📊 DIAGNOSTIC REPORT');
  console.log('=' .repeat(60));
  
  if (successes.length > 0) {
    console.log('\n✅ SUCCESSFUL CHECKS:');
    successes.forEach(success => console.log(`   ${success}`));
  }
  
  if (warnings.length > 0) {
    console.log('\n⚠️  WARNINGS:');
    warnings.forEach(warning => console.log(`   ${warning}`));
  }
  
  if (issues.length > 0) {
    console.log('\n❌ ISSUES FOUND:');
    issues.forEach(issue => console.log(`   ${issue}`));
    
    console.log('\n🔧 RECOMMENDED ACTIONS:');
    console.log('   1. Fix all issues marked with ❌');
    console.log('   2. Review warnings marked with ⚠️');
    console.log('   3. Ensure your Sevalla environment variables match your .env file');
    console.log('   4. Verify your Neon database is accessible from Sevalla');
    console.log('   5. Check Sevalla deployment logs for additional errors');
    
  } else {
    console.log('\n🎉 NO CRITICAL ISSUES FOUND!');
    console.log('✅ Your configuration appears ready for Sevalla deployment');
  }
  
  // 10. Next Steps
  console.log('\n📋 NEXT STEPS FOR SEVALLA DEPLOYMENT:');
  console.log('   1. Ensure all environment variables are set in Sevalla dashboard');
  console.log('   2. Deploy your backend to Sevalla');
  console.log('   3. Test the health endpoint: https://your-backend.sevalla.app/health');
  console.log('   4. Deploy your frontend to Sevalla');
  console.log('   5. Test the complete application flow');
  
  console.log('\n📚 HELPFUL RESOURCES:');
  console.log('   • Sevalla Documentation: https://docs.sevalla.com');
  console.log('   • Neon Database Docs: https://neon.tech/docs');
  console.log('   • Discord Developer Portal: https://discord.com/developers/applications');
  
  console.log('\n🆘 IF YOU STILL HAVE ISSUES:');
  console.log('   1. Check Sevalla deployment logs');
  console.log('   2. Verify your DATABASE_URL is correct');
  console.log('   3. Test database connection from your local machine');
  console.log('   4. Ensure Discord bot permissions are properly configured');
  
  return {
    issues: issues.length,
    warnings: warnings.length,
    successes: successes.length
  };
}

// Run the diagnostic
runSevallaDiagnostic()
  .then(result => {
    console.log(`\n📈 SUMMARY: ${result.successes} successes, ${result.warnings} warnings, ${result.issues} issues`);
    process.exit(result.issues > 0 ? 1 : 0);
  })
  .catch(err => {
    console.error('\n💥 DIAGNOSTIC TOOL ERROR:', err.message);
    process.exit(1);
  });
