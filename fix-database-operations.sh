#!/bin/bash

# Database Operation Fix Script
# Converts Drizzle ORM operations to Redis service calls

set -e

echo "🔧 Starting database operation fixes..."

# Function to fix imports in a file
fix_imports() {
    local file="$1"
    echo "  📝 Fixing imports in $file"
    
    # Add RedisDatabaseService import if not present
    if grep -q "RedisDatabaseService" "$file"; then
        echo "    ✅ RedisDatabaseService import already exists"
    else
        # Find the last import line and add after it
        sed -i '/import.*from.*@nestjs/a import { RedisDatabaseService } from '\''../../core/database/redis-database.service'\'';' "$file"
        echo "    ✅ Added RedisDatabaseService import"
    fi
    
    # Remove old Drizzle-related imports
    sed -i '/import.*from.*drizzle/d' "$file"
    sed -i '/import.*eq.*from/d' "$file" 
    sed -i '/import.*and.*from/d' "$file"
    sed -i '/import.*or.*from/d' "$file"
    sed -i '/import.*sql.*from/d' "$file"
}

# Function to fix constructor
fix_constructor() {
    local file="$1"
    echo "  🏗️ Fixing constructor in $file"
    
    # Check if RedisDatabaseService is already in constructor
    if grep -q "redisDatabaseService: RedisDatabaseService" "$file"; then
        echo "    ✅ RedisDatabaseService already in constructor"
    else
        # Add RedisDatabaseService to constructor
        sed -i '/constructor(/,/^\s*) {/ {
            /^\s*) {/ i\    private readonly redisDatabaseService: RedisDatabaseService,
        }' "$file"
        echo "    ✅ Added RedisDatabaseService to constructor"
    fi
}

# Function to fix database operations
fix_database_operations() {
    local file="$1"
    echo "  🔄 Fixing database operations in $file"
    
    # Track changes
    local changes=0
    
    # Fix common patterns
    # Pattern 1: this.db.select().from(users).where(eq(users.discordId, id))
    if sed -i 's/this\.db\.select()\.from(users)\.where(eq(users\.discordId,\s*\([^)]*\)))/this.redisDatabaseService.users.findByDiscordId(\1)/g' "$file"; then
        changes=$((changes + 1))
    fi
    
    # Pattern 2: this.db.select().from(guilds).where(eq(guilds.discordId, id))
    if sed -i 's/this\.db\.select()\.from(guilds)\.where(eq(guilds\.discordId,\s*\([^)]*\)))/this.redisDatabaseService.guilds.findByDiscordId(\1)/g' "$file"; then
        changes=$((changes + 1))
    fi
    
    # Pattern 3: this.db.update(users).set(...).where(eq(users.id, id))
    if sed -i 's/this\.db\.update(users)\.set({\s*preferences:\s*\([^}]*\)\s*}[^)]*\.where(eq(users\.id,\s*\([^)]*\)))/this.redisDatabaseService.users.updatePreferences(\2, \1)/g' "$file"; then
        changes=$((changes + 1))
    fi
    
    # Pattern 4: this.db.update(guilds).set({ settings: ... }).where(eq(guilds.id, id))
    if sed -i 's/this\.db\.update(guilds)\.set({\s*settings:\s*\([^}]*\)\s*}[^)]*\.where(eq(guilds\.id,\s*\([^)]*\)))/this.redisDatabaseService.guilds.updateSettings(\2, \1)/g' "$file"; then
        changes=$((changes + 1))
    fi
    
    # Pattern 5: this.db.insert(users).values(...).returning()
    if grep -q "this\.db\.insert(users)" "$file"; then
        echo "    ⚠️  Manual fix needed: User creation operations found"
    fi
    
    # Pattern 6: this.db.insert(guilds).values(...).returning()  
    if grep -q "this\.db\.insert(guilds)" "$file"; then
        echo "    ⚠️  Manual fix needed: Guild creation operations found"
    fi
    
    # Pattern 7: this.db.select().from(users)
    if sed -i 's/this\.db\.select()\.from(users)/this.redisDatabaseService.users.find()/g' "$file"; then
        changes=$((changes + 1))
    fi
    
    # Pattern 8: this.db.select().from(guilds)
    if sed -i 's/this\.db\.select()\.from(guilds)/this.redisDatabaseService.guilds.find()/g' "$file"; then
        changes=$((changes + 1))
    fi
    
    echo "    📊 Applied $changes automated fixes"
    
    # Check for remaining issues
    local remaining=$(grep -n "this\.db\." "$file" | wc -l)
    if [ $remaining -gt 0 ]; then
        echo "    ⚠️  $remaining database operations still need manual fixes:"
        grep -n "this\.db\." "$file" | head -3
    else
        echo "    ✅ All database operations appear to be fixed"
    fi
}

# Main execution
echo "🔍 Finding TypeScript service files..."
find /home/<USER>/Discordbot-EnergeX/src -name "*.service.ts" -type f | while read -r file; do
    echo "🔧 Processing $file"
    
    # Skip files that don't use database operations
    if ! grep -q "this\.db\." "$file"; then
        echo "  ℹ️  No database operations found, skipping"
        continue
    fi
    
    # Create backup
    cp "$file" "$file.backup"
    
    # Apply fixes
    fix_imports "$file"
    fix_constructor "$file"
    fix_database_operations "$file"
    
    echo "  ✅ Completed processing $file"
    echo
done

echo "🎉 Database operation fixes completed!"
echo "📋 Summary:"
echo "  - Check the console output above for manual fixes needed"
echo "  - Backup files created with .backup extension"
echo "  - Test the application after applying these changes"