#!/usr/bin/env python3

import os
import re

def fix_relations_in_file(filepath):
    """Remove Drizzle relations from entity files for Redis migration."""
    with open(filepath, 'r') as f:
        content = f.read()
    
    # Pattern to match relations export blocks
    # This handles multi-line relation definitions
    relations_pattern = r'export const \w+Relations = relations\([^{]*\{[^}]*\}\);'
    
    # More comprehensive pattern for complex relations
    complex_relations_pattern = r'export const \w+Relations = relations\([^;]*\)\s*=>\s*\([^;]*\)\);'
    
    # Simple pattern for single-line relations
    simple_relations_pattern = r'export const \w+Relations = relations\([^)]*\);'
    
    # Remove or comment out relations
    content = re.sub(relations_pattern, r'// \g<0>', content, flags=re.MULTILINE | re.DOTALL)
    content = re.sub(complex_relations_pattern, r'// \g<0>', content, flags=re.MULTILINE | re.DOTALL) 
    content = re.sub(simple_relations_pattern, r'// \g<0>', content, flags=re.MULTILINE)
    
    # Remove imports of relations function
    content = re.sub(r'^import.*relations.*from.*drizzle-orm.*$', r'// \g<0>', content, flags=re.MULTILINE)
    
    with open(filepath, 'w') as f:
        f.write(content)
    
    print(f"Fixed relations in: {filepath}")

def main():
    # Find all entity files
    entities_dir = '/home/<USER>/Discordbot-EnergeX/src/core/database/entities'
    
    for filename in os.listdir(entities_dir):
        if filename.endswith('.entity.ts'):
            filepath = os.path.join(entities_dir, filename)
            fix_relations_in_file(filepath)

if __name__ == '__main__':
    main()