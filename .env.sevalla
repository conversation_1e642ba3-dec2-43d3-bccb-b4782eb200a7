# Sevalla Production Environment Variables Template
# NOTE: These are already configured in your Sevalla deployment dashboard
# This file serves as a reference for the environment variables that are set

# Application Configuration
NODE_ENV="production"
PORT="8080"

# Discord Bot Configuration
DISCORD_BOT_TOKEN="YOUR_PRODUCTION_DISCORD_BOT_TOKEN"
BOT_CLIENT_ID="YOUR_PRODUCTION_BOT_CLIENT_ID"
BOT_CLIENT_SECRET="YOUR_PRODUCTION_BOT_CLIENT_SECRET"
NEXT_PUBLIC_BOT_CLIENT_ID="YOUR_PRODUCTION_BOT_CLIENT_ID"
DISCORD_CLIENT_ID="YOUR_PRODUCTION_DISCORD_CLIENT_ID"
DISCORD_CLIENT_SECRET="YOUR_PRODUCTION_DISCORD_CLIENT_SECRET"

# Database Configuration
DATABASE_URL="postgresql://username:password@hostname:port/database"
DB_HOST="your-db-host"
DB_PORT="5432"
DB_NAME="your-db-name"
DB_USER="your-db-user"
DB_PASSWORD="your-db-password"

# Application URLs (Update these with your Sevalla deployment URLs)
APP_URL="https://your-app-name.sevalla.app"
WEB_URL="https://your-frontend-name.sevalla.app"
FRONTEND_URL="https://your-frontend-name.sevalla.app"
NEXT_PUBLIC_API_ENDPOINT="https://your-backend-name.sevalla.app"

# Internal Network Configuration
INTERNAL_API_ENDPOINT="https://your-backend-name.sevalla.app"

# Whop Integration
WHOP_API_KEY="YOUR_PRODUCTION_WHOP_API_KEY"
WHOP_APP_ID="YOUR_PRODUCTION_WHOP_APP_ID"

# Additional production configurations
CORS_ORIGIN="https://your-frontend-name.sevalla.app"
ENABLE_ENV_LOGIN="false"
NEXT_TELEMETRY_DISABLED="1"

# Cookie Domain Configuration (Optional)
# If not set, domain will be auto-detected from request host
# For Sevalla deployments, this is usually auto-detected correctly
# COOKIE_DOMAIN=".sevalla.app"
