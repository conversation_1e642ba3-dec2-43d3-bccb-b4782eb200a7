import { Injectable, Logger } from '@nestjs/common';
import { AIProvider } from '@/core/database';
import { getModelsByProvider, getRecommendedModels, getModelById, ModelConfig } from './providers/model-config';
import { ApiKeyManagerService } from './api-key-manager.service';

export interface ModelSelectionOptions {
  userId: string;
  guildId: string;
  provider: AIProvider;
  apiKeyId?: string;
  taskType?: 'text-generation' | 'code-generation' | 'image-generation' | 'embeddings';
}

export interface ModelSelection {
  modelId: string;
  modelConfig: ModelConfig;
  reasoning: string;
}

@Injectable()
export class ModelSelectionService {
  private readonly logger = new Logger(ModelSelectionService.name);

  constructor(
    private readonly apiKeyManager: ApiKeyManagerService,
  ) {}

  /**
   * Get available models for a user's API key
   */
  async getAvailableModels(userId: string, guildId: string, provider: AIProvider): Promise<ModelConfig[]> {
    try {
      // Check if user has API key for this provider
      const userKeys = await this.apiKeyManager.getUserApiKeys(userId, guildId);
      const hasProviderKey = userKeys.some(key => key.provider === provider && key.validation?.isValid);
      
      if (!hasProviderKey) {
        this.logger.debug(`User ${userId} has no valid API key for provider ${provider}`);
        return [];
      }

      return getModelsByProvider(provider);
    } catch (error) {
      this.logger.error(`Failed to get available models for user ${userId}:`, error);
      return [];
    }
  }

  /**
   * Get the selected model for a user's API key
   */
  async getSelectedModel(userId: string, guildId: string, provider: AIProvider): Promise<ModelConfig | null> {
    try {
      const userKeys = await this.apiKeyManager.getUserApiKeys(userId, guildId);
      const providerKey = userKeys.find(key => key.provider === provider && key.isDefault);
      
      if (!providerKey || !providerKey.config.selectedModel) {
        // Return recommended model as fallback
        const recommendedModels = getRecommendedModels(provider);
        return recommendedModels[0] || null;
      }

      return getModelById(providerKey.config.selectedModel) || null;
    } catch (error) {
      this.logger.error(`Failed to get selected model for user ${userId}:`, error);
      return null;
    }
  }

  /**
   * Update the selected model for a user's API key
   */
  async updateSelectedModel(
    userId: string, 
    guildId: string, 
    apiKeyId: string, 
    modelId: string
  ): Promise<boolean> {
    try {
      const modelConfig = getModelById(modelId);
      if (!modelConfig) {
        this.logger.warn(`Model ${modelId} not found`);
        return false;
      }

      // Get the API key
      const userKeys = await this.apiKeyManager.getUserApiKeys(userId, guildId);
      const apiKey = userKeys.find(key => key.id === apiKeyId);
      
      if (!apiKey) {
        this.logger.warn(`API key ${apiKeyId} not found for user ${userId}`);
        return false;
      }

      // Verify model is compatible with provider
      if (modelConfig.provider !== apiKey.provider) {
        this.logger.warn(`Model ${modelId} is not compatible with provider ${apiKey.provider}`);
        return false;
      }

      // Update the API key configuration
      const updatedConfig = {
        ...apiKey.config,
        selectedModel: modelId,
        modelPreferences: {
          ...apiKey.config.modelPreferences,
          textGeneration: modelConfig.capabilities.includes('text-generation') ? modelId : apiKey.config.modelPreferences?.textGeneration,
          codeGeneration: modelConfig.capabilities.includes('code-generation') ? modelId : apiKey.config.modelPreferences?.codeGeneration,
          imageGeneration: modelConfig.capabilities.includes('image-generation') ? modelId : apiKey.config.modelPreferences?.imageGeneration,
          embeddings: modelConfig.capabilities.includes('embeddings') ? modelId : apiKey.config.modelPreferences?.embeddings,
        }
      };

      return await this.apiKeyManager.updateApiKeyConfig(userId, guildId, apiKeyId, updatedConfig);
    } catch (error) {
      this.logger.error(`Failed to update selected model for user ${userId}:`, error);
      return false;
    }
  }

  /**
   * Get the best model for a specific task type
   */
  async getBestModelForTask(
    userId: string, 
    guildId: string, 
    provider: AIProvider, 
    taskType: 'text-generation' | 'code-generation' | 'image-generation' | 'embeddings'
  ): Promise<ModelSelection | null> {
    try {
      const availableModels = await this.getAvailableModels(userId, guildId, provider);
      if (availableModels.length === 0) {
        return null;
      }

      // Filter models by capability
      const capableModels = availableModels.filter((model: any) => 
        model.capabilities.includes(taskType as any)
      );

      if (capableModels.length === 0) {
        return null;
      }

      // Prioritize recommended models
      const recommendedModels = capableModels.filter((model: any) => model.isRecommended);
      const selectedModels = recommendedModels.length > 0 ? recommendedModels : capableModels;

      // For different task types, select the best model
      let bestModel: ModelConfig;
      let reasoning: string;

      switch (taskType) {
        case 'text-generation':
          // Prioritize latest and recommended models
          bestModel = selectedModels.find(m => m.isLatest && m.isRecommended) || 
                     selectedModels.find(m => m.isRecommended) || 
                     selectedModels[0];
          reasoning = 'Best general-purpose text generation model';
          break;

        case 'code-generation':
          // Look for models with strong code capabilities
          bestModel = selectedModels.find(m => m.capabilities.includes('code-generation') && m.isRecommended) || 
                     selectedModels.find(m => m.capabilities.includes('code-generation')) || 
                     selectedModels[0];
          reasoning = 'Optimized for code generation and programming tasks';
          break;

        case 'image-generation':
          // Find image generation models
          bestModel = selectedModels.find(m => m.capabilities.includes('image-generation')) || selectedModels[0];
          reasoning = 'Specialized for image creation and visual content';
          break;

        case 'embeddings':
          // Find embedding models, typically cheaper
          bestModel = selectedModels.find(m => m.capabilities.includes('embeddings')) || selectedModels[0];
          reasoning = 'Optimized for text embeddings and semantic search';
          break;

        default:
          bestModel = selectedModels[0];
          reasoning = 'Default model selection';
      }

      return {
        modelId: bestModel.id,
        modelConfig: bestModel,
        reasoning
      };
    } catch (error) {
      this.logger.error(`Failed to get best model for task ${taskType}:`, error);
      return null;
    }
  }

  /**
   * Get model recommendations for a provider
   */
  getModelRecommendations(provider: AIProvider): { category: string; models: ModelConfig[]; description: string }[] {
    const allModels = getModelsByProvider(provider);
    const recommendations = [];

    // General Purpose
    const generalModels = allModels.filter((m: any) => 
      m.capabilities.includes('text-generation') && 
      m.capabilities.includes('code-generation') &&
      m.isRecommended
    );
    if (generalModels.length > 0) {
      recommendations.push({
        category: 'General Purpose',
        models: generalModels.slice(0, 2),
        description: 'Best for most tasks including text and code generation'
      });
    }

    // Cost Effective
    const costEffectiveModels = allModels
      .filter((m: any) => m.capabilities.includes('text-generation'))
      .sort((a, b) => a.inputPrice - b.inputPrice)
      .slice(0, 2);
    if (costEffectiveModels.length > 0) {
      recommendations.push({
        category: 'Cost Effective',
        models: costEffectiveModels,
        description: 'Best value for money with good performance'
      });
    }

    // Specialized
    const specializedModels = allModels.filter((m: any) => 
      m.capabilities.includes('image-generation') || 
      m.capabilities.includes('embeddings')
    );
    if (specializedModels.length > 0) {
      recommendations.push({
        category: 'Specialized',
        models: specializedModels.slice(0, 2),
        description: 'For specific tasks like image generation or embeddings'
      });
    }

    return recommendations;
  }
}