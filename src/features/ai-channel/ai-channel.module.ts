import { Module } from '@nestjs/common';
import { AIChannelPanelService } from './ai-channel-panel.service';
import { PrivateChatManagerService } from './private-chat-manager.service';
import { AIChannelCommands } from './ai-channel.commands';
import { AIChannelAutoSetupService } from './ai-channel-auto-setup.service';
import { ApiKeyManagerService } from './api-key-manager.service';
import { ApiKeyCommands } from './api-key.commands';
import { ModelSelectionService } from './model-selection.service';
import { ExaByokService } from './exa-byok.service';
import { DatabaseModule } from '../../core/database/database.module';
import { WhopModule } from '../../api/whop/whop.module';
import { DiscordModule } from '../../discord/discord.module';
import { ExaSearchModule } from '../../core/agents/exa-search.module';

@Module({
  imports: [
    DatabaseModule,
    WhopModule,
    DiscordModule,
    ExaSearchModule,
  ],
  providers: [
    AIChannelPanelService,
    PrivateChatManagerService,
    AIChannelCommands,
    AIChannelAutoSetupService,
    ApiKeyManagerService,
    ApiKeyCommands,
    ModelSelectionService,
    ExaByokService,
  ],
  exports: [
    AIChannelPanelService,
    PrivateChatManagerService,
    AIChannelAutoSetupService,
    ApiKeyManagerService,
    ModelSelectionService,
    ExaByokService,
  ],
})
export class AIChannelModule {}