import { Injectable, Logger, Inject } from '@nestjs/common';
import { DatabaseService } from '@/core/database';
import { Cron, CronExpression } from '@nestjs/schedule';
import { 
  Client, 
  Guild, 
  ChannelType, 
  PermissionFlagsBits, 
  TextChannel,
  CategoryChannel
} from 'discord.js';



import { aiChannelConfigs } from '@/core/database';
import { AIChannelPanelService } from './ai-channel-panel.service';
import { ConfigService } from '@nestjs/config';

interface AutoSetupConfig {
  enabled: boolean;
  onGuildJoin: boolean;
  checkInterval: boolean;
  channelName: string;
  categoryName?: string;
  channelTopic: string;
  welcomeMessage: boolean;
  requiredPermissions: string[];
}

@Injectable()
export class AIChannelAutoSetupService {
  private readonly logger = new Logger(AIChannelAutoSetupService.name);
  private readonly autoSetupConfig: AutoSetupConfig;
  private isInitialized = false;

  constructor(
    private readonly databaseService: DatabaseService,
    private readonly panelService: AIChannelPanelService,
    private readonly configService: ConfigService,
    private readonly client: Client,
  ) {
    this.autoSetupConfig = {
      enabled: this.configService.get<boolean>('AI_AUTO_SETUP_ENABLED', true),
      onGuildJoin: this.configService.get<boolean>('AI_AUTO_SETUP_ON_GUILD_JOIN', true),
      checkInterval: this.configService.get<boolean>('AI_AUTO_SETUP_CHECK_INTERVAL', true),
      channelName: this.configService.get<string>('AI_CHANNEL_NAME', 'ai-agents'),
      categoryName: this.configService.get<string>('AI_CATEGORY_NAME', 'AI Assistants'),
      channelTopic: this.configService.get<string>('AI_CHANNEL_TOPIC', '🤖 AI Agent Panel - Start private conversations with AI assistants'),
      welcomeMessage: this.configService.get<boolean>('AI_WELCOME_MESSAGE', true),
      requiredPermissions: ['ViewChannel', 'ReadMessageHistory'],
    };
  }

  async onModuleInit() {
    if (!this.autoSetupConfig.enabled) {
      this.logger.log('🚫 AI Channel Auto-Setup is disabled');
      return;
    }

    this.logger.log('🤖 Initializing AI Channel Auto-Setup Service...');

    // Setup event listeners
    this.setupEventListeners();

    // Run initial check for existing guilds
    await this.checkExistingGuilds();

    this.isInitialized = true;
    this.logger.log('✅ AI Channel Auto-Setup Service initialized');
  }

  /**
   * Setup Discord event listeners for automatic deployment
   */
  private setupEventListeners() {
    if (!this.client) {
      this.logger.warn('Discord client not available, skipping event listeners');
      return;
    }

    // Auto-setup when bot joins a new guild
    if (this.autoSetupConfig.onGuildJoin) {
      this.client.on('guildCreate', async (guild: Guild) => {
        this.logger.log(`🆕 Bot joined new guild: ${guild.name} (${guild.id})`);
        await this.delay(5000); // Wait 5 seconds for guild to be fully loaded
        await this.autoSetupGuild(guild, 'guild_join');
      });
    }

    // Auto-setup when bot gains necessary permissions
    this.client.on('guildMemberUpdate', async (oldMember, newMember) => {
      if (newMember.user.id !== this.client.user?.id) return;

      const hasNewPermissions = newMember.permissions.has(PermissionFlagsBits.ManageChannels) &&
                               !oldMember.permissions.has(PermissionFlagsBits.ManageChannels);

      if (hasNewPermissions) {
        this.logger.log(`🔑 Gained permissions in guild: ${newMember.guild.name}`);
        await this.autoSetupGuild(newMember.guild, 'permission_gained');
      }
    });

    this.logger.log('📡 Discord event listeners configured for auto-setup');
  }

  /**
   * Check existing guilds for missing AI channels
   */
  private async checkExistingGuilds() {
    if (!this.client?.guilds) return;

    this.logger.log('🔍 Checking existing guilds for AI channel setup...');

    const guilds = this.client.guilds.cache;
    let setupCount = 0;

    for (const [guildId, guild] of guilds) {
      try {
        const hasSetup = await this.hasAIChannelSetup(guildId);
        if (!hasSetup && this.canSetupInGuild(guild)) {
          await this.autoSetupGuild(guild, 'initial_check');
          setupCount++;
          await this.delay(2000); // Rate limiting
        }
      } catch (error) {
        this.logger.error(`Failed to check guild ${guildId}:`, error);
      }
    }

    this.logger.log(`✅ Initial setup complete: ${setupCount} guilds configured`);
  }

  /**
   * Automatically setup AI channel in a guild
   */
  async autoSetupGuild(guild: Guild, trigger: string): Promise<boolean> {
    try {
      this.logger.log(`🚀 Auto-setting up AI channel in ${guild.name} (trigger: ${trigger})`);

      // Check if already setup
      const hasSetup = await this.hasAIChannelSetup(guild.id);
      if (hasSetup) {
        this.logger.debug(`⏭️ Guild ${guild.name} already has AI channel setup`);
        return true;
      }

      // Check permissions
      if (!this.canSetupInGuild(guild)) {
        this.logger.warn(`⚠️ Insufficient permissions to setup AI channel in ${guild.name}`);
        return false;
      }

      // Create category if specified
      let category: CategoryChannel | undefined;
      if (this.autoSetupConfig.categoryName) {
        category = await this.createOrGetCategory(guild, this.autoSetupConfig.categoryName);
      }

      // Create AI channel
      const aiChannel = await this.createAIChannel(guild, category);
      if (!aiChannel) {
        this.logger.error(`❌ Failed to create AI channel in ${guild.name}`);
        return false;
      }

      // Deploy AI panel (createPanel now includes deduplication logic)
      const panelMessage = await this.panelService.createPanel(guild.id, aiChannel.id);
      if (!panelMessage) {
        this.logger.error(`❌ Failed to create AI panel in ${guild.name}`);
        return false;
      }

      // Send welcome message if enabled
      if (this.autoSetupConfig.welcomeMessage) {
        await this.sendSetupWelcomeMessage(aiChannel, trigger);
      }

      this.logger.log(`✅ Successfully auto-setup AI channel in ${guild.name}`);
      return true;

    } catch (error) {
      this.logger.error(`❌ Failed to auto-setup AI channel in ${guild.name}:`, error);
      return false;
    }
  }

  /**
   * Create or get existing category
   */
  private async createOrGetCategory(guild: Guild, categoryName: string): Promise<CategoryChannel | undefined> {
    try {
      // Check if category already exists
      const existingCategory = guild.channels.cache.find(
        channel => channel.type === ChannelType.GuildCategory && 
                  channel.name.toLowerCase() === categoryName.toLowerCase()
      ) as CategoryChannel;

      if (existingCategory) {
        this.logger.debug(`📁 Using existing category: ${categoryName}`);
        return existingCategory;
      }

      // Create new category
      const category = await guild.channels.create({
        name: categoryName,
        type: ChannelType.GuildCategory,
        reason: 'AI Channel Auto-Setup: Category for AI assistants',
      });

      this.logger.log(`📁 Created AI category: ${categoryName}`);
      return category;

    } catch (error) {
      this.logger.error(`Failed to create category ${categoryName}:`, error);
      return undefined;
    }
  }

  /**
   * Create or update AI channel (move to correct category if needed)
   */
  private async createAIChannel(guild: Guild, category?: CategoryChannel): Promise<TextChannel | null> {
    try {
      // Check if AI channel already exists
      const existingChannel = guild.channels.cache.find(
        channel => channel.type === ChannelType.GuildText && 
                  channel.name === this.autoSetupConfig.channelName
      ) as TextChannel;

      if (existingChannel) {
        // Check if channel needs to be moved to correct category
        if (category && existingChannel.parentId !== category.id) {
          this.logger.log(`📁 Moving AI channel ${this.autoSetupConfig.channelName} to category ${category.name}`);
          try {
            await existingChannel.setParent(category, {
              reason: 'AI Channel Auto-Setup: Moving to correct category'
            });
            this.logger.log(`✅ Successfully moved AI channel to ${category.name} category`);
          } catch (error) {
            this.logger.error(`Failed to move AI channel to category:`, error);
            // Continue with existing channel even if move failed
          }
        }
        
        this.logger.debug(`🤖 Using existing AI channel: ${this.autoSetupConfig.channelName}`);
        return existingChannel;
      }

      // Create new AI channel
      const aiChannel = await guild.channels.create({
        name: this.autoSetupConfig.channelName,
        type: ChannelType.GuildText,
        topic: this.autoSetupConfig.channelTopic,
        parent: category?.id,
        reason: 'AI Channel Auto-Setup: AI Agent Panel',
        permissionOverwrites: [
          {
            id: guild.roles.everyone.id,
            allow: [PermissionFlagsBits.ViewChannel, PermissionFlagsBits.ReadMessageHistory],
            deny: [PermissionFlagsBits.SendMessages, PermissionFlagsBits.AddReactions],
          },
        ],
      });

      this.logger.log(`🤖 Created AI channel: ${this.autoSetupConfig.channelName}`);
      return aiChannel as TextChannel;

    } catch (error) {
      this.logger.error(`Failed to create AI channel:`, error);
      return null;
    }
  }

  /**
   * Send welcome message after setup
   */
  private async sendSetupWelcomeMessage(channel: TextChannel, trigger: string) {
    try {
      const welcomeMessages = {
        guild_join: `🎉 **Welcome to ${channel.guild.name}!**\n\nI've automatically set up your AI Assistant Panel! Users can now click the buttons below to start private conversations with AI agents.`,
        permission_gained: `🔓 **Permissions Updated!**\n\nThanks for giving me the necessary permissions. I've now set up your AI Assistant Panel for everyone to use!`,
        initial_check: `🤖 **AI Assistant Panel Ready!**\n\nI've set up this channel for AI agent interactions. Users can click the buttons below to start private conversations.`,
        manual: `⚙️ **Manual Setup Complete!**\n\nYour AI Assistant Panel is now ready! Users can interact with AI agents by clicking the buttons below.`
      };

      const message = welcomeMessages[trigger as keyof typeof welcomeMessages] || welcomeMessages.manual;

      await channel.send({
        content: `${message}\n\n**Available Features:**\n• 🌱 Personal Growth Coach (Free)\n• 📋 Intake Specialist (Free)\n• 📊 Progress Tracker (Premium)\n\n*Each conversation is private and personalized!*`
      });

      this.logger.debug(`📝 Sent welcome message in ${channel.guild.name}`);
    } catch (error) {
      this.logger.error('Failed to send welcome message:', error);
    }
  }

  /**
   * Check if guild already has AI channel setup in the correct category
   */
  private async hasAIChannelSetup(guildId: string): Promise<boolean> {
    try {
      const config = await this.db.select().from(aiChannelConfigs)
        .where(eq(aiChannelConfigs.guildId, guildId))
        .limit(1);

      if (config.length === 0) {
        return false;
      }

      // Verify the panel message still exists in the channel
      const panelConfig = config[0];
      if (panelConfig.panelMessageId && panelConfig.aiChannelId) {
        try {
          const channel = await this.client.channels.fetch(panelConfig.aiChannelId) as TextChannel;
          if (channel) {
            const message = await channel.messages.fetch(panelConfig.panelMessageId);
            if (message) {
              // Check if channel is in the correct category
              if (this.autoSetupConfig.categoryName) {
                const guild = channel.guild;
                const expectedCategory = guild.channels.cache.find(
                  ch => ch.type === ChannelType.GuildCategory && 
                        ch.name.toLowerCase() === this.autoSetupConfig.categoryName!.toLowerCase()
                ) as CategoryChannel;
                
                if (expectedCategory && channel.parentId !== expectedCategory.id) {
                  this.logger.log(`AI channel ${channel.name} is not in correct category ${this.autoSetupConfig.categoryName}, triggering auto-setup`);
                  return false; // Channel exists but not in correct category
                }
              }
              
              this.logger.debug(`Verified existing AI panel in guild ${guildId} with correct category`);
              return true;
            } else {
              this.logger.warn(`AI panel message ${panelConfig.panelMessageId} not found in guild ${guildId}, setup may be needed`);
              return false;
            }
          }
        } catch (error) {
          this.logger.warn(`Failed to verify AI panel in guild ${guildId}:`, error);
          return false;
        }
      }

      return true; // Config exists but no specific message to verify
    } catch (error) {
      this.logger.error(`Failed to check AI setup for guild ${guildId}:`, error);
      return false;
    }
  }

  /**
   * Check if bot can setup AI channel in guild
   */
  private canSetupInGuild(guild: Guild): boolean {
    try {
      const botMember = guild.members.cache.get(this.client.user?.id || '');
      if (!botMember) return false;

      const requiredPermissions = [
        PermissionFlagsBits.ManageChannels,
        PermissionFlagsBits.ViewChannel,
        PermissionFlagsBits.SendMessages,
        PermissionFlagsBits.EmbedLinks,
        PermissionFlagsBits.UseExternalEmojis,
        PermissionFlagsBits.ReadMessageHistory,
      ];

      return botMember.permissions.has(requiredPermissions);
    } catch (error) {
      this.logger.error(`Failed to check permissions in guild ${guild.id}:`, error);
      return false;
    }
  }

  /**
   * Periodic check for guilds without AI setup (runs every 6 hours)
   */
  @Cron(CronExpression.EVERY_6_HOURS)
  async periodicSetupCheck() {
    if (!this.autoSetupConfig.enabled || !this.autoSetupConfig.checkInterval || !this.isInitialized) {
      return;
    }

    this.logger.log('🔄 Running periodic AI channel setup check...');

    try {
      const guilds = this.client?.guilds?.cache;
      if (!guilds) return;

      let setupCount = 0;
      for (const [guildId, guild] of guilds) {
        try {
          const hasSetup = await this.hasAIChannelSetup(guildId);
          if (!hasSetup && this.canSetupInGuild(guild)) {
            const success = await this.autoSetupGuild(guild, 'periodic_check');
            if (success) setupCount++;
            await this.delay(3000); // Rate limiting
          }
        } catch (error) {
          this.logger.error(`Periodic check failed for guild ${guildId}:`, error);
        }
      }

      if (setupCount > 0) {
        this.logger.log(`✅ Periodic setup complete: ${setupCount} guilds configured`);
      }
    } catch (error) {
      this.logger.error('Periodic setup check failed:', error);
    }
  }

  /**
   * Manual trigger for auto-setup (can be called from admin commands)
   */
  async triggerAutoSetup(guildId: string): Promise<boolean> {
    try {
      const guild = this.client.guilds.cache.get(guildId);
      if (!guild) {
        this.logger.error(`Guild ${guildId} not found for manual auto-setup`);
        return false;
      }

      return await this.autoSetupGuild(guild, 'manual');
    } catch (error) {
      this.logger.error(`Manual auto-setup failed for guild ${guildId}:`, error);
      return false;
    }
  }

  /**
   * Get auto-setup status and statistics
   */
  async getAutoSetupStats() {
    try {
      const totalGuilds = this.client?.guilds?.cache.size || 0;
      
      const setupGuilds = await this.db.select().from(aiChannelConfigs);
      const setupCount = setupGuilds.length;

      return {
        enabled: this.autoSetupConfig.enabled,
        totalGuilds,
        setupCount,
        pendingSetup: totalGuilds - setupCount,
        config: {
          onGuildJoin: this.autoSetupConfig.onGuildJoin,
          checkInterval: this.autoSetupConfig.checkInterval,
          channelName: this.autoSetupConfig.channelName,
          categoryName: this.autoSetupConfig.categoryName,
        },
        lastCheck: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Failed to get auto-setup stats:', error);
      return {
        error: 'Failed to retrieve auto-setup statistics',
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * Utility function for delays
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Disable auto-setup for specific guild
   */
  async disableAutoSetupForGuild(guildId: string): Promise<void> {
    // This could store guild-specific preferences in the database
    // For now, it's just a placeholder for future implementation
    this.logger.log(`Auto-setup disabled for guild ${guildId}`);
  }

  /**
   * Enable auto-setup for specific guild
   */
  async enableAutoSetupForGuild(guildId: string): Promise<void> {
    // This could store guild-specific preferences in the database
    // For now, it's just a placeholder for future implementation
    this.logger.log(`Auto-setup enabled for guild ${guildId}`);
  }
}