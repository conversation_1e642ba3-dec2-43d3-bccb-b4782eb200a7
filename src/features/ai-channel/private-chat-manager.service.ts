import { Injectable, Logger, Inject } from '@nestjs/common';
import { DatabaseService } from '@/core/database';
import { 
  Client, 
  Guild, 
  GuildMember, 
  TextChannel, 
  ThreadChannel, 
  ChannelType, 
  PermissionFlagsBits,
  Collection
} from 'discord.js';



import { 
  AIChatSession, 
  NewAIChatSession, 
  aiChatSessions,
  AIChannelConfig,
  aiChannelConfigs,
  AIChatSessionStatus,
  AIAccessLevel
} from '@/core/database';
import { WhopService } from '../../api/whop/whop.service';

interface CreateChatSessionOptions {
  userId: string;
  guildId: string;
  agentType: string;
  channelId: string;
  accessLevel?: AIAccessLevel;
}

interface ChatSessionInfo {
  session: AIChatSession;
  thread: ThreadChannel;
  isNew: boolean;
}

@Injectable()
export class PrivateChatManagerService {
  private readonly logger = new Logger(PrivateChatManagerService.name);
  private readonly activeSessions = new Map<string, ThreadChannel>();

  constructor(
    private readonly databaseService: DatabaseService,
    private readonly whopService: WhopService,
    private readonly client: Client,
  ) {}

  /**
   * Create or retrieve existing private chat session for user and agent
   */
  async createOrGetPrivateChat(options: CreateChatSessionOptions): Promise<ChatSessionInfo | null> {
    try {
      const { userId, guildId, agentType, channelId } = options;
      
      // Check if user already has an active session with this agent
      const existingSession = await this.getActiveSession(userId, guildId, agentType);
      
      if (existingSession) {
        const thread = await this.getThreadById(existingSession.threadId);
        if (thread) {
          return {
            session: existingSession,
            thread,
            isNew: false
          };
        } else {
          // Thread was deleted, mark session as expired
          await this.updateSessionStatus(existingSession.id, 'expired');
        }
      }

      // Validate access level through Whop
      const accessLevel = await this.validateUserAccess(userId, agentType);
      if (!accessLevel) {
        this.logger.warn(`User ${userId} does not have access to agent ${agentType}`);
        return null;
      }

      // Create new private thread
      const thread = await this.createPrivateThread(guildId, channelId, userId, agentType);
      if (!thread) {
        return null;
      }

      // Create session record in database
      const sessionId = this.generateSessionId(userId, agentType);
      const newSession: NewAIChatSession = {
        id: sessionId,
        userId,
        guildId,
        agentType,
        threadId: thread.id,
        channelId,
        status: 'active',
        accessLevel,
        sessionData: {
          createdBy: 'ai-panel',
          agentType,
          initialContext: `User started conversation with ${agentType}`
        },
        messageCount: 0,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
      } as NewAIChatSession;

      await this.db.insert(aiChatSessions).values(newSession);
      
      const session = await this.getSessionById(sessionId);
      if (!session) {
        throw new Error('Failed to create session record');
      }

      // Cache the active session
      this.activeSessions.set(sessionId, thread);

      this.logger.log(`Created new private chat session ${sessionId} for user ${userId} with agent ${agentType}`);
      
      return {
        session,
        thread,
        isNew: true
      };

    } catch (error) {
      this.logger.error('Failed to create or get private chat:', error);
      return null;
    }
  }

  /**
   * Create a private thread for the user and agent conversation
   */
  private async createPrivateThread(
    guildId: string, 
    channelId: string, 
    userId: string, 
    agentType: string
  ): Promise<ThreadChannel | null> {
    try {
      const guild = this.client.guilds.cache.get(guildId);
      if (!guild) {
        this.logger.error(`Guild ${guildId} not found`);
        return null;
      }

      const channel = guild.channels.cache.get(channelId) as TextChannel;
      if (!channel || channel.type !== ChannelType.GuildText) {
        this.logger.error(`Channel ${channelId} not found or not a text channel`);
        return null;
      }

      const member = await guild.members.fetch(userId);
      if (!member) {
        this.logger.error(`Member ${userId} not found in guild ${guildId}`);
        return null;
      }

      // Generate thread name
      const threadName = this.generateThreadName(member, agentType);

      // Create private thread
      const thread = await channel.threads.create({
        name: threadName,
        type: ChannelType.PrivateThread,
        invitable: false,
        reason: `AI Agent conversation: ${agentType}`,
      });

      // Add the user to the thread
      await thread.members.add(userId);

      // Set thread permissions to be private
      await this.configureThreadPermissions(thread, userId);

      this.logger.log(`Created private thread ${thread.id} for user ${userId} and agent ${agentType}`);
      return thread;

    } catch (error) {
      this.logger.error('Failed to create private thread:', error);
      return null;
    }
  }

  /**
   * Configure thread permissions to ensure privacy
   */
  private async configureThreadPermissions(thread: ThreadChannel, userId: string) {
    try {
      // The thread is already private by type, but we can add additional restrictions
      // Private threads are only visible to invited members
      
      // Optionally, we could set additional permissions here if needed
      this.logger.debug(`Configured permissions for private thread ${thread.id}`);
    } catch (error) {
      this.logger.error('Failed to configure thread permissions:', error);
    }
  }

  /**
   * Validate user access to specific agent through Whop
   */
  private async validateUserAccess(userId: string, agentType: string): Promise<AIAccessLevel | null> {
    try {
      // Define which agents require premium access
      const premiumAgents = ['personal_growth_coach_premium', 'progress_tracker_advanced'];
      const enterpriseAgents = ['custom_agent'];

      // Check if agent requires premium access
      if (enterpriseAgents.includes(agentType)) {
        // Check enterprise access through Whop
        const hasEnterpriseAccess = await this.whopService.verifyDeveloperAccess(userId);
        return hasEnterpriseAccess ? 'enterprise' : null;
      }

      if (premiumAgents.includes(agentType)) {
        // Check premium access through Whop
        const hasPremiumAccess = await this.whopService.verifyClientAccess(userId);
        return hasPremiumAccess ? 'premium' : null;
      }

      // Default agents are free
      return 'free';

    } catch (error) {
      this.logger.error(`Failed to validate access for user ${userId} and agent ${agentType}:`, error);
      return null;
    }
  }

  /**
   * Get active session for user and agent
   */
  async getActiveSession(userId: string, guildId: string, agentType: string): Promise<AIChatSession | null> {
    try {
      const result = await this.db.select().from(aiChatSessions)
        .where(and(
          eq(aiChatSessions.userId, userId),
          eq(aiChatSessions.guildId, guildId),
          eq(aiChatSessions.agentType, agentType),
          eq(aiChatSessions.status, 'active')
        ))
        .limit(1);

      return result[0] || null;
    } catch (error) {
      this.logger.error('Failed to get active session:', error);
      return null;
    }
  }

  /**
   * Get session by ID
   */
  async getSessionById(sessionId: string): Promise<AIChatSession | null> {
    try {
      const result = await this.db.select().from(aiChatSessions)
        .where(eq(aiChatSessions.id, sessionId))
        .limit(1);

      return result[0] || null;
    } catch (error) {
      this.logger.error(`Failed to get session ${sessionId}:`, error);
      return null;
    }
  }

  /**
   * Get thread by ID
   */
  private async getThreadById(threadId: string): Promise<ThreadChannel | null> {
    try {
      // Check cache first
      for (const [sessionId, thread] of this.activeSessions.entries()) {
        if (thread.id === threadId) {
          return thread;
        }
      }

      // Fetch from Discord API
      const channel = await this.client.channels.fetch(threadId);
      if (channel && channel.isThread()) {
        return channel as ThreadChannel;
      }

      return null;
    } catch (error) {
      this.logger.error(`Failed to get thread ${threadId}:`, error);
      return null;
    }
  }

  /**
   * Update session status
   */
  async updateSessionStatus(sessionId: string, status: AIChatSessionStatus): Promise<void> {
    try {
      await this.db.update(aiChatSessions)
        .set({ 
          status, 
          updatedAt: new Date(),
          ...(status === 'archived' && { archivedAt: new Date() })
        } as any)
        .where(eq(aiChatSessions.id, sessionId));

      // Remove from cache if no longer active
      if (status !== 'active') {
        this.activeSessions.delete(sessionId);
      }

      this.logger.log(`Updated session ${sessionId} status to ${status}`);
    } catch (error) {
      this.logger.error(`Failed to update session ${sessionId} status:`, error);
    }
  }

  /**
   * Archive inactive sessions
   */
  async archiveInactiveSessions(): Promise<void> {
    try {
      const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago

      const inactiveSessions = await this.db.select().from(aiChatSessions)
        .where(and(
          eq(aiChatSessions.status, 'active'),
          // lastMessageAt is less than cutoff time or null
        ));

      for (const session of inactiveSessions) {
        const thread = await this.getThreadById(session.threadId);
        if (thread) {
          await thread.setArchived(true);
        }
        await this.updateSessionStatus(session.id, 'archived');
      }

      this.logger.log(`Archived ${inactiveSessions.length} inactive sessions`);
    } catch (error) {
      this.logger.error('Failed to archive inactive sessions:', error);
    }
  }

  /**
   * Update session message count and last activity
   */
  async updateSessionActivity(sessionId: string): Promise<void> {
    try {
      const session = await this.getSessionById(sessionId);
      if (!session) return;

      await this.db.update(aiChatSessions)
        .set({ 
          messageCount: (session.messageCount || 0) + 1,
          lastMessageAt: new Date(),
          updatedAt: new Date()
        } as any)
        .where(eq(aiChatSessions.id, sessionId));

    } catch (error) {
      this.logger.error(`Failed to update session activity for ${sessionId}:`, error);
    }
  }

  /**
   * Get session by thread ID
   */
  async getSessionByThreadId(threadId: string): Promise<AIChatSession | null> {
    try {
      const result = await this.db.select().from(aiChatSessions)
        .where(eq(aiChatSessions.threadId, threadId))
        .limit(1);

      return result[0] || null;
    } catch (error) {
      this.logger.error(`Failed to get session by thread ID ${threadId}:`, error);
      return null;
    }
  }

  /**
   * Generate unique session ID
   */
  private generateSessionId(userId: string, agentType: string): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `session_${userId}_${agentType}_${timestamp}_${random}`;
  }

  /**
   * Generate thread name
   */
  private generateThreadName(member: GuildMember, agentType: string): string {
    const agentNames = {
      'personal_growth_coach': '🌱 Personal Growth Coach',
      'intake_specialist': '📋 Intake Assessment',
      'progress_tracker': '📊 Progress Tracker',
    };

    const agentName = agentNames[agentType as keyof typeof agentNames] || agentType;
    return `${agentName} - ${member.displayName}`;
  }

  /**
   * Get user's active sessions
   */
  async getUserActiveSessions(userId: string, guildId: string): Promise<AIChatSession[]> {
    try {
      return await this.db.select().from(aiChatSessions)
        .where(and(
          eq(aiChatSessions.userId, userId),
          eq(aiChatSessions.guildId, guildId),
          eq(aiChatSessions.status, 'active')
        ));
    } catch (error) {
      this.logger.error(`Failed to get active sessions for user ${userId}:`, error);
      return [];
    }
  }

  /**
   * Close session and archive thread
   */
  async closeSession(sessionId: string): Promise<void> {
    try {
      const session = await this.getSessionById(sessionId);
      if (!session) return;

      const thread = await this.getThreadById(session.threadId);
      if (thread && !thread.archived) {
        await thread.setArchived(true);
      }

      await this.updateSessionStatus(sessionId, 'closed');
      this.logger.log(`Closed session ${sessionId}`);
    } catch (error) {
      this.logger.error(`Failed to close session ${sessionId}:`, error);
    }
  }
}