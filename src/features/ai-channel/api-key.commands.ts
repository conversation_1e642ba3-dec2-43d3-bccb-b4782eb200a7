import { Injectable, Logger } from '@nestjs/common';
import { 
  ChannelType, 
  EmbedBuilder, 
  PermissionFlagsBits,
  GuildMember,
  ModalSubmitInteraction,
  Interaction
} from 'discord.js';
import { 
  Context, 
  Options, 
  SlashCommand, 
  SlashCommandContext, 
  StringOption,
  BooleanOption,
  On,
  ModalContext
} from 'necord';
import { ApiKeyManagerService } from './api-key-manager.service';
import { AIProvider } from '@/core/database';
import { validateApiKey, getProviderConfig } from './providers/provider-config';

export class ApiKeyManageDto {
  @StringOption({
    name: 'action',
    description: 'Action to perform',
    required: true,
    choices: [
      { name: 'List', value: 'list' },
      { name: 'Add', value: 'add' },
      { name: 'Delete', value: 'delete' },
      { name: 'Set Default', value: 'default' },
    ],
  })
  action: string;

  @StringOption({
    name: 'provider',
    description: 'AI Provider',
    required: false,
    choices: [
      { name: 'OpenAI', value: 'openai' },
      { name: 'Anthropic', value: 'anthropic' },
      { name: 'Google', value: 'google' },
      { name: 'Azure', value: 'azure' },
      { name: 'Custom', value: 'custom' },
    ],
  })
  provider?: string;

  @StringOption({
    name: 'key_name',
    description: 'Name for your API key',
    required: false,
  })
  keyName?: string;

  @StringOption({
    name: 'api_key',
    description: 'Your API key (will be encrypted)',
    required: false,
  })
  apiKey?: string;

  @BooleanOption({
    name: 'is_default',
    description: 'Set as default for this provider',
    required: false,
  })
  isDefault?: boolean;
}

@Injectable()
export class ApiKeyCommands {
  private readonly logger = new Logger(ApiKeyCommands.name);

  constructor(
    private readonly apiKeyManager: ApiKeyManagerService,
  ) {}

  @SlashCommand({
    name: 'api-keys',
    description: 'Manage your personal AI provider API keys',
  })
  async onApiKeys(
    @Context() [interaction]: SlashCommandContext,
    @Options() { action, provider, keyName, apiKey, isDefault }: ApiKeyManageDto,
  ) {
    try {
      if (!interaction.guild || !interaction.member) {
        await interaction.reply({
          content: '❌ This command can only be used in a server.',
          ephemeral: true,
        });
        return;
      }

      await interaction.deferReply({ ephemeral: true });

      switch (action) {
        case 'list':
          await this.listApiKeys(interaction);
          break;
        case 'add':
          if (!provider || !keyName || !apiKey) {
            await interaction.editReply({
              content: '❌ For adding keys, please provide: provider, key_name, and api_key parameters.',
            });
            return;
          }
          await this.addApiKey(interaction, provider as AIProvider, keyName, apiKey, isDefault || false);
          break;
        case 'delete':
          if (!keyName) {
            await interaction.editReply({
              content: '❌ Please provide the key_name to delete.',
            });
            return;
          }
          await this.deleteApiKey(interaction, keyName);
          break;
        case 'default':
          if (!keyName) {
            await interaction.editReply({
              content: '❌ Please provide the key_name to set as default.',
            });
            return;
          }
          await this.setDefaultKey(interaction, keyName);
          break;
        default:
          await interaction.editReply({
            content: '❌ Unknown action. Use list, add, delete, or default.',
          });
      }

    } catch (error) {
      this.logger.error('API keys command failed:', error);
      
      if (interaction.deferred) {
        await interaction.editReply({
          content: '❌ Command failed. Please try again.',
        });
      } else {
        await interaction.reply({
          content: '❌ Command failed. Please try again.',
          ephemeral: true,
        });
      }
    }
  }

  @On('interactionCreate')
  async onModalSubmit(@Context() [interaction]: [Interaction]) {
    if (!interaction.isModalSubmit() || !interaction.customId.startsWith('api_key_add_modal')) {
      return;
    }

    // Type cast to ModalSubmitInteraction after validation
    const modalInteraction = interaction as ModalSubmitInteraction;

    try {
      await modalInteraction.deferReply({ ephemeral: true });

      const provider = modalInteraction.fields.getTextInputValue('provider').toLowerCase().trim() as AIProvider;
      const keyName = modalInteraction.fields.getTextInputValue('key_name').trim();
      const apiKey = modalInteraction.fields.getTextInputValue('api_key').trim();
      const isDefaultStr = modalInteraction.fields.getTextInputValue('is_default').toLowerCase().trim();
      const isDefault = isDefaultStr === 'yes' || isDefaultStr === 'true' || isDefaultStr === '1';

      // Validate provider
      const validProviders: AIProvider[] = ['openai', 'anthropic', 'google', 'azure', 'custom'];
      if (!validProviders.includes(provider)) {
        await modalInteraction.editReply({
          content: `❌ Invalid provider "${provider}". Supported: ${validProviders.join(', ')}`,
        });
        return;
      }

      // Validate API key format
      const keyValidation = validateApiKey(provider, apiKey);
      if (!keyValidation.isValid) {
        const providerConfig = getProviderConfig(provider);
        await modalInteraction.editReply({
          content: `❌ **Invalid ${providerConfig.name} API Key Format**\n\n` +
                  `${keyValidation.message}\n\n` +
                  `**Expected format:** \`${providerConfig.keyFormat}\`\n` +
                  `**Get your key:** ${providerConfig.apiKeyUrl}`,
        });
        return;
      }

      // Add the API key
      const result = await this.apiKeyManager.addApiKey({
        userId: modalInteraction.user.id,
        guildId: modalInteraction.guildId!,
        provider,
        keyName,
        apiKey,
        displayName: keyName,
        isDefault,
        features: [],
      });

      if (result) {
        const embed = new EmbedBuilder()
          .setTitle('✅ API Key Added Successfully')
          .setDescription(
            `**Key Name:** ${keyName}\n` +
            `**Provider:** ${provider.toUpperCase()}\n` +
            `**Default:** ${isDefault ? 'Yes' : 'No'}\n` +
            `**Status:** ${result.validation?.isValid ? '✅ Valid' : '❌ Invalid'}\n\n` +
            `Your API key has been encrypted and stored securely. You can now use it with AI agents that support ${provider}.`
          )
          .setColor('#00FF00')
          .setTimestamp();

        if (!result.validation?.isValid && result.validation?.errorMessage) {
          embed.addFields([{
            name: '⚠️ Validation Warning',
            value: result.validation.errorMessage,
            inline: false,
          }]);
        }

        await modalInteraction.editReply({ embeds: [embed] });
      } else {
        await modalInteraction.editReply({
          content: '❌ Failed to add API key. Please check your inputs and try again.',
        });
      }

    } catch (error) {
      this.logger.error('Failed to handle API key modal submission:', error);
      
      if (modalInteraction.deferred) {
        await modalInteraction.editReply({
          content: '❌ Failed to process API key. Please try again.',
        });
      } else {
        await modalInteraction.reply({
          content: '❌ Failed to process API key. Please try again.',
          ephemeral: true,
        });
      }
    }
  }

  private async listApiKeys(interaction: any): Promise<void> {
    const userKeys = await this.apiKeyManager.getUserApiKeys(interaction.user.id, interaction.guild.id);

    const embed = new EmbedBuilder()
      .setTitle('🔑 Your API Keys')
      .setColor('#5865F2')
      .setTimestamp();

    if (userKeys.length === 0) {
      embed.setDescription(
        '**No API keys found.**\n\n' +
        'Add your first API key using:\n' +
        '`/api-keys action:add provider:openai key_name:"My Key" api_key:"sk-..."`\n\n' +
        'Or use the AI panel buttons for an easier interface!'
      );
    } else {
      const keyList = userKeys.map((key, index) => {
        const isDefault = key.isDefault ? ' 🌟' : '';
        const status = key.validation?.isValid ? '✅' : '❌';
        const usageCount = key.config.usageCount || 0;
        
        return `**${index + 1}.** ${status} **${key.config.displayName}**${isDefault}\n` +
               `Provider: ${key.provider.toUpperCase()}\n` +
               `Usage: ${usageCount} requests\n` +
               `Added: <t:${Math.floor(new Date(key.createdAt).getTime() / 1000)}:R>`;
      }).join('\n\n');

      embed.setDescription(
        keyList + '\n\n' +
        '**Legend:**\n' +
        '✅ = Valid & Working\n' +
        '❌ = Invalid or Error\n' +
        '🌟 = Default for provider'
      );
    }

    await interaction.editReply({ embeds: [embed] });
  }

  private async addApiKey(
    interaction: any, 
    provider: AIProvider, 
    keyName: string, 
    apiKey: string, 
    isDefault: boolean
  ): Promise<void> {
    const result = await this.apiKeyManager.addApiKey({
      userId: interaction.user.id,
      guildId: interaction.guild.id,
      provider,
      keyName,
      apiKey,
      displayName: keyName,
      isDefault,
      features: [],
    });

    if (result) {
      await interaction.editReply({
        content: `✅ **API Key Added Successfully**\n\n` +
                `Key: ${keyName}\n` +
                `Provider: ${provider.toUpperCase()}\n` +
                `Status: ${result.validation?.isValid ? 'Valid ✅' : 'Invalid ❌'}\n` +
                `Default: ${isDefault ? 'Yes' : 'No'}`,
      });
    } else {
      await interaction.editReply({
        content: '❌ Failed to add API key. The key name might already exist or there was a validation error.',
      });
    }
  }

  private async deleteApiKey(interaction: any, keyName: string): Promise<void> {
    // First find the key to get its ID
    const userKeys = await this.apiKeyManager.getUserApiKeys(interaction.user.id, interaction.guild.id);
    const keyToDelete = userKeys.find(key => key.keyName === keyName || key.config.displayName === keyName);

    if (!keyToDelete) {
      await interaction.editReply({
        content: `❌ API key "${keyName}" not found. Use \`/api-keys action:list\` to see your keys.`,
      });
      return;
    }

    const success = await this.apiKeyManager.deleteApiKey(interaction.user.id, interaction.guild.id, keyToDelete.id);

    if (success) {
      await interaction.editReply({
        content: `✅ **API Key Deleted**\n\nDeleted: ${keyToDelete.config.displayName} (${keyToDelete.provider.toUpperCase()})`,
      });
    } else {
      await interaction.editReply({
        content: '❌ Failed to delete API key. Please try again.',
      });
    }
  }

  private async setDefaultKey(interaction: any, keyName: string): Promise<void> {
    // First find the key to get its ID
    const userKeys = await this.apiKeyManager.getUserApiKeys(interaction.user.id, interaction.guild.id);
    const keyToSet = userKeys.find(key => key.keyName === keyName || key.config.displayName === keyName);

    if (!keyToSet) {
      await interaction.editReply({
        content: `❌ API key "${keyName}" not found. Use \`/api-keys action:list\` to see your keys.`,
      });
      return;
    }

    const success = await this.apiKeyManager.setDefaultKey(interaction.user.id, interaction.guild.id, keyToSet.id);

    if (success) {
      await interaction.editReply({
        content: `✅ **Default Key Updated**\n\n${keyToSet.config.displayName} is now the default for ${keyToSet.provider.toUpperCase()}`,
      });
    } else {
      await interaction.editReply({
        content: '❌ Failed to set default key. Please try again.',
      });
    }
  }
}