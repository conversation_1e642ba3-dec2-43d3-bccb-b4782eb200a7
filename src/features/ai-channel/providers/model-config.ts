import { AIProvider } from '@/core/database';

export interface ModelConfig {
  id: string;
  name: string;
  displayName: string;
  description: string;
  provider: AIProvider;
  contextWindow: number;
  maxOutputTokens: number;
  inputPrice: number;  // Price per 1M input tokens in USD
  outputPrice: number; // Price per 1M output tokens in USD
  capabilities: ModelCapability[];
  isRecommended?: boolean;
  isLatest?: boolean;
  isDeprecated?: boolean;
  launchDate: string;
  notes?: string;
}

export type ModelCapability = 
  | 'text-generation'
  | 'code-generation'
  | 'creative-writing'
  | 'reasoning'
  | 'math'
  | 'vision'
  | 'function-calling'
  | 'json-mode'
  | 'embeddings'
  | 'image-generation'
  | 'multimodal'
  | 'long-context'
  | 'real-time'
  | 'web-search';

export const MODEL_CONFIGS: Record<string, ModelConfig> = {
  // OpenAI Models
  'gpt-4o': {
    id: 'gpt-4o',
    name: 'GPT-4o',
    displayName: 'GPT-4o (Omni)',
    description: 'Most advanced multimodal model with vision, audio, and text capabilities',
    provider: 'openai',
    contextWindow: 128000,
    maxOutputTokens: 4096,
    inputPrice: 5.00,
    outputPrice: 15.00,
    capabilities: ['text-generation', 'code-generation', 'creative-writing', 'reasoning', 'math', 'vision', 'function-calling', 'json-mode', 'multimodal'],
    isRecommended: true,
    isLatest: true,
    launchDate: '2024-05-13',
    notes: 'Flagship model with multimodal capabilities'
  },
  'gpt-4o-mini': {
    id: 'gpt-4o-mini',
    name: 'GPT-4o mini',
    displayName: 'GPT-4o mini',
    description: 'Fast and affordable multimodal model for lightweight tasks',
    provider: 'openai',
    contextWindow: 128000,
    maxOutputTokens: 16384,
    inputPrice: 0.15,
    outputPrice: 0.60,
    capabilities: ['text-generation', 'code-generation', 'creative-writing', 'reasoning', 'vision', 'function-calling', 'json-mode', 'multimodal'],
    isRecommended: true,
    launchDate: '2024-07-18',
    notes: 'Cost-effective choice for most use cases'
  },
  'gpt-4-turbo': {
    id: 'gpt-4-turbo',
    name: 'GPT-4 Turbo',
    displayName: 'GPT-4 Turbo',
    description: 'High-performance model with 128K context window',
    provider: 'openai',
    contextWindow: 128000,
    maxOutputTokens: 4096,
    inputPrice: 10.00,
    outputPrice: 30.00,
    capabilities: ['text-generation', 'code-generation', 'creative-writing', 'reasoning', 'math', 'vision', 'function-calling', 'json-mode'],
    launchDate: '2024-04-09',
    notes: 'Previous generation flagship model'
  },
  'gpt-4': {
    id: 'gpt-4',
    name: 'GPT-4',
    displayName: 'GPT-4',
    description: 'Original GPT-4 model with strong reasoning capabilities',
    provider: 'openai',
    contextWindow: 8192,
    maxOutputTokens: 4096,
    inputPrice: 30.00,
    outputPrice: 60.00,
    capabilities: ['text-generation', 'code-generation', 'creative-writing', 'reasoning', 'math', 'function-calling'],
    launchDate: '2023-03-14',
    notes: 'Legacy model, use GPT-4o instead'
  },
  'gpt-3.5-turbo': {
    id: 'gpt-3.5-turbo',
    name: 'GPT-3.5 Turbo',
    displayName: 'GPT-3.5 Turbo',
    description: 'Fast and affordable model for simpler tasks',
    provider: 'openai',
    contextWindow: 16385,
    maxOutputTokens: 4096,
    inputPrice: 0.50,
    outputPrice: 1.50,
    capabilities: ['text-generation', 'code-generation', 'creative-writing', 'function-calling', 'json-mode'],
    launchDate: '2023-03-01',
    notes: 'Budget-friendly option for basic tasks'
  },
  'text-embedding-3-large': {
    id: 'text-embedding-3-large',
    name: 'text-embedding-3-large',
    displayName: 'Text Embedding 3 Large',
    description: 'High-quality text embeddings for semantic search and similarity',
    provider: 'openai',
    contextWindow: 8191,
    maxOutputTokens: 0,
    inputPrice: 0.13,
    outputPrice: 0.00,
    capabilities: ['embeddings'],
    isRecommended: true,
    launchDate: '2024-01-25',
    notes: 'Best for semantic search and RAG applications'
  },
  'dall-e-3': {
    id: 'dall-e-3',
    name: 'DALL-E 3',
    displayName: 'DALL-E 3',
    description: 'Advanced text-to-image generation model',
    provider: 'openai',
    contextWindow: 4000,
    maxOutputTokens: 0,
    inputPrice: 40.00, // Per image
    outputPrice: 0.00,
    capabilities: ['image-generation'],
    isLatest: true,
    launchDate: '2023-10-01',
    notes: 'High-quality image generation'
  },

  // Anthropic Models
  'claude-3-5-sonnet-20241022': {
    id: 'claude-3-5-sonnet-20241022',
    name: 'Claude 3.5 Sonnet',
    displayName: 'Claude 3.5 Sonnet (Latest)',
    description: 'Most advanced Claude model with superior reasoning and coding abilities',
    provider: 'anthropic',
    contextWindow: 200000,
    maxOutputTokens: 8192,
    inputPrice: 3.00,
    outputPrice: 15.00,
    capabilities: ['text-generation', 'code-generation', 'creative-writing', 'reasoning', 'math', 'vision', 'long-context'],
    isRecommended: true,
    isLatest: true,
    launchDate: '2024-10-22',
    notes: 'Best Claude model for complex reasoning and coding'
  },
  'claude-3-5-haiku-20241022': {
    id: 'claude-3-5-haiku-20241022',
    name: 'Claude 3.5 Haiku',
    displayName: 'Claude 3.5 Haiku',
    description: 'Fast and affordable Claude model for everyday tasks',
    provider: 'anthropic',
    contextWindow: 200000,
    maxOutputTokens: 8192,
    inputPrice: 0.80,
    outputPrice: 4.00,
    capabilities: ['text-generation', 'code-generation', 'creative-writing', 'reasoning', 'long-context'],
    isRecommended: true,
    isLatest: true,
    launchDate: '2024-10-22',
    notes: 'Most cost-effective Claude model'
  },
  'claude-3-opus-20240229': {
    id: 'claude-3-opus-20240229',
    name: 'Claude 3 Opus',
    displayName: 'Claude 3 Opus',
    description: 'Previous flagship model with exceptional reasoning capabilities',
    provider: 'anthropic',
    contextWindow: 200000,
    maxOutputTokens: 4096,
    inputPrice: 15.00,
    outputPrice: 75.00,
    capabilities: ['text-generation', 'code-generation', 'creative-writing', 'reasoning', 'math', 'vision', 'long-context'],
    launchDate: '2024-02-29',
    notes: 'High-quality but expensive, use Claude 3.5 Sonnet instead'
  },
  'claude-3-sonnet-20240229': {
    id: 'claude-3-sonnet-20240229',
    name: 'Claude 3 Sonnet',
    displayName: 'Claude 3 Sonnet',
    description: 'Balanced performance and cost for general use',
    provider: 'anthropic',
    contextWindow: 200000,
    maxOutputTokens: 4096,
    inputPrice: 3.00,
    outputPrice: 15.00,
    capabilities: ['text-generation', 'code-generation', 'creative-writing', 'reasoning', 'math', 'vision', 'long-context'],
    launchDate: '2024-02-29',
    notes: 'Superseded by Claude 3.5 Sonnet'
  },
  'claude-3-haiku-20240307': {
    id: 'claude-3-haiku-20240307',
    name: 'Claude 3 Haiku',
    displayName: 'Claude 3 Haiku',
    description: 'Fast and lightweight model for simple tasks',
    provider: 'anthropic',
    contextWindow: 200000,
    maxOutputTokens: 4096,
    inputPrice: 0.25,
    outputPrice: 1.25,
    capabilities: ['text-generation', 'code-generation', 'creative-writing', 'reasoning', 'long-context'],
    launchDate: '2024-03-07',
    notes: 'Superseded by Claude 3.5 Haiku'
  },

  // Google Models
  'gemini-1.5-pro-002': {
    id: 'gemini-1.5-pro-002',
    name: 'Gemini 1.5 Pro',
    displayName: 'Gemini 1.5 Pro (Latest)',
    description: 'Advanced multimodal model with massive 2M token context window',
    provider: 'google',
    contextWindow: 2000000,
    maxOutputTokens: 8192,
    inputPrice: 1.25,
    outputPrice: 5.00,
    capabilities: ['text-generation', 'code-generation', 'creative-writing', 'reasoning', 'math', 'vision', 'function-calling', 'multimodal', 'long-context'],
    isRecommended: true,
    isLatest: true,
    launchDate: '2024-09-24',
    notes: 'Largest context window available, excellent for document analysis'
  },
  'gemini-1.5-flash-002': {
    id: 'gemini-1.5-flash-002',
    name: 'Gemini 1.5 Flash',
    displayName: 'Gemini 1.5 Flash',
    description: 'Fast and efficient model optimized for speed and cost',
    provider: 'google',
    contextWindow: 1000000,
    maxOutputTokens: 8192,
    inputPrice: 0.075,
    outputPrice: 0.30,
    capabilities: ['text-generation', 'code-generation', 'creative-writing', 'reasoning', 'vision', 'function-calling', 'multimodal', 'long-context'],
    isRecommended: true,
    isLatest: true,
    launchDate: '2024-09-24',
    notes: 'Best price-performance ratio'
  },
  'gemini-1.5-flash-8b': {
    id: 'gemini-1.5-flash-8b',
    name: 'Gemini 1.5 Flash-8B',
    displayName: 'Gemini 1.5 Flash-8B',
    description: 'Ultra-fast and affordable model for high-volume use cases',
    provider: 'google',
    contextWindow: 1000000,
    maxOutputTokens: 8192,
    inputPrice: 0.0375,
    outputPrice: 0.15,
    capabilities: ['text-generation', 'code-generation', 'creative-writing', 'reasoning', 'vision', 'function-calling', 'multimodal', 'long-context'],
    launchDate: '2024-10-03',
    notes: 'Most affordable option with good performance'
  },
  'gemini-1.0-pro': {
    id: 'gemini-1.0-pro',
    name: 'Gemini 1.0 Pro',
    displayName: 'Gemini 1.0 Pro',
    description: 'Original Gemini model for text-only tasks',
    provider: 'google',
    contextWindow: 30720,
    maxOutputTokens: 2048,
    inputPrice: 0.50,
    outputPrice: 1.50,
    capabilities: ['text-generation', 'code-generation', 'creative-writing', 'reasoning'],
    isDeprecated: true,
    launchDate: '2023-12-06',
    notes: 'Legacy model, use Gemini 1.5 instead'
  },
  'text-embedding-004': {
    id: 'text-embedding-004',
    name: 'Text Embedding 004',
    displayName: 'Text Embedding 004',
    description: 'Google\'s text embedding model for semantic understanding',
    provider: 'google',
    contextWindow: 2048,
    maxOutputTokens: 0,
    inputPrice: 0.00,
    outputPrice: 0.00,
    capabilities: ['embeddings'],
    isLatest: true,
    launchDate: '2024-09-24',
    notes: 'Free embedding model with good performance'
  },

  // Azure OpenAI Models (same as OpenAI but through Azure)
  'azure-gpt-4o': {
    id: 'gpt-4o',
    name: 'GPT-4o (Azure)',
    displayName: 'Azure GPT-4o',
    description: 'GPT-4o through Azure OpenAI Service with enterprise features',
    provider: 'azure',
    contextWindow: 128000,
    maxOutputTokens: 4096,
    inputPrice: 5.00,
    outputPrice: 15.00,
    capabilities: ['text-generation', 'code-generation', 'creative-writing', 'reasoning', 'math', 'vision', 'function-calling', 'json-mode', 'multimodal'],
    isRecommended: true,
    launchDate: '2024-05-13',
    notes: 'Enterprise-grade with data residency and compliance features'
  },
  'azure-gpt-4-turbo': {
    id: 'gpt-4-turbo',
    name: 'GPT-4 Turbo (Azure)',
    displayName: 'Azure GPT-4 Turbo',
    description: 'GPT-4 Turbo through Azure with enterprise security',
    provider: 'azure',
    contextWindow: 128000,
    maxOutputTokens: 4096,
    inputPrice: 10.00,
    outputPrice: 30.00,
    capabilities: ['text-generation', 'code-generation', 'creative-writing', 'reasoning', 'math', 'vision', 'function-calling', 'json-mode'],
    launchDate: '2024-04-09',
    notes: 'Enterprise deployment with private networks'
  },
  'azure-gpt-35-turbo': {
    id: 'gpt-35-turbo',
    name: 'GPT-3.5 Turbo (Azure)',
    displayName: 'Azure GPT-3.5 Turbo',
    description: 'Cost-effective Azure deployment of GPT-3.5',
    provider: 'azure',
    contextWindow: 16385,
    maxOutputTokens: 4096,
    inputPrice: 0.50,
    outputPrice: 1.50,
    capabilities: ['text-generation', 'code-generation', 'creative-writing', 'function-calling', 'json-mode'],
    launchDate: '2023-03-01',
    notes: 'Budget-friendly enterprise option'
  },

  // Exa Search Models
  'exa-web-search': {
    id: 'exa-web-search',
    name: 'Exa Web Search',
    displayName: 'Exa Web Search',
    description: 'Neural web search with real-time information retrieval',
    provider: 'exa',
    contextWindow: 100000,
    maxOutputTokens: 8192,
    inputPrice: 1.00,
    outputPrice: 1.00,
    capabilities: ['web-search', 'real-time'],
    isRecommended: true,
    isLatest: true,
    launchDate: '2024-01-15',
    notes: 'Primary web search functionality'
  },
  'exa-neural-search': {
    id: 'exa-neural-search',
    name: 'Exa Neural Search',
    displayName: 'Exa Neural Search',
    description: 'Advanced neural search with semantic understanding',
    provider: 'exa',
    contextWindow: 100000,
    maxOutputTokens: 8192,
    inputPrice: 1.50,
    outputPrice: 1.50,
    capabilities: ['web-search', 'reasoning', 'real-time'],
    isRecommended: true,
    launchDate: '2024-02-01',
    notes: 'Enhanced search with AI reasoning'
  },
  'exa-content-extraction': {
    id: 'exa-content-extraction',
    name: 'Exa Content Extraction',
    displayName: 'Exa Content Extraction',
    description: 'Extract and analyze content from web pages',
    provider: 'exa',
    contextWindow: 200000,
    maxOutputTokens: 16384,
    inputPrice: 2.00,
    outputPrice: 2.00,
    capabilities: ['web-search', 'text-generation', 'real-time'],
    launchDate: '2024-01-20',
    notes: 'Full content analysis and extraction'
  },
  'exa-find-similar': {
    id: 'exa-find-similar',
    name: 'Exa Find Similar',
    displayName: 'Exa Find Similar',
    description: 'Discover pages similar to a given URL',
    provider: 'exa',
    contextWindow: 50000,
    maxOutputTokens: 4096,
    inputPrice: 0.75,
    outputPrice: 0.75,
    capabilities: ['web-search', 'real-time'],
    launchDate: '2024-01-10',
    notes: 'Find related content across the web'
  }
};

export function getModelsByProvider(provider: AIProvider): ModelConfig[] {
  return Object.values(MODEL_CONFIGS).filter((model: any) => model.provider === provider);
}

export function getRecommendedModels(provider: AIProvider): ModelConfig[] {
  return getModelsByProvider(provider).filter((model: any) => model.isRecommended);
}

export function getLatestModels(provider: AIProvider): ModelConfig[] {
  return getModelsByProvider(provider).filter((model: any) => model.isLatest);
}

export function getModelById(modelId: string): ModelConfig | undefined {
  return MODEL_CONFIGS[modelId];
}

export function getModelsByCapability(capability: ModelCapability): ModelConfig[] {
  return Object.values(MODEL_CONFIGS).filter((model: any) => 
    model.capabilities.includes(capability)
  );
}

export function formatPrice(price: number, isImage: boolean = false): string {
  if (price === 0) return 'Free';
  if (isImage) return `$${price.toFixed(2)}/image`;
  if (price < 1) return `$${price.toFixed(3)}/1M tokens`;
  return `$${price.toFixed(2)}/1M tokens`;
}

export function getCapabilityIcon(capability: ModelCapability): string {
  const icons: Record<ModelCapability, string> = {
    'text-generation': '📝',
    'code-generation': '💻',
    'creative-writing': '✍️',
    'reasoning': '🧠',
    'math': '🔢',
    'vision': '👁️',
    'function-calling': '⚡',
    'json-mode': '📋',
    'embeddings': '🔍',
    'image-generation': '🎨',
    'multimodal': '🎭',
    'long-context': '📚',
    'real-time': '⚡',
    'web-search': '🌐'
  };
  return icons[capability] || '🔧';
}

export function getCapabilityDescription(capability: ModelCapability): string {
  const descriptions: Record<ModelCapability, string> = {
    'text-generation': 'Generate human-like text responses',
    'code-generation': 'Write and understand programming code',
    'creative-writing': 'Create stories, poems, and creative content',
    'reasoning': 'Complex logical thinking and problem solving',
    'math': 'Mathematical calculations and problem solving',
    'vision': 'Analyze and understand images',
    'function-calling': 'Execute external functions and tools',
    'json-mode': 'Generate structured JSON responses',
    'embeddings': 'Convert text to numerical vectors',
    'image-generation': 'Create images from text descriptions',
    'multimodal': 'Process text, images, and other media types',
    'long-context': 'Handle very long documents and conversations',
    'real-time': 'Real-time processing and responses',
    'web-search': 'Search and access web information'
  };
  return descriptions[capability] || 'Advanced AI capability';
}