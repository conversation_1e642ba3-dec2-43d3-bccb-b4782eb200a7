import { Injectable, Logger } from '@nestjs/common';
import { 
  ChannelType, 
  EmbedBuilder, 
  PermissionFlagsBits,
  TextChannel,
  GuildMember 
} from 'discord.js';
import { 
  Context, 
  Options, 
  SlashCommand, 
  SlashCommandContext, 
  StringOption,
  ChannelOption,
  BooleanOption
} from 'necord';
import { AIChannelPanelService } from './ai-channel-panel.service';
import { PrivateChatManagerService } from './private-chat-manager.service';
import { AIChannelAutoSetupService } from './ai-channel-auto-setup.service';

export class AISetupDto {
  @ChannelOption({
    name: 'channel',
    description: 'Channel to set up as AI channel',
    required: false,
  })
  channel?: TextChannel;

  @BooleanOption({
    name: 'create_new',
    description: 'Create a new AI channel instead of using existing',
    required: false,
  })
  createNew?: boolean;
}

export class AIPanelDto {
  @ChannelOption({
    name: 'channel',
    description: 'Channel to deploy the AI panel in',
    required: false,
  })
  channel?: TextChannel;
}

export class AISessionDto {
  @StringOption({
    name: 'user_id',
    description: 'User ID to manage sessions for',
    required: true,
  })
  userId: string;

  @StringOption({
    name: 'action',
    description: 'Action to perform',
    required: true,
    choices: [
      { name: 'List Active', value: 'list' },
      { name: 'Close All', value: 'close_all' },
      { name: 'Archive All', value: 'archive_all' },
    ],
  })
  action: string;
}

@Injectable()
export class AIChannelCommands {
  private readonly logger = new Logger(AIChannelCommands.name);

  constructor(
    private readonly panelService: AIChannelPanelService,
    private readonly chatManager: PrivateChatManagerService,
    private readonly autoSetupService: AIChannelAutoSetupService,
  ) {}

  @SlashCommand({
    name: 'ai-setup',
    description: 'Set up AI channel for this server (Admin only)',
    defaultMemberPermissions: PermissionFlagsBits.Administrator,
  })
  async onAiSetup(
    @Context() [interaction]: SlashCommandContext,
    @Options() { channel, createNew }: AISetupDto,
  ) {
    try {
      if (!interaction.guild || !interaction.member) {
        await interaction.reply({
          content: '❌ This command can only be used in a server.',
          ephemeral: true,
        });
        return;
      }

      const member = interaction.member as GuildMember;
      if (!member.permissions.has(PermissionFlagsBits.Administrator)) {
        await interaction.reply({
          content: '❌ You need Administrator permissions to use this command.',
          ephemeral: true,
        });
        return;
      }

      await interaction.deferReply({ ephemeral: true });

      // Create a timeout promise to prevent interaction expiry
      const timeoutPromise = new Promise<void>((resolve) => {
        setTimeout(() => {
          this.logger.warn('AI setup command timed out, continuing in background');
          resolve();
        }, 12000); // 12 seconds timeout
      });

      const setupPromise = this.performAiSetup(interaction, channel, createNew);

      // Race between setup and timeout
      await Promise.race([setupPromise, timeoutPromise]);

      // If we reach here due to timeout, send a timeout message
      if (!setupPromise) {
        try {
          await interaction.editReply({
            content: '⏱️ Setup is taking longer than expected. The process will continue in the background.',
          });
        } catch (timeoutError) {
          this.logger.error('Failed to send timeout message:', timeoutError);
        }
      }

    } catch (error) {
      this.logger.error('AI setup command failed:', error);
      
      try {
        const errorMessage = interaction.deferred 
          ? { content: '❌ Setup failed. Please check permissions and try again.' }
          : { content: '❌ Setup failed. Please check permissions and try again.', ephemeral: true };

        if (interaction.deferred) {
          await interaction.editReply(errorMessage);
        } else {
          await interaction.reply(errorMessage);
        }
      } catch (replyError) {
        this.logger.error('Failed to send error message:', replyError);
      }
    }
  }

  private async performAiSetup(
    interaction: any,
    channel?: TextChannel,
    createNew?: boolean
  ): Promise<void> {
    let targetChannel: TextChannel;

    if (createNew) {
      // Create new AI channel
      const newChannel = await interaction.guild.channels.create({
        name: 'ai-agents',
        type: ChannelType.GuildText,
        topic: '🤖 AI Agent Panel - Start private conversations with AI assistants',
        reason: 'AI Channel setup by administrator',
        permissionOverwrites: [
          {
            id: interaction.guild.roles.everyone.id,
            allow: [PermissionFlagsBits.ViewChannel, PermissionFlagsBits.ReadMessageHistory],
            deny: [PermissionFlagsBits.SendMessages, PermissionFlagsBits.AddReactions],
          },
        ],
      });

      targetChannel = newChannel as TextChannel;
      this.logger.log(`Created new AI channel: ${newChannel.id} in guild ${interaction.guild.id}`);
    } else {
      // Use specified channel or current channel
      targetChannel = channel || (interaction.channel as TextChannel);
      
      if (!targetChannel || targetChannel.type !== ChannelType.GuildText) {
        await interaction.editReply({
          content: '❌ Please specify a valid text channel or use the `create_new` option.',
        });
        return;
      }
    }

    // Create or update the AI panel to latest version
    const panelMessage = await this.panelService.ensurePanelUpToDate(
      interaction.guild.id,
      targetChannel.id
    );

    if (!panelMessage) {
      await interaction.editReply({
        content: '❌ Failed to create AI panel. Please check permissions and try again.',
      });
      return;
    }

    // Success response
    const successEmbed = new EmbedBuilder()
      .setTitle('✅ AI Channel Setup Complete!')
      .setDescription(
        `The AI agent panel has been successfully deployed!\n\n` +
        `**Channel:** <#${targetChannel.id}>\n` +
        `**Panel Message:** [Click here](${panelMessage.url})\n\n` +
        `**What happens next:**\n` +
        `• Users can click buttons in the panel to start private AI conversations\n` +
        `• Each user gets their own private thread with their chosen AI agent\n` +
        `• Premium features are automatically gated based on Whop subscriptions\n` +
        `• Future servers will be automatically configured when the bot joins!\n\n` +
        `**Available AI Agents:**\n` +
        `🌱 Personal Growth Coach (Free)\n` +
        `📋 Intake Specialist (Free)\n` +
        `📊 Progress Tracker (Premium)`
      )
      .setColor('#00FF00')
      .addFields([
        {
          name: '🔧 Management Commands',
          value: 
            '`/ai-panel` - Deploy panel in different channel\n' +
            '`/ai-sessions` - Manage user sessions\n' +
            '`/ai-auto-setup` - Trigger automatic setup\n' +
            '`/ai-auto-stats` - View auto-setup statistics\n' +
            '`/ai-reset` - Reset AI channel configuration',
          inline: false,
        },
        {
          name: '📊 Features',
          value:
            '• Private thread conversations\n' +
            '• Whop integration for premium access\n' +
            '• Automatic session management\n' +
            '• Conversation memory and context',
          inline: false,
        }
      ])
      .setTimestamp();

    await interaction.editReply({ embeds: [successEmbed] });
  }

  @SlashCommand({
    name: 'ai-panel',
    description: 'Deploy AI panel in current or specified channel (Admin only)',
    defaultMemberPermissions: PermissionFlagsBits.Administrator,
  })
  async onAiPanel(
    @Context() [interaction]: SlashCommandContext,
    @Options() { channel }: AIPanelDto,
  ) {
    try {
      if (!interaction.guild || !interaction.member) {
        await interaction.reply({
          content: '❌ This command can only be used in a server.',
          ephemeral: true,
        });
        return;
      }

      const member = interaction.member as GuildMember;
      if (!member.permissions.has(PermissionFlagsBits.Administrator)) {
        await interaction.reply({
          content: '❌ You need Administrator permissions to use this command.',
          ephemeral: true,
        });
        return;
      }

      await interaction.deferReply({ ephemeral: true });

      const targetChannel = channel || (interaction.channel as TextChannel);
      
      if (!targetChannel || targetChannel.type !== ChannelType.GuildText) {
        await interaction.editReply({
          content: '❌ Please specify a valid text channel.',
        });
        return;
      }

      const panelMessage = await this.panelService.createPanel(
        interaction.guild.id,
        targetChannel.id
      );

      if (!panelMessage) {
        await interaction.editReply({
          content: '❌ Failed to create AI panel. Please check permissions and try again.',
        });
        return;
      }

      await interaction.editReply({
        content: `✅ AI panel deployed successfully in <#${targetChannel.id}>!\n[View Panel](${panelMessage.url})`,
      });

    } catch (error) {
      this.logger.error('AI panel command failed:', error);
      
      if (interaction.deferred) {
        await interaction.editReply({
          content: '❌ Failed to deploy panel. Please try again.',
        });
      } else {
        await interaction.reply({
          content: '❌ Failed to deploy panel. Please try again.',
          ephemeral: true,
        });
      }
    }
  }

  @SlashCommand({
    name: 'ai-sessions',
    description: 'Manage AI chat sessions (Admin only)',
    defaultMemberPermissions: PermissionFlagsBits.Administrator,
  })
  async onAiSessions(
    @Context() [interaction]: SlashCommandContext,
    @Options() { userId, action }: AISessionDto,
  ) {
    try {
      if (!interaction.guild || !interaction.member) {
        await interaction.reply({
          content: '❌ This command can only be used in a server.',
          ephemeral: true,
        });
        return;
      }

      const member = interaction.member as GuildMember;
      if (!member.permissions.has(PermissionFlagsBits.Administrator)) {
        await interaction.reply({
          content: '❌ You need Administrator permissions to use this command.',
          ephemeral: true,
        });
        return;
      }

      await interaction.deferReply({ ephemeral: true });

      const sessions = await this.chatManager.getUserActiveSessions(userId, interaction.guild.id);

      if (action === 'list') {
        if (sessions.length === 0) {
          await interaction.editReply({
            content: `📋 User <@${userId}> has no active AI chat sessions.`,
          });
          return;
        }

        const sessionList = sessions.map((session, index) => 
          `${index + 1}. **${session.agentType}** - <#${session.threadId}> (${session.accessLevel})\n` +
          `   📅 Created: <t:${Math.floor(new Date(session.createdAt).getTime() / 1000)}:R>\n` +
          `   💬 Messages: ${session.messageCount || 0}`
        ).join('\n\n');

        const embed = new EmbedBuilder()
          .setTitle(`📋 AI Sessions for <@${userId}>`)
          .setDescription(sessionList)
          .setColor('#5865F2')
          .setFooter({ text: `Total active sessions: ${sessions.length}` })
          .setTimestamp();

        await interaction.editReply({ embeds: [embed] });

      } else if (action === 'close_all') {
        let closedCount = 0;
        for (const session of sessions) {
          await this.chatManager.closeSession(session.id);
          closedCount++;
        }

        await interaction.editReply({
          content: `✅ Closed ${closedCount} active AI chat sessions for <@${userId}>.`,
        });

      } else if (action === 'archive_all') {
        let archivedCount = 0;
        for (const session of sessions) {
          await this.chatManager.updateSessionStatus(session.id, 'archived');
          archivedCount++;
        }

        await interaction.editReply({
          content: `🗄️ Archived ${archivedCount} active AI chat sessions for <@${userId}>.`,
        });
      }

    } catch (error) {
      this.logger.error('AI sessions command failed:', error);
      
      if (interaction.deferred) {
        await interaction.editReply({
          content: '❌ Failed to manage sessions. Please try again.',
        });
      } else {
        await interaction.reply({
          content: '❌ Failed to manage sessions. Please try again.',
          ephemeral: true,
        });
      }
    }
  }

  @SlashCommand({
    name: 'ai-reset',
    description: 'Reset AI channel configuration (Admin only)',
    defaultMemberPermissions: PermissionFlagsBits.Administrator,
  })
  async onAiReset(@Context() [interaction]: SlashCommandContext) {
    try {
      if (!interaction.guild || !interaction.member) {
        await interaction.reply({
          content: '❌ This command can only be used in a server.',
          ephemeral: true,
        });
        return;
      }

      const member = interaction.member as GuildMember;
      if (!member.permissions.has(PermissionFlagsBits.Administrator)) {
        await interaction.reply({
          content: '❌ You need Administrator permissions to use this command.',
          ephemeral: true,
        });
        return;
      }

      await interaction.deferReply({ ephemeral: true });

      const success = await this.panelService.removePanel(interaction.guild.id);

      if (success) {
        await interaction.editReply({
          content: '✅ AI channel configuration has been reset. You can run `/ai-setup` to set it up again.',
        });
      } else {
        await interaction.editReply({
          content: '⚠️ No AI channel configuration found or already reset.',
        });
      }

    } catch (error) {
      this.logger.error('AI reset command failed:', error);
      
      if (interaction.deferred) {
        await interaction.editReply({
          content: '❌ Failed to reset configuration. Please try again.',
        });
      } else {
        await interaction.reply({
          content: '❌ Failed to reset configuration. Please try again.',
          ephemeral: true,
        });
      }
    }
  }

  @SlashCommand({
    name: 'ai-update-panel',
    description: 'Update AI panel to latest version (Admin only)',
    defaultMemberPermissions: PermissionFlagsBits.Administrator,
  })
  async onAiUpdatePanel(@Context() [interaction]: SlashCommandContext) {
    try {
      if (!interaction.guild || !interaction.member) {
        await interaction.reply({
          content: '❌ This command can only be used in a server.',
          ephemeral: true,
        });
        return;
      }

      const member = interaction.member as GuildMember;
      if (!member.permissions.has(PermissionFlagsBits.Administrator)) {
        await interaction.reply({
          content: '❌ You need Administrator permissions to use this command.',
          ephemeral: true,
        });
        return;
      }

      await interaction.deferReply({ ephemeral: true });

      // Get current panel config
      const config = await this.panelService.getPanelConfig(interaction.guild.id);
      if (!config || !config.aiChannelId) {
        await interaction.editReply({
          content: '❌ No AI panel found. Use `/ai-setup` to create one first.',
        });
        return;
      }

      // Update the panel to latest version
      const panelMessage = await this.panelService.ensurePanelUpToDate(
        interaction.guild.id, 
        config.aiChannelId
      );

      if (panelMessage) {
        await interaction.editReply({
          content: `✅ AI panel updated to latest version in <#${config.aiChannelId}>!\n\n` +
                  `**New features:**\n` +
                  `• 🔑 API Key Management buttons\n` +
                  `• ➕ Quick "Add API Key" option\n` +
                  `• ❓ API Key Help and documentation\n\n` +
                  `Users can now add their own OpenAI, Anthropic, Google, and custom API keys!`,
        });
      } else {
        await interaction.editReply({
          content: '❌ Failed to update AI panel. Please try again or use `/ai-setup` to recreate it.',
        });
      }

    } catch (error) {
      this.logger.error('AI update panel command failed:', error);
      
      if (interaction.deferred) {
        await interaction.editReply({
          content: '❌ Failed to update panel. Please try again.',
        });
      } else {
        await interaction.reply({
          content: '❌ Failed to update panel. Please try again.',
          ephemeral: true,
        });
      }
    }
  }

  @SlashCommand({
    name: 'ai-stats',
    description: 'Show AI channel statistics (Admin only)',
    defaultMemberPermissions: PermissionFlagsBits.Administrator,
  })
  async onAiStats(@Context() [interaction]: SlashCommandContext) {
    try {
      if (!interaction.guild || !interaction.member) {
        await interaction.reply({
          content: '❌ This command can only be used in a server.',
          ephemeral: true,
        });
        return;
      }

      const member = interaction.member as GuildMember;
      if (!member.permissions.has(PermissionFlagsBits.Administrator)) {
        await interaction.reply({
          content: '❌ You need Administrator permissions to use this command.',
          ephemeral: true,
        });
        return;
      }

      await interaction.deferReply({ ephemeral: true });

      const config = await this.panelService.getPanelConfig(interaction.guild.id);
      const availableAgents = this.panelService.getAvailableAgents();

      const embed = new EmbedBuilder()
        .setTitle('📊 AI Channel Statistics')
        .setColor('#5865F2')
        .addFields([
          {
            name: '⚙️ Configuration Status',
            value: config 
              ? `✅ Configured\n📍 Channel: <#${config.aiChannelId}>\n🕐 Last Update: <t:${Math.floor(new Date(config.lastPanelUpdate || config.createdAt).getTime() / 1000)}:R>`
              : '❌ Not configured',
            inline: true,
          },
          {
            name: '🤖 Available Agents',
            value: availableAgents.map((agent: any) => 
              `${agent.emoji} ${agent.name} (${agent.accessLevel})`
            ).join('\n'),
            inline: true,
          },
          {
            name: '📈 Features',
            value: 
              '• Private thread conversations\n' +
              '• Whop premium integration\n' +
              '• Automatic session management\n' +
              '• Memory and context preservation',
            inline: false,
          }
        ])
        .setTimestamp();

      await interaction.editReply({ embeds: [embed] });

    } catch (error) {
      this.logger.error('AI stats command failed:', error);
      
      if (interaction.deferred) {
        await interaction.editReply({
          content: '❌ Failed to get statistics. Please try again.',
        });
      } else {
        await interaction.reply({
          content: '❌ Failed to get statistics. Please try again.',
          ephemeral: true,
        });
      }
    }
  }

  @SlashCommand({
    name: 'ai-auto-setup',
    description: 'Trigger automatic AI channel setup (Admin only)',
    defaultMemberPermissions: PermissionFlagsBits.Administrator,
  })
  async onAiAutoSetup(@Context() [interaction]: SlashCommandContext) {
    try {
      if (!interaction.guild || !interaction.member) {
        await interaction.reply({
          content: '❌ This command can only be used in a server.',
          ephemeral: true,
        });
        return;
      }

      const member = interaction.member as GuildMember;
      if (!member.permissions.has(PermissionFlagsBits.Administrator)) {
        await interaction.reply({
          content: '❌ You need Administrator permissions to use this command.',
          ephemeral: true,
        });
        return;
      }

      await interaction.deferReply({ ephemeral: true });

      // Add timeout to prevent interaction expiry
      const timeoutPromise = new Promise<boolean>((_, reject) => {
        setTimeout(() => reject(new Error('Setup timeout')), 12000); // 12 seconds timeout
      });

      try {
        const success = await Promise.race([
          this.autoSetupService.triggerAutoSetup(interaction.guild.id),
          timeoutPromise
        ]);

        if (success) {
          await interaction.editReply({
            content: '✅ Automatic AI channel setup completed! Check your server for the new AI agents channel.',
          });
        } else {
          await interaction.editReply({
            content: '⚠️ Auto-setup failed or AI channel already exists. Use `/ai-stats` to check current status.',
          });
        }
      } catch (timeoutError) {
        if (timeoutError.message === 'Setup timeout') {
          await interaction.editReply({
            content: '⏱️ Setup is taking longer than expected. Please check your server in a moment or use `/ai-stats` to verify completion.',
          });
          // Continue setup in background
          this.autoSetupService.triggerAutoSetup(interaction.guild.id).catch(err => 
            this.logger.error('Background auto-setup failed:', err)
          );
        } else {
          throw timeoutError;
        }
      }

    } catch (error) {
      this.logger.error('AI auto-setup command failed:', error);
      
      try {
        if (interaction.deferred) {
          await interaction.editReply({
            content: '❌ Auto-setup failed. Please try again or use manual setup.',
          });
        } else {
          await interaction.reply({
            content: '❌ Auto-setup failed. Please try again or use manual setup.',
            ephemeral: true,
          });
        }
      } catch (replyError) {
        // If we can't reply, the interaction has likely expired
        this.logger.error('Failed to send error response - interaction may have expired:', replyError);
      }
    }
  }

  @SlashCommand({
    name: 'ai-debug',
    description: 'Debug AI channel setup issues (Admin only)',
    defaultMemberPermissions: PermissionFlagsBits.Administrator,
  })
  async onAiDebug(@Context() [interaction]: SlashCommandContext) {
    try {
      if (!interaction.guild || !interaction.member) {
        await interaction.reply({
          content: '❌ This command can only be used in a server.',
          ephemeral: true,
        });
        return;
      }

      const member = interaction.member as GuildMember;
      if (!member.permissions.has(PermissionFlagsBits.Administrator)) {
        await interaction.reply({
          content: '❌ You need Administrator permissions to use this command.',
          ephemeral: true,
        });
        return;
      }

      await interaction.deferReply({ ephemeral: true });

      // Get current config
      const config = await this.panelService.getPanelConfig(interaction.guild.id);
      
      // Check if auto-setup thinks it's configured
      const autoSetupStats = await this.autoSetupService.getAutoSetupStats();
      
      let debugInfo = `🔍 **AI Channel Debug Information**\n\n`;
      
      // Database config check
      if (config) {
        debugInfo += `✅ **Database Config Found:**\n`;
        debugInfo += `• Guild ID: ${config.guildId}\n`;
        debugInfo += `• Channel ID: ${config.aiChannelId}\n`;
        debugInfo += `• Panel Message ID: ${config.panelMessageId}\n`;
        debugInfo += `• Enabled: ${config.enabled}\n`;
        debugInfo += `• Last Update: <t:${Math.floor(new Date(config.lastPanelUpdate || config.createdAt).getTime() / 1000)}:R>\n\n`;
        
        // Check if channel exists
        try {
          const channel = await interaction.client.channels.fetch(config.aiChannelId);
          if (channel) {
            debugInfo += `✅ **AI Channel Exists:** <#${config.aiChannelId}>\n`;
            
            // Check if panel message exists
            if (config.panelMessageId) {
              try {
                const message = await (channel as TextChannel).messages.fetch(config.panelMessageId);
                if (message) {
                  debugInfo += `✅ **Panel Message Exists:** [Link](${message.url})\n`;
                  
                  // Check if it's actually an AI panel
                  if (message.embeds.length && message.embeds[0].title === '🤖 AI Agent Panel') {
                    debugInfo += `✅ **Valid AI Panel:** Message has correct embed title\n`;
                  } else {
                    debugInfo += `❌ **Invalid Panel:** Message is not an AI panel\n`;
                  }
                } else {
                  debugInfo += `❌ **Panel Message Missing:** Message ID ${config.panelMessageId} not found\n`;
                }
              } catch (msgError: any) {
                debugInfo += `❌ **Panel Message Error:** ${msgError.message} (Code: ${msgError.code})\n`;
              }
            } else {
              debugInfo += `⚠️ **No Panel Message ID:** Database config missing panel message ID\n`;
            }
          } else {
            debugInfo += `❌ **AI Channel Missing:** Channel ID ${config.aiChannelId} not found\n`;
          }
        } catch (channelError: any) {
          debugInfo += `❌ **Channel Error:** ${channelError.message} (Code: ${channelError.code})\n`;
        }
      } else {
        debugInfo += `❌ **No Database Config:** No AI channel configuration found for this guild\n\n`;
      }
      
      // Bot permissions check
      debugInfo += `🔧 **Bot Permissions:**\n`;
      const botMember = interaction.guild.members.cache.get(interaction.client.user?.id || '');
      if (botMember) {
        const requiredPerms = [
          PermissionFlagsBits.ManageChannels,
          PermissionFlagsBits.ViewChannel,
          PermissionFlagsBits.SendMessages,
          PermissionFlagsBits.EmbedLinks,
          PermissionFlagsBits.UseExternalEmojis,
          PermissionFlagsBits.ReadMessageHistory,
        ];
        
        const hasAllPerms = botMember.permissions.has(requiredPerms);
        debugInfo += hasAllPerms ? `✅ All required permissions present\n` : `❌ Missing required permissions\n`;
        
        requiredPerms.forEach(perm => {
          const hasPermission = botMember.permissions.has(perm);
          const permName = Object.keys(PermissionFlagsBits).find(key => PermissionFlagsBits[key as keyof typeof PermissionFlagsBits] === perm);
          debugInfo += `  ${hasPermission ? '✅' : '❌'} ${permName}\n`;
        });
      } else {
        debugInfo += `❌ Bot member not found in guild\n`;
      }
      
      debugInfo += `\n🤖 **Auto-Setup Status:**\n`;
      debugInfo += `• Enabled: ${autoSetupStats.enabled ? '✅' : '❌'}\n`;
      debugInfo += `• Total Guilds: ${autoSetupStats.totalGuilds}\n`;
      debugInfo += `• Setup Complete: ${autoSetupStats.setupCount}\n`;
      
      debugInfo += `\n💡 **Recommended Actions:**\n`;
      if (!config) {
        debugInfo += `• Run \`/ai-auto-setup\` to trigger automatic setup\n`;
      } else if (!config.panelMessageId) {
        debugInfo += `• Database config exists but panel missing - run \`/ai-auto-setup\` to recreate\n`;
      } else {
        try {
          const channel = await interaction.client.channels.fetch(config.aiChannelId);
          if (channel && config.panelMessageId) {
            const message = await (channel as TextChannel).messages.fetch(config.panelMessageId);
            if (!message) {
              debugInfo += `• Panel message was deleted - run \`/ai-auto-setup\` to recreate\n`;
            }
          }
        } catch {
          debugInfo += `• Channel or message verification failed - run \`/ai-auto-setup\` to fix\n`;
        }
      }
      
      // Split message if too long
      if (debugInfo.length > 2000) {
        const part1 = debugInfo.substring(0, 2000);
        const part2 = debugInfo.substring(2000);
        
        await interaction.editReply({ content: part1 });
        await interaction.followUp({ content: part2, ephemeral: true });
      } else {
        await interaction.editReply({ content: debugInfo });
      }

    } catch (error) {
      this.logger.error('AI debug command failed:', error);
      
      if (interaction.deferred) {
        await interaction.editReply({
          content: '❌ Failed to get debug information. Please try again.',
        });
      } else {
        await interaction.reply({
          content: '❌ Failed to get debug information. Please try again.',
          ephemeral: true,
        });
      }
    }
  }

  @SlashCommand({
    name: 'ai-force-heal',
    description: 'Force immediate healing of all AI setups (Admin only)',
    defaultMemberPermissions: PermissionFlagsBits.Administrator,
  })
  async onAiForceHeal(@Context() [interaction]: SlashCommandContext) {
    try {
      if (!interaction.guild || !interaction.member) {
        await interaction.reply({
          content: '❌ This command can only be used in a server.',
          ephemeral: true,
        });
        return;
      }

      const member = interaction.member as GuildMember;
      if (!member.permissions.has(PermissionFlagsBits.Administrator)) {
        await interaction.reply({
          content: '❌ You need Administrator permissions to use this command.',
          ephemeral: true,
        });
        return;
      }

      await interaction.deferReply({ ephemeral: true });

      // Start immediate setup for all guilds
      // Note: This will trigger auto-setup for all guilds the bot is in
      // this.autoSetupService.checkExistingGuilds() - method is private

      await interaction.editReply({
        content: '🚨 **Emergency healing initiated!**\n\nForce healing all AI setups across all servers. This process will:\n• Check every guild for broken AI panels\n• Automatically recreate missing panels\n• Fix orphaned database configurations\n• Complete within 1-2 minutes\n\nCheck server logs for detailed progress updates.',
      });

    } catch (error) {
      this.logger.error('AI force heal command failed:', error);
      
      if (interaction.deferred) {
        await interaction.editReply({
          content: '❌ Failed to start emergency healing. Please try again.',
        });
      } else {
        await interaction.reply({
          content: '❌ Failed to start emergency healing. Please try again.',
          ephemeral: true,
        });
      }
    }
  }

  @SlashCommand({
    name: 'ai-auto-stats',
    description: 'Show automatic setup statistics (Admin only)',
    defaultMemberPermissions: PermissionFlagsBits.Administrator,
  })
  async onAiAutoStats(@Context() [interaction]: SlashCommandContext) {
    try {
      if (!interaction.guild || !interaction.member) {
        await interaction.reply({
          content: '❌ This command can only be used in a server.',
          ephemeral: true,
        });
        return;
      }

      const member = interaction.member as GuildMember;
      if (!member.permissions.has(PermissionFlagsBits.Administrator)) {
        await interaction.reply({
          content: '❌ You need Administrator permissions to use this command.',
          ephemeral: true,
        });
        return;
      }

      await interaction.deferReply({ ephemeral: true });

      const stats = await this.autoSetupService.getAutoSetupStats();

      const embed = new EmbedBuilder()
        .setTitle('🤖 AI Auto-Setup Statistics')
        .setColor(stats.enabled ? '#00FF00' : '#FF0000')
        .addFields([
          {
            name: '⚙️ Auto-Setup Status',
            value: stats.enabled ? '✅ Enabled' : '❌ Disabled',
            inline: true,
          },
          {
            name: '📊 Guild Coverage',
            value: `Total Guilds: ${stats.totalGuilds}\nSetup Complete: ${stats.setupCount}\nPending Setup: ${stats.pendingSetup}`,
            inline: true,
          },
          {
            name: '🔧 Configuration',
            value: 
              `On Guild Join: ${stats.config?.onGuildJoin ? '✅' : '❌'}\n` +
              `Periodic Check: ${stats.config?.checkInterval ? '✅' : '❌'}\n` +
              `Channel Name: ${stats.config?.channelName || 'ai-agents'}\n` +
              `Category: ${stats.config?.categoryName || 'None'}`,
            inline: false,
          },
          {
            name: '⏰ Last Check',
            value: `<t:${Math.floor(new Date(stats.lastCheck || Date.now()).getTime() / 1000)}:R>`,
            inline: true,
          }
        ])
        .setTimestamp();

      if (stats.error) {
        embed.addFields([{
          name: '❌ Error',
          value: stats.error,
          inline: false,
        }]);
      }

      await interaction.editReply({ embeds: [embed] });

    } catch (error) {
      this.logger.error('AI auto-stats command failed:', error);
      
      if (interaction.deferred) {
        await interaction.editReply({
          content: '❌ Failed to get auto-setup statistics. Please try again.',
        });
      } else {
        await interaction.reply({
          content: '❌ Failed to get auto-setup statistics. Please try again.',
          ephemeral: true,
        });
      }
    }
  }
}