import { Injectable, Logger } from '@nestjs/common';
import { RedisDatabaseService } from '../../core/database/redis-database.service';



import { TierManagementService } from '../dev-on-demand/services/tier-management.service';
import { 
  ChannelType, 
  PermissionFlagsBits, 
  Guild, 
  CategoryChannel, 
  TextChannel, 
  VoiceChannel,
  EmbedBuilder,
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  PermissionOverwriteOptions
} from 'discord.js';

export interface ChannelStructure {
  name: string;
  type: 'category' | 'text' | 'voice' | 'forum';
  description?: string;
  position?: number;
  parent?: string;
  requiredTier?: string;
  permissions?: Array<{
    roleId: string;
    allow: bigint[];
    deny: bigint[];
  }>;
  children?: ChannelStructure[];
}

export interface CommunityChannelConfig {
  guildId: string;
  structure: ChannelStructure[];
  autoSetup: boolean;
  tierIntegration: boolean;
  customizations: Record<string, any>;
}

@Injectable()
export class CommunityChannelsService {
  private readonly logger = new Logger(CommunityChannelsService.name);

  private readonly defaultChannelStructure: ChannelStructure[] = [
    {
      name: '📋 COMMUNITY HUB',
      type: 'category',
      position: 1,
      children: [
        {
          name: '👋-welcome',
          type: 'text',
          description: 'Welcome new members to the Dev On Demand community',
          requiredTier: 'free'
        },
        {
          name: '📢-announcements',
          type: 'text',
          description: 'Important community updates and announcements',
          requiredTier: 'free'
        },
        {
          name: '💬-general-chat',
          type: 'text',
          description: 'General community discussion and networking',
          requiredTier: 'free'
        },
        {
          name: '❓-help-support',
          type: 'text',
          description: 'Get help with community features and technical questions',
          requiredTier: 'free'
        },
        {
          name: '🎉-member-introductions',
          type: 'text',
          description: 'Introduce yourself to the community',
          requiredTier: 'free'
        }
      ]
    },
    {
      name: '🤖 AI MASTERY',
      type: 'category',
      position: 2,
      children: [
        {
          name: '🧠-ai-fundamentals',
          type: 'text',
          description: 'Learn AI basics and foundational concepts',
          requiredTier: 'free'
        },
        {
          name: '🛠️-ai-tools-discussion',
          type: 'text',
          description: 'Discuss and share AI tools and resources',
          requiredTier: 'ai_explorer'
        },
        {
          name: '⚡-productivity-automation',
          type: 'text',
          description: 'Automate your workflows with AI-powered tools',
          requiredTier: 'ai_explorer'
        },
        {
          name: '🎨-prompt-engineering',
          type: 'text',
          description: 'Master the art of crafting effective AI prompts',
          requiredTier: 'ai_explorer'
        },
        {
          name: '🤝-ai-agent-consultations',
          type: 'text',
          description: 'Access our specialized AI advisors',
          requiredTier: 'ai_explorer'
        },
        {
          name: '🔬-ai-experiments',
          type: 'text',
          description: 'Share your AI experiments and discoveries',
          requiredTier: 'wealth_builder'
        }
      ]
    },
    {
      name: '💰 WEALTH CREATION',
      type: 'category',
      position: 3,
      children: [
        {
          name: '📚-business-fundamentals',
          type: 'text',
          description: 'Learn core business and entrepreneurship concepts',
          requiredTier: 'free'
        },
        {
          name: '💡-startup-strategies',
          type: 'text',
          description: 'Discuss startup ideas and growth strategies',
          requiredTier: 'wealth_builder'
        },
        {
          name: '📈-investment-insights',
          type: 'text',
          description: 'Share investment strategies and market analysis',
          requiredTier: 'wealth_builder'
        },
        {
          name: '💸-passive-income',
          type: 'text',
          description: 'Explore passive income opportunities and strategies',
          requiredTier: 'wealth_builder'
        },
        {
          name: '🏢-business-networking',
          type: 'text',
          description: 'Connect with other entrepreneurs and business owners',
          requiredTier: 'wealth_builder'
        },
        {
          name: '📊-success-stories',
          type: 'text',
          description: 'Share your business wins and learn from others',
          requiredTier: 'wealth_builder'
        }
      ]
    },
    {
      name: '💻 DEV ON DEMAND',
      type: 'category',
      position: 4,
      children: [
        {
          name: '🚀-project-requests',
          type: 'text',
          description: 'Post and browse development project requests',
          requiredTier: 'ai_explorer'
        },
        {
          name: '👨‍💻-developer-showcase',
          type: 'text',
          description: 'Developers showcase their skills and portfolios',
          requiredTier: 'free'
        },
        {
          name: '🔧-technical-discussions',
          type: 'text',
          description: 'Technical discussions and programming help',
          requiredTier: 'free'
        },
        {
          name: '📋-project-collaboration',
          type: 'text',
          description: 'Active project updates and collaboration',
          requiredTier: 'wealth_builder'
        },
        {
          name: '💎-premium-matching',
          type: 'text',
          description: 'Priority developer matching for premium members',
          requiredTier: 'dev_premium'
        },
        {
          name: '🏆-completed-projects',
          type: 'text',
          description: 'Celebrate completed projects and successes',
          requiredTier: 'free'
        }
      ]
    },
    {
      name: '🧘 PERSONAL GROWTH',
      type: 'category',
      position: 5,
      children: [
        {
          name: '🎯-goal-setting',
          type: 'text',
          description: 'Set and track your personal and professional goals',
          requiredTier: 'free'
        },
        {
          name: '💪-mindset-mastery',
          type: 'text',
          description: 'Develop a growth mindset and mental resilience',
          requiredTier: 'ai_explorer'
        },
        {
          name: '⚖️-work-life-balance',
          type: 'text',
          description: 'Discuss strategies for maintaining healthy balance',
          requiredTier: 'ai_explorer'
        },
        {
          name: '📖-learning-resources',
          type: 'text',
          description: 'Share books, courses, and learning materials',
          requiredTier: 'free'
        },
        {
          name: '🤝-accountability-partners',
          type: 'text',
          description: 'Find accountability partners for your journey',
          requiredTier: 'wealth_builder'
        },
        {
          name: '🏃-daily-challenges',
          type: 'text',
          description: 'Participate in personal growth challenges',
          requiredTier: 'ai_explorer'
        }
      ]
    },
    {
      name: '🎪 COMMUNITY EVENTS',
      type: 'category',
      position: 6,
      children: [
        {
          name: '📅-event-announcements',
          type: 'text',
          description: 'Upcoming community events and workshops',
          requiredTier: 'free'
        },
        {
          name: '🎙️-live-sessions',
          type: 'voice',
          description: 'Live community sessions and workshops',
          requiredTier: 'ai_explorer'
        },
        {
          name: '💼-networking-events',
          type: 'voice',
          description: 'Structured networking and mastermind sessions',
          requiredTier: 'wealth_builder'
        },
        {
          name: '🎓-workshops',
          type: 'text',
          description: 'Hands-on workshops and learning sessions',
          requiredTier: 'ai_explorer'
        },
        {
          name: '🏆-competitions',
          type: 'text',
          description: 'Community challenges and competitions',
          requiredTier: 'free'
        }
      ]
    },
    {
      name: '👑 PREMIUM LOUNGE',
      type: 'category',
      position: 7,
      children: [
        {
          name: '💎-vip-chat',
          type: 'text',
          description: 'Exclusive chat for premium members',
          requiredTier: 'dev_premium'
        },
        {
          name: '🤵-concierge-support',
          type: 'text',
          description: 'White-glove support for enterprise members',
          requiredTier: 'enterprise'
        },
        {
          name: '🎯-strategy-sessions',
          type: 'voice',
          description: 'Private strategy and planning sessions',
          requiredTier: 'dev_premium'
        },
        {
          name: '🏛️-enterprise-lounge',
          type: 'text',
          description: 'Exclusive space for enterprise members',
          requiredTier: 'enterprise'
        },
        {
          name: '📞-one-on-one-booking',
          type: 'text',
          description: 'Schedule personal consultations',
          requiredTier: 'dev_premium'
        }
      ]
    }
  ];

  constructor(
    private readonly redisDatabaseService: RedisDatabaseService,
    private readonly tierService: TierManagementService,
  ) {}

  async setupCommunityStructure(guild: Guild): Promise<boolean> {
    try {
      this.logger.log(`Setting up community structure for guild: ${guild.name}`);

      // Initialize default tiers first
      await this.tierService.initializeDefaultTiers(guild.id);

      let createdChannelsCount = 0;

      for (const categoryData of this.defaultChannelStructure) {
        // Create category
        const category = await guild.channels.create({
          name: categoryData.name,
          type: ChannelType.GuildCategory,
          position: categoryData.position,
          permissionOverwrites: await this.buildPermissionOverwrites(guild, categoryData.requiredTier),
        });

        this.logger.log(`Created category: ${category.name}`);

        // Create child channels
        if (categoryData.children) {
          for (const channelData of categoryData.children) {
            const channelType = channelData.type === 'voice' 
              ? ChannelType.GuildVoice 
              : ChannelType.GuildText;

            const channel = await guild.channels.create({
              name: channelData.name,
              type: channelType,
              parent: category,
              topic: channelData.description,
              permissionOverwrites: await this.buildPermissionOverwrites(guild, channelData.requiredTier),
            });

            // Set up channel-specific features
            if (channelData.type === 'text') {
              await this.setupChannelFeatures(channel as TextChannel, channelData);
            }

            createdChannelsCount++;
            this.logger.log(`Created channel: ${channel.name} in ${category.name}`);
          }
        }

        // Add small delay to avoid rate limits
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      this.logger.log(`Successfully created ${createdChannelsCount} channels in ${this.defaultChannelStructure.length} categories`);
      return true;

    } catch (error) {
      this.logger.error(`Failed to set up community structure for guild ${guild.id}:`, error);
      return false;
    }
  }

  async createChannelGuideEmbed(guildId: string): Promise<EmbedBuilder> {
    const embed = new EmbedBuilder()
      .setColor(0x7C3AED)
      .setTitle('🗺️ Community Channel Guide')
      .setDescription('Navigate your Dev On Demand community like a pro!')
      .addFields([
        {
          name: '📋 Community Hub',
          value: 'Start here! Welcome, announcements, and general chat',
          inline: true
        },
        {
          name: '🤖 AI Mastery',
          value: 'Learn AI tools, automation, and get expert guidance',
          inline: true
        },
        {
          name: '💰 Wealth Creation',
          value: 'Business strategies, investments, and networking',
          inline: true
        },
        {
          name: '💻 Dev On Demand',
          value: 'Find developers, collaborate on projects',
          inline: true
        },
        {
          name: '🧘 Personal Growth',
          value: 'Mindset, goals, and self-improvement',
          inline: true
        },
        {
          name: '🎪 Community Events',
          value: 'Workshops, networking, and live sessions',
          inline: true
        }
      ])
      .addFields([
        {
          name: '🎯 Tier-Based Access',
          value: [
            '🆓 **Free:** Community Hub, basics of each category',
            '🚀 **AI Explorer ($29.99):** Full AI Mastery + automation',
            '💎 **Wealth Builder ($49.99):** Business strategies + networking',
            '👑 **Dev Premium ($99.99):** Priority matching + premium features',
            '🏆 **Enterprise ($299.99):** Everything + white-glove service'
          ].join('\n'),
          inline: false
        },
        {
          name: '💡 Pro Tips',
          value: [
            '• Use `/ai-agents` to consult with specialized AI advisors',
            '• Post in `#member-introductions` to get started',
            '• Check `#event-announcements` for upcoming workshops',
            '• Upgrade your tier for access to more channels and features'
          ].join('\n'),
          inline: false
        }
      ])
      .setFooter({ 
        text: 'Need help? Visit #help-support or use /support' 
      });

    return embed;
  }

  async createTierUpgradePrompt(
    currentTier: string,
    requestedChannel: string
  ): Promise<{ embed: EmbedBuilder; components: ActionRowBuilder<ButtonBuilder>[] }> {
    const embed = new EmbedBuilder()
      .setColor(0xFF6B6B)
      .setTitle('🔒 Channel Access Restricted')
      .setDescription(`You need a higher membership tier to access **${requestedChannel}**`)
      .addFields([
        {
          name: '📊 Your Current Tier',
          value: this.formatTierName(currentTier),
          inline: true
        },
        {
          name: '🎯 Required Tier',
          value: this.getRequiredTierForChannel(requestedChannel),
          inline: true
        }
      ]);

    // Add tier comparison
    const tierBenefits = this.getTierUpgradeBenefits(currentTier);
    if (tierBenefits.length > 0) {
      embed.addFields([
        {
          name: '⬆️ Upgrade Benefits',
          value: tierBenefits.join('\n'),
          inline: false
        }
      ]);
    }

    const upgradeButton = new ActionRowBuilder<ButtonBuilder>()
      .addComponents(
        new ButtonBuilder()
          .setLabel('Upgrade Membership')
          .setURL('https://whop.com/dev-on-demand-community')
          .setStyle(ButtonStyle.Link)
          .setEmoji('⬆️'),
        new ButtonBuilder()
          .setCustomId('view_tier_comparison')
          .setLabel('Compare Tiers')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('📊'),
        new ButtonBuilder()
          .setCustomId('free_trial_request')
          .setLabel('Request Free Trial')
          .setStyle(ButtonStyle.Primary)
          .setEmoji('🎁')
      );

    return { embed, components: [upgradeButton] };
  }

  async getChannelsByTier(guildId: string, tierLevel: string): Promise<string[]> {
    const accessibleChannels: string[] = [];

    for (const category of this.defaultChannelStructure) {
      if (category.children) {
        for (const channel of category.children) {
          if (!channel.requiredTier || this.canAccessWithTier(channel.requiredTier, tierLevel)) {
            accessibleChannels.push(channel.name);
          }
        }
      }
    }

    return accessibleChannels;
  }

  async syncChannelPermissions(guild: Guild): Promise<void> {
    try {
      this.logger.log(`Syncing channel permissions for guild: ${guild.name}`);

      // Get all categories and channels
      const categories = guild.channels.cache.filter((ch: any) => ch.type === ChannelType.GuildCategory);
      
      for (const [_, category] of categories) {
        const categoryChannel = category as CategoryChannel;
        
        // Find matching structure
        const structureCategory = this.defaultChannelStructure.find(
          s => s.name.toLowerCase() === categoryChannel.name.toLowerCase()
        );

        if (structureCategory) {
          // Update category permissions
          const categoryPermissions = await this.buildPermissionOverwrites(guild, structureCategory.requiredTier);
          await categoryChannel.permissionOverwrites.set(categoryPermissions);

          // Update child channel permissions
          const childChannels = guild.channels.cache.filter((ch: any) => ch.parentId === categoryChannel.id);
          
          for (const [_, childChannel] of childChannels) {
            const structureChild = structureCategory.children?.find(
              c => c.name.toLowerCase() === childChannel.name.toLowerCase()
            );

            if (structureChild && 'permissionOverwrites' in childChannel) {
              const childPermissions = await this.buildPermissionOverwrites(guild, structureChild.requiredTier);
              await childChannel.permissionOverwrites.set(childPermissions);
            }
          }
        }

        // Small delay to avoid rate limits
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      this.logger.log(`Successfully synced permissions for ${categories.size} categories`);

    } catch (error) {
      this.logger.error(`Failed to sync channel permissions for guild ${guild.id}:`, error);
    }
  }

  private async buildPermissionOverwrites(guild: Guild, requiredTier?: string) {
    const overwrites: any[] = [
      {
        id: guild.roles.everyone.id,
        deny: [PermissionFlagsBits.ViewChannel],
      }
    ];

    if (!requiredTier) {
      // Public channel - allow everyone to view
      overwrites[0].deny = [];
      return overwrites;
    }

    // Add tier-specific role permissions
    const tierRoles = await this.getTierRoles(guild.id, requiredTier);
    
    for (const roleId of tierRoles) {
      const role = guild.roles.cache.get(roleId);
      if (role) {
        overwrites.push({
          id: role.id,
          allow: [
            PermissionFlagsBits.ViewChannel,
            PermissionFlagsBits.SendMessages,
            PermissionFlagsBits.ReadMessageHistory,
            PermissionFlagsBits.Connect, // For voice channels
            PermissionFlagsBits.Speak,   // For voice channels
          ],
          deny: []
        });
      }
    }

    return overwrites;
  }

  private async setupChannelFeatures(channel: TextChannel, channelData: ChannelStructure): Promise<void> {
    // Set up channel-specific features based on channel type
    if (channelData.name.includes('welcome')) {
      // Set up welcome channel features
      await this.setupWelcomeChannel(channel);
    } else if (channelData.name.includes('announcements')) {
      // Set up announcement channel features
      await this.setupAnnouncementChannel(channel);
    } else if (channelData.name.includes('project-requests')) {
      // Set up project request channel features
      await this.setupProjectRequestChannel(channel);
    }
  }

  private async setupWelcomeChannel(channel: TextChannel): Promise<void> {
    // Send welcome embed and set up auto-reactions
    const welcomeEmbed = new EmbedBuilder()
      .setColor(0x00AE86)
      .setTitle('🎉 Welcome to Dev On Demand Community!')
      .setDescription('The ultimate AI-focused Discord community for developers, entrepreneurs, and creators')
      .addFields([
        {
          name: '🚀 What We Offer',
          value: [
            '• **AI Mastery:** Learn to leverage AI for productivity and growth',
            '• **Wealth Creation:** Strategies for making money and building businesses',
            '• **Dev On Demand:** Connect with skilled developers for your projects',
            '• **Personal Growth:** Become the best version of yourself'
          ].join('\n'),
          inline: false
        },
        {
          name: '🎯 Getting Started',
          value: [
            '1. Introduce yourself in <#member-introductions>',
            '2. Explore channels based on your interests',
            '3. Use `/ai-agents` to consult our AI specialists',
            '4. Upgrade your membership for premium features'
          ].join('\n'),
          inline: false
        }
      ])
      .setFooter({ text: 'Ready to transform your life with AI and community?' });

    await channel.send({ embeds: [welcomeEmbed] });
  }

  private async setupAnnouncementChannel(channel: TextChannel): Promise<void> {
    // Pin important announcement about the community
    const announcementEmbed = new EmbedBuilder()
      .setColor(0x7C3AED)
      .setTitle('📢 Community Guidelines & Features')
      .setDescription('Important information about our Dev On Demand community')
      .addFields([
        {
          name: '📋 Community Rules',
          value: [
            '• Be respectful and constructive in all interactions',
            '• No spam, self-promotion without value, or off-topic content',
            '• Help others grow and succeed - we rise together',
            '• Keep discussions relevant to each channel\'s purpose'
          ].join('\n'),
          inline: false
        },
        {
          name: '🎯 Tier System',
          value: 'Different membership tiers unlock different features and channels. Use `/tier-info` to learn more!',
          inline: false
        }
      ]);

    const message = await channel.send({ embeds: [announcementEmbed] });
    await message.pin();
  }

  private async setupProjectRequestChannel(channel: TextChannel): Promise<void> {
    const requestGuideEmbed = new EmbedBuilder()
      .setColor(0x3B82F6)
      .setTitle('🚀 Project Request Guidelines')
      .setDescription('How to post effective development requests')
      .addFields([
        {
          name: '📝 Creating a Request',
          value: [
            '• Use `/dev-request` command for structured requests',
            '• Be specific about your requirements and timeline',
            '• Include your budget range for better matching',
            '• Mention preferred communication methods'
          ].join('\n'),
          inline: false
        },
        {
          name: '🎯 What Makes a Great Request',
          value: [
            '• Clear project goals and success criteria',
            '• Detailed technical requirements',
            '• Realistic timeline and budget',
            '• Contact information and availability'
          ].join('\n'),
          inline: false
        }
      ]);

    await channel.send({ embeds: [requestGuideEmbed] });
  }

  private async getTierRoles(guildId: string, tierLevel: string): Promise<string[]> {
    try {
      const redis = this.redisDatabaseService.redis.getClient();
      const tierRolesKey = `tier_roles:${guildId}:${tierLevel}`;
      const roleIds = await redis.lrange(tierRolesKey, 0, -1);
      return roleIds;
    } catch (error) {
      this.logger.error(`Failed to get tier roles for ${guildId}:${tierLevel}:`, error);
      return [];
    }
  }

  private canAccessWithTier(requiredTier: string, userTier: string): boolean {
    const tierHierarchy = ['free', 'ai_explorer', 'wealth_builder', 'dev_premium', 'enterprise'];
    const requiredIndex = tierHierarchy.indexOf(requiredTier);
    const userIndex = tierHierarchy.indexOf(userTier);
    return userIndex >= requiredIndex;
  }

  private formatTierName(tier: string): string {
    const tierNames = {
      free: '🆓 Free Community',
      ai_explorer: '🚀 AI Explorer',
      wealth_builder: '💎 Wealth Builder',
      dev_premium: '👑 Dev Premium',
      enterprise: '🏆 Enterprise'
    };
    return ((tierNames as any)[tier]) || tier;
  }

  private getRequiredTierForChannel(channelName: string): string {
    // Find the required tier for the requested channel
    for (const category of this.defaultChannelStructure) {
      if (category.children) {
        const channel = category.children.find(c => c.name === channelName);
        if (channel?.requiredTier) {
          return this.formatTierName(channel.requiredTier);
        }
      }
    }
    return 'Unknown';
  }

  private getTierUpgradeBenefits(currentTier: string): string[] {
    const benefits = {
      free: [
        '• Access to AI Mastery channels and tools',
        '• Personal AI assistant consultations',
        '• Productivity automation guides',
        '• 100 AI agent queries per month'
      ],
      ai_explorer: [
        '• Full Wealth Creation category access',
        '• Business strategy consultations',
        '• Investment and passive income resources',
        '• Priority networking opportunities'
      ],
      wealth_builder: [
        '• Dev On Demand premium matching',
        '• Priority project placement',
        '• One-on-one consultations',
        '• Dedicated support channel'
      ],
      dev_premium: [
        '• Enterprise-level features',
        '• White-glove service',
        '• Custom solutions',
        '• Unlimited usage across all features'
      ]
    };

    return benefits[currentTier] || [];
  }
}