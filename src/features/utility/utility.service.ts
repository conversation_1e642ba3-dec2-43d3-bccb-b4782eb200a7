import { Injectable, Logger } from '@nestjs/common';
import { SlashCommand, SlashCommandContext, Context, Options, UserOption } from 'necord';
import { User } from 'discord.js';

class UserInfoDto {
  @UserOption({ 
    name: 'user', 
    description: 'Select a user to view their profile info (leave empty for yourself)', 
    required: false 
  })
  user?: User;
}

@Injectable()
export class UtilityService {
  private readonly logger = new Logger(UtilityService.name);

  @SlashCommand({
    name: 'ping',
    description: '🏓 Check bot response time and latency',
  })
  // @ts-ignore: Necord decorator compatibility issue
  async onPingCommand(@Context() [interaction]: SlashCommandContext) {
    const sent = Date.now();
    await interaction.reply('🏓 Pong!');
    const received = Date.now();
    
    await interaction.editReply(`🏓 Pong! Latency: ${received - sent}ms`);
  }

  @SlashCommand({
    name: 'serverinfo',
    description: '📊 Display detailed information about this Discord server',
  })
  // @ts-ignore: Necord decorator compatibility issue
  async onServerInfoCommand(@Context() [interaction]: SlashCommandContext) {
    if (!interaction.guild) {
      await interaction.reply({
        content: '❌ This command can only be used in a server.',
        ephemeral: true,
      });
      return;
    }

    const guild = interaction.guild;
    
    await interaction.reply({
      content: `📊 **Server Information**\n\n🏷️ Name: **${guild.name}**\n👥 Members: **${guild.memberCount}**\n📅 Created: **${guild.createdAt.toDateString()}**\n👑 Owner: **<@${guild.ownerId}>**`,
      ephemeral: true,
    });
  }

  @SlashCommand({ 
    name: 'userinfo', 
    description: '👤 View detailed profile information for yourself or another user' 
  })
  async onUserInfoCommand(@Context() [interaction]: SlashCommandContext, @Options() options: UserInfoDto) {
    const targetUser = options.user || interaction.user;
    const member = interaction.guild?.members.cache.get(targetUser.id);
    const content = `👤 **${targetUser === interaction.user ? 'Your' : `${targetUser.tag}'s`} Information**\n\n🏷️ Username: **${targetUser.tag}**\n🆔 ID: **${targetUser.id}**\n📅 Created: **${targetUser.createdAt.toDateString()}**${member ? `\n🎯 Joined: **${member.joinedAt?.toDateString()}**\n🎭 Roles: **${member.roles.cache.size - 1}**` : ''}`;
    await interaction.reply({ content, ephemeral: true });
    this.logger.log(`Userinfo: ${targetUser.tag} requested by ${interaction.user.tag}`);
  }

  @SlashCommand({
    name: 'help',
    description: '❓ Show all available bot commands and how to use them',
  })
  // @ts-ignore: Necord decorator compatibility issue
  async onHelpCommand(@Context() [interaction]: SlashCommandContext) {
    await interaction.reply({
      content: `🤖 **EnergeX Bot Help**\n\n**AI Agents:**\n• \`/coach\` - Connect with your personal growth coach\n• \`/intake\` - Complete your intake assessment\n• \`/progress\` - Check your progress and goals\n\n**Utility:**\n• \`/ping\` - Check bot latency\n• \`/serverinfo\` - Server information\n• \`/userinfo\` - User information\n• \`/level\` - Check your level and XP\n• \`/balance\` - Check your coin balance\n\n**More features coming soon!** 🚀`,
      ephemeral: true,
    });
  }
}