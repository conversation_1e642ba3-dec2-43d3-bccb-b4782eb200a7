import { RedisDatabaseService } from '@/core/database/redis-database.service';
import type { GuildSettings } from '@/core/database/types';
import { Injectable, Logger } from '@nestjs/common';
import { EmbedBuilder, GuildMember, PermissionFlagsBits, Role } from 'discord.js';
import { Context, On, Options, RoleOption, SlashCommand, SlashCommandContext, StringOption } from 'necord';

export class AccessTierDto {
  @StringOption({
    name: 'action',
    description: 'Action to perform',
    required: true,
  })
  action!: string;

  @StringOption({
    name: 'name',
    description: 'Tier name',
    required: false,
  })
  name?: string;

  @RoleOption({
    name: 'role',
    description: 'Role for the tier',
    required: false,
  })
  role?: Role;
}

@Injectable()
export class RoleAccessService {
  private readonly logger = new Logger(RoleAccessService.name);

  constructor(
    private readonly redisDatabaseService: RedisDatabaseService,
  ) {}

  @SlashCommand({
    name: 'access-setup',
    description: 'Set up role-based access control tiers',
    defaultMemberPermissions: [PermissionFlagsBits.ManageRoles],
  })
  async onAccessSetupCommand(@Context() [interaction]: SlashCommandContext) {
    if (!interaction.guild) {
      await interaction.reply({
        content: '❌ This command can only be used in a server.',
        ephemeral: true,
      });
      return;
    }

    try {
      let guild = await this.redisDatabaseService.findGuildByDiscordId(interaction.guild.id);

      if (!guild) {
        guild = await this.redisDatabaseService.createGuild({
          discordId: interaction.guild.id,
          name: interaction.guild.name,
          features: {
            roleAccess: {
              enabled: false,
              tiers: [],
              autoAssign: false,
              restrictedChannels: [],
            },
          },
        });
      }

      const embed = new EmbedBuilder()
        .setColor(0x7C3AED)
        .setTitle('🎯 Role-Based Access Control Setup')
        .setDescription('Configure tiered access control for your server')
        .addFields([
          {
            name: '📋 Available Commands',
            value: [
              '`/access-tier create <name> <role>` - Create access tier',
              '`/access-tier delete <name>` - Delete access tier',
              '`/access-channel <channel> <tier>` - Restrict channel to tier',
              '`/access-status` - View current configuration',
            ].join('\n'),
          },
          {
            name: '🏷️ Tier System',
            value: 'Create tiers like "Free", "Premium", "VIP" with different role requirements and channel access levels.',
          },
          {
            name: '🔐 Auto Assignment',
            value: 'Enable automatic role assignment based on user activity, payments, or other criteria.',
          },
        ])
        .setFooter({ text: 'Use the commands above to configure your access control system' });

      await interaction.reply({ embeds: [embed], ephemeral: true });

      // Initialize features if not present
      if (!guild.features?.roleAccess) {
        const updatedFeatures = {
          ...guild.features,
          roleAccess: {
            enabled: false,
            tiers: [],
            restrictedChannels: [],
            autoAssign: false,
            logChannel: null,
          },
        };
        await this.redisDatabaseService.updateGuildFeatures(guild.id, updatedFeatures);
      }
    } catch (error) {
      this.logger.error('Failed to set up access control:', error);
      await interaction.reply({
        content: '❌ Failed to set up access control. Please try again.',
        ephemeral: true,
      });
    }
  }

  @SlashCommand({
    name: 'access-tier',
    description: 'Manage access control tiers',
    defaultMemberPermissions: [PermissionFlagsBits.ManageRoles],
  })
  async onAccessTierCommand(
    @Context() [interaction]: SlashCommandContext,
    @Options() { action, name, role }: AccessTierDto
  ) {
    if (!interaction.guild) {
      await interaction.reply({
        content: '❌ This command can only be used in a server.',
        ephemeral: true,
      });
      return;
    }

    try {
      const guild = await this.redisDatabaseService.findGuildByDiscordId(interaction.guild.id);

      if (!guild) {
        await interaction.reply({
          content: '❌ Please run `/access-setup` first to initialize the system.',
          ephemeral: true,
        });
        return;
      }

      const roleAccess: NonNullable<GuildSettings['roleAccess']> =
        (guild.features?.roleAccess as NonNullable<GuildSettings['roleAccess']>) ||
        { enabled: false, tiers: [], autoAssign: false, restrictedChannels: [] };

      switch (action) {
        case 'create':
          if (!name || !role) {
            await interaction.reply({
              content: '❌ Both tier name and role are required for creating a tier.',
              ephemeral: true,
            });
            return;
          }

          // Check if tier already exists
          const existingTiers = roleAccess.tiers || [];
          if (existingTiers.some((tier: { name: string }) => tier.name.toLowerCase() === name.toLowerCase())) {
            await interaction.reply({
              content: `❌ A tier named "${name}" already exists.`,
              ephemeral: true,
            });
            return;
          }

          roleAccess.tiers.push({
            id: `tier_${Date.now()}`,
            name,
            roleIds: [role.id],
            permissions: [],
            priority: roleAccess.tiers.length + 1,
          });

          const updatedFeatures = { ...guild.features, roleAccess };
          await this.redisDatabaseService.updateGuildFeatures(guild.id, updatedFeatures);

          await interaction.reply({
            content: `✅ **Access tier created successfully!**\n\n🏷️ **Tier:** ${name}\n👥 **Role:** ${role.name}\n\nUsers with the ${role.name} role now belong to the "${name}" tier.`,
            ephemeral: true,
          });
          break;

        case 'delete':
          if (!name) {
            await interaction.reply({
              content: '❌ Tier name is required for deletion.',
              ephemeral: true,
            });
            return;
          }

          const tierIndex = roleAccess.tiers.findIndex((tier: { name: string }) => tier.name.toLowerCase() === name.toLowerCase());
          if (tierIndex === -1) {
            await interaction.reply({
              content: `❌ No tier named "${name}" found.`,
              ephemeral: true,
            });
            return;
          }

          roleAccess.tiers.splice(tierIndex, 1);
          const deleteUpdatedFeatures = { ...guild.features, roleAccess };
          await this.redisDatabaseService.updateGuildFeatures(guild.id, deleteUpdatedFeatures);

          await interaction.reply({
            content: `✅ **Access tier "${name}" deleted successfully!**`,
            ephemeral: true,
          });
          break;

        case 'list':
          if (roleAccess.tiers.length === 0) {
            await interaction.reply({
              content: '📋 **No access tiers configured.**\n\nUse `/access-tier create` to create your first tier.',
              ephemeral: true,
            });
            return;
          }

          const embed = new EmbedBuilder()
            .setColor(0x7C3AED)
            .setTitle('🎯 Access Control Tiers')
            .setDescription(`Total tiers: **${roleAccess.tiers.length}**`);

          roleAccess.tiers.forEach((tier: { name: string; roleIds: string[]; priority: number }, index: number) => {
            embed.addFields([{
              name: `${index + 1}. ${tier.name}`,
              value: `**Role:** <@&${tier.roleIds[0]}>\n**Priority:** ${tier.priority}`,
              inline: true,
            }]);
          });

          await interaction.reply({ embeds: [embed], ephemeral: true });
          break;
      }
    } catch (error) {
      this.logger.error('Failed to manage access tier:', error);
      await interaction.reply({
        content: '❌ Failed to manage access tier. Please try again.',
        ephemeral: true,
      });
    }
  }

  @SlashCommand({
    name: 'access-status',
    description: 'View current access control configuration',
    defaultMemberPermissions: [PermissionFlagsBits.ManageRoles],
  })
  async onAccessStatusCommand(@Context() [interaction]: SlashCommandContext) {
    if (!interaction.guild) {
      await interaction.reply({
        content: '❌ This command can only be used in a server.',
        ephemeral: true,
      });
      return;
    }

    try {
      const guild = await this.redisDatabaseService.findGuildByDiscordId(interaction.guild.id);

      const roleAccess = guild?.features?.roleAccess || { enabled: false, tiers: [] };

      const embed = new EmbedBuilder()
        .setColor(roleAccess.enabled ? 0x10B981 : 0x6B7280)
        .setTitle('🎯 Access Control Status')
        .addFields([
          {
            name: '🔘 System Status',
            value: roleAccess.enabled ? '✅ Enabled' : '❌ Disabled',
            inline: true,
          },
          {
            name: '🏷️ Total Tiers',
            value: roleAccess.tiers?.length?.toString() || '0',
            inline: true,
          },
          {
            name: '🔐 Auto Assignment',
            value: roleAccess.autoAssign ? '✅ Enabled' : '❌ Disabled',
            inline: true,
          },
        ]);

      if (roleAccess.tiers && roleAccess.tiers.length > 0) {
        const tierList = roleAccess.tiers
          .map((tier: { name: string; roleIds: string[] }) => `• **${tier.name}** - <@&${tier.roleIds[0]}>`)
          .join('\n');
        
        embed.addFields([{
          name: '📋 Configured Tiers',
          value: tierList,
        }]);
      }

      if (roleAccess.restrictedChannels && roleAccess.restrictedChannels.length > 0) {
        embed.addFields([{
          name: '🔒 Restricted Channels',
          value: `${roleAccess.restrictedChannels.length} channels with tier restrictions`,
        }]);
      }

      embed.setFooter({ 
        text: roleAccess.enabled 
          ? 'System is active and enforcing access controls'
          : 'Use /access-setup to configure and enable the system'
      });

      await interaction.reply({ embeds: [embed], ephemeral: true });
    } catch (error) {
      this.logger.error('Failed to get access status:', error);
      await interaction.reply({
        content: '❌ Failed to get access status. Please try again.',
        ephemeral: true,
      });
    }
  }

  @On('guildMemberUpdate')
  async handleMemberUpdate(@Context() [oldMember, newMember]: [GuildMember, GuildMember]) {
    try {
      // Check if roles changed
      if (oldMember.roles.cache.size === newMember.roles.cache.size &&
          oldMember.roles.cache.every(role => newMember.roles.cache.has(role.id))) {
        return; // No role changes
      }

      const guild = await this.redisDatabaseService.findGuildByDiscordId(newMember.guild.id);

      const roleAccess = guild?.features?.roleAccess;
      if (!roleAccess?.enabled || !roleAccess.tiers) return;

      // Determine new tier based on roles
      const userTiers = roleAccess.tiers.filter((tier: { roleIds: string[]; name: string }) =>
        tier.roleIds.some((roleId: string) => newMember.roles.cache.has(roleId))
      );

      if (userTiers.length > 0) {
        // Log tier change if configured
        if (roleAccess.logChannel) {
          const logChannel = newMember.guild.channels.cache.get(roleAccess.logChannel);
          if (logChannel?.isTextBased()) {
            const tierNames = userTiers.map((tier: { name: string }) => tier.name).join(', ');
            await logChannel.send({
              content: `🎯 **Access Tier Update**\n👤 ${newMember.user.tag} now belongs to tier(s): **${tierNames}**`,
            });
          }
        }

        this.logger.log(`User ${newMember.user.tag} tier updated in ${newMember.guild.name}: ${userTiers.map((t: any) => t.name).join(', ')}`);
      }
    } catch (error) {
      this.logger.error('Failed to handle member update for access control:', error);
    }
  }

  async getUserTier(guildId: string, userId: string): Promise<any[]> {
    try {
      const guild = await this.redisDatabaseService.findGuildByDiscordId(guildId);

      const roleAccess = guild?.features?.roleAccess;
      if (!roleAccess?.enabled || !roleAccess.tiers) return [];

      const discordGuild = await guild; // Would need Discord client here
      const member = await discordGuild; // Would fetch member

      return roleAccess.tiers.filter((tier: { name: string }) =>
        // tier.roleIds.some(roleId => member.roles.cache.has(roleId)) // Would check roles
        false // Placeholder
      );
    } catch (error) {
      this.logger.error('Failed to get user tier:', error);
      return [];
    }
  }

  async hasAccess(guildId: string, userId: string, requiredTier: string): Promise<boolean> {
    try {
      const userTiers = await this.getUserTier(guildId, userId);
      return userTiers.some(tier => tier.name === requiredTier);
    } catch (error) {
      this.logger.error('Failed to check access:', error);
      return false;
    }
  }
}