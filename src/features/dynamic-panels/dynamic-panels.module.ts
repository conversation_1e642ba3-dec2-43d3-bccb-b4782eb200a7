import { Module } from '@nestjs/common';
import { CoreModule } from '../../core/core.module';
import { AgentsModule } from '../../core/agents/agents.module';
import { PanelConfigService } from './services/panel-config.service';
import { PanelOrchestratorService } from './services/panel-orchestrator.service';
import { PanelInteractionHandler } from './handlers/panel-interaction.handler';
import { DynamicPanelsListener } from './listeners/dynamic-panels.listener';

@Module({
  imports: [CoreModule, AgentsModule],
  providers: [
    PanelConfigService,
    PanelOrchestratorService,
    PanelInteractionHandler,
    DynamicPanelsListener
  ],
  exports: [
    PanelConfigService,
    PanelOrchestratorService
  ]
})
export class DynamicPanelsModule {}