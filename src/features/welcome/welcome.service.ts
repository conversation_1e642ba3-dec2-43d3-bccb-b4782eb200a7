import { Guild } from '@/core/database/schema';
import { Injectable, Logger } from '@nestjs/common';
import { EmbedBuilder, GuildMember, TextChannel } from 'discord.js';
import { Context, On } from 'necord';
import { RedisDatabaseService } from '../../core/database/redis-database.service';

@Injectable()
export class WelcomeService {
  private readonly logger = new Logger(WelcomeService.name);

  constructor(
    private readonly redisDb: RedisDatabaseService,
  ) {}

  @On('guildMemberAdd')
  async handleMemberJoin(@Context() [member]: [GuildMember]) {
    try {
      // Get guild configuration
      const guild = await this.redisDb.findGuildByDiscordId(member.guild.id);

      if (!guild || !guild.welcomeEnabled) {
        return;
      }

      // Create or update user record
      await this.createOrUpdateUser(member);

      // Send welcome message
      await this.sendWelcomeMessage(member, guild);

      // Assign welcome roles if configured
      await this.assignWelcomeRoles(member, guild);

      this.logger.log(`Welcome process completed for ${member.user.tag} in ${member.guild.name}`);
    } catch (error) {
      this.logger.error(`Failed to handle member join for ${member.user.tag}:`, error);
    }
  }

  private async createOrUpdateUser(member: GuildMember) {
    try {
      let user = await this.redisDb.findUserByDiscordId(member.user.id);

      if (!user) {
        await this.redisDb.createUser({
          discordId: member.user.id,
          username: member.user.username,
          discriminator: member.user.discriminator || '0000',
          avatarUrl: member.user.displayAvatarURL(),
          isActive: true,
        });
      } else {
        await this.redisDb.updateUser(user.id, {
          username: member.user.username,
          discriminator: member.user.discriminator || '0000',
          avatarUrl: member.user.displayAvatarURL(),
          isActive: true,
        });
      }
    } catch (error) {
      this.logger.error(`Failed to create/update user ${member.user.tag}:`, error);
    }
  }

  private async sendWelcomeMessage(member: GuildMember, guild: Guild) {
    try {
      const welcomeChannelId = guild.welcomeChannelId;
      if (!welcomeChannelId) return;

      const channel = member.guild.channels.cache.get(welcomeChannelId) as TextChannel;
      if (!channel) return;

      const embed = new EmbedBuilder()
        .setTitle(`Welcome to ${member.guild.name}! 🎉`)
        .setDescription(this.getWelcomeMessage(member, guild))
        .setColor(0x00AE86)
        .setThumbnail(member.user.displayAvatarURL())
        .setTimestamp()
        .setFooter({
          text: `Member #${member.guild.memberCount}`,
          ...(member.guild.iconURL() ? { iconURL: member.guild.iconURL()! } : {}),
        });

      await channel.send({ embeds: [embed] });
    } catch (error) {
      this.logger.error(`Failed to send welcome message for ${member.user.tag}:`, error);
    }
  }

  private getWelcomeMessage(member: GuildMember, guild: Guild): string {
    const customMessage = guild.welcomeMessage;
    
    if (customMessage) {
      return customMessage
        .replace('{user}', `<@${member.user.id}>`)
        .replace('{guild}', member.guild.name)
        .replace('{memberCount}', member.guild.memberCount.toString());
    }

    return `Hey <@${member.user.id}>! Welcome to **${member.guild.name}**! 🌟

We're excited to have you join our community of growth-focused individuals. Here's how to get started:

🎯 **Get Started:**
• Use \`/intake\` to complete your personalized assessment
• Use \`/coach\` to connect with your personal growth coach
• Check out our community channels and introduce yourself

💬 **Need Help?**
Feel free to ask questions in our community channels or reach out to our moderators.

Let's grow together! 🚀`;
  }

  private async assignWelcomeRoles(member: GuildMember, guild: Guild) {
    try {
      const welcomeRoles = guild.welcomeRoles?.roles?.map((role: any) => role.id) ?? null;
      if (!welcomeRoles || welcomeRoles.length === 0) return;

      const rolesToAdd = [];
      
      for (const roleId of welcomeRoles) {
        const role = member.guild.roles.cache.get(roleId);
        if (role && !member.roles.cache.has(roleId)) {
          rolesToAdd.push(role);
        }
      }

      if (rolesToAdd.length > 0) {
        await member.roles.add(rolesToAdd);
        this.logger.log(`Assigned welcome roles to ${member.user.tag}: ${rolesToAdd.map((r: any) => r.name).join(', ')}`);
      }
    } catch (error) {
      this.logger.error(`Failed to assign welcome roles to ${member.user.tag}:`, error);
    }
  }

  async updateWelcomeConfig(
    guildId: string,
    config: {
      enabled?: boolean;
      channelId?: string;
      message?: string;
      roles?: string[];
    }
  ) {
    try {
      const guild = await this.redisDb.findGuildByDiscordId(guildId);

      if (!guild) {
        throw new Error('Guild not found');
      }

      const updateData: Partial<Guild> = {};
      if (config.enabled !== undefined) updateData.welcomeEnabled = config.enabled;
      if (config.channelId) updateData.welcomeChannelId = config.channelId;
      if (config.message) updateData.welcomeMessage = config.message;
      if (config.roles) updateData.welcomeRoles = { enabled: true, roles: config.roles.map((roleId: any) => ({ id: roleId, name: '', automatic: true })) };

      await this.redisDb.updateGuild(guild.id, updateData);
      
      // Get updated guild for return value
      const updatedGuild = await this.redisDb.findGuildByDiscordId(guildId);
      
      return {
        message: 'Welcome configuration updated successfully',
        config: {
          enabled: updatedGuild?.welcomeEnabled ?? false,
          channelId: updatedGuild?.welcomeChannelId ?? null,
          message: updatedGuild?.welcomeMessage ?? null,
          roles: updatedGuild?.welcomeRoles ?? null,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to update welcome config for guild ${guildId}:`, error);
      throw error;
    }
  }

  async getWelcomeConfig(guildId: string) {
    try {
      const guild = await this.redisDb.findGuildByDiscordId(guildId);

      if (!guild) {
        return {
          enabled: true, // Enable by default
          channelId: null,
          message: null,
          roles: [],
        };
      }

      return {
        enabled: guild.welcomeEnabled !== false, // Default to true unless explicitly disabled
        channelId: guild.welcomeChannelId ?? null,
        message: guild.welcomeMessage ?? null,
        roles: guild.welcomeRoles?.roles ?? [],
      };
    } catch (error) {
      this.logger.error(`Failed to get welcome config for guild ${guildId}:`, error);
      throw error;
    }
  }
}