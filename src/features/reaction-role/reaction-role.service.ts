import { RedisDatabaseService } from '@/core/database';
import { GuildFeatures } from '@/core/database/schema';
import { Injectable, Logger } from '@nestjs/common';
import { MessageReaction, User } from 'discord.js';
import { Context, On } from 'necord';

@Injectable()
export class ReactionRoleService {
  private readonly logger = new Logger(ReactionRoleService.name);

  constructor(
    private readonly redisDatabaseService: RedisDatabaseService,
  ) {}

  @On('messageReactionAdd')
  async handleReactionAdd(@Context() [reaction, user]: [MessageReaction, User]) {
    if (user.bot) return;

    try {
      const guild = reaction.message.guild;
      if (!guild) return;

      const guildData = await this.redisDatabaseService.findGuildByDiscordId(guild.id);

      if (!guildData?.features?.['reaction-role']?.enabled) return;

      const reactionRoles = guildData.features['reaction-role'].roles || [];
      const matchingRule = reactionRoles.find(rule => 
        rule.messageId === reaction.message.id &&
        rule.channelId === reaction.message.channelId &&
        rule.emoji === reaction.emoji.toString()
      );

      if (!matchingRule) return;

      const member = guild.members.cache.get(user.id);
      if (!member) return;

      const role = guild.roles.cache.get(matchingRule.roleId);
      if (!role) {
        this.logger.warn(`Role ${matchingRule.roleId} not found in guild ${guild.id}`);
        return;
      }

      if (!member.roles.cache.has(role.id)) {
        await member.roles.add(role);
        this.logger.log(`Added role ${role.name} to ${user.tag} in guild ${guild.name}`);
      }
    } catch (error) {
      this.logger.error('Failed to handle reaction role add:', error);
    }
  }

  @On('messageReactionRemove')
  async handleReactionRemove(@Context() [reaction, user]: [MessageReaction, User]) {
    if (user.bot) return;

    try {
      const guild = reaction.message.guild;
      if (!guild) return;

      const guildData = await this.redisDatabaseService.findGuildByDiscordId(guild.id);

      if (!guildData?.features?.['reaction-role']?.enabled) return;

      const reactionRoles = guildData.features['reaction-role'].roles || [];
      const matchingRule = reactionRoles.find(rule => 
        rule.messageId === reaction.message.id &&
        rule.channelId === reaction.message.channelId &&
        rule.emoji === reaction.emoji.toString()
      );

      if (!matchingRule) return;

      const member = guild.members.cache.get(user.id);
      if (!member) return;

      const role = guild.roles.cache.get(matchingRule.roleId);
      if (!role) return;

      if (member.roles.cache.has(role.id)) {
        await member.roles.remove(role);
        this.logger.log(`Removed role ${role.name} from ${user.tag} in guild ${guild.name}`);
      }
    } catch (error) {
      this.logger.error('Failed to handle reaction role remove:', error);
    }
  }

  async updateReactionRoleConfig(
    guildId: string,
    config: {
      enabled?: boolean;
      roles?: Array<{
        messageId: string;
        channelId: string;
        emoji: string;
        roleId: string;
      }>;
    }
  ) {
    try {
      let guild = await this.redisDatabaseService.findGuildByDiscordId(guildId);

      const reactionRoleConfig = {
        enabled: config.enabled !== undefined ? config.enabled : guild?.features?.['reaction-role']?.enabled || false,
        roles: config.roles || guild?.features?.['reaction-role']?.roles || [],
      };

      const updatedFeatures: GuildFeatures = {
        ...guild?.features,
        'reaction-role': reactionRoleConfig,
      };

      if (!guild) {
        throw new Error('Guild not found. Please ensure the guild is registered first.');
      }
      
      await this.redisDatabaseService.updateGuild(guild.id, { features: updatedFeatures });

      return {
        message: 'Reaction role configuration updated successfully',
        config: reactionRoleConfig,
      };
    } catch (error) {
      this.logger.error(`Failed to update reaction role config for guild ${guildId}:`, error);
      throw error;
    }
  }

  async getReactionRoleConfig(guildId: string) {
    try {
      const guild = await this.redisDatabaseService.findGuildByDiscordId(guildId);

      return {
        enabled: guild?.features?.['reaction-role']?.enabled || false,
        roles: guild?.features?.['reaction-role']?.roles || [],
      };
    } catch (error) {
      this.logger.error(`Failed to get reaction role config for guild ${guildId}:`, error);
      throw error;
    }
  }

  async addReactionRole(
    guildId: string,
    rule: {
      messageId: string;
      channelId: string;
      emoji: string;
      roleId: string;
    }
  ) {
    try {
      const currentConfig = await this.getReactionRoleConfig(guildId);
      const existingRoles = currentConfig.roles.filter(
        r => !(r.messageId === rule.messageId && r.emoji === rule.emoji)
      );

      await this.updateReactionRoleConfig(guildId, {
        enabled: currentConfig.enabled,
        roles: [...existingRoles, rule],
      });

      return {
        message: 'Reaction role rule added successfully',
        rule,
      };
    } catch (error) {
      this.logger.error(`Failed to add reaction role rule for guild ${guildId}:`, error);
      throw error;
    }
  }

  async removeReactionRole(
    guildId: string,
    messageId: string,
    emoji: string
  ) {
    try {
      const currentConfig = await this.getReactionRoleConfig(guildId);
      const updatedRoles = currentConfig.roles.filter(
        r => !(r.messageId === messageId && r.emoji === emoji)
      );

      await this.updateReactionRoleConfig(guildId, {
        enabled: currentConfig.enabled,
        roles: updatedRoles,
      });

      return {
        message: 'Reaction role rule removed successfully',
      };
    } catch (error) {
      this.logger.error(`Failed to remove reaction role rule for guild ${guildId}:`, error);
      throw error;
    }
  }
}