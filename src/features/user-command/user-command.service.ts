import { GuildFeatures } from '@/core/database/entities/guild.entity';
import { RedisDatabaseService } from '@/core/database/redis-database.service';
import type { GuildSettings } from '@/core/database/types';
import { Injectable, Logger } from '@nestjs/common';
import { Message } from 'discord.js';
import { Context, On } from 'necord';

interface UserCommand {
  name: string;
  response: string;
  description?: string;
  permissions?: string[];
  cooldown?: number;
  enabled: boolean;
}

@Injectable()
export class UserCommandService {
  private readonly logger = new Logger(UserCommandService.name);
  private readonly cooldowns = new Map<string, Map<string, number>>();

  constructor(
    private readonly redisDatabaseService: RedisDatabaseService,
  ) {}

  @On('messageCreate')
  async handleMessage(@Context() [message]: [Message]) {
    if (message.author.bot || !message.guild) return;

    try {
      const guildData = await this.redisDatabaseService.getGuild(message.guild.id);

      if (!guildData?.features?.userCommand?.enabled) return;

      const prefix = guildData.features.userCommand?.prefix || '!';
      const allowedChannels = guildData.features.userCommand?.allowedChannels || [];

      // Check if command starts with prefix
      if (!message.content.startsWith(prefix)) return;

      // Check if channel is allowed (if specific channels are configured)
      if (allowedChannels.length > 0 && !allowedChannels.includes(message.channel.id)) return;

      const args = message.content.slice(prefix.length).trim().split(/ +/);
      const commandName = args.shift()?.toLowerCase();

      if (!commandName) return;

      const commands: NonNullable<NonNullable<GuildSettings['userCommand']>['commands']> =
        guildData.features.userCommand?.commands || [];
      const command = commands.find((cmd: UserCommand) => cmd.name.toLowerCase() === commandName && cmd.enabled);

      if (!command) return;

      // Check cooldown
      if (command.cooldown && this.isOnCooldown(message.author.id, commandName, command.cooldown)) {
        return;
      }

      // Check permissions
      if (command.permissions && command.permissions.length > 0) {
        const member = message.guild.members.cache.get(message.author.id);
        if (!member) return;

        const hasPermission = command.permissions.some((permission: string) => {
          if (permission === 'ADMINISTRATOR') {
            return member.permissions.has('Administrator');
          }
          if (permission === 'MANAGE_GUILD') {
            return member.permissions.has('ManageGuild');
          }
          if (permission === 'MANAGE_MESSAGES') {
            return member.permissions.has('ManageMessages');
          }
          // Check for role-based permissions
          return member.roles.cache.some(role => role.name.toLowerCase() === permission.toLowerCase());
        });

        if (!hasPermission) {
          await message.reply('❌ You do not have permission to use this command.');
          return;
        }
      }

      // Process command response with variables
      let response = command.response;
      response = response.replace(/\{user\}/g, `<@${message.author.id}>`);
      response = response.replace(/\{user\.mention\}/g, `<@${message.author.id}>`);
      response = response.replace(/\{user\.tag\}/g, message.author.tag);
      response = response.replace(/\{user\.username\}/g, message.author.username);
      response = response.replace(/\{guild\.name\}/g, message.guild.name);
      response = response.replace(
        /\{channel\.name\}/g,
        (message.channel && 'name' in message.channel && message.channel.name)
          ? String((message.channel as any).name)
          : 'DM'
      );
      response = response.replace(/\{args\}/g, args.join(' '));

      await message.reply(response);

      // Set cooldown
      if (command.cooldown) {
        this.setCooldown(message.author.id, commandName);
      }

      this.logger.log(`User command '${commandName}' executed by ${message.author.tag} in guild ${message.guild.name}`);
    } catch (error) {
      this.logger.error('Failed to handle user command:', error);
    }
  }

  private isOnCooldown(userId: string, commandName: string, cooldownTime: number): boolean {
    const userCooldowns = this.cooldowns.get(userId) || new Map();
    const lastUsed = userCooldowns.get(commandName);

    if (!lastUsed) return false;

    const timePassed = Date.now() - lastUsed;
    return timePassed < cooldownTime * 1000;
  }

  private setCooldown(userId: string, commandName: string): void {
    const userCooldowns = this.cooldowns.get(userId) || new Map();
    userCooldowns.set(commandName, Date.now());
    this.cooldowns.set(userId, userCooldowns);
  }

  async updateUserCommandConfig(
    guildId: string,
    config: {
      enabled?: boolean;
      prefix?: string;
      allowedChannels?: string[];
      commands?: UserCommand[];
    }
  ) {
    try {
      let guild = await this.redisDatabaseService.getGuild(guildId);

      const userCommandConfig = {
        enabled: config.enabled !== undefined ? config.enabled : guild?.features?.userCommand?.enabled || false,
        prefix: config.prefix || guild?.features?.userCommand?.prefix || '!',
        allowedChannels: config.allowedChannels || guild?.features?.userCommand?.allowedChannels || [],
        commands: config.commands || guild?.features?.userCommand?.commands || [],
      };

      const updatedFeatures: GuildFeatures = {
        ...guild?.features,
        userCommand: userCommandConfig,
      };

      if (!guild) {
        throw new Error('Guild not found. Please ensure the guild is registered first.');
      }
      
      await this.redisDatabaseService.updateGuild(guildId, { features: updatedFeatures });

      return {
        message: 'User command configuration updated successfully',
        config: userCommandConfig,
      };
    } catch (error) {
      this.logger.error(`Failed to update user command config for guild ${guildId}:`, error);
      throw error;
    }
  }

  async getUserCommandConfig(guildId: string) {
    try {
      const guild = await this.redisDatabaseService.getGuild(guildId);

      return {
        enabled: guild?.features?.userCommand?.enabled || false,
        prefix: guild?.features?.userCommand?.prefix || '!',
        allowedChannels: guild?.features?.userCommand?.allowedChannels || [],
        commands: guild?.features?.userCommand?.commands || [],
      };
    } catch (error) {
      this.logger.error(`Failed to get user command config for guild ${guildId}:`, error);
      throw error;
    }
  }

  async addUserCommand(
    guildId: string,
    command: UserCommand
  ) {
    try {
      const currentConfig = await this.getUserCommandConfig(guildId);
      const existingCommands = currentConfig.commands.filter((cmd: UserCommand) => cmd.name.toLowerCase() !== command.name.toLowerCase());

      await this.updateUserCommandConfig(guildId, {
        ...currentConfig,
        commands: [...existingCommands, command],
      });

      return {
        message: 'User command added successfully',
        command,
      };
    } catch (error) {
      this.logger.error(`Failed to add user command for guild ${guildId}:`, error);
      throw error;
    }
  }

  async removeUserCommand(
    guildId: string,
    commandName: string
  ) {
    try {
      const currentConfig = await this.getUserCommandConfig(guildId);
      const updatedCommands = currentConfig.commands.filter((cmd: UserCommand) => cmd.name.toLowerCase() !== commandName.toLowerCase());

      await this.updateUserCommandConfig(guildId, {
        ...currentConfig,
        commands: updatedCommands,
      });

      return {
        message: 'User command removed successfully',
      };
    } catch (error) {
      this.logger.error(`Failed to remove user command for guild ${guildId}:`, error);
      throw error;
    }
  }

  async toggleUserCommand(
    guildId: string,
    commandName: string,
    enabled: boolean
  ) {
    try {
      const currentConfig = await this.getUserCommandConfig(guildId);
      const updatedCommands = currentConfig.commands.map((cmd: UserCommand) => 
        cmd.name.toLowerCase() === commandName.toLowerCase() 
          ? { ...cmd, enabled }
          : cmd
      );

      await this.updateUserCommandConfig(guildId, {
        ...currentConfig,
        commands: updatedCommands,
      });

      return {
        message: `User command ${enabled ? 'enabled' : 'disabled'} successfully`,
      };
    } catch (error) {
      this.logger.error(`Failed to toggle user command for guild ${guildId}:`, error);
      throw error;
    }
  }
}