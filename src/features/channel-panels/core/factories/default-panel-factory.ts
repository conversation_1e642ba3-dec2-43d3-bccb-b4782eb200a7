/**
 * Default Panel Factory
 * 
 * Creates panel instances with proper dependency injection and clean separation.
 * <PERSON>les all supported panel types through a unified interface.
 */

import { Injectable, Logger } from '@nestjs/common';
import { 
  ButtonInteraction, 
  StringSelectMenuInteraction,
  EmbedBuilder,
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle
} from 'discord.js';
import {
  IPanelFactory,
  IPanelInstance,
  PanelConfiguration,
  UserContext,
  PanelRenderData,
  InteractionResult,
  IStateManager,
  IContentProvider
} from '../interfaces/panel-contracts.interface';

/**
 * Default implementation of IPanelInstance
 */
class DefaultPanelInstance implements IPanelInstance {
  private readonly logger = new Logger(`${DefaultPanelInstance.name}:${this.configuration.panelType}`);

  constructor(
    public readonly configuration: PanelConfiguration,
    public readonly supportedActions: any[],
    private readonly stateManager: IStateManager,
    private readonly contentProviders: Map<string, IContentProvider>
  ) {}

  async generateInitialContent(userContext: UserContext): Promise<PanelRenderData> {
    try {
      this.logger.debug(`Generating initial content for ${this.configuration.panelType} panel`);

      // Get panel-specific content based on type
      const content = await this.getPanelTypeContent(userContext);
      return content;

    } catch (error) {
      this.logger.error('Failed to generate initial content:', error);
      return this.createErrorContent('Failed to load panel content');
    }
  }

  async processInteraction(
    interaction: ButtonInteraction | StringSelectMenuInteraction,
    userContext: UserContext
  ): Promise<InteractionResult> {
    try {
      this.logger.debug(`Processing interaction ${interaction.customId} for ${this.configuration.panelType}`);

      // This would typically delegate to action handlers
      // For now, return a simple acknowledgment
      return {
        success: true,
        renderData: {
          embeds: [],
          components: [],
          content: '✅ Interaction processed successfully!'
        }
      };

    } catch (error) {
      this.logger.error('Failed to process interaction:', error);
      return {
        success: false,
        errorMessage: 'Failed to process interaction'
      };
    }
  }

  async refreshContent(userContext: UserContext, currentState: any): Promise<PanelRenderData> {
    try {
      this.logger.debug(`Refreshing content for ${this.configuration.panelType} panel`);

      // Generate fresh content based on current state
      const content = await this.getPanelTypeContent(userContext);
      return content;

    } catch (error) {
      this.logger.error('Failed to refresh content:', error);
      return this.createErrorContent('Failed to refresh panel content');
    }
  }

  async destroy(): Promise<void> {
    try {
      this.logger.debug(`Destroying ${this.configuration.panelType} panel instance`);
      
      // Clean up resources, cancel timers, etc.
      // This would be implemented based on specific panel needs

    } catch (error) {
      this.logger.error('Failed to destroy panel instance:', error);
    }
  }

  private async getPanelTypeContent(userContext: UserContext): Promise<PanelRenderData> {
    const { panelType } = this.configuration;

    switch (panelType) {
      case 'announcement':
        return this.generateAnnouncementContent(userContext);
      
      case 'community':
        return this.generateCommunityContent(userContext);
      
      case 'ai-coding':
        return this.generateAICodingContent(userContext);
      
      case 'premium':
        return this.generatePremiumContent(userContext);
      
      case 'goal-tracking':
        return this.generateGoalTrackingContent(userContext);
      
      default:
        return this.createErrorContent(`Unknown panel type: ${panelType}`);
    }
  }

  private async generateAnnouncementContent(userContext: UserContext): Promise<PanelRenderData> {

    const embed = new EmbedBuilder()
      .setTitle('📢 Announcement Center')
      .setDescription('Stay updated with the latest news and announcements from our community.')
      .addFields(
        { name: '🔔 Subscribe', value: 'Get notified about new announcements', inline: true },
        { name: '📋 History', value: 'View recent announcements you may have missed', inline: true },
        { name: '⚙️ Settings', value: 'Configure your notification preferences', inline: true }
      )
      .setColor('#3b82f6')
      .setTimestamp();

    const row = new ActionRowBuilder<ButtonBuilder>()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('announcement_subscribe')
          .setLabel('Subscribe')
          .setStyle(ButtonStyle.Success)
          .setEmoji('🔔'),
        new ButtonBuilder()
          .setCustomId('announcement_view_history')
          .setLabel('View History')
          .setStyle(ButtonStyle.Primary)
          .setEmoji('📋'),
        new ButtonBuilder()
          .setCustomId('announcement_configure_settings')
          .setLabel('Settings')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('⚙️'),
        new ButtonBuilder()
          .setCustomId('announcement_get_help')
          .setLabel('Help')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('❓')
      );

    return { embeds: [embed], components: [row] };
  }

  private async generateCommunityContent(userContext: UserContext): Promise<PanelRenderData> {

    const embed = new EmbedBuilder()
      .setTitle('🏘️ Community Hub')
      .setDescription('Connect with fellow community members and stay engaged with our activities.')
      .addFields(
        { name: '📋 Guidelines', value: 'Learn about our community standards', inline: true },
        { name: '🎉 Events', value: 'Discover upcoming community events', inline: true },
        { name: '🏆 Leaderboard', value: 'See top community contributors', inline: true },
        { name: '💬 Feedback', value: 'Share your thoughts and suggestions', inline: true }
      )
      .setColor('#10b981')
      .setTimestamp();

    const row = new ActionRowBuilder<ButtonBuilder>()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('community_view_guidelines')
          .setLabel('Guidelines')
          .setStyle(ButtonStyle.Primary)
          .setEmoji('📋'),
        new ButtonBuilder()
          .setCustomId('community_browse_events')
          .setLabel('Events')
          .setStyle(ButtonStyle.Success)
          .setEmoji('🎉'),
        new ButtonBuilder()
          .setCustomId('community_view_leaderboard')
          .setLabel('Leaderboard')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('🏆'),
        new ButtonBuilder()
          .setCustomId('community_submit_feedback')
          .setLabel('Feedback')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('💬')
      );

    return { embeds: [embed], components: [row] };
  }

  private async generateAICodingContent(userContext: UserContext): Promise<PanelRenderData> {

    const embed = new EmbedBuilder()
      .setTitle('🤖 AI Coding & Development')
      .setDescription('Explore coding projects, resources, and get help with your development journey.')
      .addFields(
        { name: '💻 Projects', value: 'Browse and showcase coding projects', inline: true },
        { name: '📚 Resources', value: 'Access curated development resources', inline: true },
        { name: '🆘 Get Help', value: 'Connect with experienced developers', inline: true },
        { name: '🚀 Showcase', value: 'Share your amazing projects', inline: true }
      )
      .setColor('#6366f1')
      .setTimestamp();

    const row = new ActionRowBuilder<ButtonBuilder>()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('ai_coding_browse_projects')
          .setLabel('Browse Projects')
          .setStyle(ButtonStyle.Primary)
          .setEmoji('💻'),
        new ButtonBuilder()
          .setCustomId('ai_coding_view_resources')
          .setLabel('Resources')
          .setStyle(ButtonStyle.Success)
          .setEmoji('📚'),
        new ButtonBuilder()
          .setCustomId('ai_coding_get_help')
          .setLabel('Get Help')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('🆘'),
        new ButtonBuilder()
          .setCustomId('ai_coding_showcase_project')
          .setLabel('Showcase')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('🚀')
      );

    return { embeds: [embed], components: [row] };
  }

  private async generatePremiumContent(userContext: UserContext): Promise<PanelRenderData> {

    const embed = new EmbedBuilder()
      .setTitle('⭐ Premium Community')
      .setDescription('Unlock exclusive benefits and connect with premium members.')
      .addFields(
        { name: '🎁 Full Benefits', value: 'Explore all premium features and perks', inline: true },
        { name: '🎪 Exclusive Events', value: 'Access premium-only events and workshops', inline: true },
        { name: '🏃 Priority Support', value: 'Get faster response times and dedicated help', inline: true },
        { name: '📖 Resources', value: 'Access premium guides and materials', inline: true }
      )
      .setColor('#f59e0b')
      .setTimestamp();

    const row = new ActionRowBuilder<ButtonBuilder>()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('premium_view_benefits')
          .setLabel('Full Benefits')
          .setStyle(ButtonStyle.Primary)
          .setEmoji('🎁'),
        new ButtonBuilder()
          .setCustomId('premium_exclusive_events')
          .setLabel('Exclusive Events')
          .setStyle(ButtonStyle.Success)
          .setEmoji('🎪'),
        new ButtonBuilder()
          .setCustomId('premium_priority_support')
          .setLabel('Priority Support')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('🏃'),
        new ButtonBuilder()
          .setCustomId('premium_resources')
          .setLabel('Resources')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('📖')
      );

    return { embeds: [embed], components: [row] };
  }

  private async generateGoalTrackingContent(userContext: UserContext): Promise<PanelRenderData> {

    const embed = new EmbedBuilder()
      .setTitle('🎯 Goal Tracking & Achievement')
      .setDescription('Set, track, and achieve your personal and professional goals.')
      .addFields(
        { name: '🎯 Set Goals', value: 'Define your short and long-term objectives', inline: true },
        { name: '📊 Track Progress', value: 'Monitor your advancement and milestones', inline: true },
        { name: '🤝 Accountability', value: 'Find partners to keep you motivated', inline: true },
        { name: '🏆 Celebrate', value: 'Share your wins with the community', inline: true }
      )
      .setColor('#8b5cf6')
      .setTimestamp();

    const row = new ActionRowBuilder<ButtonBuilder>()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('goals_set_goals')
          .setLabel('Set Goals')
          .setStyle(ButtonStyle.Primary)
          .setEmoji('🎯'),
        new ButtonBuilder()
          .setCustomId('goals_track_progress')
          .setLabel('Track Progress')
          .setStyle(ButtonStyle.Success)
          .setEmoji('📊'),
        new ButtonBuilder()
          .setCustomId('goals_find_accountability')
          .setLabel('Find Accountability')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('🤝'),
        new ButtonBuilder()
          .setCustomId('goals_celebrate_wins')
          .setLabel('Celebrate Wins')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('🏆')
      );

    return { embeds: [embed], components: [row] };
  }

  private createErrorContent(message: string): PanelRenderData {
    return {
      embeds: [],
      components: [],
      content: `❌ ${message}`,
      ephemeral: true
    };
  }
}

@Injectable()
export class DefaultPanelFactory implements IPanelFactory {
  private readonly logger = new Logger(DefaultPanelFactory.name);

  readonly supportedPanelTypes = [
    'announcement',
    'community', 
    'ai-coding',
    'premium',
    'goal-tracking'
  ];

  constructor(
    private readonly stateManager: IStateManager,
    // Content providers would be injected here
    // For now, we'll create an empty map
  ) {}

  /**
   * Check if this factory can create the requested panel type
   */
  canCreate(panelType: string): boolean {
    return this.supportedPanelTypes.includes(panelType);
  }

  /**
   * Create a new panel instance
   */
  async createPanel(configuration: PanelConfiguration): Promise<IPanelInstance> {
    try {
      this.logger.debug(`Creating panel instance: ${configuration.panelId} (${configuration.panelType})`);

      if (!this.canCreate(configuration.panelType)) {
        throw new Error(`Unsupported panel type: ${configuration.panelType}`);
      }

      // Get supported actions for this panel type
      const supportedActions = this.getSupportedActions(configuration.panelType);

      // Create content providers map (would be populated with real providers)
      const contentProviders = new Map<string, IContentProvider>();

      // Create the panel instance
      const panelInstance = new DefaultPanelInstance(
        configuration,
        supportedActions,
        this.stateManager,
        contentProviders
      );

      this.logger.log(`Successfully created ${configuration.panelType} panel: ${configuration.panelId}`);
      return panelInstance;

    } catch (error) {
      this.logger.error(`Failed to create panel ${configuration.panelId}:`, error);
      throw error;
    }
  }

  /**
   * Get default configuration for a panel type
   */
  getDefaultConfiguration(panelType: string, channelId: string, guildId: string): PanelConfiguration {
    const panelId = `${panelType}-${channelId}`;

    const baseConfig: PanelConfiguration = {
      panelType,
      panelId,
      channelId,
      guildId,
      title: this.getDefaultTitle(panelType),
      description: this.getDefaultDescription(panelType),
      isEnabled: true,
      customSettings: {}
    };

    // Add type-specific settings - create new objects due to readonly constraint
    switch (panelType) {
      case 'announcement':
        return {
          ...baseConfig,
          customSettings: {
            allowSubscriptions: true,
            defaultCategories: ['general', 'updates', 'events'],
            maxHistoryItems: 50
          }
        };

      case 'community':
        return {
          ...baseConfig,
          customSettings: {
            showLeaderboard: true,
            maxEventsDisplay: 10,
            allowFeedback: true
          }
        };

      case 'ai-coding':
        return {
          ...baseConfig,
          customSettings: {
            showFeaturedProjects: true,
            maxProjectsDisplay: 15,
            allowProjectSubmission: true
          }
        };

      case 'premium':
        return {
          ...baseConfig,
          customSettings: {
            requireVerification: true,
            showExclusiveContent: true
          }
        };

      case 'goal-tracking':
        return {
          ...baseConfig,
          customSettings: {
            allowPublicGoals: true,
            enableAccountabilityPartners: true,
            showProgress: true
          }
        };
    }

    return baseConfig;
  }

  private getSupportedActions(panelType: string): any[] {
    // This would return the actual action definitions
    // For now, return empty array
    return [];
  }

  private getDefaultTitle(panelType: string): string {
    const titles = {
      'announcement': '📢 Announcement Center',
      'community': '🏘️ Community Hub',
      'ai-coding': '🤖 AI Coding & Development',
      'premium': '⭐ Premium Community',
      'goal-tracking': '🎯 Goal Tracking & Achievement'
    };

    return titles[panelType as keyof typeof titles] || `${panelType} Panel`;
  }

  private getDefaultDescription(panelType: string): string {
    const descriptions = {
      'announcement': 'Stay updated with the latest news and announcements from our community.',
      'community': 'Connect with fellow community members and stay engaged with our activities.',
      'ai-coding': 'Explore coding projects, resources, and get help with your development journey.',
      'premium': 'Unlock exclusive benefits and connect with premium members.',
      'goal-tracking': 'Set, track, and achieve your personal and professional goals.'
    };

    return descriptions[panelType as keyof typeof descriptions] || `${panelType} panel description`;
  }

  /**
   * Get factory statistics for monitoring
   */
  getFactoryStats(): {
    supportedTypes: string[];
    totalPanelsCreated: number;
    averageCreationTime: number;
  } {
    return {
      supportedTypes: this.supportedPanelTypes,
      totalPanelsCreated: 0, // Would track this in production
      averageCreationTime: 0 // Would track this in production
    };
  }
}