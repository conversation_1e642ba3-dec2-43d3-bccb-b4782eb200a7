import { Injectable, Logger } from '@nestjs/common';
import { ButtonInteraction, StringSelectMenuInteraction, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } from 'discord.js';

export interface AnnouncementSubscription {
  userId: string;
  guildId: string;
  categories: string[];
  createdAt: Date;
  isActive: boolean;
}

export interface AnnouncementItem {
  id: string;
  title: string;
  content: string;
  category: 'server-rules' | 'community' | 'features' | 'notifications';
  createdAt: Date;
  authorId: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
}

@Injectable()
export class AnnouncementActionsHandler {
  private readonly logger = new Logger(AnnouncementActionsHandler.name);
  private subscriptions = new Map<string, AnnouncementSubscription>();
  private announcements: AnnouncementItem[] = [];

  constructor() {
    // Initialize with some sample data - in production this would come from database
    this.initializeSampleData();
  }

  async handleSubscribeAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    const userId = interaction.user.id;
    const guildId = interaction.guildId!;
    const subscriptionKey = `${userId}-${guildId}`;

    try {
      const existingSubscription = this.subscriptions.get(subscriptionKey);
      
      if (existingSubscription && existingSubscription.isActive) {
        await interaction.editReply({
          content: '✅ You are already subscribed to announcements! Use the Settings button to manage your preferences.'
        });
        return;
      }

      // Create or reactivate subscription
      const subscription: AnnouncementSubscription = {
        userId,
        guildId,
        categories: ['server-rules', 'community', 'features', 'notifications'],
        createdAt: new Date(),
        isActive: true
      };

      this.subscriptions.set(subscriptionKey, subscription);

      const embed = new EmbedBuilder()
        .setColor(0x00ff00)
        .setTitle('🔔 Subscription Confirmed!')
        .setDescription('You have successfully subscribed to announcements. You will receive notifications for:')
        .addFields([
          { name: '📋 Server Rules & Guidelines', value: 'Important server updates', inline: true },
          { name: '🏘️ Community Announcements', value: 'Community events and news', inline: true },
          { name: '✨ Feature Updates', value: 'New features and changes', inline: true },
          { name: '🔔 Important Notifications', value: 'Critical server notifications', inline: true }
        ])
        .setFooter({ text: 'You can manage your preferences anytime using the Settings button' })
        .setTimestamp();

      const row = new ActionRowBuilder<ButtonBuilder>()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('announcement_settings')
            .setLabel('⚙️ Manage Preferences')
            .setStyle(ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('announcement_unsubscribe')
            .setLabel('🔕 Unsubscribe')
            .setStyle(ButtonStyle.Danger)
        );

      await interaction.editReply({
        embeds: [embed],
        components: [row],
        content: null
      });

      this.logger.log(`User ${userId} subscribed to announcements in guild ${guildId}`);
    } catch (error) {
      this.logger.error('Failed to handle subscribe action:', error);
      await interaction.editReply({
        content: '❌ Failed to subscribe. Please try again later.'
      });
    }
  }

  async handleViewHistoryAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {
      const recentAnnouncements = this.announcements
        .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
        .slice(0, 5);

      if (recentAnnouncements.length === 0) {
        await interaction.editReply({
          content: '📭 No recent announcements found.'
        });
        return;
      }

      const embed = new EmbedBuilder()
        .setColor(0x3498db)
        .setTitle('📰 Recent Announcements')
        .setDescription('Here are the latest announcements from the server:')
        .setTimestamp();

      for (const announcement of recentAnnouncements) {
        const categoryEmoji = this.getCategoryEmoji(announcement.category);
        const priorityIndicator = this.getPriorityIndicator(announcement.priority);
        
        embed.addFields([{
          name: `${categoryEmoji} ${announcement.title} ${priorityIndicator}`,
          value: `${announcement.content.substring(0, 100)}${announcement.content.length > 100 ? '...' : ''}\n*${announcement.createdAt.toLocaleDateString()}*`,
          inline: false
        }]);
      }

      const row = new ActionRowBuilder<ButtonBuilder>()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('announcement_history_prev')
            .setLabel('⬅️ Previous')
            .setStyle(ButtonStyle.Secondary)
            .setDisabled(true), // Implement pagination logic
          new ButtonBuilder()
            .setCustomId('announcement_history_next')
            .setLabel('Next ➡️')
            .setStyle(ButtonStyle.Secondary)
            .setDisabled(recentAnnouncements.length < 5),
          new ButtonBuilder()
            .setCustomId('announcement_back')
            .setLabel('🔙 Back to Main')
            .setStyle(ButtonStyle.Primary)
        );

      await interaction.editReply({
        embeds: [embed],
        components: [row],
        content: null
      });

      this.logger.log(`User ${interaction.user.id} viewed announcement history`);
    } catch (error) {
      this.logger.error('Failed to handle view history action:', error);
      await interaction.editReply({
        content: '❌ Failed to load announcement history. Please try again.'
      });
    }
  }

  async handleSettingsAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    const userId = interaction.user.id;
    const guildId = interaction.guildId!;
    const subscriptionKey = `${userId}-${guildId}`;

    try {
      const subscription = this.subscriptions.get(subscriptionKey);
      
      if (!subscription || !subscription.isActive) {
        await interaction.editReply({
          content: '❌ You are not subscribed to announcements. Please subscribe first using the Subscribe button.'
        });
        return;
      }

      const embed = new EmbedBuilder()
        .setColor(0xf39c12)
        .setTitle('⚙️ Notification Settings')
        .setDescription('Manage your announcement preferences:')
        .addFields([
          { 
            name: '📋 Current Subscriptions', 
            value: subscription.categories.map((cat: any) => `✅ ${this.getCategoryName(cat)}`).join('\n'),
            inline: false 
          },
          { 
            name: '🔔 Subscription Status', 
            value: subscription.isActive ? '✅ Active' : '❌ Inactive',
            inline: true 
          },
          { 
            name: '📅 Subscribed Since', 
            value: subscription.createdAt.toLocaleDateString(),
            inline: true 
          }
        ])
        .setFooter({ text: 'Use the buttons below to modify your preferences' })
        .setTimestamp();

      const row1 = new ActionRowBuilder<ButtonBuilder>()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('announcement_settings_rules')
            .setLabel('📋 Server Rules')
            .setStyle(subscription.categories.includes('server-rules') ? ButtonStyle.Success : ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('announcement_settings_community')
            .setLabel('🏘️ Community')
            .setStyle(subscription.categories.includes('community') ? ButtonStyle.Success : ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('announcement_settings_features')
            .setLabel('✨ Features')
            .setStyle(subscription.categories.includes('features') ? ButtonStyle.Success : ButtonStyle.Secondary)
        );

      const row2 = new ActionRowBuilder<ButtonBuilder>()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('announcement_settings_notifications')
            .setLabel('🔔 Important Notifications')
            .setStyle(subscription.categories.includes('notifications') ? ButtonStyle.Success : ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('announcement_settings_save')
            .setLabel('💾 Save Changes')
            .setStyle(ButtonStyle.Primary),
          new ButtonBuilder()
            .setCustomId('announcement_back')
            .setLabel('🔙 Back')
            .setStyle(ButtonStyle.Secondary)
        );

      await interaction.editReply({
        embeds: [embed],
        components: [row1, row2],
        content: null
      });

      this.logger.log(`User ${userId} accessed announcement settings`);
    } catch (error) {
      this.logger.error('Failed to handle settings action:', error);
      await interaction.editReply({
        content: '❌ Failed to load settings. Please try again.'
      });
    }
  }

  async handleHelpAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {
      const embed = new EmbedBuilder()
        .setColor(0xe74c3c)
        .setTitle('❓ Announcement Center Help')
        .setDescription('Get help with using the announcement system:')
        .addFields([
          {
            name: '🔔 Subscribing to Announcements',
            value: '• Click "Subscribe to Updates" to receive notifications\n• Choose which categories you want to follow\n• Get notified when important announcements are posted',
            inline: false
          },
          {
            name: '📰 Viewing History',
            value: '• Click "View History" to see recent announcements\n• Browse through past announcements by category\n• Search for specific announcements using keywords',
            inline: false
          },
          {
            name: '⚙️ Managing Settings',
            value: '• Customize which types of announcements you receive\n• Toggle specific categories on/off\n• Update your notification preferences anytime',
            inline: false
          },
          {
            name: '🔧 Troubleshooting',
            value: '• Not receiving notifications? Check your Discord DM settings\n• Missing announcements? Ensure you\'re subscribed to the right categories\n• Still having issues? Contact server moderators',
            inline: false
          }
        ])
        .setFooter({ text: 'For additional support, contact the server moderators' })
        .setTimestamp();

      const row = new ActionRowBuilder<ButtonBuilder>()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('announcement_help_dm')
            .setLabel('📩 Get Help via DM')
            .setStyle(ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('announcement_help_support')
            .setLabel('🎫 Contact Support')
            .setStyle(ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('announcement_back')
            .setLabel('🔙 Back to Main')
            .setStyle(ButtonStyle.Primary)
        );

      await interaction.editReply({
        embeds: [embed],
        components: [row],
        content: null
      });

      this.logger.log(`User ${interaction.user.id} accessed announcement help`);
    } catch (error) {
      this.logger.error('Failed to handle help action:', error);
      await interaction.editReply({
        content: '❌ Failed to load help information. Please try again.'
      });
    }
  }

  async handleUnsubscribeAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    const userId = interaction.user.id;
    const guildId = interaction.guildId!;
    const subscriptionKey = `${userId}-${guildId}`;

    try {
      const subscription = this.subscriptions.get(subscriptionKey);
      
      if (!subscription || !subscription.isActive) {
        await interaction.editReply({
          content: '❌ You are not currently subscribed to announcements.'
        });
        return;
      }

      // Deactivate subscription
      subscription.isActive = false;
      this.subscriptions.set(subscriptionKey, subscription);

      const embed = new EmbedBuilder()
        .setColor(0xe67e22)
        .setTitle('🔕 Unsubscribed Successfully')
        .setDescription('You have been unsubscribed from all announcements.')
        .addFields([
          { name: '📝 What this means', value: 'You will no longer receive notifications for server announcements', inline: false },
          { name: '🔄 Want to resubscribe?', value: 'You can resubscribe anytime using the "Subscribe to Updates" button', inline: false }
        ])
        .setFooter({ text: 'We\'re sorry to see you go!' })
        .setTimestamp();

      const row = new ActionRowBuilder<ButtonBuilder>()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('announcement_subscribe')
            .setLabel('🔔 Resubscribe')
            .setStyle(ButtonStyle.Success),
          new ButtonBuilder()
            .setCustomId('announcement_back')
            .setLabel('🔙 Back to Main')
            .setStyle(ButtonStyle.Primary)
        );

      await interaction.editReply({
        embeds: [embed],
        components: [row],
        content: null
      });

      this.logger.log(`User ${userId} unsubscribed from announcements in guild ${guildId}`);
    } catch (error) {
      this.logger.error('Failed to handle unsubscribe action:', error);
      await interaction.editReply({
        content: '❌ Failed to unsubscribe. Please try again.'
      });
    }
  }

  private initializeSampleData(): void {
    // Sample announcements - in production, this would come from database
    this.announcements = [
      {
        id: '1',
        title: 'New Server Rules Update',
        content: 'We have updated our server rules to ensure a better community experience. Please review the changes in the rules channel.',
        category: 'server-rules',
        createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
        authorId: 'server',
        priority: 'high'
      },
      {
        id: '2',
        title: 'Community Event: Weekly Challenge',
        content: 'Join us for our weekly coding challenge! This week we are focusing on AI integration projects.',
        category: 'community',
        createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
        authorId: 'community-team',
        priority: 'medium'
      },
      {
        id: '3',
        title: 'New Feature: Enhanced Panel System',
        content: 'We have launched our new interactive panel system with improved user experience and functionality.',
        category: 'features',
        createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
        authorId: 'dev-team',
        priority: 'medium'
      }
    ];
  }

  private getCategoryEmoji(category: string): string {
    const emojiMap: Record<string, string> = {
      'server-rules': '📋',
      'community': '🏘️',
      'features': '✨',
      'notifications': '🔔'
    };
    return emojiMap[category] || '📢';
  }

  private getCategoryName(category: string): string {
    const nameMap: Record<string, string> = {
      'server-rules': 'Server Rules & Guidelines',
      'community': 'Community Announcements',
      'features': 'Feature Updates',
      'notifications': 'Important Notifications'
    };
    return nameMap[category] || category;
  }

  private getPriorityIndicator(priority: string): string {
    const priorityMap: Record<string, string> = {
      'low': '',
      'medium': '🔸',
      'high': '🔥',
      'urgent': '🚨'
    };
    return priorityMap[priority] || '';
  }
}