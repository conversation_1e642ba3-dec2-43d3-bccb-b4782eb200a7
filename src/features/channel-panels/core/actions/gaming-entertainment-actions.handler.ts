import { Injectable, Logger } from '@nestjs/common';
import { ButtonInteraction, StringSelectMenuInteraction, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, StringSelectMenuBuilder, StringSelectMenuOptionBuilder } from 'discord.js';

export interface Tournament {
  id: string;
  title: string;
  description: string;
  game: string;
  platform: string[];
  format: 'single-elimination' | 'double-elimination' | 'round-robin' | 'swiss';
  maxParticipants: number;
  currentParticipants: number;
  prizePool: string;
  registrationStart: Date;
  registrationEnd: Date;
  tournamentStart: Date;
  tournamentEnd: Date;
  status: 'upcoming' | 'registration-open' | 'in-progress' | 'completed';
  organizer: string;
  rules: string[];
  requirements: string[];
  participants: string[];
  bracket?: string;
  streamLink?: string;
}

export interface GameSession {
  id: string;
  hostId: string;
  hostName: string;
  game: string;
  platform: string;
  maxPlayers: number;
  currentPlayers: number;
  scheduledTime: Date;
  duration: number; // minutes
  difficulty: 'casual' | 'competitive' | 'hardcore';
  description: string;
  requirements: string[];
  voiceChannel?: string;
  joinedPlayers: string[];
  status: 'scheduled' | 'active' | 'completed' | 'cancelled';
  gameMode: string;
  isPrivate: boolean;
}

export interface PlayerProfile {
  userId: string;
  gamertag: string;
  platforms: string[];
  favoriteGames: string[];
  skillLevel: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  playstyle: 'casual' | 'competitive' | 'speedrun' | 'social';
  achievements: GameAchievement[];
  stats: PlayerStats;
  availability: string[];
  lookingFor: string[];
  bio: string;
  streamingProfile?: {
    platform: string;
    username: string;
    followers: number;
  };
}

export interface GameAchievement {
  id: string;
  title: string;
  description: string;
  game: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  unlockedAt: Date;
  points: number;
  category: string;
}

export interface PlayerStats {
  totalGamesPlayed: number;
  totalHoursPlayed: number;
  tournamentsEntered: number;
  tournamentsWon: number;
  winRate: number;
  favoriteGenre: string;
  recentActivity: Date;
  weeklyHours: number;
}

export interface GameReview {
  id: string;
  userId: string;
  username: string;
  game: string;
  platform: string;
  rating: number; // 1-5 stars
  title: string;
  content: string;
  playTime: number; // hours
  completedGame: boolean;
  recommendToFriends: boolean;
  pros: string[];
  cons: string[];
  postedAt: Date;
  helpful: number;
  notHelpful: number;
  tags: string[];
}

@Injectable()
export class GamingEntertainmentActionsHandler {
  private readonly logger = new Logger(GamingEntertainmentActionsHandler.name);
  private tournaments: Tournament[] = [];
  private gameSessions: GameSession[] = [];
  private playerProfiles = new Map<string, PlayerProfile>();
  private gameReviews: GameReview[] = [];
  private leaderboard = new Map<string, any>();

  constructor() {
    this.initializeSampleData();
  }

  async handleJoinTournamentAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {
      const userId = interaction.user.id;
      const activeTournaments = this.tournaments
        .filter((t: any) => t.status === 'registration-open' || t.status === 'upcoming')
        .sort((a, b) => a.registrationEnd.getTime() - b.registrationEnd.getTime());

      const embed = new EmbedBuilder()
        .setColor(0x00d4ff)
        .setTitle('🏆 Gaming Tournaments')
        .setDescription('Join competitive tournaments and prove your gaming skills!')
        .setTimestamp();

      if (activeTournaments.length === 0) {
        embed.addFields([{
          name: '🎮 No Active Tournaments',
          value: 'Check back soon for exciting tournament announcements! You can also suggest tournaments for games you\'d like to compete in.',
          inline: false
        }]);
      } else {
        embed.addFields([
          {
            name: '🔥 Active Tournaments',
            value: `**${activeTournaments.length}** tournaments accepting registrations`,
            inline: false
          }
        ]);

        for (const tournament of activeTournaments.slice(0, 3)) {
          const spotsLeft = tournament.maxParticipants - tournament.currentParticipants;
          const daysUntilStart = Math.ceil((tournament.tournamentStart.getTime() - Date.now()) / (1000 * 60 * 60 * 24));
          const statusEmoji = tournament.status === 'registration-open' ? '🔓' : '⏰';
          
          embed.addFields([{
            name: `${statusEmoji} ${tournament.title}`,
            value: `🎮 **Game:** ${tournament.game} | 🖥️ **Platform:** ${tournament.platform.join(', ')}\n` +
                   `👥 **Players:** ${tournament.currentParticipants}/${tournament.maxParticipants} (${spotsLeft} spots left)\n` +
                   `💰 **Prize:** ${tournament.prizePool} | 📊 **Format:** ${tournament.format}\n` +
                   `📅 **Starts:** ${tournament.tournamentStart.toLocaleDateString()} (${daysUntilStart} days)\n` +
                   `⏰ **Registration ends:** ${tournament.registrationEnd.toLocaleDateString()}`,
            inline: false
          }]);
        }

        if (activeTournaments.length > 3) {
          embed.addFields([{
            name: '🎯 More Tournaments',
            value: `+ ${activeTournaments.length - 3} more tournaments available. Browse all to find your perfect competition!`,
            inline: false
          }]);
        }

        // Tournament statistics
        const userProfile = this.playerProfiles.get(userId);
        if (userProfile) {
          embed.addFields([
            {
              name: '📊 Your Tournament History',
              value: `**Tournaments Entered:** ${userProfile.stats.tournamentsEntered}\n**Tournaments Won:** ${userProfile.stats.tournamentsWon}\n**Win Rate:** ${userProfile.stats.winRate.toFixed(1)}%\n**Current Rank:** ${this.getPlayerRank(userId)}`,
              inline: false
            }
          ]);
        }
      }

      const selectMenu = new StringSelectMenuBuilder()
        .setCustomId('gaming_entertainment_tournament_filter')
        .setPlaceholder('Filter by game type...')
        .addOptions(
          new StringSelectMenuOptionBuilder()
            .setLabel('🎯 FPS Games')
            .setDescription('First-person shooter tournaments')
            .setValue('fps')
            .setEmoji('🎯'),
          new StringSelectMenuOptionBuilder()
            .setLabel('⚔️ MOBA Games')
            .setDescription('Multiplayer online battle arena')
            .setValue('moba')
            .setEmoji('⚔️'),
          new StringSelectMenuOptionBuilder()
            .setLabel('🏎️ Racing Games')
            .setDescription('Racing and driving competitions')
            .setValue('racing')
            .setEmoji('🏎️'),
          new StringSelectMenuOptionBuilder()
            .setLabel('🎮 Platform Games')
            .setDescription('Platform and indie game tournaments')
            .setValue('platform')
            .setEmoji('🎮'),
          new StringSelectMenuOptionBuilder()
            .setLabel('🃏 Card Games')
            .setDescription('Digital card game competitions')
            .setValue('card')
            .setEmoji('🃏')
        );

      const selectRow = new ActionRowBuilder<StringSelectMenuBuilder>()
        .addComponents(selectMenu);

      const buttonRow = new ActionRowBuilder<ButtonBuilder>()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('gaming_entertainment_register_tournament')
            .setLabel('📝 Register')
            .setStyle(ButtonStyle.Success)
            .setDisabled(activeTournaments.length === 0),
          new ButtonBuilder()
            .setCustomId('gaming_entertainment_tournament_brackets')
            .setLabel('🏆 View Brackets')
            .setStyle(ButtonStyle.Primary),
          new ButtonBuilder()
            .setCustomId('gaming_entertainment_create_tournament')
            .setLabel('➕ Create Tournament')
            .setStyle(ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('gaming_entertainment_back')
            .setLabel('🔙 Back')
            .setStyle(ButtonStyle.Secondary)
        );

      await interaction.editReply({
        embeds: [embed],
        components: [selectRow, buttonRow],
        content: null
      });

      this.logger.log(`User ${userId} browsed gaming tournaments`);
    } catch (error) {
      this.logger.error('Failed to handle join tournament action:', error);
      await interaction.editReply({
        content: '❌ Failed to load tournaments. Please try again.'
      });
    }
  }

  async handleViewLeaderboardAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {
      const userId = interaction.user.id;
      
      const embed = new EmbedBuilder()
        .setColor(0xffa500)
        .setTitle('🏆 Gaming Leaderboard')
        .setDescription('See who\'s dominating the gaming community this month!')
        .addFields([
          {
            name: '🥇 Top Players This Month',
            value: '1. 🏆 **GamerPro** - 2,847 points\n   📊 12 tournament wins | 🎮 47 games played\n\n2. 🥈 **SkillMaster** - 2,213 points\n   📊 8 tournament wins | 🎮 52 games played\n\n3. 🥉 **ElitePlayer** - 1,956 points\n   📊 6 tournament wins | 🎮 39 games played\n\n4. 🏅 **ProGamer2024** - 1,743 points\n   📊 5 tournament wins | 🎮 43 games played\n\n5. ⭐ **GameChampion** - 1,621 points\n   📊 4 tournament wins | 🎮 38 games played',
            inline: false
          }
        ])
        .setTimestamp();

      const userProfile = this.playerProfiles.get(userId);
      if (userProfile) {
        const userRank = this.getUserRank(userId);
        const userPoints = this.getUserPoints(userId);
        
        embed.addFields([
          {
            name: '📊 Your Performance',
            value: `🎯 **Current Rank:** #${userRank}\n⭐ **Total Points:** ${userPoints.toLocaleString()}\n🏅 **Tournaments Won:** ${userProfile.stats.tournamentsWon}\n🎮 **Games Played:** ${userProfile.stats.totalGamesPlayed}\n📈 **Win Rate:** ${userProfile.stats.winRate.toFixed(1)}%\n⏱️ **Hours Played:** ${userProfile.stats.totalHoursPlayed}`,
            inline: false
          }
        ]);
      }

      embed.addFields([
        {
          name: '🎪 How to Earn Points',
          value: '🏆 **Win tournaments** → +500 points\n🎯 **Participate in events** → +100 points\n🤝 **Help other gamers** → +50 points\n📝 **Write game reviews** → +25 points\n👥 **Host game sessions** → +75 points\n🎮 **Daily challenges** → +10 points',
          inline: false
        },
        {
          name: '🏅 Leaderboard Categories',
          value: '🌟 **Overall Ranking** - Total points across all activities\n🏆 **Tournament Masters** - Tournament wins and performance\n🎮 **Most Active** - Games played and community participation\n📈 **Rising Stars** - Biggest point gains this month\n🤝 **Community Heroes** - Helping and mentoring others',
          inline: false
        }
      ]);

      const selectMenu = new StringSelectMenuBuilder()
        .setCustomId('gaming_entertainment_leaderboard_category')
        .setPlaceholder('Select leaderboard category...')
        .addOptions(
          new StringSelectMenuOptionBuilder()
            .setLabel('🌟 Overall Ranking')
            .setDescription('Total points across all activities')
            .setValue('overall')
            .setEmoji('🌟'),
          new StringSelectMenuOptionBuilder()
            .setLabel('🏆 Tournament Masters')
            .setDescription('Tournament wins and performance')
            .setValue('tournaments')
            .setEmoji('🏆'),
          new StringSelectMenuOptionBuilder()
            .setLabel('🎮 Most Active Players')
            .setDescription('Games played and participation')
            .setValue('activity')
            .setEmoji('🎮'),
          new StringSelectMenuOptionBuilder()
            .setLabel('📈 Rising Stars')
            .setDescription('Biggest point gains this month')
            .setValue('rising')
            .setEmoji('📈'),
          new StringSelectMenuOptionBuilder()
            .setLabel('🤝 Community Heroes')
            .setDescription('Helping and mentoring others')
            .setValue('community')
            .setEmoji('🤝')
        );

      const selectRow = new ActionRowBuilder<StringSelectMenuBuilder>()
        .addComponents(selectMenu);

      const buttonRow = new ActionRowBuilder<ButtonBuilder>()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('gaming_entertainment_my_rank')
            .setLabel('📊 My Detailed Stats')
            .setStyle(ButtonStyle.Success),
          new ButtonBuilder()
            .setCustomId('gaming_entertainment_challenges')
            .setLabel('🎯 Daily Challenges')
            .setStyle(ButtonStyle.Primary),
          new ButtonBuilder()
            .setCustomId('gaming_entertainment_compare_players')
            .setLabel('⚖️ Compare Players')
            .setStyle(ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('gaming_entertainment_back')
            .setLabel('🔙 Back')
            .setStyle(ButtonStyle.Secondary)
        );

      await interaction.editReply({
        embeds: [embed],
        components: [selectRow, buttonRow],
        content: null
      });

      this.logger.log(`User ${userId} viewed gaming leaderboard`);
    } catch (error) {
      this.logger.error('Failed to handle view leaderboard action:', error);
      await interaction.editReply({
        content: '❌ Failed to load leaderboard. Please try again.'
      });
    }
  }

  async handleGameSessionsAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {
      const userId = interaction.user.id;
      const activeSessions = this.gameSessions
        .filter((s: any) => s.status === 'scheduled' && s.scheduledTime > new Date())
        .sort((a, b) => a.scheduledTime.getTime() - b.scheduledTime.getTime());

      const embed = new EmbedBuilder()
        .setColor(0x00ff88)
        .setTitle('🎮 Game Sessions & Parties')
        .setDescription('Join or host multiplayer gaming sessions with community members')
        .setTimestamp();

      if (activeSessions.length === 0) {
        embed.addFields([{
          name: '🚀 Start Gaming!',
          value: 'No active sessions right now. Be the first to host a gaming session and bring the community together!',
          inline: false
        }]);
      } else {
        embed.addFields([
          {
            name: '🎯 Active Sessions',
            value: `**${activeSessions.length}** sessions scheduled | Looking for players!`,
            inline: false
          }
        ]);

        for (const session of activeSessions.slice(0, 4)) {
          const spotsLeft = session.maxPlayers - session.currentPlayers;
          const timeUntilStart = this.getTimeUntilStart(session.scheduledTime);
          const difficultyEmoji = this.getDifficultyEmoji(session.difficulty);
          const platformEmoji = this.getPlatformEmoji(session.platform);
          
          embed.addFields([{
            name: `${platformEmoji} ${session.game} ${difficultyEmoji}`,
            value: `👤 **Host:** ${session.hostName}\n` +
                   `👥 **Players:** ${session.currentPlayers}/${session.maxPlayers} (${spotsLeft} spots available)\n` +
                   `⏰ **Starts:** ${session.scheduledTime.toLocaleString()} (${timeUntilStart})\n` +
                   `🎮 **Mode:** ${session.gameMode} | ⏱️ **Duration:** ${Math.floor(session.duration / 60)}h ${session.duration % 60}m\n` +
                   `${session.description.substring(0, 80)}${session.description.length > 80 ? '...' : ''}`,
            inline: false
          }]);
        }

        if (activeSessions.length > 4) {
          embed.addFields([{
            name: '🎪 More Sessions',
            value: `+ ${activeSessions.length - 4} more gaming sessions available. Browse all to find your perfect match!`,
            inline: false
          }]);
        }
      }

      embed.addFields([
        {
          name: '🎯 Popular Games This Week',
          value: '🔫 **Call of Duty** - 23 sessions\n🏎️ **Rocket League** - 18 sessions\n⚔️ **League of Legends** - 15 sessions\n🎮 **Minecraft** - 12 sessions\n🃏 **Among Us** - 9 sessions',
          inline: false
        },
        {
          name: '🎮 Session Types',
          value: '🆚 **Competitive** - Ranked matches and serious gameplay\n🎪 **Casual** - Fun, relaxed gaming sessions\n💀 **Hardcore** - High-difficulty challenges\n🎓 **Learning** - Teaching and coaching sessions\n🎉 **Community Events** - Special themed sessions',
          inline: false
        }
      ]);

      const selectMenu = new StringSelectMenuBuilder()
        .setCustomId('gaming_entertainment_session_filter')
        .setPlaceholder('Filter by game category...')
        .addOptions(
          new StringSelectMenuOptionBuilder()
            .setLabel('🔫 FPS Games')
            .setDescription('First-person shooter sessions')
            .setValue('fps')
            .setEmoji('🔫'),
          new StringSelectMenuOptionBuilder()
            .setLabel('🏎️ Racing Games')
            .setDescription('Racing and driving sessions')
            .setValue('racing')
            .setEmoji('🏎️'),
          new StringSelectMenuOptionBuilder()
            .setLabel('🎲 Strategy Games')
            .setDescription('Strategy and tactical sessions')
            .setValue('strategy')
            .setEmoji('🎲'),
          new StringSelectMenuOptionBuilder()
            .setLabel('🎭 Social Games')
            .setDescription('Party and social gaming')
            .setValue('social')
            .setEmoji('🎭'),
          new StringSelectMenuOptionBuilder()
            .setLabel('🏗️ Building Games')
            .setDescription('Creative and building sessions')
            .setValue('building')
            .setEmoji('🏗️')
        );

      const selectRow = new ActionRowBuilder<StringSelectMenuBuilder>()
        .addComponents(selectMenu);

      const buttonRow = new ActionRowBuilder<ButtonBuilder>()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('gaming_entertainment_join_session')
            .setLabel('🎮 Join Session')
            .setStyle(ButtonStyle.Success)
            .setDisabled(activeSessions.length === 0),
          new ButtonBuilder()
            .setCustomId('gaming_entertainment_host_session')
            .setLabel('🎯 Host Session')
            .setStyle(ButtonStyle.Primary),
          new ButtonBuilder()
            .setCustomId('gaming_entertainment_my_sessions')
            .setLabel('📅 My Sessions')
            .setStyle(ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('gaming_entertainment_back')
            .setLabel('🔙 Back')
            .setStyle(ButtonStyle.Secondary)
        );

      await interaction.editReply({
        embeds: [embed],
        components: [selectRow, buttonRow],
        content: null
      });

      this.logger.log(`User ${userId} browsed game sessions`);
    } catch (error) {
      this.logger.error('Failed to handle game sessions action:', error);
      await interaction.editReply({
        content: '❌ Failed to load game sessions. Please try again.'
      });
    }
  }

  async handleGameReviewsAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {
      const userId = interaction.user.id;
      const recentReviews = this.gameReviews
        .sort((a, b) => b.postedAt.getTime() - a.postedAt.getTime())
        .slice(0, 5);

      const embed = new EmbedBuilder()
        .setColor(0xff6b6b)
        .setTitle('⭐ Game Reviews & Recommendations')
        .setDescription('Discover great games and share your gaming experiences with the community')
        .setTimestamp();

      if (recentReviews.length === 0) {
        embed.addFields([{
          name: '📝 Share Your First Review',
          value: 'Be the first to share your gaming experience! Write reviews to help others discover amazing games.',
          inline: false
        }]);
      } else {
        embed.addFields([
          {
            name: '🔥 Recent Reviews',
            value: `Showing ${recentReviews.length} latest community reviews`,
            inline: false
          }
        ]);

        for (const review of recentReviews.slice(0, 3)) {
          const stars = '⭐'.repeat(review.rating) + '☆'.repeat(5 - review.rating);
          const recommendEmoji = review.recommendToFriends ? '👍' : '👎';
          
          embed.addFields([{
            name: `${stars} ${review.game}`,
            value: `👤 **Reviewer:** ${review.username} | 🎮 **Platform:** ${review.platform}\n` +
                   `⏰ **Playtime:** ${review.playTime} hours | ${recommendEmoji} **Recommends:** ${review.recommendToFriends ? 'Yes' : 'No'}\n` +
                   `💭 **"${review.title}"**\n` +
                   `${review.content.substring(0, 100)}${review.content.length > 100 ? '...' : ''}\n` +
                   `👍 **Helpful:** ${review.helpful} | 📅 **Posted:** ${review.postedAt.toLocaleDateString()}`,
            inline: false
          }]);
        }

        if (recentReviews.length > 3) {
          embed.addFields([{
            name: '📚 More Reviews',
            value: `+ ${recentReviews.length - 3} more reviews available. Search for specific games to find detailed reviews!`,
            inline: false
          }]);
        }
      }

      // Review statistics
      const avgRating = recentReviews.length > 0 
        ? (recentReviews.reduce((sum, r) => sum + r.rating, 0) / recentReviews.length).toFixed(1)
        : '0';
      const totalReviews = this.gameReviews.length;
      const recommendedGames = this.gameReviews.filter((r: any) => r.recommendToFriends).length;

      embed.addFields([
        {
          name: '📊 Review Statistics',
          value: `**Total Reviews:** ${totalReviews}\n**Average Rating:** ${avgRating}/5 ⭐\n**Recommended Games:** ${recommendedGames} (${totalReviews > 0 ? Math.round((recommendedGames / totalReviews) * 100) : 0}%)\n**Most Reviewed:** ${this.getMostReviewedGame()}`,
          inline: false
        },
        {
          name: '🎮 Review Categories',
          value: '🎯 **Gameplay** - Mechanics, controls, and fun factor\n🎨 **Graphics** - Visual quality and art style\n🎵 **Audio** - Music, sound effects, and voice acting\n📖 **Story** - Narrative, characters, and plot\n💰 **Value** - Price vs content and replayability\n🔧 **Technical** - Performance, bugs, and stability',
          inline: false
        }
      ]);

      const selectMenu = new StringSelectMenuBuilder()
        .setCustomId('gaming_entertainment_review_filter')
        .setPlaceholder('Filter reviews by category...')
        .addOptions(
          new StringSelectMenuOptionBuilder()
            .setLabel('🔥 Trending Games')
            .setDescription('Most reviewed games this week')
            .setValue('trending')
            .setEmoji('🔥'),
          new StringSelectMenuOptionBuilder()
            .setLabel('⭐ Highest Rated')
            .setDescription('Games with 4+ star ratings')
            .setValue('highest-rated')
            .setEmoji('⭐'),
          new StringSelectMenuOptionBuilder()
            .setLabel('🆕 New Releases')
            .setDescription('Recently released games')
            .setValue('new-releases')
            .setEmoji('🆕'),
          new StringSelectMenuOptionBuilder()
            .setLabel('💰 Best Value')
            .setDescription('Great games for the price')
            .setValue('best-value')
            .setEmoji('💰'),
          new StringSelectMenuOptionBuilder()
            .setLabel('🏆 Must-Play')
            .setDescription('Community recommended games')
            .setValue('must-play')
            .setEmoji('🏆')
        );

      const selectRow = new ActionRowBuilder<StringSelectMenuBuilder>()
        .addComponents(selectMenu);

      const buttonRow = new ActionRowBuilder<ButtonBuilder>()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('gaming_entertainment_write_review')
            .setLabel('✍️ Write Review')
            .setStyle(ButtonStyle.Success),
          new ButtonBuilder()
            .setCustomId('gaming_entertainment_browse_reviews')
            .setLabel('📖 Browse Reviews')
            .setStyle(ButtonStyle.Primary),
          new ButtonBuilder()
            .setCustomId('gaming_entertainment_my_reviews')
            .setLabel('📝 My Reviews')
            .setStyle(ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('gaming_entertainment_back')
            .setLabel('🔙 Back')
            .setStyle(ButtonStyle.Secondary)
        );

      await interaction.editReply({
        embeds: [embed],
        components: [selectRow, buttonRow],
        content: null
      });

      this.logger.log(`User ${userId} browsed game reviews`);
    } catch (error) {
      this.logger.error('Failed to handle game reviews action:', error);
      await interaction.editReply({
        content: '❌ Failed to load game reviews. Please try again.'
      });
    }
  }

  private initializeSampleData(): void {
    // Sample tournaments
    this.tournaments = [
      {
        id: 'tournament-1',
        title: 'Spring Championship 2024',
        description: 'Epic battle royale tournament with amazing prizes and recognition',
        game: 'Fortnite',
        platform: ['PC', 'PlayStation', 'Xbox'],
        format: 'single-elimination',
        maxParticipants: 64,
        currentParticipants: 47,
        prizePool: '$500 + Recognition',
        registrationStart: new Date('2024-05-01'),
        registrationEnd: new Date('2024-06-01'),
        tournamentStart: new Date('2024-06-05'),
        tournamentEnd: new Date('2024-06-07'),
        status: 'registration-open',
        organizer: 'Gaming Committee',
        rules: ['Fair play required', 'No cheating', 'Respect opponents'],
        requirements: ['Discord account', 'Stable internet', 'Game installed'],
        participants: ['player1', 'player2'],
        streamLink: 'https://twitch.tv/gaming-tournament'
      },
      {
        id: 'tournament-2',
        title: 'Rocket League Championship',
        description: 'Fast-paced rocket-powered vehicle soccer tournament',
        game: 'Rocket League',
        platform: ['PC', 'PlayStation', 'Xbox', 'Nintendo Switch'],
        format: 'double-elimination',
        maxParticipants: 32,
        currentParticipants: 28,
        prizePool: '$300 + Trophies',
        registrationStart: new Date('2024-05-10'),
        registrationEnd: new Date('2024-06-05'),
        tournamentStart: new Date('2024-06-10'),
        tournamentEnd: new Date('2024-06-12'),
        status: 'registration-open',
        organizer: 'Rocket League Community',
        rules: ['3v3 format', 'Standard rules', 'No custom maps'],
        requirements: ['Rocket League game', 'Team of 3 players'],
        participants: ['team1', 'team2']
      }
    ];

    // Sample game sessions
    this.gameSessions = [
      {
        id: 'session-1',
        hostId: 'host-1',
        hostName: 'GameMaster',
        game: 'Call of Duty: Warzone',
        platform: 'PC',
        maxPlayers: 4,
        currentPlayers: 2,
        scheduledTime: new Date(Date.now() + 2 * 60 * 60 * 1000), // 2 hours from now
        duration: 180, // 3 hours
        difficulty: 'competitive',
        description: 'Serious Warzone session, looking for experienced players with good communication',
        requirements: ['Microphone required', 'KD ratio 1.5+', 'Discord'],
        joinedPlayers: ['player1', 'player2'],
        status: 'scheduled',
        gameMode: 'Battle Royale',
        isPrivate: false
      },
      {
        id: 'session-2',
        hostId: 'host-2',
        hostName: 'CasualGamer',
        game: 'Minecraft',
        platform: 'Java Edition',
        maxPlayers: 6,
        currentPlayers: 3,
        scheduledTime: new Date(Date.now() + 1 * 60 * 60 * 1000), // 1 hour from now
        duration: 240, // 4 hours
        difficulty: 'casual',
        description: 'Chill Minecraft building session, working on a massive castle project',
        requirements: ['Minecraft Java Edition', 'Creative mindset'],
        joinedPlayers: ['builder1', 'builder2', 'builder3'],
        status: 'scheduled',
        gameMode: 'Creative',
        isPrivate: false
      }
    ];

    // Sample player profiles
    const sampleProfile: PlayerProfile = {
      userId: 'sample-user',
      gamertag: 'ProGamer2024',
      platforms: ['PC', 'PlayStation 5', 'Xbox Series X'],
      favoriteGames: ['Call of Duty', 'Rocket League', 'Minecraft', 'League of Legends'],
      skillLevel: 'advanced',
      playstyle: 'competitive',
      achievements: [
        {
          id: 'ach-1',
          title: 'Tournament Victor',
          description: 'Won your first tournament',
          game: 'Call of Duty',
          rarity: 'rare',
          unlockedAt: new Date('2024-04-15'),
          points: 500,
          category: 'Tournament'
        },
        {
          id: 'ach-2',
          title: 'Community Helper',
          description: 'Helped 10+ players improve their skills',
          game: 'General',
          rarity: 'epic',
          unlockedAt: new Date('2024-05-01'),
          points: 750,
          category: 'Community'
        }
      ],
      stats: {
        totalGamesPlayed: 342,
        totalHoursPlayed: 1247,
        tournamentsEntered: 12,
        tournamentsWon: 3,
        winRate: 67.2,
        favoriteGenre: 'FPS',
        recentActivity: new Date(),
        weeklyHours: 28
      },
      availability: ['Evening', 'Weekend'],
      lookingFor: ['Tournament partners', 'Skill improvement', 'Casual gaming'],
      bio: 'Competitive gamer looking to connect with skilled players. Always happy to help newcomers learn the ropes!',
      streamingProfile: {
        platform: 'Twitch',
        username: 'ProGamer2024_TV',
        followers: 1250
      }
    };

    // Sample game reviews
    this.gameReviews = [
      {
        id: 'review-1',
        userId: 'reviewer-1',
        username: 'GameCritic',
        game: 'Cyberpunk 2077',
        platform: 'PC',
        rating: 4,
        title: 'Amazing story, improved performance',
        content: 'After many updates, Cyberpunk 2077 has become the game it was meant to be. The story is compelling, the graphics are stunning, and most bugs have been fixed.',
        playTime: 89,
        completedGame: true,
        recommendToFriends: true,
        pros: ['Great story', 'Beautiful graphics', 'Improved stability'],
        cons: ['Still some minor bugs', 'High system requirements'],
        postedAt: new Date('2024-05-20'),
        helpful: 23,
        notHelpful: 3,
        tags: ['RPG', 'Open World', 'Futuristic']
      },
      {
        id: 'review-2',
        userId: 'reviewer-2',
        username: 'IndieExplorer',
        game: 'Hades',
        platform: 'Nintendo Switch',
        rating: 5,
        title: 'Perfect roguelike experience',
        content: 'Hades combines tight gameplay mechanics with an incredible story. Every run feels meaningful and the art style is absolutely gorgeous.',
        playTime: 156,
        completedGame: true,
        recommendToFriends: true,
        pros: ['Perfect gameplay loop', 'Amazing art', 'Great story integration'],
        cons: ['Can be repetitive for some'],
        postedAt: new Date('2024-05-18'),
        helpful: 45,
        notHelpful: 2,
        tags: ['Roguelike', 'Indie', 'Action']
      }
    ];

    this.playerProfiles.set('sample-user', sampleProfile);
  }

  private getDifficultyEmoji(difficulty: string): string {
    const emojiMap: Record<string, string> = {
      'casual': '😎',
      'competitive': '🔥',
      'hardcore': '💀'
    };
    return emojiMap[difficulty] || '🎮';
  }

  private getPlatformEmoji(platform: string): string {
    const emojiMap: Record<string, string> = {
      'PC': '💻',
      'PlayStation': '🎮',
      'Xbox': '🎯',
      'Nintendo Switch': '🕹️',
      'Mobile': '📱'
    };
    return emojiMap[platform] || '🎮';
  }

  private getTimeUntilStart(scheduledTime: Date): string {
    const diff = scheduledTime.getTime() - Date.now();
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  }

  private getPlayerRank(userId: string): string {
    // Mock implementation - would calculate actual rank from database
    const ranks = ['Bronze', 'Silver', 'Gold', 'Platinum', 'Diamond', 'Master', 'Grandmaster'];
    return ranks[Math.floor(Math.random() * ranks.length)];
  }

  private getUserRank(userId: string): number {
    // Mock implementation - would get actual rank from database
    return Math.floor(Math.random() * 100) + 1;
  }

  private getUserPoints(userId: string): number {
    // Mock implementation - would get actual points from database
    return Math.floor(Math.random() * 3000) + 500;
  }

  private getMostReviewedGame(): string {
    if (this.gameReviews.length === 0) return 'N/A';
    const gameCounts: Record<string, number> = {};
    
    this.gameReviews.forEach(review => {
      gameCounts[review.game] = (gameCounts[review.game] || 0) + 1;
    });
    
    const mostReviewed = Object.entries(gameCounts)
      .sort(([,a], [,b]) => b - a)[0];
    
    return mostReviewed ? mostReviewed[0] : 'N/A';
  }
}