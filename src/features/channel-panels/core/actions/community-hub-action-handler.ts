/**
 * Community Hub Action Handler
 * 
 * Handles community-related actions:
 * - View community guidelines
 * - Browse upcoming events
 * - Check leaderboard rankings
 * - Submit feedback
 */

import { Injectable } from '@nestjs/common';
import { Embed<PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle, StringSelectMenuBuilder } from 'discord.js';
import { BaseActionHandler } from './base-action-handler';
import { 
  ActionContext, 
  InteractionResult, 
  IContentProvider 
} from '../interfaces/panel-contracts.interface';

export interface CommunityEvent {
  id: string;
  title: string;
  description: string;
  startTime: Date;
  endTime: Date;
  location: string;
  maxParticipants?: number;
  currentParticipants: number;
  organizer: string;
  category: 'gaming' | 'educational' | 'social' | 'competitive';
}

export interface LeaderboardEntry {
  userId: string;
  username: string;
  rank: number;
  score: number;
  level: number;
  badges: string[];
  joinedAt: Date;
}

export interface CommunityFeedback {
  id: string;
  userId: string;
  category: 'suggestion' | 'bug' | 'feature' | 'other';
  subject: string;
  content: string;
  status: 'pending' | 'reviewed' | 'implemented' | 'declined';
  submittedAt: Date;
}

@Injectable()
export class CommunityHubActionHandler extends BaseActionHandler {
  readonly handlerId = 'community-hub-handler';
  readonly supportedPanelTypes = ['community'];
  readonly supportedActions = [
    'view_guidelines',
    'browse_events',
    'view_leaderboard',
    'submit_feedback',
    'join_event',
    'leave_event',
    'view_my_feedback',
    'report_issue',
    'view_tasks',
    'create_task'
  ];

  constructor(
    private readonly eventsContentProvider: IContentProvider<CommunityEvent[]>,
    private readonly leaderboardContentProvider: IContentProvider<LeaderboardEntry[]>,
    private readonly feedbackContentProvider: IContentProvider<CommunityFeedback[]>
  ) {
    super();
  }

  protected async executeAction(context: ActionContext): Promise<InteractionResult> {
    const { action, interactionData } = context;

    switch (action.actionId) {
      case 'view_guidelines':
        return this.handleViewGuidelines(context);
      
      case 'browse_events':
        return this.handleBrowseEvents(context);
      
      case 'view_leaderboard':
        return this.handleViewLeaderboard(context);
      
      case 'submit_feedback':
        return this.handleSubmitFeedback(context);
      
      case 'join_event':
        return this.handleJoinEvent(context, interactionData as { eventId: string });
      
      case 'leave_event':
        return this.handleLeaveEvent(context, interactionData as { eventId: string });
      
      case 'view_my_feedback':
        return this.handleViewMyFeedback(context);
      
      case 'report_issue':
        return this.handleReportIssue(context);
      
      case 'view_tasks':
        return this.handleViewTasks(context);
      
      case 'create_task':
        return this.handleCreateTask(context);
      
      default:
        return this.createErrorResult(`Unknown action: ${action.actionId}`);
    }
  }

  /**
   * Handle viewing community guidelines
   */
  private async handleViewGuidelines(context: ActionContext): Promise<InteractionResult> {
    return this.createSuccessResult(
      this.createGuidelinesResponse()
    );
  }

  /**
   * Handle browsing upcoming events
   */
  private async handleBrowseEvents(context: ActionContext): Promise<InteractionResult> {
    const { userContext } = context;

    try {
      const eventsRequest = {
        contentType: 'upcoming-events',
        parameters: { limit: 10, includeUserParticipation: true, userId: userContext.userId },
        userContext,
        cacheStrategy: 'memory' as const,
        freshnessTolerance: 300 // 5 minutes
      };

      const eventsResponse = await this.eventsContentProvider.getContent(eventsRequest);
      const events = eventsResponse.data;

      return this.createSuccessResult(
        this.createEventsResponse(events, userContext.userId)
      );

    } catch (error) {
      this.logger.error('Failed to get events:', error);
      return this.createErrorResult('Failed to load events. Please try again.');
    }
  }

  /**
   * Handle viewing leaderboard
   */
  private async handleViewLeaderboard(context: ActionContext): Promise<InteractionResult> {
    const { userContext } = context;

    try {
      const leaderboardRequest = {
        contentType: 'community-leaderboard',
        parameters: { limit: 20, includeUserRank: true, userId: userContext.userId },
        userContext,
        cacheStrategy: 'memory' as const,
        freshnessTolerance: 600 // 10 minutes
      };

      const leaderboardResponse = await this.leaderboardContentProvider.getContent(leaderboardRequest);
      const entries = leaderboardResponse.data;

      return this.createSuccessResult(
        this.createLeaderboardResponse(entries, userContext.userId)
      );

    } catch (error) {
      this.logger.error('Failed to get leaderboard:', error);
      return this.createErrorResult('Failed to load leaderboard. Please try again.');
    }
  }

  /**
   * Handle submitting feedback
   */
  private async handleSubmitFeedback(context: ActionContext): Promise<InteractionResult> {
    return this.createSuccessResult(
      this.createFeedbackFormResponse()
    );
  }

  /**
   * Handle joining an event
   */
  private async handleJoinEvent(
    context: ActionContext, 
    data: { eventId: string }
  ): Promise<InteractionResult> {
    const { userContext } = context;
    const { eventId } = data;

    try {
      // Get event details
      const event = await this.getEvent(eventId);
      if (!event) {
        return this.createErrorResult('Event not found.');
      }

      // Check if event is full
      if (event.maxParticipants && event.currentParticipants >= event.maxParticipants) {
        return this.createErrorResult('This event is full.');
      }

      // Check if user is already participating
      const isParticipating = await this.isUserParticipatingInEvent(userContext.userId, eventId);
      if (isParticipating) {
        return this.createErrorResult('You are already participating in this event.');
      }

      // Add user to event
      await this.addUserToEvent(userContext.userId, eventId);

      return this.createSuccessResult(
        this.createEventJoinSuccessResponse(event),
        { joinedEvents: [eventId] }
      );

    } catch (error) {
      this.logger.error('Failed to join event:', error);
      return this.createErrorResult('Failed to join event. Please try again.');
    }
  }

  /**
   * Handle leaving an event
   */
  private async handleLeaveEvent(
    context: ActionContext, 
    data: { eventId: string }
  ): Promise<InteractionResult> {
    const { userContext } = context;
    const { eventId } = data;

    try {
      const event = await this.getEvent(eventId);
      if (!event) {
        return this.createErrorResult('Event not found.');
      }

      const isParticipating = await this.isUserParticipatingInEvent(userContext.userId, eventId);
      if (!isParticipating) {
        return this.createErrorResult('You are not participating in this event.');
      }

      await this.removeUserFromEvent(userContext.userId, eventId);

      return this.createSuccessResult(
        this.createEventLeaveSuccessResponse(event),
        { leftEvents: [eventId] }
      );

    } catch (error) {
      this.logger.error('Failed to leave event:', error);
      return this.createErrorResult('Failed to leave event. Please try again.');
    }
  }

  /**
   * Handle viewing user's feedback
   */
  private async handleViewMyFeedback(context: ActionContext): Promise<InteractionResult> {
    const { userContext } = context;

    try {
      const feedbackRequest = {
        contentType: 'user-feedback',
        parameters: { userId: userContext.userId },
        userContext,
        cacheStrategy: 'memory' as const,
        freshnessTolerance: 300
      };

      const feedbackResponse = await this.feedbackContentProvider.getContent(feedbackRequest);
      const feedback = feedbackResponse.data;

      return this.createSuccessResult(
        this.createMyFeedbackResponse(feedback)
      );

    } catch (error) {
      this.logger.error('Failed to get user feedback:', error);
      return this.createErrorResult('Failed to load your feedback. Please try again.');
    }
  }

  /**
   * Handle reporting an issue
   */
  private async handleReportIssue(context: ActionContext): Promise<InteractionResult> {
    return this.createSuccessResult(
      this.createIssueReportResponse()
    );
  }

  // ============================================================================
  // RESPONSE BUILDERS
  // ============================================================================

  private createGuidelinesResponse() {
    const embed = new EmbedBuilder()
      .setTitle('📋 Community Guidelines')
      .setDescription('Welcome to our community! Please follow these guidelines to ensure a positive experience for everyone.')
      .addFields(
        { 
          name: '🤝 Respect & Kindness', 
          value: '• Treat all members with respect\n• Be patient with newcomers\n• Help others when you can', 
          inline: false 
        },
        { 
          name: '💬 Communication', 
          value: '• Keep conversations on-topic\n• Use appropriate channels\n• No spam or excessive self-promotion', 
          inline: false 
        },
        { 
          name: '🔒 Privacy & Safety', 
          value: '• No sharing personal information\n• Report inappropriate behavior\n• Follow Discord Terms of Service', 
          inline: false 
        },
        { 
          name: '🎯 Quality Content', 
          value: '• Share valuable insights\n• Ask thoughtful questions\n• Contribute meaningfully to discussions', 
          inline: false 
        }
      )
      .setColor('#3b82f6')
      .setFooter({ text: 'Last updated: ' + new Date().toLocaleDateString() });

    const row = new ActionRowBuilder<ButtonBuilder>()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('community_report_issue')
          .setLabel('Report Issue')
          .setStyle(ButtonStyle.Danger),
        new ButtonBuilder()
          .setCustomId('community_submit_feedback')
          .setLabel('Give Feedback')
          .setStyle(ButtonStyle.Secondary)
      );

    return { embeds: [embed], components: [row] };
  }

  private createEventsResponse(events: CommunityEvent[], userId: string) {
    const embed = new EmbedBuilder()
      .setTitle('🎉 Upcoming Events')
      .setColor('#8b5cf6');

    if (events.length === 0) {
      embed.setDescription('No upcoming events at the moment. Check back soon!');
    } else {
      const eventFields = events.slice(0, 5).map((event: any) => {
        const spotsLeft = event.maxParticipants ? 
          `${event.maxParticipants - event.currentParticipants} spots left` : 
          'Unlimited spots';
        
        return {
          name: `${this.getCategoryEmoji(event.category)} ${event.title}`,
          value: `📅 ${event.startTime.toLocaleDateString()} at ${event.startTime.toLocaleTimeString()}\n📍 ${event.location}\n👥 ${event.currentParticipants} participants • ${spotsLeft}`,
          inline: false
        };
      });

      embed.addFields(eventFields);
    }

    const components = [];
    
    if (events.length > 0) {
      const eventSelectMenu = new StringSelectMenuBuilder()
        .setCustomId('community_event_actions')
        .setPlaceholder('Select an event to interact with')
        .addOptions(
          events.slice(0, 10).map((event: any) => ({
            label: event.title,
            value: `join_${event.id}`,
            description: `${event.startTime.toLocaleDateString()} - ${event.currentParticipants} participants`,
            emoji: this.getCategoryEmoji(event.category)
          }))
        );

      components.push(new ActionRowBuilder<StringSelectMenuBuilder>().addComponents(eventSelectMenu));
    }

    const buttonRow = new ActionRowBuilder<ButtonBuilder>()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('community_view_leaderboard')
          .setLabel('View Leaderboard')
          .setStyle(ButtonStyle.Primary),
        new ButtonBuilder()
          .setCustomId('community_submit_feedback')
          .setLabel('Suggest Event')
          .setStyle(ButtonStyle.Secondary)
      );

    components.push(buttonRow);

    return { embeds: [embed], components };
  }

  private createLeaderboardResponse(entries: LeaderboardEntry[], userId: string) {
    const embed = new EmbedBuilder()
      .setTitle('🏆 Community Leaderboard')
      .setDescription('Top community members based on participation and contributions')
      .setColor('#f59e0b');

    if (entries.length === 0) {
      embed.addFields({ name: 'No data', value: 'Leaderboard data is not available yet.' });
    } else {
      const topEntries = entries.slice(0, 10);
      const leaderboardText = topEntries.map((entry: any) => {
        const medal = this.getRankMedal(entry.rank);
        const badgeText = entry.badges.length > 0 ? ` ${entry.badges.join(' ')}` : '';
        return `${medal} **${entry.username}** - Level ${entry.level} (${entry.score} pts)${badgeText}`;
      }).join('\n');

      embed.addFields({ name: 'Top Members', value: leaderboardText, inline: false });

      // Find user's rank if not in top 10
      const userEntry = entries.find(entry => entry.userId === userId);
      if (userEntry && userEntry.rank > 10) {
        embed.addFields({ 
          name: 'Your Rank', 
          value: `#${userEntry.rank} - Level ${userEntry.level} (${userEntry.score} pts)`, 
          inline: false 
        });
      }
    }

    const row = new ActionRowBuilder<ButtonBuilder>()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('community_browse_events')
          .setLabel('Join Events')
          .setStyle(ButtonStyle.Success),
        new ButtonBuilder()
          .setCustomId('community_view_guidelines')
          .setLabel('Guidelines')
          .setStyle(ButtonStyle.Secondary)
      );

    return { embeds: [embed], components: [row] };
  }

  private createFeedbackFormResponse() {
    const embed = new EmbedBuilder()
      .setTitle('💬 Submit Feedback')
      .setDescription('Help us improve the community! Your feedback is valuable to us.')
      .addFields(
        { name: 'What can you provide feedback on?', value: '• New feature suggestions\n• Bug reports\n• Community improvements\n• Event ideas\n• General suggestions', inline: false },
        { name: 'How to submit?', value: 'Choose a category below and describe your feedback in detail. We review all submissions!', inline: false }
      )
      .setColor('#10b981');

    const categorySelect = new StringSelectMenuBuilder()
      .setCustomId('community_feedback_category')
      .setPlaceholder('Select feedback category')
      .addOptions([
        { label: 'Feature Suggestion', value: 'feature', emoji: '💡' },
        { label: 'Bug Report', value: 'bug', emoji: '🐛' },
        { label: 'Community Improvement', value: 'suggestion', emoji: '✨' },
        { label: 'Other', value: 'other', emoji: '💭' }
      ]);

    const row1 = new ActionRowBuilder<StringSelectMenuBuilder>().addComponents(categorySelect);

    const row2 = new ActionRowBuilder<ButtonBuilder>()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('community_view_my_feedback')
          .setLabel('My Feedback')
          .setStyle(ButtonStyle.Secondary)
      );

    return { embeds: [embed], components: [row1, row2] };
  }

  private createEventJoinSuccessResponse(event: CommunityEvent) {
    const embed = new EmbedBuilder()
      .setTitle('✅ Successfully Joined Event!')
      .setDescription(`You have joined **${event.title}**`)
      .addFields(
        { name: '📅 Date & Time', value: `${event.startTime.toLocaleDateString()} at ${event.startTime.toLocaleTimeString()}`, inline: true },
        { name: '📍 Location', value: event.location, inline: true },
        { name: '👥 Participants', value: `${event.currentParticipants + 1}${event.maxParticipants ? `/${event.maxParticipants}` : ''}`, inline: true }
      )
      .setColor('#22c55e')
      .setTimestamp();

    const row = new ActionRowBuilder<ButtonBuilder>()
      .addComponents(
        new ButtonBuilder()
          .setCustomId(`community_leave_event|${event.id}`)
          .setLabel('Leave Event')
          .setStyle(ButtonStyle.Danger),
        new ButtonBuilder()
          .setCustomId('community_browse_events')
          .setLabel('View Other Events')
          .setStyle(ButtonStyle.Secondary)
      );

    return { embeds: [embed], components: [row] };
  }

  private createEventLeaveSuccessResponse(event: CommunityEvent) {
    const embed = new EmbedBuilder()
      .setTitle('❌ Left Event')
      .setDescription(`You have left **${event.title}**`)
      .setColor('#ef4444')
      .setTimestamp();

    const row = new ActionRowBuilder<ButtonBuilder>()
      .addComponents(
        new ButtonBuilder()
          .setCustomId(`community_join_event|${event.id}`)
          .setLabel('Rejoin Event')
          .setStyle(ButtonStyle.Success),
        new ButtonBuilder()
          .setCustomId('community_browse_events')
          .setLabel('Browse Events')
          .setStyle(ButtonStyle.Primary)
      );

    return { embeds: [embed], components: [row] };
  }

  private createMyFeedbackResponse(feedback: CommunityFeedback[]) {
    const embed = new EmbedBuilder()
      .setTitle('📝 Your Feedback')
      .setColor('#6366f1');

    if (feedback.length === 0) {
      embed.setDescription('You haven\'t submitted any feedback yet.');
    } else {
      const feedbackFields = feedback.slice(0, 5).map((item: any) => ({
        name: `${this.getStatusEmoji(item.status)} ${item.subject}`,
        value: `**Category:** ${item.category}\n**Status:** ${item.status}\n**Submitted:** ${item.submittedAt.toLocaleDateString()}`,
        inline: true
      }));

      embed.addFields(feedbackFields);
    }

    const row = new ActionRowBuilder<ButtonBuilder>()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('community_submit_feedback')
          .setLabel('Submit New Feedback')
          .setStyle(ButtonStyle.Primary)
      );

    return { embeds: [embed], components: [row] };
  }

  private createIssueReportResponse() {
    const embed = new EmbedBuilder()
      .setTitle('🚨 Report an Issue')
      .setDescription('If you\'ve encountered a problem or witnessed inappropriate behavior, please report it here.')
      .addFields(
        { name: 'What to report?', value: '• Inappropriate behavior\n• Technical issues\n• Spam or harassment\n• Rule violations', inline: false },
        { name: 'Information to include:', value: '• What happened?\n• When did it occur?\n• Who was involved? (if applicable)\n• Screenshots (if relevant)', inline: false }
      )
      .setColor('#ef4444');

    const row = new ActionRowBuilder<ButtonBuilder>()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('community_urgent_report')
          .setLabel('Report Now')
          .setStyle(ButtonStyle.Danger),
        new ButtonBuilder()
          .setCustomId('community_view_guidelines')
          .setLabel('View Guidelines')
          .setStyle(ButtonStyle.Secondary)
      );

    return { embeds: [embed], components: [row] };
  }

  // ============================================================================
  // HELPER METHODS
  // ============================================================================

  private getCategoryEmoji(category: string): string {
    switch (category) {
      case 'gaming': return '🎮';
      case 'educational': return '📚';
      case 'social': return '🎉';
      case 'competitive': return '🏆';
      default: return '📅';
    }
  }

  private getRankMedal(rank: number): string {
    switch (rank) {
      case 1: return '🥇';
      case 2: return '🥈';
      case 3: return '🥉';
      default: return `#${rank}`;
    }
  }

  private getStatusEmoji(status: string): string {
    switch (status) {
      case 'pending': return '⏳';
      case 'reviewed': return '👀';
      case 'implemented': return '✅';
      case 'declined': return '❌';
      default: return '📝';
    }
  }

  private async getEvent(eventId: string): Promise<CommunityEvent | null> {
    // Mock implementation - would integrate with actual data service
    return null;
  }

  private async isUserParticipatingInEvent(userId: string, eventId: string): Promise<boolean> {
    // Mock implementation - would check actual participation data
    return false;
  }

  private async addUserToEvent(userId: string, eventId: string): Promise<void> {
    // Mock implementation - would add user to event
    this.logger.debug(`Adding user ${userId} to event ${eventId}`);
  }

  private async removeUserFromEvent(userId: string, eventId: string): Promise<void> {
    // Mock implementation - would remove user from event
    this.logger.debug(`Removing user ${userId} from event ${eventId}`);
  }

  /**
   * Handle viewing tasks for the channel
   */
  private async handleViewTasks(context: ActionContext): Promise<InteractionResult> {
    const { userContext } = context;
    
    // Simple task list stored in session data
    const tasks = context.currentState.sessionData.tasks as any[] || [];
    
    const embed = new EmbedBuilder()
      .setTitle('📋 Channel Tasks')
      .setDescription(`Tasks for ${userContext.channelId}`)
      .setColor(0x00AE86);
    
    if (tasks.length === 0) {
      embed.addFields([{
        name: 'No Tasks',
        value: 'No tasks found. Create your first task!',
        inline: false
      }]);
    } else {
      const taskList = tasks.slice(0, 10).map((task, i) => 
        `${i + 1}. ${task.title} - ${task.status || 'pending'}`
      ).join('\n');
      
      embed.addFields([{
        name: `Tasks (${tasks.length})`,
        value: taskList,
        inline: false
      }]);
    }

    const components = new ActionRowBuilder<ButtonBuilder>()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('create_task')
          .setLabel('Create Task')
          .setStyle(ButtonStyle.Success)
          .setEmoji('➕')
      );

    return this.createSuccessResult({
      embeds: [embed],
      components: [components],
      ephemeral: true
    });
  }

  /**
   * Handle creating a new task
   */
  private async handleCreateTask(context: ActionContext): Promise<InteractionResult> {
    const { userContext, interactionData } = context;
    
    // Get task title from interaction data or use default
    const taskTitle = (interactionData as any)?.title || `New task in ${userContext.channelId}`;
    
    // Get existing tasks or initialize empty array
    const tasks = context.currentState.sessionData.tasks as any[] || [];
    
    // Create new task
    const newTask = {
      id: `task_${Date.now()}`,
      title: taskTitle,
      status: 'pending',
      createdBy: userContext.userId,
      createdAt: new Date().toISOString(),
      channelId: userContext.channelId
    };
    
    // Add to tasks list
    tasks.push(newTask);
    
    const embed = new EmbedBuilder()
      .setTitle('✅ Task Created')
      .setDescription(`Created task: ${taskTitle}`)
      .setColor(0x00FF00)
      .addFields([
        { name: 'Task ID', value: newTask.id, inline: true },
        { name: 'Status', value: newTask.status, inline: true },
        { name: 'Created By', value: `<@${userContext.userId}>`, inline: true }
      ]);

    return this.createSuccessResult({
      embeds: [embed],
      components: [],
      ephemeral: true
    }, { tasks });
  }
}