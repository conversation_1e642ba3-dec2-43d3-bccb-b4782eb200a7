import { Injectable, Logger } from '@nestjs/common';
import { ButtonInteraction, StringSelectMenuInteraction, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, StringSelectMenuBuilder, StringSelectMenuOptionBuilder } from 'discord.js';

export interface CommunityEvent {
  id: string;
  title: string;
  description: string;
  startDate: Date;
  endDate?: Date;
  type: 'challenge' | 'workshop' | 'meetup' | 'competition';
  maxParticipants?: number;
  currentParticipants: number;
  tags: string[];
}

export interface LeaderboardEntry {
  userId: string;
  username: string;
  points: number;
  level: number;
  badges: string[];
  monthlyRank: number;
  allTimeRank: number;
}

export interface FeedbackSubmission {
  id: string;
  userId: string;
  type: 'suggestion' | 'bug-report' | 'feature-request' | 'general';
  title: string;
  description: string;
  status: 'pending' | 'reviewed' | 'in-progress' | 'completed' | 'rejected';
  createdAt: Date;
  votes: number;
}

@Injectable()
export class CommunityHubActionsHandler {
  private readonly logger = new Logger(CommunityHubActionsHandler.name);
  private events: CommunityEvent[] = [];
  private leaderboard: LeaderboardEntry[] = [];
  private feedbackSubmissions: FeedbackSubmission[] = [];
  private userEventParticipation = new Map<string, string[]>();

  constructor() {
    this.initializeSampleData();
  }

  async handleGuidelinesAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {
      const embed = new EmbedBuilder()
        .setColor(0x3498db)
        .setTitle('📋 Community Guidelines')
        .setDescription('Welcome to our community! Please follow these guidelines to ensure a positive experience for everyone.')
        .addFields([
          {
            name: '🤝 Be Respectful',
            value: '• Treat all members with respect and kindness\n• No harassment, discrimination, or hate speech\n• Keep discussions constructive and professional',
            inline: false
          },
          {
            name: '💬 Communication',
            value: '• Use appropriate channels for different topics\n• Keep conversations on-topic\n• Use clear and concise language\n• No spam or excessive self-promotion',
            inline: false
          },
          {
            name: '🚀 Participation',
            value: '• Share knowledge and help others learn\n• Participate in community events and challenges\n• Provide constructive feedback\n• Celebrate others\' achievements',
            inline: false
          },
          {
            name: '⚠️ Prohibited Content',
            value: '• No NSFW or inappropriate content\n• No sharing of illegal or copyrighted material\n• No doxxing or sharing personal information\n• No malicious links or files',
            inline: false
          }
        ])
        .setFooter({ text: 'By participating, you agree to follow these guidelines' })
        .setTimestamp();

      const row = new ActionRowBuilder<ButtonBuilder>()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('community_guidelines_detailed')
            .setLabel('📖 Detailed Rules')
            .setStyle(ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('community_guidelines_report')
            .setLabel('🚨 Report Violation')
            .setStyle(ButtonStyle.Danger),
          new ButtonBuilder()
            .setCustomId('community_back')
            .setLabel('🔙 Back to Hub')
            .setStyle(ButtonStyle.Primary)
        );

      await interaction.editReply({
        embeds: [embed],
        components: [row],
        content: null
      });

      this.logger.log(`User ${interaction.user.id} viewed community guidelines`);
    } catch (error) {
      this.logger.error('Failed to handle guidelines action:', error);
      await interaction.editReply({
        content: '❌ Failed to load community guidelines. Please try again.'
      });
    }
  }

  async handleEventsAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {
      const upcomingEvents = this.events
        .filter((event: any) => event.startDate > new Date())
        .sort((a, b) => a.startDate.getTime() - b.startDate.getTime())
        .slice(0, 5);

      const embed = new EmbedBuilder()
        .setColor(0x2ecc71)
        .setTitle('🎉 Community Events')
        .setDescription('Join our exciting community events and connect with fellow members!')
        .setTimestamp();

      if (upcomingEvents.length === 0) {
        embed.addFields([{
          name: '📅 No Upcoming Events',
          value: 'Check back soon for new events! In the meantime, participate in ongoing challenges and discussions.',
          inline: false
        }]);
      } else {
        for (const event of upcomingEvents) {
          const spotsLeft = event.maxParticipants ? event.maxParticipants - event.currentParticipants : null;
          const eventEmoji = this.getEventTypeEmoji(event.type);
          
          embed.addFields([{
            name: `${eventEmoji} ${event.title}`,
            value: `${event.description}\n` +
                   `📅 **Date:** ${event.startDate.toLocaleDateString()} at ${event.startDate.toLocaleTimeString()}\n` +
                   `👥 **Participants:** ${event.currentParticipants}${event.maxParticipants ? `/${event.maxParticipants}` : ''}\n` +
                   `${spotsLeft !== null ? `🎯 **Spots Left:** ${spotsLeft}\n` : ''}` +
                   `🏷️ **Tags:** ${event.tags.join(', ')}`,
            inline: false
          }]);
        }
      }

      const row = new ActionRowBuilder<ButtonBuilder>()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('community_events_join')
            .setLabel('✅ Join Event')
            .setStyle(ButtonStyle.Success)
            .setDisabled(upcomingEvents.length === 0),
          new ButtonBuilder()
            .setCustomId('community_events_create')
            .setLabel('➕ Suggest Event')
            .setStyle(ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('community_events_calendar')
            .setLabel('📅 Full Calendar')
            .setStyle(ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('community_back')
            .setLabel('🔙 Back')
            .setStyle(ButtonStyle.Primary)
        );

      await interaction.editReply({
        embeds: [embed],
        components: [row],
        content: null
      });

      this.logger.log(`User ${interaction.user.id} viewed community events`);
    } catch (error) {
      this.logger.error('Failed to handle events action:', error);
      await interaction.editReply({
        content: '❌ Failed to load community events. Please try again.'
      });
    }
  }

  async handleLeaderboardAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {
      const topUsers = this.leaderboard
        .sort((a, b) => b.points - a.points)
        .slice(0, 10);

      const currentUser = this.leaderboard.find(entry => entry.userId === interaction.user.id);

      const embed = new EmbedBuilder()
        .setColor(0xf1c40f)
        .setTitle('🏆 Community Leaderboard')
        .setDescription('Top contributors in our community this month!')
        .setTimestamp();

      if (topUsers.length === 0) {
        embed.addFields([{
          name: '📊 No Data Available',
          value: 'Leaderboard data is being collected. Start participating to see your ranking!',
          inline: false
        }]);
      } else {
        let leaderboardText = '';
        for (let i = 0; i < topUsers.length; i++) {
          const user = topUsers[i];
          const rankEmoji = this.getRankEmoji(i + 1);
          const badges = user.badges.slice(0, 3).join(' ');
          leaderboardText += `${rankEmoji} **${user.username}** - ${user.points} pts (Lvl ${user.level}) ${badges}\n`;
        }

        embed.addFields([
          {
            name: '🎯 Top Contributors',
            value: leaderboardText,
            inline: false
          }
        ]);

        if (currentUser && !topUsers.includes(currentUser)) {
          embed.addFields([
            {
              name: '📈 Your Stats',
              value: `**Rank:** #${currentUser.monthlyRank} (Monthly) | #${currentUser.allTimeRank} (All Time)\n` +
                     `**Points:** ${currentUser.points} | **Level:** ${currentUser.level}\n` +
                     `**Badges:** ${currentUser.badges.join(' ') || 'No badges yet'}`,
              inline: false
            }
          ]);
        }

        embed.addFields([
          {
            name: '💎 How to Earn Points',
            value: '• Help other members (+10 pts)\n• Share resources (+15 pts)\n• Complete challenges (+25 pts)\n• Host events (+50 pts)\n• Weekly activity bonus (+5 pts)',
            inline: false
          }
        ]);
      }

      const row = new ActionRowBuilder<ButtonBuilder>()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('community_leaderboard_monthly')
            .setLabel('📅 Monthly')
            .setStyle(ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('community_leaderboard_alltime')
            .setLabel('🏆 All Time')
            .setStyle(ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('community_leaderboard_badges')
            .setLabel('🎖️ Badges')
            .setStyle(ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('community_back')
            .setLabel('🔙 Back')
            .setStyle(ButtonStyle.Primary)
        );

      await interaction.editReply({
        embeds: [embed],
        components: [row],
        content: null
      });

      this.logger.log(`User ${interaction.user.id} viewed community leaderboard`);
    } catch (error) {
      this.logger.error('Failed to handle leaderboard action:', error);
      await interaction.editReply({
        content: '❌ Failed to load leaderboard. Please try again.'
      });
    }
  }

  async handleFeedbackAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {
      const embed = new EmbedBuilder()
        .setColor(0x9b59b6)
        .setTitle('💬 Community Feedback')
        .setDescription('Your voice matters! Help us improve the community by sharing your feedback.')
        .addFields([
          {
            name: '💡 Suggestion',
            value: 'Share ideas for new features or improvements',
            inline: true
          },
          {
            name: '🐛 Bug Report',
            value: 'Report issues or problems you\'ve encountered',
            inline: true
          },
          {
            name: '🚀 Feature Request',
            value: 'Request new functionality or tools',
            inline: true
          },
          {
            name: '💬 General Feedback',
            value: 'Share your overall thoughts and experiences',
            inline: true
          },
          {
            name: '📊 Recent Submissions',
            value: `${this.feedbackSubmissions.length} total submissions\n${this.feedbackSubmissions.filter((f: any) => f.status === 'completed').length} implemented\n${this.feedbackSubmissions.filter((f: any) => f.status === 'in-progress').length} in progress`,
            inline: true
          },
          {
            name: '🎯 Response Time',
            value: 'We typically respond within 24-48 hours',
            inline: true
          }
        ])
        .setFooter({ text: 'All feedback is reviewed by our community team' })
        .setTimestamp();

      const selectMenu = new StringSelectMenuBuilder()
        .setCustomId('community_feedback_type')
        .setPlaceholder('Choose feedback type...')
        .addOptions(
          new StringSelectMenuOptionBuilder()
            .setLabel('💡 Make a Suggestion')
            .setDescription('Share ideas for improvements')
            .setValue('suggestion')
            .setEmoji('💡'),
          new StringSelectMenuOptionBuilder()
            .setLabel('🐛 Report a Bug')
            .setDescription('Report issues or problems')
            .setValue('bug-report')
            .setEmoji('🐛'),
          new StringSelectMenuOptionBuilder()
            .setLabel('🚀 Request a Feature')
            .setDescription('Request new functionality')
            .setValue('feature-request')
            .setEmoji('🚀'),
          new StringSelectMenuOptionBuilder()
            .setLabel('💬 General Feedback')
            .setDescription('Share your thoughts')
            .setValue('general')
            .setEmoji('💬')
        );

      const selectRow = new ActionRowBuilder<StringSelectMenuBuilder>()
        .addComponents(selectMenu);

      const buttonRow = new ActionRowBuilder<ButtonBuilder>()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('community_feedback_view')
            .setLabel('📋 View My Feedback')
            .setStyle(ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('community_feedback_popular')
            .setLabel('🔥 Popular Suggestions')
            .setStyle(ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('community_back')
            .setLabel('🔙 Back')
            .setStyle(ButtonStyle.Primary)
        );

      await interaction.editReply({
        embeds: [embed],
        components: [selectRow, buttonRow],
        content: null
      });

      this.logger.log(`User ${interaction.user.id} accessed feedback system`);
    } catch (error) {
      this.logger.error('Failed to handle feedback action:', error);
      await interaction.editReply({
        content: '❌ Failed to load feedback system. Please try again.'
      });
    }
  }

  async handleEventJoinAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    const userId = interaction.user.id;
    
    try {
      const upcomingEvents = this.events.filter((event: any) => event.startDate > new Date());
      
      if (upcomingEvents.length === 0) {
        await interaction.editReply({
          content: '❌ No upcoming events available to join.'
        });
        return;
      }

      // For this example, join the first available event
      const eventToJoin = upcomingEvents[0];
      const userParticipations = this.userEventParticipation.get(userId) || [];
      
      if (userParticipations.includes(eventToJoin.id)) {
        await interaction.editReply({
          content: `✅ You are already registered for "${eventToJoin.title}"!`
        });
        return;
      }

      if (eventToJoin.maxParticipants && eventToJoin.currentParticipants >= eventToJoin.maxParticipants) {
        await interaction.editReply({
          content: `❌ Sorry, "${eventToJoin.title}" is full. Please try again later or join a different event.`
        });
        return;
      }

      // Register user for event
      userParticipations.push(eventToJoin.id);
      this.userEventParticipation.set(userId, userParticipations);
      eventToJoin.currentParticipants++;

      const embed = new EmbedBuilder()
        .setColor(0x2ecc71)
        .setTitle('🎉 Successfully Registered!')
        .setDescription(`You have been registered for **${eventToJoin.title}**`)
        .addFields([
          { name: '📅 Event Date', value: eventToJoin.startDate.toLocaleDateString(), inline: true },
          { name: '⏰ Event Time', value: eventToJoin.startDate.toLocaleTimeString(), inline: true },
          { name: '👥 Participants', value: `${eventToJoin.currentParticipants}${eventToJoin.maxParticipants ? `/${eventToJoin.maxParticipants}` : ''}`, inline: true }
        ])
        .setFooter({ text: 'You will receive a reminder before the event starts' })
        .setTimestamp();

      const row = new ActionRowBuilder<ButtonBuilder>()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('community_events_calendar')
            .setLabel('📅 View Calendar')
            .setStyle(ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('community_events_leave')
            .setLabel('❌ Leave Event')
            .setStyle(ButtonStyle.Danger),
          new ButtonBuilder()
            .setCustomId('community_back')
            .setLabel('🔙 Back')
            .setStyle(ButtonStyle.Primary)
        );

      await interaction.editReply({
        embeds: [embed],
        components: [row],
        content: null
      });

      this.logger.log(`User ${userId} joined event ${eventToJoin.id}`);
    } catch (error) {
      this.logger.error('Failed to handle event join action:', error);
      await interaction.editReply({
        content: '❌ Failed to join event. Please try again.'
      });
    }
  }

  private initializeSampleData(): void {
    // Sample events
    this.events = [
      {
        id: 'event-1',
        title: 'Weekly Coding Challenge',
        description: 'Join our weekly coding challenge and showcase your skills!',
        startDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // 2 days from now
        type: 'challenge',
        maxParticipants: 50,
        currentParticipants: 23,
        tags: ['coding', 'challenge', 'competition']
      },
      {
        id: 'event-2',
        title: 'AI Workshop: Building Chatbots',
        description: 'Learn how to build intelligent chatbots using modern AI frameworks.',
        startDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5 days from now
        endDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000 + 2 * 60 * 60 * 1000), // 2 hours later
        type: 'workshop',
        maxParticipants: 30,
        currentParticipants: 12,
        tags: ['ai', 'workshop', 'chatbots', 'learning']
      }
    ];

    // Sample leaderboard
    this.leaderboard = [
      {
        userId: 'user1',
        username: 'CodeMaster',
        points: 1250,
        level: 15,
        badges: ['🏆', '🔥', '💎'],
        monthlyRank: 1,
        allTimeRank: 3
      },
      {
        userId: 'user2',
        username: 'AIExplorer',
        points: 1100,
        level: 13,
        badges: ['🚀', '🧠', '⭐'],
        monthlyRank: 2,
        allTimeRank: 5
      },
      {
        userId: 'user3',
        username: 'DevHelper',
        points: 950,
        level: 11,
        badges: ['🤝', '💡'],
        monthlyRank: 3,
        allTimeRank: 8
      }
    ];

    // Sample feedback submissions
    this.feedbackSubmissions = [
      {
        id: 'feedback-1',
        userId: 'user1',
        type: 'suggestion',
        title: 'Add more coding challenges',
        description: 'It would be great to have daily coding challenges instead of weekly ones.',
        status: 'in-progress',
        createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
        votes: 15
      },
      {
        id: 'feedback-2',
        userId: 'user2',
        type: 'feature-request',
        title: 'Integration with GitHub',
        description: 'Allow users to connect their GitHub profiles to showcase projects.',
        status: 'completed',
        createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
        votes: 28
      }
    ];
  }

  private getEventTypeEmoji(type: string): string {
    const emojiMap: Record<string, string> = {
      'challenge': '🏆',
      'workshop': '🎓',
      'meetup': '🤝',
      'competition': '⚡'
    };
    return emojiMap[type] || '📅';
  }

  private getRankEmoji(rank: number): string {
    if (rank === 1) return '🥇';
    if (rank === 2) return '🥈';
    if (rank === 3) return '🥉';
    return `${rank}.`;
  }
}