import { Injectable, Logger } from '@nestjs/common';
import { ButtonInteraction, StringSelectMenuInteraction, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, StringSelectMenuBuilder, StringSelectMenuOptionBuilder } from 'discord.js';
import { SupportDatabaseService, SupportTicketWithResponses, TicketStats } from '../../services/support-database.service';
import { KnowledgeBaseArticle, TroubleshootingGuide, SystemStatus } from '../../../../core/database/entities/support-tickets.entity';

@Injectable()
export class TechnicalSupportActionsDatabaseHandler {
  private readonly logger = new Logger(TechnicalSupportActionsDatabaseHandler.name);

  constructor(private readonly supportDb: SupportDatabaseService) {}

  async handleCreateTicketAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {
      // Ensure user exists in database
      await this.supportDb.ensureUser(interaction.user.id, interaction.user.username);
      
      const userId = interaction.user.id;
      const userTickets = await this.supportDb.getUserTickets(userId, 3);
      const ticketStats = await this.supportDb.getTicketStats(interaction.guildId!);

      const embed = new EmbedBuilder()
        .setColor(0xff6b6b)
        .setTitle('🎫 Support Ticket System')
        .setDescription('Get expert help with technical issues, feature requests, and general questions')
        .addFields([
          {
            name: '🚨 Ticket Categories',
            value: '🐛 **Bug Report** - Software issues and unexpected behavior\n✨ **Feature Request** - Suggest new features or improvements\n👤 **Account Issues** - Login, profile, and account management\n💳 **Billing Support** - Payment and subscription questions\n🔒 **Security Concerns** - Security vulnerabilities and issues\n🔗 **Integration Help** - API and third-party integration support\n❓ **General Questions** - General usage and how-to questions',
            inline: false
          },
          {
            name: '⚡ Priority Levels',
            value: '🔴 **Critical** - System down, security issues (< 1 hour)\n🟠 **High** - Major functionality broken (< 4 hours)\n🟡 **Medium** - Minor bugs, feature requests (< 24 hours)\n🟢 **Low** - Questions, documentation (< 48 hours)',
            inline: false
          }
        ])
        .setTimestamp();

      if (userTickets.length > 0) {
        embed.addFields([
          {
            name: '📊 Your Recent Tickets',
            value: userTickets.map((ticket: any) => {
              const statusEmoji = this.getStatusEmoji(ticket.status);
              const priorityEmoji = this.getPriorityEmoji(ticket.priority);
              return `${statusEmoji} **${ticket.title}** ${priorityEmoji}\n   ${ticket.category} | ${ticket.ticketNumber} | Created: ${ticket.createdAt.toLocaleDateString()}`;
            }).join('\n\n'),
            inline: false
          }
        ]);
      }

      // Add support statistics
      embed.addFields([
        {
          name: '📈 Support Statistics',
          value: `**Your Tickets:** ${userTickets.length} recent\n**Guild Stats:** ${ticketStats.total} total, ${ticketStats.resolved} resolved\n**Success Rate:** ${ticketStats.successRate}%\n**Avg Response Time:** ${ticketStats.avgResponseTime}\n**Team Status:** 🟢 Online (5 agents available)`,
          inline: false
        }
      ]);

      const selectMenu = new StringSelectMenuBuilder()
        .setCustomId('technical_support_ticket_category')
        .setPlaceholder('Select ticket category...')
        .addOptions(
          new StringSelectMenuOptionBuilder()
            .setLabel('🐛 Bug Report')
            .setDescription('Report software issues and bugs')
            .setValue('bug')
            .setEmoji('🐛'),
          new StringSelectMenuOptionBuilder()
            .setLabel('✨ Feature Request')
            .setDescription('Suggest new features or improvements')
            .setValue('feature')
            .setEmoji('✨'),
          new StringSelectMenuOptionBuilder()
            .setLabel('👤 Account Issues')
            .setDescription('Login, profile, and account problems')
            .setValue('account')
            .setEmoji('👤'),
          new StringSelectMenuOptionBuilder()
            .setLabel('💳 Billing Support')
            .setDescription('Payment and subscription questions')
            .setValue('billing')
            .setEmoji('💳'),
          new StringSelectMenuOptionBuilder()
            .setLabel('🔒 Security Concerns')
            .setDescription('Security vulnerabilities and issues')
            .setValue('security')
            .setEmoji('🔒')
        );

      const selectRow = new ActionRowBuilder<StringSelectMenuBuilder>()
        .addComponents(selectMenu);

      const buttonRow = new ActionRowBuilder<ButtonBuilder>()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('technical_support_quick_ticket')
            .setLabel('⚡ Create Quick Ticket')
            .setStyle(ButtonStyle.Success),
          new ButtonBuilder()
            .setCustomId('technical_support_detailed_ticket')
            .setLabel('📝 Detailed Ticket')
            .setStyle(ButtonStyle.Primary),
          new ButtonBuilder()
            .setCustomId('technical_support_my_tickets')
            .setLabel('📁 My Tickets')
            .setStyle(ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('technical_support_back')
            .setLabel('🔙 Back')
            .setStyle(ButtonStyle.Secondary)
        );

      await interaction.editReply({
        embeds: [embed],
        components: [selectRow, buttonRow],
        content: null
      });

      this.logger.log(`User ${userId} accessed ticket creation system`);
    } catch (error) {
      this.logger.error('Failed to handle create ticket action:', error);
      await interaction.editReply({
        content: '❌ Failed to load ticket system. Please try again.'
      });
    }
  }

  async handleKnowledgeBaseAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {
      // Ensure user exists in database
      await this.supportDb.ensureUser(interaction.user.id, interaction.user.username);
      
      const popularArticles = await this.supportDb.getKnowledgeBaseArticles(undefined, 6);

      const embed = new EmbedBuilder()
        .setColor(0x00d4ff)
        .setTitle('📚 Knowledge Base & FAQ')
        .setDescription('Find instant answers to common questions and learn how to use our platform effectively')
        .addFields([
          {
            name: '🔍 Search Categories',
            value: '🤖 **Bot Configuration** - Setup, permissions, and basic configuration\n🔧 **Troubleshooting** - Common issues and solutions\n⚙️ **Integration Guides** - Third-party service integrations\n📊 **Analytics & Reporting** - Data analysis and reporting features\n🔒 **Security & Privacy** - Security settings and privacy controls\n📱 **Mobile & API** - Mobile app and API documentation',
            inline: false
          }
        ])
        .setTimestamp();

      if (popularArticles.length === 0) {
        embed.addFields([{
          name: '📖 Knowledge Base Coming Soon',
          value: 'Our comprehensive knowledge base is being built. In the meantime, feel free to create a support ticket for any questions.',
          inline: false
        }]);
      } else {
        embed.addFields([
          {
            name: '🔥 Popular Articles',
            value: `Showing ${popularArticles.length} most helpful articles`,
            inline: false
          }
        ]);

        for (const article of popularArticles.slice(0, 4)) {
          const difficultyEmoji = this.getDifficultyEmoji(article.difficulty);
          const helpfulPercentage = article.helpful + article.notHelpful > 0 
            ? Math.round((article.helpful / (article.helpful + article.notHelpful)) * 100)
            : 0;
          
          embed.addFields([{
            name: `${difficultyEmoji} ${article.title}`,
            value: `**Category:** ${article.category} > ${article.subcategory}\n` +
                   `👀 **Views:** ${article.views} | 👍 **Helpful:** ${helpfulPercentage}%\n` +
                   `📅 **Updated:** ${article.updatedAt.toLocaleDateString()}\n` +
                   `🏷️ **Tags:** ${article.tags?.slice(0, 3).join(', ') || 'No tags'}`,
            inline: false
          }]);
        }

        if (popularArticles.length > 4) {
          embed.addFields([{
            name: '📈 More Articles',
            value: `+ ${popularArticles.length - 4} more helpful articles. Use search to find specific topics.`,
            inline: false
          }]);
        }

        // Add knowledge base statistics
        const totalViews = popularArticles.reduce((sum, article) => sum + article.views, 0);
        const avgHelpfulness = this.calculateAverageHelpfulness(popularArticles);

        embed.addFields([
          {
            name: '📊 Knowledge Base Stats',
            value: `**Total Articles:** ${popularArticles.length}\n**Total Views:** ${totalViews.toLocaleString()}\n**Average Helpfulness:** ${avgHelpfulness}%\n**Last Updated:** ${this.getLastUpdateDate(popularArticles)}`,
            inline: false
          }
        ]);
      }

      const selectMenu = new StringSelectMenuBuilder()
        .setCustomId('technical_support_kb_category')
        .setPlaceholder('Browse by category...')
        .addOptions(
          new StringSelectMenuOptionBuilder()
            .setLabel('🤖 Bot Setup & Configuration')
            .setDescription('Initial setup and basic configuration')
            .setValue('bot-config')
            .setEmoji('🤖'),
          new StringSelectMenuOptionBuilder()
            .setLabel('🔧 Troubleshooting')
            .setDescription('Common issues and error solutions')
            .setValue('troubleshooting')
            .setEmoji('🔧'),
          new StringSelectMenuOptionBuilder()
            .setLabel('🔗 Integrations')
            .setDescription('Third-party service integrations')
            .setValue('integrations')
            .setEmoji('🔗'),
          new StringSelectMenuOptionBuilder()
            .setLabel('📊 Analytics & Reports')
            .setDescription('Data analysis and reporting')
            .setValue('analytics')
            .setEmoji('📊'),
          new StringSelectMenuOptionBuilder()
            .setLabel('🔒 Security & Privacy')
            .setDescription('Security settings and privacy')
            .setValue('security')
            .setEmoji('🔒')
        );

      const selectRow = new ActionRowBuilder<StringSelectMenuBuilder>()
        .addComponents(selectMenu);

      const buttonRow = new ActionRowBuilder<ButtonBuilder>()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('technical_support_search_kb')
            .setLabel('🔍 Search Articles')
            .setStyle(ButtonStyle.Success),
          new ButtonBuilder()
            .setCustomId('technical_support_popular_articles')
            .setLabel('🔥 Popular Articles')
            .setStyle(ButtonStyle.Primary),
          new ButtonBuilder()
            .setCustomId('technical_support_suggest_article')
            .setLabel('💡 Suggest Article')
            .setStyle(ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('technical_support_back')
            .setLabel('🔙 Back')
            .setStyle(ButtonStyle.Secondary)
        );

      await interaction.editReply({
        embeds: [embed],
        components: [selectRow, buttonRow],
        content: null
      });

      this.logger.log(`User ${interaction.user.id} browsed knowledge base`);
    } catch (error) {
      this.logger.error('Failed to handle knowledge base action:', error);
      await interaction.editReply({
        content: '❌ Failed to load knowledge base. Please try again.'
      });
    }
  }

  async handleTroubleshootingAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {
      // Ensure user exists in database
      await this.supportDb.ensureUser(interaction.user.id, interaction.user.username);
      
      const topGuides = await this.supportDb.getTroubleshootingGuides(undefined, 5);

      const embed = new EmbedBuilder()
        .setColor(0x00ff88)
        .setTitle('🔧 Troubleshooting Guides')
        .setDescription('Step-by-step guides to diagnose and resolve common technical issues')
        .addFields([
          {
            name: '🎯 Quick Diagnostics',
            value: '🔍 **Connection Issues** - Network and API connectivity problems\n⚡ **Performance Problems** - Slow response times and timeouts\n🤖 **Bot Not Responding** - Command recognition and response issues\n🔒 **Permission Errors** - Access and authorization problems\n💾 **Data Sync Issues** - Database and storage synchronization\n🔧 **Configuration Problems** - Settings and setup issues',
            inline: false
          }
        ])
        .setTimestamp();

      if (topGuides.length === 0) {
        embed.addFields([{
          name: '🛠️ Guides Coming Soon',
          value: 'Comprehensive troubleshooting guides are being prepared. For immediate help, create a support ticket.',
          inline: false
        }]);
      } else {
        embed.addFields([
          {
            name: '⭐ Top-Rated Guides',
            value: `${topGuides.length} proven solutions with high success rates`,
            inline: false
          }
        ]);

        for (const guide of topGuides.slice(0, 3)) {
          const difficultyEmoji = this.getTroubleshootingDifficultyEmoji(guide.difficulty);
          const timeEstimate = guide.estimatedTime < 60 
            ? `${guide.estimatedTime} min`
            : `${Math.round(guide.estimatedTime / 60)} hr`;
          
          embed.addFields([{
            name: `${difficultyEmoji} ${guide.title}`,
            value: `${guide.description.substring(0, 100)}${guide.description.length > 100 ? '...' : ''}\n` +
                   `⏱️ **Time:** ${timeEstimate} | 📈 **Success Rate:** ${guide.successRate}%\n` +
                   `🔧 **Difficulty:** ${guide.difficulty} | 📊 **Steps:** ${guide.solutions?.length || 0}\n` +
                   `🏷️ **Category:** ${guide.category}`,
            inline: false
          }]);
        }

        if (topGuides.length > 3) {
          embed.addFields([{
            name: '📚 More Guides',
            value: `+ ${topGuides.length - 3} more troubleshooting guides available. Browse by category to find specific solutions.`,
            inline: false
          }]);
        }

        // Add troubleshooting statistics
        const avgSuccessRate = topGuides.reduce((sum, guide) => sum + guide.successRate, 0) / topGuides.length;
        const totalSteps = topGuides.reduce((sum, guide) => sum + (guide.solutions?.length || 0), 0);
        const avgTime = topGuides.reduce((sum, guide) => sum + guide.estimatedTime, 0) / topGuides.length;

        embed.addFields([
          {
            name: '📊 Troubleshooting Stats',
            value: `**Success Rate:** ${avgSuccessRate.toFixed(1)}% average\n**Resolution Time:** ${Math.round(avgTime)} min average\n**Total Solutions:** ${totalSteps} steps available\n**Auto-Resolution:** 67% of issues resolved without tickets`,
            inline: false
          }
        ]);
      }

      const selectMenu = new StringSelectMenuBuilder()
        .setCustomId('technical_support_troubleshooting_category')
        .setPlaceholder('Select issue category...')
        .addOptions(
          new StringSelectMenuOptionBuilder()
            .setLabel('🔍 Connection Issues')
            .setDescription('Network and API connectivity problems')
            .setValue('connection')
            .setEmoji('🔍'),
          new StringSelectMenuOptionBuilder()
            .setLabel('⚡ Performance Issues')
            .setDescription('Slow response and timeout problems')
            .setValue('performance')
            .setEmoji('⚡'),
          new StringSelectMenuOptionBuilder()
            .setLabel('🤖 Bot Problems')
            .setDescription('Command and response issues')
            .setValue('bot-issues')
            .setEmoji('🤖'),
          new StringSelectMenuOptionBuilder()
            .setLabel('🔒 Permission Errors')
            .setDescription('Access and authorization problems')
            .setValue('permissions')
            .setEmoji('🔒'),
          new StringSelectMenuOptionBuilder()
            .setLabel('🔧 Configuration')
            .setDescription('Setup and settings problems')
            .setValue('configuration')
            .setEmoji('🔧')
        );

      const selectRow = new ActionRowBuilder<StringSelectMenuBuilder>()
        .addComponents(selectMenu);

      const buttonRow = new ActionRowBuilder<ButtonBuilder>()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('technical_support_diagnostic_tool')
            .setLabel('🔬 Run Diagnostics')
            .setStyle(ButtonStyle.Success),
          new ButtonBuilder()
            .setCustomId('technical_support_guided_solution')
            .setLabel('🧭 Guided Solution')
            .setStyle(ButtonStyle.Primary),
          new ButtonBuilder()
            .setCustomId('technical_support_common_fixes')
            .setLabel('⚡ Quick Fixes')
            .setStyle(ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('technical_support_back')
            .setLabel('🔙 Back')
            .setStyle(ButtonStyle.Secondary)
        );

      await interaction.editReply({
        embeds: [embed],
        components: [selectRow, buttonRow],
        content: null
      });

      this.logger.log(`User ${interaction.user.id} accessed troubleshooting guides`);
    } catch (error) {
      this.logger.error('Failed to handle troubleshooting action:', error);
      await interaction.editReply({
        content: '❌ Failed to load troubleshooting guides. Please try again.'
      });
    }
  }

  async handleSystemStatusAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {
      // Ensure user exists in database
      await this.supportDb.ensureUser(interaction.user.id, interaction.user.username);
      
      const systemStatus = await this.supportDb.getSystemStatus();

      const embed = new EmbedBuilder()
        .setColor(this.getOverallStatusColor(systemStatus))
        .setTitle('🚦 System Status & Health')
        .setDescription('Real-time status of all services and infrastructure components')
        .addFields([
          {
            name: '🌟 Overall Status',
            value: this.getOverallStatusMessage(systemStatus),
            inline: false
          }
        ])
        .setTimestamp();

      // Group services by status
      const operationalServices = systemStatus.filter((s: any) => s.status === 'operational');
      const degradedServices = systemStatus.filter((s: any) => s.status === 'degraded');
      const outageServices = systemStatus.filter((s: any) => s.status === 'partial-outage' || s.status === 'major-outage');

      if (operationalServices.length > 0) {
        embed.addFields([
          {
            name: '✅ Operational Services',
            value: operationalServices.map((service: any) => 
              `🟢 **${service.service}** - ${service.uptime.toFixed(2)}% uptime\n   Response: ${service.responseTime}ms | Last check: ${service.lastChecked.toLocaleTimeString()}`
            ).join('\n\n'),
            inline: false
          }
        ]);
      }

      if (degradedServices.length > 0) {
        embed.addFields([
          {
            name: '⚠️ Degraded Performance',
            value: degradedServices.map((service: any) => 
              `🟡 **${service.service}** - ${service.uptime.toFixed(2)}% uptime\n   Response: ${service.responseTime}ms | ${service.description || 'Performance issues detected'}`
            ).join('\n\n'),
            inline: false
          }
        ]);
      }

      if (outageServices.length > 0) {
        embed.addFields([
          {
            name: '🚨 Service Outages',
            value: outageServices.map((service: any) => 
              `🔴 **${service.service}** - ${this.getStatusDisplayName(service.status)}\n   ${service.description || 'Service temporarily unavailable'}\n   Incidents: ${service.incidents} today`
            ).join('\n\n'),
            inline: false
          }
        ]);
      }

      // Add system metrics
      const avgUptime = systemStatus.reduce((sum, s) => sum + s.uptime, 0) / systemStatus.length;
      const avgResponseTime = systemStatus.reduce((sum, s) => sum + s.responseTime, 0) / systemStatus.length;
      const totalIncidents = systemStatus.reduce((sum, s) => sum + s.incidents, 0);

      embed.addFields([
        {
          name: '📊 System Metrics (24h)',
          value: `**Average Uptime:** ${avgUptime.toFixed(2)}%\n**Average Response:** ${Math.round(avgResponseTime)}ms\n**Total Incidents:** ${totalIncidents}\n**Services Monitored:** ${systemStatus.length}`,
          inline: false
        },
        {
          name: '🔔 Status Updates',
          value: '• Subscribe to status notifications\n• Automatic incident reports\n• Maintenance schedule alerts\n• Performance degradation warnings',
          inline: false
        }
      ]);

      const buttonRow = new ActionRowBuilder<ButtonBuilder>()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('technical_support_refresh_status')
            .setLabel('🔄 Refresh Status')
            .setStyle(ButtonStyle.Success),
          new ButtonBuilder()
            .setCustomId('technical_support_incident_history')
            .setLabel('📜 Incident History')
            .setStyle(ButtonStyle.Primary),
          new ButtonBuilder()
            .setCustomId('technical_support_subscribe_alerts')
            .setLabel('🔔 Subscribe to Alerts')
            .setStyle(ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('technical_support_back')
            .setLabel('🔙 Back')
            .setStyle(ButtonStyle.Secondary)
        );

      await interaction.editReply({
        embeds: [embed],
        components: [buttonRow],
        content: null
      });

      this.logger.log(`User ${interaction.user.id} checked system status`);
    } catch (error) {
      this.logger.error('Failed to handle system status action:', error);
      await interaction.editReply({
        content: '❌ Failed to load system status. Please try again.'
      });
    }
  }

  async handleQuickTicketAction(interaction: ButtonInteraction): Promise<void> {
    try {
      // Ensure user exists in database
      await this.supportDb.ensureUser(interaction.user.id, interaction.user.username);
      
      // Create a quick ticket with default values
      const ticket = await this.supportDb.createTicket({
        userId: interaction.user.id,
        guildId: interaction.guildId!,
        title: 'Quick Support Request',
        description: 'User submitted a quick support request via panel. Details to be provided in follow-up.',
        category: 'general',
        priority: 'medium'
      });

      const embed = new EmbedBuilder()
        .setColor(0x2ecc71)
        .setTitle('✅ Support Ticket Created!')
        .setDescription(`Your support ticket has been created successfully.`)
        .addFields([
          { name: '🎫 Ticket Number', value: ticket.ticketNumber, inline: true },
          { name: '📅 Created', value: ticket.createdAt.toLocaleDateString(), inline: true },
          { name: '⏰ Priority', value: ticket.priority.toUpperCase(), inline: true },
          { name: '📝 Next Steps', value: 'Our support team will review your ticket and respond within 24 hours. You can add more details by replying to this ticket.', inline: false }
        ])
        .setFooter({ text: 'You will receive updates as your ticket progresses' })
        .setTimestamp();

      const row = new ActionRowBuilder<ButtonBuilder>()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('technical_support_add_details')
            .setLabel('📝 Add Details')
            .setStyle(ButtonStyle.Primary),
          new ButtonBuilder()
            .setCustomId('technical_support_my_tickets')
            .setLabel('📁 My Tickets')
            .setStyle(ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('technical_support_back')
            .setLabel('🔙 Back')
            .setStyle(ButtonStyle.Secondary)
        );

      await interaction.editReply({
        embeds: [embed],
        components: [row],
        content: null
      });

      this.logger.log(`User ${interaction.user.id} created quick ticket ${ticket.ticketNumber}`);
    } catch (error) {
      this.logger.error('Failed to handle quick ticket action:', error);
      await interaction.editReply({
        content: '❌ Failed to create support ticket. Please try again.'
      });
    }
  }

  async handleEscalateAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {
      // Ensure user exists in database
      await this.supportDb.ensureUser(interaction.user.id, interaction.user.username);
      
      const embed = new EmbedBuilder()
        .setColor(0xFF6B6B)
        .setTitle('🚨 Escalate Support Request')
        .setDescription('Request priority escalation for urgent or complex issues')
        .addFields([
          {
            name: '⚡ When to Escalate',
            value: '• **Critical Issues** - System outages or security concerns\n• **Urgent Business Impact** - Blocking business operations\n• **Complex Technical Problems** - Requiring specialized expertise\n• **Unresolved Issues** - No response within SLA timeframe\n• **Account Security** - Potential security breaches',
            inline: false
          },
          {
            name: '📋 Escalation Process',
            value: '1. **Assessment** - Issue reviewed by senior support\n2. **Priority Assignment** - Elevated to appropriate level\n3. **Expert Assignment** - Routed to specialist team\n4. **Accelerated Response** - Faster resolution timeline\n5. **Management Notification** - Leadership awareness for critical issues',
            inline: false
          },
          {
            name: '⏱️ Response Times',
            value: '• **Critical:** 15 minutes\n• **High:** 1 hour\n• **Medium:** 4 hours\n• **Standard:** 24 hours',
            inline: true
          },
          {
            name: '📞 Emergency Contact',
            value: '• **24/7 Hotline:** Available for critical issues\n• **Management Escalation:** Direct leadership contact\n• **Security Team:** Immediate security response',
            inline: true
          }
        ])
        .setFooter({ text: 'Escalation requests are reviewed immediately by senior support staff' })
        .setTimestamp();

      const row = new ActionRowBuilder<ButtonBuilder>()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('support_escalate_critical')
            .setLabel('Critical Escalation')
            .setStyle(ButtonStyle.Danger)
            .setEmoji('🚨'),
          new ButtonBuilder()
            .setCustomId('support_escalate_urgent')
            .setLabel('Urgent Escalation')
            .setStyle(ButtonStyle.Primary)
            .setEmoji('⚡'),
          new ButtonBuilder()
            .setCustomId('support_escalate_standard')
            .setLabel('Standard Escalation')
            .setStyle(ButtonStyle.Secondary)
            .setEmoji('📈'),
          new ButtonBuilder()
            .setCustomId('support_escalation_guidelines')
            .setLabel('Guidelines')
            .setStyle(ButtonStyle.Secondary)
            .setEmoji('📋')
        );

      await interaction.editReply({
        embeds: [embed],
        components: [row]
      });

      this.logger.log(`User ${interaction.user.id} requested escalation options`);
    } catch (error) {
      this.logger.error('Error in handleEscalateAction:', error);
      await interaction.editReply({
        content: '❌ An error occurred while loading escalation options. Please try again later.',
        embeds: [],
        components: []
      });
    }
  }

  // Helper methods
  private getStatusEmoji(status: string): string {
    const emojiMap: Record<string, string> = {
      'open': '🔓',
      'in-progress': '⏳',
      'waiting-response': '⏰',
      'resolved': '✅',
      'closed': '🔒'
    };
    return emojiMap[status] || '❓';
  }

  private getPriorityEmoji(priority: string): string {
    const emojiMap: Record<string, string> = {
      'low': '🟢',
      'medium': '🟡',
      'high': '🟠',
      'critical': '🔴'
    };
    return emojiMap[priority] || '⚪';
  }

  private getDifficultyEmoji(difficulty: string): string {
    const emojiMap: Record<string, string> = {
      'beginner': '🟢',
      'intermediate': '🟡',
      'advanced': '🔴'
    };
    return emojiMap[difficulty] || '⚪';
  }

  private getTroubleshootingDifficultyEmoji(difficulty: string): string {
    const emojiMap: Record<string, string> = {
      'easy': '🟢',
      'medium': '🟡',
      'hard': '🔴'
    };
    return emojiMap[difficulty] || '⚪';
  }

  private calculateAverageHelpfulness(articles: KnowledgeBaseArticle[]): number {
    if (articles.length === 0) return 0;
    const totalRatings = articles.reduce((sum, article) => {
      const total = article.helpful + article.notHelpful;
      return sum + (total > 0 ? (article.helpful / total) * 100 : 0);
    }, 0);
    return Math.round(totalRatings / articles.length);
  }

  private getLastUpdateDate(articles: KnowledgeBaseArticle[]): string {
    if (articles.length === 0) return 'N/A';
    const lastUpdate = Math.max(...articles.map((article: any) => article.updatedAt.getTime()));
    return new Date(lastUpdate).toLocaleDateString();
  }

  private getOverallStatusColor(systemStatus: SystemStatus[]): number {
    const hasOutage = systemStatus.some(s => s.status === 'partial-outage' || s.status === 'major-outage');
    const hasDegraded = systemStatus.some(s => s.status === 'degraded');
    
    if (hasOutage) return 0xff0000; // Red
    if (hasDegraded) return 0xffa500; // Orange
    return 0x00ff00; // Green
  }

  private getOverallStatusMessage(systemStatus: SystemStatus[]): string {
    const operational = systemStatus.filter((s: any) => s.status === 'operational').length;
    const total = systemStatus.length;
    
    if (operational === total) {
      return '✅ **All Systems Operational** - Everything is running smoothly';
    }
    
    const issues = total - operational;
    return `⚠️ **${issues} Service${issues > 1 ? 's' : ''} Experiencing Issues** - ${operational}/${total} services operational`;
  }

  private getStatusDisplayName(status: string): string {
    const displayNames: Record<string, string> = {
      'operational': 'Operational',
      'degraded': 'Degraded Performance',
      'partial-outage': 'Partial Outage',
      'major-outage': 'Major Outage'
    };
    return displayNames[status] || status;
  }
}