import { Injectable, Logger } from '@nestjs/common';
import { ButtonInteraction, StringSelectMenuInteraction, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, StringSelectMenuBuilder, StringSelectMenuOptionBuilder } from 'discord.js';
import { CommunityDatabaseService, CommunityEventWithParticipants, LeaderboardEntryWithUser } from '../../services/community-database.service';
import { CommunityFeedback } from '../../../../core/database/entities/community-events.entity';

@Injectable()
export class CommunityHubActionsDatabaseHandler {
  private readonly logger = new Logger(CommunityHubActionsDatabaseHandler.name);

  constructor(private readonly communityDb: CommunityDatabaseService) {}

  async handleGuidelinesAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {
      // Ensure user exists in database
      await this.communityDb.ensureUser(interaction.user.id, interaction.user.username);
      
      const embed = new EmbedBuilder()
        .setColor(0x3498db)
        .setTitle('📋 Community Guidelines')
        .setDescription('Welcome to our community! Please follow these guidelines to ensure a positive experience for everyone.')
        .addFields([
          {
            name: '🤝 Be Respectful',
            value: '• Treat all members with respect and kindness\n• No harassment, discrimination, or hate speech\n• Keep discussions constructive and professional',
            inline: false
          },
          {
            name: '💬 Communication',
            value: '• Use appropriate channels for different topics\n• Keep conversations on-topic\n• Use clear and concise language\n• No spam or excessive self-promotion',
            inline: false
          },
          {
            name: '🚀 Participation',
            value: '• Share knowledge and help others learn\n• Participate in community events and challenges\n• Provide constructive feedback\n• Celebrate others\' achievements',
            inline: false
          },
          {
            name: '⚠️ Prohibited Content',
            value: '• No NSFW or inappropriate content\n• No sharing of illegal or copyrighted material\n• No doxxing or sharing personal information\n• No malicious links or files',
            inline: false
          }
        ])
        .setFooter({ text: 'By participating, you agree to follow these guidelines' })
        .setTimestamp();

      const row = new ActionRowBuilder<ButtonBuilder>()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('community_guidelines_detailed')
            .setLabel('📖 Detailed Rules')
            .setStyle(ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('community_guidelines_report')
            .setLabel('🚨 Report Violation')
            .setStyle(ButtonStyle.Danger),
          new ButtonBuilder()
            .setCustomId('community_back')
            .setLabel('🔙 Back to Hub')
            .setStyle(ButtonStyle.Primary)
        );

      await interaction.editReply({
        embeds: [embed],
        components: [row],
        content: null
      });

      // Award points for reading guidelines
      await this.communityDb.updateUserPoints(
        interaction.user.id, 
        interaction.guildId!, 
        5, 
        'Read community guidelines'
      );

      this.logger.log(`User ${interaction.user.id} viewed community guidelines and earned 5 points`);
    } catch (error) {
      this.logger.error('Failed to handle guidelines action:', error);
      await interaction.editReply({
        content: '❌ Failed to load community guidelines. Please try again.'
      });
    }
  }

  async handleEventsAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {
      // Ensure user exists in database
      await this.communityDb.ensureUser(interaction.user.id, interaction.user.username);
      
      const upcomingEvents = await this.communityDb.getUpcomingEvents(interaction.guildId!, 5);

      const embed = new EmbedBuilder()
        .setColor(0x2ecc71)
        .setTitle('🎉 Community Events')
        .setDescription('Join our exciting community events and connect with fellow members!')
        .setTimestamp();

      if (upcomingEvents.length === 0) {
        embed.addFields([{
          name: '📅 No Upcoming Events',
          value: 'Check back soon for new events! In the meantime, participate in ongoing challenges and discussions.',
          inline: false
        }]);
      } else {
        for (const event of upcomingEvents) {
          const spotsLeft = event.maxParticipants ? event.maxParticipants - event.currentParticipants : null;
          const eventEmoji = this.getEventTypeEmoji(event.type);
          
          embed.addFields([{
            name: `${eventEmoji} ${event.title}`,
            value: `${event.description}\n` +
                   `📅 **Date:** ${event.startDate.toLocaleDateString()} at ${event.startDate.toLocaleTimeString()}\n` +
                   `👥 **Participants:** ${event.currentParticipants}${event.maxParticipants ? `/${event.maxParticipants}` : ''}\n` +
                   `${spotsLeft !== null ? `🎯 **Spots Left:** ${spotsLeft}\n` : ''}` +
                   `🏷️ **Tags:** ${event.tags?.join(', ') || 'No tags'}`,
            inline: false
          }]);
        }
      }

      const row = new ActionRowBuilder<ButtonBuilder>()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('community_events_join')
            .setLabel('✅ Join Event')
            .setStyle(ButtonStyle.Success)
            .setDisabled(upcomingEvents.length === 0),
          new ButtonBuilder()
            .setCustomId('community_events_create')
            .setLabel('➕ Suggest Event')
            .setStyle(ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('community_events_calendar')
            .setLabel('📅 Full Calendar')
            .setStyle(ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('community_back')
            .setLabel('🔙 Back')
            .setStyle(ButtonStyle.Primary)
        );

      await interaction.editReply({
        embeds: [embed],
        components: [row],
        content: null
      });

      this.logger.log(`User ${interaction.user.id} viewed community events`);
    } catch (error) {
      this.logger.error('Failed to handle events action:', error);
      await interaction.editReply({
        content: '❌ Failed to load community events. Please try again.'
      });
    }
  }

  async handleLeaderboardAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {
      // Ensure user exists in database
      await this.communityDb.ensureUser(interaction.user.id, interaction.user.username);
      
      const topUsers = await this.communityDb.getLeaderboard(interaction.guildId!, 10);

      const embed = new EmbedBuilder()
        .setColor(0xf1c40f)
        .setTitle('🏆 Community Leaderboard')
        .setDescription('Top contributors in our community this month!')
        .setTimestamp();

      if (topUsers.length === 0) {
        embed.addFields([{
          name: '📊 No Data Available',
          value: 'Leaderboard data is being collected. Start participating to see your ranking!',
          inline: false
        }]);
      } else {
        let leaderboardText = '';
        for (let i = 0; i < topUsers.length; i++) {
          const user = topUsers[i];
          const rankEmoji = this.getRankEmoji(i + 1);
          const badges = user.badges?.slice(0, 3).join(' ') || '';
          leaderboardText += `${rankEmoji} **${user.username || 'Unknown User'}** - ${user.points} pts (Lvl ${user.level}) ${badges}\n`;
        }

        embed.addFields([
          {
            name: '🎯 Top Contributors',
            value: leaderboardText,
            inline: false
          }
        ]);

        const currentUser = topUsers.find(entry => entry.userId === interaction.user.id);
        if (currentUser) {
          embed.addFields([
            {
              name: '📈 Your Stats',
              value: `**Rank:** #${topUsers.indexOf(currentUser) + 1} (This Month)\n` +
                     `**Points:** ${currentUser.points} | **Level:** ${currentUser.level}\n` +
                     `**Badges:** ${currentUser.badges?.join(' ') || 'No badges yet'}`,
              inline: false
            }
          ]);
        }

        embed.addFields([
          {
            name: '💎 How to Earn Points',
            value: '• Help other members (+10 pts)\n• Share resources (+15 pts)\n• Complete challenges (+25 pts)\n• Host events (+50 pts)\n• Read guidelines (+5 pts)',
            inline: false
          }
        ]);
      }

      const row = new ActionRowBuilder<ButtonBuilder>()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('community_leaderboard_monthly')
            .setLabel('📅 Monthly')
            .setStyle(ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('community_leaderboard_alltime')
            .setLabel('🏆 All Time')
            .setStyle(ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('community_leaderboard_badges')
            .setLabel('🎖️ Badges')
            .setStyle(ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('community_back')
            .setLabel('🔙 Back')
            .setStyle(ButtonStyle.Primary)
        );

      await interaction.editReply({
        embeds: [embed],
        components: [row],
        content: null
      });

      this.logger.log(`User ${interaction.user.id} viewed community leaderboard`);
    } catch (error) {
      this.logger.error('Failed to handle leaderboard action:', error);
      await interaction.editReply({
        content: '❌ Failed to load leaderboard. Please try again.'
      });
    }
  }

  async handleFeedbackAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {
      // Ensure user exists in database
      await this.communityDb.ensureUser(interaction.user.id, interaction.user.username);
      
      const recentFeedback = await this.communityDb.getFeedback(interaction.guildId!, 10);
      const completedFeedback = recentFeedback.filter((f: any) => f.status === 'completed');
      const inProgressFeedback = recentFeedback.filter((f: any) => f.status === 'in-progress');

      const embed = new EmbedBuilder()
        .setColor(0x9b59b6)
        .setTitle('💬 Community Feedback')
        .setDescription('Your voice matters! Help us improve the community by sharing your feedback.')
        .addFields([
          {
            name: '💡 Suggestion',
            value: 'Share ideas for new features or improvements',
            inline: true
          },
          {
            name: '🐛 Bug Report',
            value: 'Report issues or problems you\'ve encountered',
            inline: true
          },
          {
            name: '🚀 Feature Request',
            value: 'Request new functionality or tools',
            inline: true
          },
          {
            name: '💬 General Feedback',
            value: 'Share your overall thoughts and experiences',
            inline: true
          },
          {
            name: '📊 Recent Submissions',
            value: `${recentFeedback.length} total submissions\n${completedFeedback.length} implemented\n${inProgressFeedback.length} in progress`,
            inline: true
          },
          {
            name: '🎯 Response Time',
            value: 'We typically respond within 24-48 hours',
            inline: true
          }
        ])
        .setFooter({ text: 'All feedback is reviewed by our community team' })
        .setTimestamp();

      const selectMenu = new StringSelectMenuBuilder()
        .setCustomId('community_feedback_type')
        .setPlaceholder('Choose feedback type...')
        .addOptions(
          new StringSelectMenuOptionBuilder()
            .setLabel('💡 Make a Suggestion')
            .setDescription('Share ideas for improvements')
            .setValue('suggestion')
            .setEmoji('💡'),
          new StringSelectMenuOptionBuilder()
            .setLabel('🐛 Report a Bug')
            .setDescription('Report issues or problems')
            .setValue('bug-report')
            .setEmoji('🐛'),
          new StringSelectMenuOptionBuilder()
            .setLabel('🚀 Request a Feature')
            .setDescription('Request new functionality')
            .setValue('feature-request')
            .setEmoji('🚀'),
          new StringSelectMenuOptionBuilder()
            .setLabel('💬 General Feedback')
            .setDescription('Share your thoughts')
            .setValue('general')
            .setEmoji('💬')
        );

      const selectRow = new ActionRowBuilder<StringSelectMenuBuilder>()
        .addComponents(selectMenu);

      const buttonRow = new ActionRowBuilder<ButtonBuilder>()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('community_feedback_view')
            .setLabel('📋 View My Feedback')
            .setStyle(ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('community_feedback_popular')
            .setLabel('🔥 Popular Suggestions')
            .setStyle(ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('community_back')
            .setLabel('🔙 Back')
            .setStyle(ButtonStyle.Primary)
        );

      await interaction.editReply({
        embeds: [embed],
        components: [selectRow, buttonRow],
        content: null
      });

      this.logger.log(`User ${interaction.user.id} accessed feedback system`);
    } catch (error) {
      this.logger.error('Failed to handle feedback action:', error);
      await interaction.editReply({
        content: '❌ Failed to load feedback system. Please try again.'
      });
    }
  }

  async handleEventJoinAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    const userId = interaction.user.id;
    
    try {
      // Ensure user exists in database
      await this.communityDb.ensureUser(userId, interaction.user.username);
      
      const upcomingEvents = await this.communityDb.getUpcomingEvents(interaction.guildId!, 1);
      
      if (upcomingEvents.length === 0) {
        await interaction.editReply({
          content: '❌ No upcoming events available to join.'
        });
        return;
      }

      // For this example, join the first available event
      const eventToJoin = upcomingEvents[0];
      const result = await this.communityDb.joinEvent(eventToJoin.id.toString(), userId);

      if (!result.success) {
        await interaction.editReply({
          content: `❌ ${result.message}`
        });
        return;
      }

      const embed = new EmbedBuilder()
        .setColor(0x2ecc71)
        .setTitle('🎉 Successfully Registered!')
        .setDescription(`You have been registered for **${eventToJoin.title}**`)
        .addFields([
          { name: '📅 Event Date', value: eventToJoin.startDate.toLocaleDateString(), inline: true },
          { name: '⏰ Event Time', value: eventToJoin.startDate.toLocaleTimeString(), inline: true },
          { name: '👥 Participants', value: `${eventToJoin.currentParticipants + 1}${eventToJoin.maxParticipants ? `/${eventToJoin.maxParticipants}` : ''}`, inline: true }
        ])
        .setFooter({ text: 'You will receive a reminder before the event starts' })
        .setTimestamp();

      const row = new ActionRowBuilder<ButtonBuilder>()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('community_events_calendar')
            .setLabel('📅 View Calendar')
            .setStyle(ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('community_events_leave')
            .setLabel('❌ Leave Event')
            .setStyle(ButtonStyle.Danger),
          new ButtonBuilder()
            .setCustomId('community_back')
            .setLabel('🔙 Back')
            .setStyle(ButtonStyle.Primary)
        );

      await interaction.editReply({
        embeds: [embed],
        components: [row],
        content: null
      });

      // Award points for joining an event
      await this.communityDb.updateUserPoints(userId, interaction.guildId!, 15, 'Joined community event');

      this.logger.log(`User ${userId} joined event ${eventToJoin.id} and earned 15 points`);
    } catch (error) {
      this.logger.error('Failed to handle event join action:', error);
      await interaction.editReply({
        content: '❌ Failed to join event. Please try again.'
      });
    }
  }

  async handleFeedbackSubmission(interaction: StringSelectMenuInteraction, feedbackType: string): Promise<void> {
    try {
      // Ensure user exists in database
      await this.communityDb.ensureUser(interaction.user.id, interaction.user.username);
      
      // In a real implementation, you would open a modal for detailed feedback
      // For now, we'll create a sample feedback entry
      const sampleTitles = {
        'suggestion': 'Community Improvement Suggestion',
        'bug-report': 'Bug Report',
        'feature-request': 'New Feature Request',
        'general': 'General Feedback'
      };

      const feedback = await this.communityDb.createFeedback({
        userId: interaction.user.id,
        guildId: interaction.guildId!,
        type: feedbackType as any,
        title: sampleTitles[feedbackType as keyof typeof sampleTitles] || 'Feedback',
        description: 'Feedback submitted via community panel (detailed feedback would be collected via modal)'
      });

      // Award points for providing feedback
      await this.communityDb.updateUserPoints(
        interaction.user.id, 
        interaction.guildId!, 
        10, 
        'Provided community feedback'
      );

      await interaction.editReply({
        content: `✅ Thank you for your ${feedbackType}! Your feedback has been recorded and our team will review it soon. You earned 10 points!`,
        components: []
      });

      this.logger.log(`User ${interaction.user.id} submitted ${feedbackType} feedback and earned 10 points`);
    } catch (error) {
      this.logger.error('Failed to handle feedback submission:', error);
      await interaction.editReply({
        content: '❌ Failed to submit feedback. Please try again.'
      });
    }
  }

  private getEventTypeEmoji(type: string): string {
    const emojiMap: Record<string, string> = {
      'challenge': '🏆',
      'workshop': '🎓',
      'meetup': '🤝',
      'competition': '⚡'
    };
    return emojiMap[type] || '📅';
  }

  private getRankEmoji(rank: number): string {
    if (rank === 1) return '🥇';
    if (rank === 2) return '🥈';
    if (rank === 3) return '🥉';
    return `${rank}.`;
  }
}