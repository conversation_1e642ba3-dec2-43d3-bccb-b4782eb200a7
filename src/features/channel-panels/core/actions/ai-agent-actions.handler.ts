import { Injectable, Logger } from '@nestjs/common';
import { ButtonInteraction, StringSelectMenuInteraction, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, StringSelectMenuBuilder, StringSelectMenuOptionBuilder } from 'discord.js';

export interface AIAgent {
  id: string;
  name: string;
  description: string;
  type: 'personal-growth' | 'intake-specialist' | 'progress-tracker' | 'custom';
  isPremium: boolean;
  capabilities: string[];
  specialties: string[];
  availability: '24/7' | 'business-hours' | 'limited';
  rating: number;
  conversations: number;
}

export interface UserConversation {
  id: string;
  userId: string;
  agentId: string;
  threadId?: string;
  lastActive: Date;
  messageCount: number;
  status: 'active' | 'paused' | 'completed';
  summary: string;
  goals: string[];
  progress: Record<string, any>;
}

export interface APIKeyConfiguration {
  userId: string;
  provider: 'openai' | 'anthropic' | 'google' | 'cohere' | 'custom';
  keyName: string;
  isActive: boolean;
  addedAt: Date;
  lastUsed?: Date;
  usageCount: number;
}

@Injectable()
export class AIAgentActionsHandler {
  private readonly logger = new Logger(AIAgentActionsHandler.name);
  private availableAgents: AIAgent[] = [];
  private userConversations = new Map<string, UserConversation[]>();
  private userAPIKeys = new Map<string, APIKeyConfiguration[]>();

  constructor() {
    this.initializeSampleData();
  }

  async handleAgentSelectionAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {
      const userId = interaction.user.id;
      const userConversations = this.userConversations.get(userId) || [];
      const activeConversations = userConversations.filter((conv: any) => conv.status === 'active');

      const embed = new EmbedBuilder()
        .setColor(0x00d4ff)
        .setTitle('🤖 AI Agent Selection')
        .setDescription('Welcome to your personal AI assistance center! Choose an agent below to start a private conversation.')
        .addFields([
          {
            name: '✨ What You Get',
            value: '• **Private one-on-one conversations** - Personalized assistance\n• **Progress tracking and memory** - Agents remember your history\n• **24/7 availability** - Get help anytime you need it\n• **Custom API keys** - Use your own providers for enhanced features\n• **Specialized expertise** - Each agent has unique capabilities',
            inline: false
          },
          {
            name: '🚀 How It Works',
            value: '1. **Choose an agent** from the options below\n2. **Private thread created** - Your own dedicated space\n3. **Start chatting** - Begin your personalized conversation\n4. **Return anytime** - Continue where you left off\n5. **Track progress** - See your growth over time',
            inline: false
          }
        ])
        .setTimestamp();

      if (activeConversations.length > 0) {
        embed.addFields([
          {
            name: '💬 Your Active Conversations',
            value: activeConversations.map((conv: any) => {
              const agent = this.availableAgents.find(a => a.id === conv.agentId);
              return `• **${agent?.name || 'Unknown Agent'}** - ${conv.messageCount} messages (Last active: ${conv.lastActive.toLocaleDateString()})`;
            }).join('\n'),
            inline: false
          }
        ]);
      }

      // Group agents by type
      const freeAgents = this.availableAgents.filter((agent: any) => !agent.isPremium);
      const premiumAgents = this.availableAgents.filter((agent: any) => agent.isPremium);

      if (freeAgents.length > 0) {
        embed.addFields([
          {
            name: '🆓 Free Agents',
            value: freeAgents.map((agent: any) => {
              const statusEmoji = agent.availability === '24/7' ? '🟢' : '🟡';
              return `${statusEmoji} **${agent.name}**\n   ${agent.description}\n   ⭐ ${agent.rating}/5 | 💬 ${agent.conversations} conversations`;
            }).join('\n\n'),
            inline: false
          }
        ]);
      }

      if (premiumAgents.length > 0) {
        embed.addFields([
          {
            name: '💎 Premium Agents',
            value: premiumAgents.map((agent: any) => {
              const statusEmoji = agent.availability === '24/7' ? '🟢' : '🟡';
              return `${statusEmoji} **${agent.name}** 💎\n   ${agent.description}\n   ⭐ ${agent.rating}/5 | 💬 ${agent.conversations} conversations`;
            }).join('\n\n'),
            inline: false
          }
        ]);
      }

      const selectMenu = new StringSelectMenuBuilder()
        .setCustomId('ai_agent_select')
        .setPlaceholder('Select an AI agent to start chatting...')
        .addOptions(
          ...this.availableAgents.map((agent: any) => 
            new StringSelectMenuOptionBuilder()
              .setLabel(`${agent.name}${agent.isPremium ? ' (Premium)' : ' (Free)'}`)
              .setDescription(agent.description.length > 100 ? agent.description.substring(0, 97) + '...' : agent.description)
              .setValue(agent.id)
              .setEmoji(agent.isPremium ? '💎' : '🤖')
          )
        );

      const selectRow = new ActionRowBuilder<StringSelectMenuBuilder>()
        .addComponents(selectMenu);

      const buttonRow = new ActionRowBuilder<ButtonBuilder>()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('ai_agent_continue_conversation')
            .setLabel('💬 Continue Conversation')
            .setStyle(ButtonStyle.Primary)
            .setDisabled(activeConversations.length === 0),
          new ButtonBuilder()
            .setCustomId('ai_agent_api_keys')
            .setLabel('🔑 Manage API Keys')
            .setStyle(ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('ai_agent_conversation_history')
            .setLabel('📜 Conversation History')
            .setStyle(ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('ai_agent_back')
            .setLabel('🔙 Back')
            .setStyle(ButtonStyle.Secondary)
        );

      await interaction.editReply({
        embeds: [embed],
        components: [selectRow, buttonRow],
        content: null
      });

      this.logger.log(`User ${userId} accessed AI agent selection`);
    } catch (error) {
      this.logger.error('Failed to handle agent selection action:', error);
      await interaction.editReply({
        content: '❌ Failed to load AI agents. Please try again.'
      });
    }
  }

  async handleStartConversationAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {
      const userId = interaction.user.id;
      const agentId = interaction.isStringSelectMenu() ? interaction.values[0] : 'personal-growth-coach';
      const agent = this.availableAgents.find(a => a.id === agentId);

      if (!agent) {
        await interaction.editReply({
          content: '❌ Agent not found. Please try selecting a different agent.'
        });
        return;
      }

      // Check if user has premium access for premium agents
      if (agent.isPremium && !this.hasPremiumAccess(userId)) {
        const embed = new EmbedBuilder()
          .setColor(0xff6b6b)
          .setTitle('💎 Premium Agent Access Required')
          .setDescription(`**${agent.name}** is a premium agent with advanced capabilities.`)
          .addFields([
            {
              name: '🌟 Premium Features',
              value: '• **Advanced analytics** - Detailed progress reports\n• **Extended memory** - Longer conversation history\n• **Priority support** - Faster response times\n• **Custom integrations** - Connect your own tools\n• **Export capabilities** - Download your conversations',
              inline: false
            },
            {
              name: '💡 What You\'re Missing',
              value: agent.capabilities.map((cap: any) => `• ${cap}`).join('\n'),
              inline: false
            }
          ])
          .setTimestamp();

        const row = new ActionRowBuilder<ButtonBuilder>()
          .addComponents(
            new ButtonBuilder()
              .setCustomId('ai_agent_upgrade_premium')
              .setLabel('🚀 Upgrade to Premium')
              .setStyle(ButtonStyle.Success),
            new ButtonBuilder()
              .setCustomId('ai_agent_try_free')
              .setLabel('🆓 Try Free Agents')
              .setStyle(ButtonStyle.Primary),
            new ButtonBuilder()
              .setCustomId('ai_agent_back')
              .setLabel('🔙 Back')
              .setStyle(ButtonStyle.Secondary)
          );

        await interaction.editReply({
          embeds: [embed],
          components: [row],
          content: null
        });
        return;
      }

      // Create new conversation
      const conversationId = `conv_${Date.now()}_${userId}`;
      const newConversation: UserConversation = {
        id: conversationId,
        userId,
        agentId: agent.id,
        lastActive: new Date(),
        messageCount: 0,
        status: 'active',
        summary: 'New conversation started',
        goals: [],
        progress: {}
      };

      const userConversations = this.userConversations.get(userId) || [];
      userConversations.push(newConversation);
      this.userConversations.set(userId, userConversations);

      const embed = new EmbedBuilder()
        .setColor(0x00ff88)
        .setTitle(`🤖 Starting Conversation with ${agent.name}`)
        .setDescription('Your private AI assistant is ready to help!')
        .addFields([
          {
            name: '👋 Meet Your Agent',
            value: `**${agent.name}**\n${agent.description}\n\n**Specialties:**\n${agent.specialties.map((s: any) => `• ${s}`).join('\n')}`,
            inline: false
          },
          {
            name: '💬 Getting Started',
            value: '• **Be specific** - The more details you provide, the better help you\'ll get\n• **Ask questions** - Your agent is here to guide and support you\n• **Share goals** - Let your agent know what you want to achieve\n• **Be honest** - Authentic conversations lead to better outcomes',
            inline: false
          },
          {
            name: '🔄 Conversation Features',
            value: `• **Memory:** Your agent remembers previous conversations\n• **Progress tracking:** See how you\'re improving over time\n• **Goal setting:** Set and track meaningful objectives\n• **24/7 availability:** ${agent.availability === '24/7' ? 'Available anytime' : 'Limited hours'}`,
            inline: false
          }
        ])
        .setFooter({ text: `Conversation ID: ${conversationId}` })
        .setTimestamp();

      const row = new ActionRowBuilder<ButtonBuilder>()
        .addComponents(
          new ButtonBuilder()
            .setCustomId(`ai_agent_chat_${agent.id}`)
            .setLabel('💬 Start Chatting')
            .setStyle(ButtonStyle.Success),
          new ButtonBuilder()
            .setCustomId('ai_agent_conversation_settings')
            .setLabel('⚙️ Settings')
            .setStyle(ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('ai_agent_back')
            .setLabel('🔙 Back to Agents')
            .setStyle(ButtonStyle.Secondary)
        );

      await interaction.editReply({
        embeds: [embed],
        components: [row],
        content: null
      });

      this.logger.log(`User ${userId} started conversation with agent ${agent.id}`);
    } catch (error) {
      this.logger.error('Failed to start conversation:', error);
      await interaction.editReply({
        content: '❌ Failed to start conversation. Please try again.'
      });
    }
  }

  async handleAPIKeysAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {
      const userId = interaction.user.id;
      const userKeys = this.userAPIKeys.get(userId) || [];

      const embed = new EmbedBuilder()
        .setColor(0xffa500)
        .setTitle('🔑 API Key Management')
        .setDescription('Manage your custom AI provider API keys for enhanced functionality')
        .addFields([
          {
            name: '✨ Benefits of Custom API Keys',
            value: '• **Higher rate limits** - More conversations per day\n• **Advanced models** - Access to latest AI capabilities\n• **Cost control** - Use your own billing\n• **Priority access** - Faster response times\n• **Custom settings** - Configure models to your needs',
            inline: false
          },
          {
            name: '🔒 Security & Privacy',
            value: '• **Encrypted storage** - Keys are securely encrypted\n• **No sharing** - Your keys are private to you\n• **Revoke anytime** - Remove keys whenever needed\n• **Usage tracking** - Monitor your API usage\n• **Secure transmission** - End-to-end encryption',
            inline: false
          }
        ])
        .setTimestamp();

      if (userKeys.length === 0) {
        embed.addFields([
          {
            name: '📝 No API Keys Configured',
            value: 'Add your first API key to unlock enhanced AI capabilities and higher usage limits.',
            inline: false
          }
        ]);
      } else {
        embed.addFields([
          {
            name: '🔑 Your API Keys',
            value: userKeys.map((key: any) => {
              const statusEmoji = key.isActive ? '🟢' : '🔴';
              const lastUsed = key.lastUsed ? key.lastUsed.toLocaleDateString() : 'Never';
              return `${statusEmoji} **${key.provider.toUpperCase()}** - ${key.keyName}\n   Added: ${key.addedAt.toLocaleDateString()} | Used: ${key.usageCount} times | Last: ${lastUsed}`;
            }).join('\n\n'),
            inline: false
          }
        ]);
      }

      const selectMenu = new StringSelectMenuBuilder()
        .setCustomId('ai_agent_add_api_key')
        .setPlaceholder('Select a provider to add API key...')
        .addOptions(
          new StringSelectMenuOptionBuilder()
            .setLabel('OpenAI (GPT-4, GPT-3.5)')
            .setDescription('Access to GPT models and advanced features')
            .setValue('openai')
            .setEmoji('🧠'),
          new StringSelectMenuOptionBuilder()
            .setLabel('Anthropic (Claude)')
            .setDescription('Advanced reasoning and long-form conversations')
            .setValue('anthropic')
            .setEmoji('🤖'),
          new StringSelectMenuOptionBuilder()
            .setLabel('Google (Gemini)')
            .setDescription('Multimodal AI with vision capabilities')
            .setValue('google')
            .setEmoji('🌟'),
          new StringSelectMenuOptionBuilder()
            .setLabel('Cohere (Command)')
            .setDescription('Enterprise-grade language models')
            .setValue('cohere')
            .setEmoji('⚡')
        );

      const selectRow = new ActionRowBuilder<StringSelectMenuBuilder>()
        .addComponents(selectMenu);

      const buttonRow = new ActionRowBuilder<ButtonBuilder>()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('ai_agent_manage_keys')
            .setLabel('⚙️ Manage Keys')
            .setStyle(ButtonStyle.Primary)
            .setDisabled(userKeys.length === 0),
          new ButtonBuilder()
            .setCustomId('ai_agent_key_help')
            .setLabel('❓ How to Get Keys')
            .setStyle(ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('ai_agent_usage_stats')
            .setLabel('📊 Usage Stats')
            .setStyle(ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('ai_agent_back')
            .setLabel('🔙 Back')
            .setStyle(ButtonStyle.Secondary)
        );

      await interaction.editReply({
        embeds: [embed],
        components: [selectRow, buttonRow],
        content: null
      });

      this.logger.log(`User ${userId} accessed API key management`);
    } catch (error) {
      this.logger.error('Failed to handle API keys action:', error);
      await interaction.editReply({
        content: '❌ Failed to load API key management. Please try again.'
      });
    }
  }

  async handleConversationHistoryAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {
      const userId = interaction.user.id;
      const userConversations = this.userConversations.get(userId) || [];
      const sortedConversations = userConversations
        .sort((a, b) => b.lastActive.getTime() - a.lastActive.getTime())
        .slice(0, 10);

      const embed = new EmbedBuilder()
        .setColor(0x9b59b6)
        .setTitle('📜 Conversation History')
        .setDescription('View and manage your AI agent conversations')
        .setTimestamp();

      if (sortedConversations.length === 0) {
        embed.addFields([
          {
            name: '💬 No Conversations Yet',
            value: 'Start your first conversation with an AI agent to see your history here.',
            inline: false
          }
        ]);
      } else {
        embed.addFields([
          {
            name: '📊 Quick Stats',
            value: `**Total Conversations:** ${userConversations.length}\n**Active Conversations:** ${userConversations.filter((c: any) => c.status === 'active').length}\n**Total Messages:** ${userConversations.reduce((sum, c) => sum + c.messageCount, 0)}`,
            inline: false
          }
        ]);

        const activeConversations = sortedConversations.filter((conv: any) => conv.status === 'active');
        const completedConversations = sortedConversations.filter((conv: any) => conv.status === 'completed');

        if (activeConversations.length > 0) {
          embed.addFields([
            {
              name: '🟢 Active Conversations',
              value: activeConversations.map((conv: any) => {
                const agent = this.availableAgents.find(a => a.id === conv.agentId);
                const timeSince = this.getTimeSince(conv.lastActive);
                return `**${agent?.name || 'Unknown Agent'}**\n${conv.summary}\n💬 ${conv.messageCount} messages | 🕐 ${timeSince} ago`;
              }).join('\n\n'),
              inline: false
            }
          ]);
        }

        if (completedConversations.length > 0) {
          embed.addFields([
            {
              name: '✅ Completed Conversations',
              value: completedConversations.slice(0, 3).map((conv: any) => {
                const agent = this.availableAgents.find(a => a.id === conv.agentId);
                return `**${agent?.name || 'Unknown Agent'}** - ${conv.messageCount} messages\n${conv.summary}`;
              }).join('\n\n'),
              inline: false
            }
          ]);
        }
      }

      const row = new ActionRowBuilder<ButtonBuilder>()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('ai_agent_resume_conversation')
            .setLabel('🔄 Resume Active')
            .setStyle(ButtonStyle.Success)
            .setDisabled(userConversations.filter((c: any) => c.status === 'active').length === 0),
          new ButtonBuilder()
            .setCustomId('ai_agent_export_history')
            .setLabel('📥 Export History')
            .setStyle(ButtonStyle.Primary)
            .setDisabled(userConversations.length === 0),
          new ButtonBuilder()
            .setCustomId('ai_agent_clear_history')
            .setLabel('🗑️ Clear History')
            .setStyle(ButtonStyle.Danger)
            .setDisabled(userConversations.length === 0),
          new ButtonBuilder()
            .setCustomId('ai_agent_back')
            .setLabel('🔙 Back')
            .setStyle(ButtonStyle.Secondary)
        );

      await interaction.editReply({
        embeds: [embed],
        components: [row],
        content: null
      });

      this.logger.log(`User ${userId} viewed conversation history`);
    } catch (error) {
      this.logger.error('Failed to handle conversation history action:', error);
      await interaction.editReply({
        content: '❌ Failed to load conversation history. Please try again.'
      });
    }
  }

  private initializeSampleData(): void {
    // Initialize available AI agents
    this.availableAgents = [
      {
        id: 'personal-growth-coach',
        name: 'Personal Growth Coach',
        description: 'Goal setting, habit building, motivation, and mindset coaching for personal development',
        type: 'personal-growth',
        isPremium: false,
        capabilities: ['Goal setting', 'Habit tracking', 'Motivation coaching', 'Mindset development', 'Progress monitoring'],
        specialties: ['SMART goals', 'Habit formation', 'Mindset shifts', 'Accountability', 'Progress tracking'],
        availability: '24/7',
        rating: 4.8,
        conversations: 1247
      },
      {
        id: 'intake-specialist',
        name: 'Intake Specialist',
        description: 'Complete your assessment and get personalized recommendations tailored to your needs',
        type: 'intake-specialist',
        isPremium: false,
        capabilities: ['Assessment creation', 'Personalized recommendations', 'Profile building', 'Need analysis', 'Goal identification'],
        specialties: ['User onboarding', 'Need assessment', 'Recommendation engine', 'Profile setup', 'Goal discovery'],
        availability: '24/7',
        rating: 4.6,
        conversations: 892
      },
      {
        id: 'progress-tracker',
        name: 'Progress Tracker',
        description: 'Advanced analytics, detailed reports, and achievement tracking with comprehensive insights',
        type: 'progress-tracker',
        isPremium: true,
        capabilities: ['Advanced analytics', 'Custom reports', 'Achievement tracking', 'Trend analysis', 'Performance insights'],
        specialties: ['Data visualization', 'Progress analytics', 'Performance metrics', 'Achievement systems', 'Reporting'],
        availability: '24/7',
        rating: 4.9,
        conversations: 456
      }
    ];

    // Sample conversation for demo user
    this.userConversations.set('sample-user', [
      {
        id: 'conv_sample_1',
        userId: 'sample-user',
        agentId: 'personal-growth-coach',
        lastActive: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
        messageCount: 15,
        status: 'active',
        summary: 'Working on building a morning routine and improving productivity habits',
        goals: ['Wake up at 6 AM daily', 'Exercise 30 minutes daily', 'Read 20 minutes daily'],
        progress: {
          goalsSet: 3,
          habitsTracked: 5,
          streakDays: 7
        }
      }
    ]);

    // Sample API key for demo user
    this.userAPIKeys.set('sample-user', [
      {
        userId: 'sample-user',
        provider: 'openai',
        keyName: 'Personal OpenAI Key',
        isActive: true,
        addedAt: new Date('2024-01-15'),
        lastUsed: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
        usageCount: 127
      }
    ]);
  }

  private hasPremiumAccess(userId: string): boolean {
    // Mock implementation - would check actual premium status
    return userId === 'sample-premium-user';
  }

  private getTimeSince(date: Date): string {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 60) {
      return `${diffMins} minutes`;
    } else if (diffHours < 24) {
      return `${diffHours} hours`;
    } else {
      return `${diffDays} days`;
    }
  }
}