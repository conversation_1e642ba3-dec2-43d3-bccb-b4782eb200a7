import { Injectable, Logger } from '@nestjs/common';
import { ButtonInteraction, StringSelectMenuInteraction, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, StringSelectMenuBuilder, StringSelectMenuOptionBuilder } from 'discord.js';

export interface CodingProject {
  id: string;
  title: string;
  description: string;
  category: 'web-app' | 'mobile-app' | 'ai-integration' | 'automation' | 'api' | 'data-science';
  techStack: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  author: string;
  githubUrl?: string;
  demoUrl?: string;
  status: 'active' | 'completed' | 'archived';
  likes: number;
  views: number;
  createdAt: Date;
  updatedAt: Date;
  tags: string[];
  learningObjectives: string[];
}

export interface DevelopmentResource {
  id: string;
  title: string;
  description: string;
  type: 'tutorial' | 'documentation' | 'tool' | 'library' | 'framework' | 'course';
  category: string;
  url: string;
  language?: string;
  framework?: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  rating: number;
  author: string;
  lastUpdated: Date;
  isFree: boolean;
  estimatedTime?: string;
}

export interface HelpRequest {
  id: string;
  userId: string;
  title: string;
  description: string;
  codeSnippet?: string;
  language: string;
  framework?: string;
  errorMessage?: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'open' | 'in-progress' | 'resolved' | 'closed';
  tags: string[];
  createdAt: Date;
  responses: HelpResponse[];
}

export interface HelpResponse {
  id: string;
  userId: string;
  username: string;
  content: string;
  codeSnippet?: string;
  isAccepted: boolean;
  votes: number;
  createdAt: Date;
}

export interface ProjectShowcase {
  id: string;
  userId: string;
  username: string;
  projectTitle: string;
  description: string;
  techStack: string[];
  features: string[];
  challenges: string[];
  learnings: string[];
  demoUrl?: string;
  githubUrl?: string;
  images: string[];
  votes: number;
  comments: number;
  createdAt: Date;
  isFeatured: boolean;
}

@Injectable()
export class AICodingDevelopmentActionsHandler {
  private readonly logger = new Logger(AICodingDevelopmentActionsHandler.name);
  private projects: CodingProject[] = [];
  private resources: DevelopmentResource[] = [];
  private helpRequests: HelpRequest[] = [];
  private showcases: ProjectShowcase[] = [];

  constructor() {
    this.initializeSampleData();
  }

  async handleProjectsAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {
      const featuredProjects = this.projects
        .filter((project: any) => project.status === 'active')
        .sort((a, b) => b.likes - a.likes)
        .slice(0, 5);

      const embed = new EmbedBuilder()
        .setColor(0x00d4ff)
        .setTitle('🚀 AI Coding Projects')
        .setDescription('Explore innovative projects integrating AI with modern development')
        .addFields([
          {
            name: '🎯 Project Categories',
            value: '**🌐 Web Applications** - Full-stack apps with AI features\n**📱 Mobile Apps** - iOS/Android with AI integration\n**🤖 AI Integration** - Adding AI to existing systems\n**⚡ Automation** - AI-powered workflow automation\n**🔗 APIs & Services** - AI-powered backend services\n**📊 Data Science** - ML models and data analysis',
            inline: false
          }
        ])
        .setTimestamp();

      if (featuredProjects.length === 0) {
        embed.addFields([{
          name: '📂 No Active Projects',
          value: 'Be the first to share your AI coding project with the community!',
          inline: false
        }]);
      } else {
        embed.addFields([
          {
            name: '🔥 Featured Projects',
            value: `Showcasing ${featuredProjects.length} trending projects`,
            inline: false
          }
        ]);

        for (const project of featuredProjects) {
          const difficultyEmoji = this.getDifficultyEmoji(project.difficulty);
          const categoryEmoji = this.getProjectCategoryEmoji(project.category);
          
          embed.addFields([{
            name: `${categoryEmoji} ${project.title} ${difficultyEmoji}`,
            value: `${project.description}\n` +
                   `**Tech Stack:** ${project.techStack.slice(0, 3).join(', ')}${project.techStack.length > 3 ? '...' : ''}\n` +
                   `**Author:** ${project.author}\n` +
                   `**Stats:** 👍 ${project.likes} | 👀 ${project.views}\n` +
                   `**Tags:** ${project.tags.slice(0, 3).join(', ')}`,
            inline: false
          }]);
        }

        embed.addFields([
          {
            name: '💡 Learning Focus',
            value: '• **AI Integration** - Learn to add AI capabilities to apps\n• **Modern Stack** - Work with cutting-edge technologies\n• **Best Practices** - Follow industry-standard patterns\n• **Real Projects** - Build portfolio-worthy applications',
            inline: false
          }
        ]);
      }

      const selectMenu = new StringSelectMenuBuilder()
        .setCustomId('ai_coding_project_category')
        .setPlaceholder('Filter by project category...')
        .addOptions(
          new StringSelectMenuOptionBuilder()
            .setLabel('🌐 Web Applications')
            .setDescription('Full-stack web apps with AI')
            .setValue('web-app')
            .setEmoji('🌐'),
          new StringSelectMenuOptionBuilder()
            .setLabel('📱 Mobile Applications')
            .setDescription('iOS/Android apps with AI')
            .setValue('mobile-app')
            .setEmoji('📱'),
          new StringSelectMenuOptionBuilder()
            .setLabel('🤖 AI Integration')
            .setDescription('Adding AI to existing systems')
            .setValue('ai-integration')
            .setEmoji('🤖'),
          new StringSelectMenuOptionBuilder()
            .setLabel('⚡ Automation Projects')
            .setDescription('AI-powered automation')
            .setValue('automation')
            .setEmoji('⚡'),
          new StringSelectMenuOptionBuilder()
            .setLabel('📊 Data Science')
            .setDescription('ML models and analytics')
            .setValue('data-science')
            .setEmoji('📊')
        );

      const selectRow = new ActionRowBuilder<StringSelectMenuBuilder>()
        .addComponents(selectMenu);

      const buttonRow = new ActionRowBuilder<ButtonBuilder>()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('ai_coding_project_details')
            .setLabel('👀 View Details')
            .setStyle(ButtonStyle.Primary),
          new ButtonBuilder()
            .setCustomId('ai_coding_project_fork')
            .setLabel('🍴 Fork Project')
            .setStyle(ButtonStyle.Success),
          new ButtonBuilder()
            .setCustomId('ai_coding_project_submit')
            .setLabel('➕ Submit Project')
            .setStyle(ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('ai_coding_back')
            .setLabel('🔙 Back')
            .setStyle(ButtonStyle.Secondary)
        );

      await interaction.editReply({
        embeds: [embed],
        components: [selectRow, buttonRow],
        content: null
      });

      this.logger.log(`User ${interaction.user.id} browsed AI coding projects`);
    } catch (error) {
      this.logger.error('Failed to handle projects action:', error);
      await interaction.editReply({
        content: '❌ Failed to load coding projects. Please try again.'
      });
    }
  }

  async handleResourcesAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {
      const topResources = this.resources
        .sort((a, b) => b.rating - a.rating)
        .slice(0, 6);

      const embed = new EmbedBuilder()
        .setColor(0xff6b35)
        .setTitle('📚 Development Resources')
        .setDescription('Curated collection of tutorials, tools, and frameworks for AI development')
        .addFields([
          {
            name: '🎯 Resource Types',
            value: '**📖 Tutorials** - Step-by-step learning guides\n**📋 Documentation** - Official docs and references\n**🛠️ Tools** - Development tools and utilities\n**📦 Libraries** - Code libraries and packages\n**🏗️ Frameworks** - Development frameworks\n**🎓 Courses** - Comprehensive learning paths',
            inline: false
          }
        ])
        .setTimestamp();

      if (topResources.length === 0) {
        embed.addFields([{
          name: '📚 No Resources Available',
          value: 'Resources are being curated. Check back soon for the latest development materials!',
          inline: false
        }]);
      } else {
        embed.addFields([
          {
            name: '⭐ Top-Rated Resources',
            value: `${topResources.length} highly-rated resources available`,
            inline: false
          }
        ]);

        for (const resource of topResources) {
          const typeEmoji = this.getResourceTypeEmoji(resource.type);
          const difficultyEmoji = this.getDifficultyEmoji(resource.difficulty);
          const priceEmoji = resource.isFree ? '🆓' : '💰';
          
          embed.addFields([{
            name: `${typeEmoji} ${resource.title} ${difficultyEmoji} ${priceEmoji}`,
            value: `${resource.description}\n` +
                   `**Author:** ${resource.author}\n` +
                   `**Language:** ${resource.language || 'Multiple'}\n` +
                   `**Rating:** ${'⭐'.repeat(Math.floor(resource.rating))} (${resource.rating}/5)\n` +
                   `${resource.estimatedTime ? `**Duration:** ${resource.estimatedTime}\n` : ''}` +
                   `**Updated:** ${resource.lastUpdated.toLocaleDateString()}`,
            inline: false
          }]);
        }

        // Statistics
        const freeResources = this.resources.filter((r: any) => r.isFree).length;
        const avgRating = (this.resources.reduce((sum, r) => sum + r.rating, 0) / this.resources.length).toFixed(1);
        
        embed.addFields([
          {
            name: '📊 Resource Stats',
            value: `**Total Resources:** ${this.resources.length}\n**Free Resources:** ${freeResources} (${Math.round((freeResources / this.resources.length) * 100)}%)\n**Average Rating:** ${avgRating}/5\n**Languages Covered:** ${this.getUniqueLanugages().length}`,
            inline: false
          }
        ]);
      }

      const selectMenu = new StringSelectMenuBuilder()
        .setCustomId('ai_coding_resource_type')
        .setPlaceholder('Filter by resource type...')
        .addOptions(
          new StringSelectMenuOptionBuilder()
            .setLabel('📖 Tutorials')
            .setDescription('Learning tutorials and guides')
            .setValue('tutorial')
            .setEmoji('📖'),
          new StringSelectMenuOptionBuilder()
            .setLabel('🛠️ Tools')
            .setDescription('Development tools and utilities')
            .setValue('tool')
            .setEmoji('🛠️'),
          new StringSelectMenuOptionBuilder()
            .setLabel('📦 Libraries')
            .setDescription('Code libraries and packages')
            .setValue('library')
            .setEmoji('📦'),
          new StringSelectMenuOptionBuilder()
            .setLabel('🏗️ Frameworks')
            .setDescription('Development frameworks')
            .setValue('framework')
            .setEmoji('🏗️'),
          new StringSelectMenuOptionBuilder()
            .setLabel('🎓 Courses')
            .setDescription('Comprehensive courses')
            .setValue('course')
            .setEmoji('🎓')
        );

      const selectRow = new ActionRowBuilder<StringSelectMenuBuilder>()
        .addComponents(selectMenu);

      const buttonRow = new ActionRowBuilder<ButtonBuilder>()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('ai_coding_resource_access')
            .setLabel('🔗 Access Resource')
            .setStyle(ButtonStyle.Success),
          new ButtonBuilder()
            .setCustomId('ai_coding_resource_bookmark')
            .setLabel('🔖 Bookmark')
            .setStyle(ButtonStyle.Primary),
          new ButtonBuilder()
            .setCustomId('ai_coding_resource_suggest')
            .setLabel('➕ Suggest Resource')
            .setStyle(ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('ai_coding_back')
            .setLabel('🔙 Back')
            .setStyle(ButtonStyle.Secondary)
        );

      await interaction.editReply({
        embeds: [embed],
        components: [selectRow, buttonRow],
        content: null
      });

      this.logger.log(`User ${interaction.user.id} browsed development resources`);
    } catch (error) {
      this.logger.error('Failed to handle resources action:', error);
      await interaction.editReply({
        content: '❌ Failed to load development resources. Please try again.'
      });
    }
  }

  async handleGetHelpAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {
      const userId = interaction.user.id;
      const recentHelp = this.helpRequests
        .filter((req: any) => req.status === 'open' || req.status === 'in-progress')
        .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
        .slice(0, 5);

      const embed = new EmbedBuilder()
        .setColor(0x00ff88)
        .setTitle('🆘 Get Coding Help')
        .setDescription('Get expert assistance with your AI development challenges')
        .addFields([
          {
            name: '🎯 What We Help With',
            value: '**🐛 Debugging** - Fix errors and unexpected behavior\n**⚡ Performance** - Optimize slow code and improve efficiency\n**🏗️ Architecture** - Design scalable system architecture\n**🤖 AI Integration** - Add AI features to your applications\n**📦 Dependencies** - Resolve package and library issues\n**🔧 Best Practices** - Code review and improvement suggestions',
            inline: false
          },
          {
            name: '💪 Support Languages & Frameworks',
            value: '**Languages:** Python, JavaScript, TypeScript, Java, C++, Go, Rust\n**Web:** React, Next.js, Vue, Angular, Node.js, Django, FastAPI\n**AI/ML:** TensorFlow, PyTorch, Scikit-learn, Hugging Face\n**Mobile:** React Native, Flutter, Swift, Kotlin\n**Cloud:** AWS, GCP, Azure, Docker, Kubernetes',
            inline: false
          }
        ])
        .setTimestamp();

      if (recentHelp.length === 0) {
        embed.addFields([{
          name: '💬 Community Help',
          value: 'No active help requests right now. Be the first to ask for assistance or help others!',
          inline: false
        }]);
      } else {
        embed.addFields([
          {
            name: '🔥 Recent Help Requests',
            value: `${recentHelp.length} active requests needing assistance`,
            inline: false
          }
        ]);

        for (const helpReq of recentHelp.slice(0, 3)) {
          const priorityEmoji = this.getPriorityEmoji(helpReq.priority);
          const statusEmoji = this.getStatusEmoji(helpReq.status);
          
          embed.addFields([{
            name: `${priorityEmoji} ${statusEmoji} ${helpReq.title}`,
            value: `${helpReq.description.substring(0, 100)}${helpReq.description.length > 100 ? '...' : ''}\n` +
                   `**Language:** ${helpReq.language}${helpReq.framework ? ` (${helpReq.framework})` : ''}\n` +
                   `**Responses:** ${helpReq.responses.length} | **Priority:** ${helpReq.priority}\n` +
                   `**Tags:** ${helpReq.tags.slice(0, 3).join(', ')}`,
            inline: false
          }]);
        }

        if (recentHelp.length > 3) {
          embed.addFields([{
            name: '➕ More Requests',
            value: `${recentHelp.length - 3} more requests available. Use "Browse All" to see everything.`,
            inline: false
          }]);
        }
      }

      // Add help statistics
      const totalHelp = this.helpRequests.length;
      const resolvedHelp = this.helpRequests.filter((req: any) => req.status === 'resolved').length;
      const avgResponseTime = this.calculateAverageResponseTime();

      embed.addFields([
        {
          name: '📊 Help Statistics',
          value: `**Total Requests:** ${totalHelp}\n**Resolved:** ${resolvedHelp} (${totalHelp > 0 ? Math.round((resolvedHelp / totalHelp) * 100) : 0}%)\n**Avg Response Time:** ${avgResponseTime}\n**Active Helpers:** ${Math.floor(Math.random() * 20) + 5}`,
          inline: false
        }
      ]);

      const row = new ActionRowBuilder<ButtonBuilder>()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('ai_coding_ask_question')
            .setLabel('❓ Ask Question')
            .setStyle(ButtonStyle.Success),
          new ButtonBuilder()
            .setCustomId('ai_coding_browse_help')
            .setLabel('👀 Browse Requests')
            .setStyle(ButtonStyle.Primary),
          new ButtonBuilder()
            .setCustomId('ai_coding_help_others')
            .setLabel('🤝 Help Others')
            .setStyle(ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('ai_coding_back')
            .setLabel('🔙 Back')
            .setStyle(ButtonStyle.Secondary)
        );

      await interaction.editReply({
        embeds: [embed],
        components: [row],
        content: null
      });

      this.logger.log(`User ${userId} accessed coding help system`);
    } catch (error) {
      this.logger.error('Failed to handle get help action:', error);
      await interaction.editReply({
        content: '❌ Failed to load help system. Please try again.'
      });
    }
  }

  async handleShowcaseAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {
      const userId = interaction.user.id;
      const featuredShowcases = this.showcases
        .filter((showcase: any) => showcase.isFeatured)
        .sort((a, b) => b.votes - a.votes)
        .slice(0, 4);

      const recentShowcases = this.showcases
        .filter((showcase: any) => !showcase.isFeatured)
        .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
        .slice(0, 3);

      const embed = new EmbedBuilder()
        .setColor(0xfeca57)
        .setTitle('⭐ Project Showcase')
        .setDescription('Discover amazing projects built by our community members')
        .addFields([
          {
            name: '🎨 What to Showcase',
            value: '**🚀 Completed Projects** - Finished applications and tools\n**🧪 Interesting Experiments** - Innovative prototypes and demos\n**🤖 AI Integrations** - Projects featuring AI capabilities\n**📱 Mobile Apps** - iOS/Android applications\n**🌐 Web Applications** - Full-stack web projects\n**🔧 Developer Tools** - Utilities and frameworks',
            inline: false
          }
        ])
        .setTimestamp();

      if (featuredShowcases.length === 0 && recentShowcases.length === 0) {
        embed.addFields([{
          name: '🌟 No Showcases Yet',
          value: 'Be the first to showcase your amazing project to the community!',
          inline: false
        }]);
      } else {
        if (featuredShowcases.length > 0) {
          embed.addFields([
            {
              name: '👑 Featured Projects',
              value: `${featuredShowcases.length} exceptional projects selected by the community`,
              inline: false
            }
          ]);

          for (const showcase of featuredShowcases) {
            embed.addFields([{
              name: `🌟 ${showcase.projectTitle}`,
              value: `**By:** ${showcase.username}\n` +
                     `${showcase.description.substring(0, 120)}${showcase.description.length > 120 ? '...' : ''}\n` +
                     `**Tech Stack:** ${showcase.techStack.slice(0, 4).join(', ')}${showcase.techStack.length > 4 ? '...' : ''}\n` +
                     `**Stats:** 👍 ${showcase.votes} votes | 💬 ${showcase.comments} comments`,
              inline: false
            }]);
          }
        }

        if (recentShowcases.length > 0) {
          embed.addFields([
            {
              name: '🆕 Recent Showcases',
              value: recentShowcases.map((showcase: any) => 
                `• **${showcase.projectTitle}** by ${showcase.username}\n  ${showcase.techStack.slice(0, 2).join(', ')} | 👍 ${showcase.votes}`
              ).join('\n\n'),
              inline: false
            }
          ]);
        }

        // Add showcase statistics
        const totalShowcases = this.showcases.length;
        const totalVotes = this.showcases.reduce((sum, s) => sum + s.votes, 0);
        const avgVotes = totalShowcases > 0 ? (totalVotes / totalShowcases).toFixed(1) : '0';

        embed.addFields([
          {
            name: '📊 Showcase Stats',
            value: `**Total Projects:** ${totalShowcases}\n**Total Votes:** ${totalVotes}\n**Average Votes:** ${avgVotes}\n**Featured Projects:** ${featuredShowcases.length}`,
            inline: false
          }
        ]);
      }

      const row = new ActionRowBuilder<ButtonBuilder>()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('ai_coding_showcase_submit')
            .setLabel('➕ Submit Project')
            .setStyle(ButtonStyle.Success),
          new ButtonBuilder()
            .setCustomId('ai_coding_showcase_browse')
            .setLabel('👀 Browse All')
            .setStyle(ButtonStyle.Primary),
          new ButtonBuilder()
            .setCustomId('ai_coding_showcase_vote')
            .setLabel('👍 Vote on Projects')
            .setStyle(ButtonStyle.Secondary),
          new ButtonBuilder()
            .setCustomId('ai_coding_back')
            .setLabel('🔙 Back')
            .setStyle(ButtonStyle.Secondary)
        );

      await interaction.editReply({
        embeds: [embed],
        components: [row],
        content: null
      });

      this.logger.log(`User ${userId} viewed project showcase`);
    } catch (error) {
      this.logger.error('Failed to handle showcase action:', error);
      await interaction.editReply({
        content: '❌ Failed to load project showcase. Please try again.'
      });
    }
  }

  private initializeSampleData(): void {
    // Sample coding projects
    this.projects = [
      {
        id: 'project-1',
        title: 'AI-Powered Task Manager',
        description: 'Smart task manager that uses AI to prioritize and schedule tasks automatically',
        category: 'web-app',
        techStack: ['React', 'Node.js', 'OpenAI API', 'MongoDB', 'TypeScript'],
        difficulty: 'intermediate',
        author: 'Sarah Chen',
        githubUrl: 'https://github.com/example/ai-task-manager',
        demoUrl: 'https://ai-taskmanager.demo.com',
        status: 'active',
        likes: 127,
        views: 1543,
        createdAt: new Date('2024-03-15'),
        updatedAt: new Date('2024-05-20'),
        tags: ['AI', 'productivity', 'automation', 'react'],
        learningObjectives: ['AI API integration', 'Task scheduling algorithms', 'React state management']
      },
      {
        id: 'project-2',
        title: 'Smart Code Review Bot',
        description: 'GitHub bot that provides intelligent code review suggestions using AI',
        category: 'automation',
        techStack: ['Python', 'GitHub API', 'FastAPI', 'PostgreSQL', 'Docker'],
        difficulty: 'advanced',
        author: 'Mike Rodriguez',
        githubUrl: 'https://github.com/example/code-review-bot',
        status: 'active',
        likes: 89,
        views: 967,
        createdAt: new Date('2024-02-10'),
        updatedAt: new Date('2024-05-18'),
        tags: ['automation', 'code-review', 'github', 'python'],
        learningObjectives: ['GitHub API integration', 'Code analysis', 'CI/CD automation']
      },
      {
        id: 'project-3',
        title: 'AI Fitness Companion',
        description: 'Mobile app that creates personalized workout plans using machine learning',
        category: 'mobile-app',
        techStack: ['React Native', 'TensorFlow Lite', 'Firebase', 'Python Backend'],
        difficulty: 'intermediate',
        author: 'Alex Johnson',
        status: 'active',
        likes: 156,
        views: 2103,
        createdAt: new Date('2024-04-01'),
        updatedAt: new Date('2024-05-25'),
        tags: ['mobile', 'health', 'ml', 'react-native'],
        learningObjectives: ['Mobile ML integration', 'Fitness algorithms', 'Real-time data processing']
      }
    ];

    // Sample development resources
    this.resources = [
      {
        id: 'resource-1',
        title: 'Complete Guide to OpenAI API Integration',
        description: 'Comprehensive tutorial on integrating OpenAI APIs into web applications',
        type: 'tutorial',
        category: 'AI Integration',
        url: 'https://example.com/openai-guide',
        language: 'JavaScript',
        framework: 'React',
        difficulty: 'intermediate',
        rating: 4.8,
        author: 'AI Academy',
        lastUpdated: new Date('2024-05-15'),
        isFree: true,
        estimatedTime: '3 hours'
      },
      {
        id: 'resource-2',
        title: 'LangChain for Developers',
        description: 'Build AI-powered applications with LangChain framework',
        type: 'course',
        category: 'AI Development',
        url: 'https://example.com/langchain-course',
        language: 'Python',
        difficulty: 'advanced',
        rating: 4.9,
        author: 'Tech Innovators',
        lastUpdated: new Date('2024-05-10'),
        isFree: false,
        estimatedTime: '12 hours'
      },
      {
        id: 'resource-3',
        title: 'TensorFlow.js Toolkit',
        description: 'JavaScript library for machine learning in the browser and Node.js',
        type: 'library',
        category: 'Machine Learning',
        url: 'https://tensorflow.org/js',
        language: 'JavaScript',
        difficulty: 'intermediate',
        rating: 4.7,
        author: 'Google',
        lastUpdated: new Date('2024-05-20'),
        isFree: true
      }
    ];

    // Sample help requests
    this.helpRequests = [
      {
        id: 'help-1',
        userId: 'user-1',
        title: 'OpenAI API rate limiting issues',
        description: 'Getting rate limited when making multiple API calls in my React app. Need advice on proper rate limiting and caching strategies.',
        language: 'JavaScript',
        framework: 'React',
        priority: 'medium',
        status: 'open',
        tags: ['openai', 'rate-limiting', 'react', 'api'],
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
        responses: []
      },
      {
        id: 'help-2',
        userId: 'user-2',
        title: 'TensorFlow model deployment to production',
        description: 'Successfully trained a model locally but struggling with deployment to AWS. Looking for best practices and architecture advice.',
        language: 'Python',
        framework: 'TensorFlow',
        priority: 'high',
        status: 'in-progress',
        tags: ['tensorflow', 'deployment', 'aws', 'production'],
        createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
        responses: [
          {
            id: 'response-1',
            userId: 'helper-1',
            username: 'DevExpert',
            content: 'I recommend using AWS SageMaker for model deployment. Here\'s a step-by-step approach...',
            isAccepted: false,
            votes: 3,
            createdAt: new Date(Date.now() - 20 * 60 * 60 * 1000)
          }
        ]
      }
    ];

    // Sample project showcases
    this.showcases = [
      {
        id: 'showcase-1',
        userId: 'creator-1',
        username: 'InnovativeDev',
        projectTitle: 'AI-Powered Stock Analysis Tool',
        description: 'Built a comprehensive stock analysis tool that uses multiple AI models to predict market trends and provide investment recommendations',
        techStack: ['Python', 'Streamlit', 'YFinance', 'Scikit-learn', 'Plotly'],
        features: ['Real-time data processing', 'Multiple ML models', 'Interactive dashboards', 'Risk assessment'],
        challenges: ['Data quality issues', 'Model accuracy optimization', 'Real-time performance'],
        learnings: ['Financial data APIs', 'Time series analysis', 'Model ensemble techniques'],
        demoUrl: 'https://stock-ai-demo.com',
        githubUrl: 'https://github.com/innovativedev/stock-ai',
        images: [],
        votes: 87,
        comments: 23,
        createdAt: new Date('2024-05-01'),
        isFeatured: true
      },
      {
        id: 'showcase-2',
        userId: 'creator-2',
        username: 'MobileWizard',
        projectTitle: 'Smart Recipe Assistant',
        description: 'Mobile app that recognizes ingredients through camera and suggests personalized recipes using AI',
        techStack: ['React Native', 'TensorFlow Lite', 'OpenAI API', 'Firebase'],
        features: ['Image recognition', 'Recipe generation', 'Dietary preferences', 'Shopping lists'],
        challenges: ['Mobile ML optimization', 'Camera integration', 'Offline functionality'],
        learnings: ['Mobile ML deployment', 'Computer vision', 'User experience design'],
        images: [],
        votes: 42,
        comments: 15,
        createdAt: new Date('2024-05-10'),
        isFeatured: false
      }
    ];
  }

  private getDifficultyEmoji(difficulty: string): string {
    const emojiMap: Record<string, string> = {
      'beginner': '🟢',
      'intermediate': '🟡',
      'advanced': '🟠',
      'expert': '🔴'
    };
    return emojiMap[difficulty] || '⚪';
  }

  private getProjectCategoryEmoji(category: string): string {
    const emojiMap: Record<string, string> = {
      'web-app': '🌐',
      'mobile-app': '📱',
      'ai-integration': '🤖',
      'automation': '⚡',
      'api': '🔗',
      'data-science': '📊'
    };
    return emojiMap[category] || '💻';
  }

  private getResourceTypeEmoji(type: string): string {
    const emojiMap: Record<string, string> = {
      'tutorial': '📖',
      'documentation': '📋',
      'tool': '🛠️',
      'library': '📦',
      'framework': '🏗️',
      'course': '🎓'
    };
    return emojiMap[type] || '📄';
  }

  private getPriorityEmoji(priority: string): string {
    const emojiMap: Record<string, string> = {
      'low': '🟢',
      'medium': '🟡',
      'high': '🟠',
      'urgent': '🔴'
    };
    return emojiMap[priority] || '⚪';
  }

  private getStatusEmoji(status: string): string {
    const emojiMap: Record<string, string> = {
      'open': '🔓',
      'in-progress': '⏳',
      'resolved': '✅',
      'closed': '🔒'
    };
    return emojiMap[status] || '❓';
  }

  private getUniqueLanugages(): string[] {
    const languages = new Set(this.resources.map((r: any) => r.language).filter(Boolean));
    return Array.from(languages) as string[];
  }

  private calculateAverageResponseTime(): string {
    // Mock calculation - would analyze actual response times
    const avgHours = Math.floor(Math.random() * 8) + 2;
    return `${avgHours} hours`;
  }
}