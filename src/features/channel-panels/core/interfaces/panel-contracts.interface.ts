/**
 * Clean Architecture Contracts for Discord Panel System
 * 
 * These interfaces define the core contracts that establish clear boundaries
 * between different layers and components of the panel system.
 */

import { ButtonInteraction, StringSelectMenuInteraction, EmbedBuilder, ActionRowBuilder } from 'discord.js';

// ============================================================================
// CORE DOMAIN INTERFACES
// ============================================================================

/**
 * Represents a user's interaction context within the panel system
 */
export interface UserContext {
  readonly userId: string;
  readonly username: string;
  readonly guildId: string;
  readonly channelId: string;
  readonly permissions: string[];
  readonly preferredLanguage?: string;
  readonly timezone?: string;
}

/**
 * Immutable panel state that tracks user interactions and panel status
 */
export interface PanelState {
  readonly panelId: string;
  readonly userId: string;
  readonly currentView: string;
  readonly previousView?: string;
  readonly sessionData: Record<string, unknown>;
  readonly lastInteraction: Date;
  readonly viewCount: number;
  readonly isActive: boolean;
}

/**
 * Panel content that will be rendered to Discord
 */
export interface PanelRenderData {
  readonly embeds: EmbedBuilder[];
  readonly components: ActionRowBuilder[];
  readonly content?: string;
  readonly ephemeral?: boolean;
}

/**
 * Result of processing a user interaction
 */
export interface InteractionResult {
  readonly success: boolean;
  readonly renderData?: PanelRenderData;
  readonly newState?: Partial<PanelState>;
  readonly errorMessage?: string;
  readonly shouldUpdatePanel?: boolean;
}

// ============================================================================
// ACTION HANDLING CONTRACTS
// ============================================================================

/**
 * Defines a specific action that can be performed within a panel
 */
export interface PanelAction {
  readonly actionId: string;
  readonly panelType: string;
  readonly displayName: string;
  readonly description: string;
  readonly requiresPermission?: string;
  readonly cooldownSeconds?: number;
}

/**
 * Context provided when an action is triggered
 */
export interface ActionContext {
  readonly action: PanelAction;
  readonly userContext: UserContext;
  readonly currentState: PanelState;
  readonly interactionData: unknown;
  readonly timestamp: Date;
}

/**
 * Contract for handling specific panel actions
 * Each panel type will have dedicated action handlers implementing this interface
 */
export interface IActionHandler {
  /**
   * Unique identifier for this action handler
   */
  readonly handlerId: string;
  
  /**
   * Panel types this handler supports
   */
  readonly supportedPanelTypes: string[];
  
  /**
   * Actions this handler can process
   */
  readonly supportedActions: string[];
  
  /**
   * Check if this handler can process the given action
   */
  canHandle(panelType: string, actionId: string): boolean;
  
  /**
   * Process the action and return the result
   */
  handleAction(context: ActionContext): Promise<InteractionResult>;
  
  /**
   * Validate if the user can perform this action
   */
  validateAction(context: ActionContext): Promise<boolean>;
}

// ============================================================================
// CONTENT PROVIDER CONTRACTS
// ============================================================================

/**
 * Configuration for fetching dynamic content
 */
export interface ContentRequest {
  readonly contentType: string;
  readonly parameters: Record<string, unknown>;
  readonly userContext: UserContext;
  readonly cacheStrategy: 'none' | 'memory' | 'persistent';
  readonly freshnessTolerance: number; // seconds
}

/**
 * Dynamic content returned by content providers
 */
export interface ContentResponse<T = unknown> {
  readonly data: T;
  readonly source: string;
  readonly timestamp: Date;
  readonly expiresAt?: Date;
  readonly metadata: Record<string, unknown>;
}

/**
 * Contract for providing dynamic content to panels
 * Separates data fetching concerns from presentation logic
 */
export interface IContentProvider<T = unknown> {
  /**
   * Unique identifier for this content provider
   */
  readonly providerId: string;
  
  /**
   * Content types this provider can supply
   */
  readonly supportedContentTypes: string[];
  
  /**
   * Check if this provider can supply the requested content
   */
  canProvide(contentType: string): boolean;
  
  /**
   * Fetch the requested content
   */
  getContent(request: ContentRequest): Promise<ContentResponse<T>>;
  
  /**
   * Check if cached content is still valid
   */
  isContentFresh(content: ContentResponse<T>, tolerance: number): boolean;
}

// ============================================================================
// STATE MANAGEMENT CONTRACTS
// ============================================================================

/**
 * Contract for managing panel state persistence and retrieval
 */
export interface IStateManager {
  /**
   * Retrieve current state for a user's panel
   */
  getState(panelId: string, userId: string): Promise<PanelState | null>;
  
  /**
   * Update panel state with new data
   */
  updateState(panelId: string, userId: string, updates: Partial<PanelState>): Promise<void>;
  
  /**
   * Create initial state for a new panel session
   */
  createInitialState(panelId: string, userContext: UserContext, initialView: string): Promise<PanelState>;
  
  /**
   * Clean up expired or inactive states
   */
  cleanupExpiredStates(maxAge: number): Promise<number>;
  
  /**
   * Get all active states for a specific panel
   */
  getActiveStates(panelId: string): Promise<PanelState[]>;
  
  /**
   * Record user interaction with a panel
   */
  recordInteraction(panelId: string, userId: string, actionId: string, actionData?: any): Promise<void>;
}

// ============================================================================
// PANEL FACTORY CONTRACTS
// ============================================================================

/**
 * Configuration required to create a panel instance
 */
export interface PanelConfiguration {
  readonly panelType: string;
  readonly panelId: string;
  readonly channelId: string;
  readonly guildId: string;
  readonly title: string;
  readonly description: string;
  readonly isEnabled: boolean;
  readonly customSettings: Record<string, unknown>;
}

/**
 * A complete panel instance with all capabilities
 */
export interface IPanelInstance {
  readonly configuration: PanelConfiguration;
  readonly supportedActions: PanelAction[];
  
  /**
   * Generate initial content for the panel
   */
  generateInitialContent(userContext: UserContext): Promise<PanelRenderData>;
  
  /**
   * Process a user interaction with this panel
   */
  processInteraction(
    interaction: ButtonInteraction | StringSelectMenuInteraction,
    userContext: UserContext
  ): Promise<InteractionResult>;
  
  /**
   * Refresh panel content when needed
   */
  refreshContent(userContext: UserContext, currentState: PanelState): Promise<PanelRenderData>;
  
  /**
   * Clean up resources when panel is destroyed
   */
  destroy(): Promise<void>;
}

/**
 * Contract for creating panel instances
 */
export interface IPanelFactory {
  /**
   * Panel types this factory can create
   */
  readonly supportedPanelTypes: string[];
  
  /**
   * Check if this factory can create the requested panel type
   */
  canCreate(panelType: string): boolean;
  
  /**
   * Create a new panel instance
   */
  createPanel(configuration: PanelConfiguration): Promise<IPanelInstance>;
  
  /**
   * Get default configuration for a panel type
   */
  getDefaultConfiguration(panelType: string, channelId: string, guildId: string): PanelConfiguration;
}

// ============================================================================
// ORCHESTRATION CONTRACTS
// ============================================================================

/**
 * High-level orchestration service that coordinates all panel operations
 * This is the main entry point for the panel system
 */
export interface IPanelOrchestrator {
  /**
   * Deploy a panel to a channel
   */
  deployPanel(configuration: PanelConfiguration): Promise<void>;
  
  /**
   * Handle an incoming Discord interaction
   */
  handleInteraction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void>;
  
  /**
   * Get the current status of a panel
   */
  getPanelStatus(panelId: string): Promise<PanelStatus>;
  
  /**
   * Refresh all panels in a channel
   */
  refreshChannelPanels(channelId: string): Promise<void>;
  
  /**
   * Remove a panel from a channel
   */
  removePanel(panelId: string): Promise<void>;
}

/**
 * Current status of a panel
 */
export interface PanelStatus {
  readonly panelId: string;
  readonly isActive: boolean;
  readonly lastUpdate: Date;
  readonly activeUsers: number;
  readonly totalInteractions: number;
  readonly errorCount: number;
  readonly health: 'healthy' | 'degraded' | 'unhealthy';
}

// ============================================================================
// ERROR HANDLING
// ============================================================================

/**
 * Panel-specific error types
 */
export class PanelError extends Error {
  constructor(
    message: string,
    public readonly panelId: string,
    public readonly userId?: string,
    public readonly actionId?: string,
    public readonly cause?: Error
  ) {
    super(message);
    this.name = 'PanelError';
  }
}

export class ActionHandlerError extends PanelError {
  constructor(
    message: string,
    panelId: string,
    actionId: string,
    userId?: string,
    cause?: Error
  ) {
    super(message, panelId, userId, actionId, cause);
    this.name = 'ActionHandlerError';
  }
}

export class ContentProviderError extends Error {
  constructor(
    message: string,
    public readonly providerId: string,
    public readonly contentType: string,
    public readonly cause?: Error
  ) {
    super(message);
    this.name = 'ContentProviderError';
  }
}