import { ChannelContext, PanelContent } from './panel.interface';

export interface EnhancedPanelContent extends PanelContent {
  personalizedElements?: {
    greeting?: string;
    recommendedActions?: string[];
    hiddenSections?: string[];
    customTheme?: string;
  };
  dynamicData?: {
    sourceId: string;
    content: any;
    lastUpdated: Date;
    freshness: 'fresh' | 'stale' | 'expired';
  }[];
  analytics?: {
    sessionId: string;
    trackingEnabled: boolean;
    experimentVariant?: string;
  };
}

export interface PanelPersonalization {
  userId: string;
  preferences: {
    theme: 'default' | 'dark' | 'minimal' | 'colorful';
    layout: 'compact' | 'expanded' | 'grid';
    showAdvanced: boolean;
    quickActions: string[];
    hiddenSections: string[];
    notifications: {
      enabled: boolean;
      types: string[];
      frequency: 'immediate' | 'daily' | 'weekly';
    };
  };
  history: {
    lastVisit: Date;
    totalVisits: number;
    averageEngagementTime: number;
    preferredFeatures: string[];
  };
  progress: {
    completedTasks: string[];
    currentStreak: number;
    achievements: string[];
    experiencePoints: number;
  };
}

export interface DynamicContentConfig {
  sourceId: string;
  refreshInterval: number; // seconds
  cacheStrategy: 'memory' | 'database' | 'hybrid';
  transformation?: {
    template?: string;
    filters?: Array<{
      field: string;
      operator: 'equals' | 'contains' | 'greater' | 'less';
      value: any;
    }>;
    mapping?: Record<string, string>;
  };
  fallback?: {
    enabled: boolean;
    content: any;
    maxAge: number; // seconds
  };
}

export interface AnalyticsConfig {
  trackViews: boolean;
  trackInteractions: boolean;
  trackPerformance: boolean;
  trackErrors: boolean;
  sessionTracking: boolean;
  abTesting?: {
    enabled: boolean;
    testId: string;
    variants: string[];
    trafficAllocation: number; // percentage
  };
  customEvents?: Array<{
    name: string;
    trigger: 'click' | 'view' | 'time' | 'custom';
    target?: string;
    condition?: any;
  }>;
}

export interface PanelHealthStatus {
  overall: 'healthy' | 'degraded' | 'critical';
  components: {
    configuration: 'valid' | 'invalid' | 'warning';
    dynamicContent: 'fresh' | 'stale' | 'error';
    analytics: 'active' | 'inactive' | 'error';
    userState: 'synced' | 'degraded' | 'error';
  };
  metrics: {
    uptime: number; // percentage
    errorRate: number; // percentage
    averageResponseTime: number; // milliseconds
    cacheHitRate: number; // percentage
  };
  issues: Array<{
    severity: 'low' | 'medium' | 'high' | 'critical';
    component: string;
    message: string;
    timestamp: Date;
    resolution?: string;
  }>;
}

export interface SmartRefreshStrategy {
  type: 'predictive' | 'adaptive' | 'scheduled' | 'event-driven';
  config: {
    predictive?: {
      userBehaviorWeight: number;
      contentChangePatternWeight: number;
      timeOfDayWeight: number;
      minimumInterval: number; // seconds
      maximumInterval: number; // seconds
    };
    adaptive?: {
      learningPeriod: number; // days
      performanceThreshold: number; // milliseconds
      engagementThreshold: number; // interactions per hour
    };
    scheduled?: {
      intervals: Array<{
        time: string; // HH:mm format
        timezone: string;
        condition?: string;
      }>;
    };
    eventDriven?: {
      events: string[];
      debounceTime: number; // milliseconds
      maxRefreshRate: number; // per hour
    };
  };
}