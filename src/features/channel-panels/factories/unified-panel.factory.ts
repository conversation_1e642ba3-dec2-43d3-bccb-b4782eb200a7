import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ConsolidatedPanelCoreService } from '../services/consolidated-panel-core.service';
import { PanelFeaturesService } from '../services/panel-features.service';
import { UserManagementService } from '../services/user-management.service';
import { PanelConfig, PanelContent, ChannelContext, BasePanel } from '../interfaces/panel.interface';

/**
 * Unified Panel Factory
 * Replaces 11+ individual panel classes:
 * - AIMasteryPanel, WealthCreationPanel, PersonalGrowthPanel, TechnicalSupportPanel
 * - TradingMarketsPanel, CommunityPanel, CreativeShowcasePanel, EducationalResourcesPanel
 * - GamingEntertainmentPanel, NetworkingBusinessPanel, AnnouncementPanel
 * 
 * Provides a single factory to create any panel type with shared functionality.
 */
@Injectable()
export class UnifiedPanelFactory {
  private readonly logger = new Logger(UnifiedPanelFactory.name);
  private readonly panelDefinitions = new Map<string, PanelDefinition>();
  private readonly panelTemplates = new Map<string, PanelTemplate>();

  constructor(
    private readonly configService: ConfigService,
    private readonly panelCoreService: ConsolidatedPanelCoreService,
    private readonly panelFeaturesService: PanelFeaturesService,
    private readonly userManagementService: UserManagementService,
  ) {
    this.initializePanelDefinitions();
  }

  /**
   * Creates a panel instance based on type and configuration
   */
  async createPanel(type: PanelType, config: PanelConfig, context: ChannelContext): Promise<BasePanel> {
    try {
      this.logger.debug(`Creating panel of type: ${type}`);

      // Get panel definition
      const definition = this.panelDefinitions.get(type);
      if (!definition) {
        throw new Error(`Unknown panel type: ${type}`);
      }

      // Get user preferences (use fallback if userId not provided)
      const userId = context.userId || 'default';
      const userPreferences = await this.userManagementService.getPanelPreferences(
        userId, 
        type
      );

      // Merge configuration with preferences
      const mergedConfig = this.mergeConfiguration(config, userPreferences, definition.defaultConfig);

      // Create the base panel instance
      const panel: BasePanel = {
        id: mergedConfig.id,
        type,
        config: mergedConfig,
        context,
        state: 'initializing',
        createdAt: new Date(),
        lastUpdated: new Date(),
        
        // Core methods (same for all panels) - will be assigned after panel creation
        activate: async () => false,
        deactivate: async () => {},
        update: async (newConfig: Partial<PanelConfig>) => {},
        generateContent: async () => ({ embeds: [], components: [] }),
        handleInteraction: async (interaction: any) => {}
      };

      // Assign methods that reference the panel
      panel.activate = () => this.activatePanel(panel);
      panel.deactivate = () => this.deactivatePanel(panel);
      panel.update = (newConfig: Partial<PanelConfig>) => this.updatePanel(panel, newConfig);
      panel.generateContent = () => this.generatePanelContent(panel);
      panel.handleInteraction = (interaction: any) => this.handlePanelInteraction(panel, interaction);
      
      // Add specialized methods based on panel type
      const specializedMethods = await this.createSpecializedMethods(type, definition, panel);
      Object.assign(panel, specializedMethods);

      // Initialize the panel
      await this.initializePanel(panel, definition);

      this.logger.log(`Successfully created ${type} panel: ${panel.id}`);
      return panel;

    } catch (error) {
      this.logger.error(`Failed to create panel of type ${type}:`, error);
      throw error;
    }
  }

  /**
   * Creates multiple panels for a channel context
   */
  async createPanelsForChannel(channelContext: ChannelContext, panelTypes: PanelType[]): Promise<BasePanel[]> {
    const panels: BasePanel[] = [];

    for (const type of panelTypes) {
      try {
        const config = await this.generateDefaultConfig(type, channelContext);
        const panel = await this.createPanel(type, config, channelContext);
        panels.push(panel);
      } catch (error) {
        this.logger.error(`Failed to create ${type} panel for channel ${channelContext.channelId}:`, error);
      }
    }

    return panels;
  }

  /**
   * Updates an existing panel with new configuration
   */
  async updatePanel(panel: BasePanel, updates: Partial<PanelConfig>): Promise<void> {
    panel.config = { ...panel.config, ...updates };
    panel.lastUpdated = new Date();
    
    await this.panelCoreService.updatePanelState(panel.id, {
      lastActivity: panel.lastUpdated
    });
  }

  // === PANEL TYPE DEFINITIONS ===

  private initializePanelDefinitions(): void {
    this.logger.log('Initializing panel definitions...');

    // AI Mastery Panel
    this.panelDefinitions.set('ai-mastery', {
      name: 'AI Mastery Panel',
      description: 'Provides AI tools, tutorials, and automation assistance',
      category: 'education',
      features: ['ai_tools', 'tutorials', 'automation', 'coding_help'],
      requiredServices: ['panelFeaturesService'],
      defaultConfig: {
        showToolRecommendations: true,
        tutorialDifficulty: 'beginner',
        codingLanguages: ['javascript', 'python', 'typescript'],
        automationLevel: 'basic'
      },
      contentSections: ['tools', 'tutorials', 'news', 'community'],
      interactions: ['tool_search', 'tutorial_request', 'code_help', 'automation_setup']
    });

    // Wealth Creation Panel
    this.panelDefinitions.set('wealth-creation', {
      name: 'Wealth Creation Panel',
      description: 'Financial planning, investment guidance, and wealth building tools',
      category: 'finance',
      features: ['financial_calculator', 'investment_tracking', 'budgeting', 'market_analysis'],
      requiredServices: ['panelFeaturesService'],
      defaultConfig: {
        defaultCurrency: 'USD',
        riskTolerance: 'moderate',
        investmentHorizon: 'long-term',
        showMarketNews: true
      },
      contentSections: ['calculator', 'investments', 'budgeting', 'market_news'],
      interactions: ['calculate', 'track_investment', 'set_budget', 'market_query']
    });

    // Personal Growth Panel
    this.panelDefinitions.set('personal-growth', {
      name: 'Personal Growth Panel',
      description: 'Habit tracking, goal setting, and personal development',
      category: 'wellness',
      features: ['habit_tracking', 'goal_setting', 'progress_monitoring', 'motivation'],
      requiredServices: ['panelFeaturesService'],
      defaultConfig: {
        reminderFrequency: 'daily',
        motivationalQuotes: true,
        progressSharing: false,
        gamification: true
      },
      contentSections: ['habits', 'goals', 'progress', 'motivation'],
      interactions: ['track_habit', 'set_goal', 'view_progress', 'get_motivation']
    });

    // Technical Support Panel
    this.panelDefinitions.set('technical-support', {
      name: 'Technical Support Panel',
      description: 'Troubleshooting guides and technical assistance',
      category: 'support',
      features: ['troubleshooting', 'ticket_system', 'knowledge_base', 'live_chat'],
      requiredServices: ['panelFeaturesService'],
      defaultConfig: {
        ticketPriority: 'medium',
        autoResponses: true,
        escalationEnabled: true,
        knowledgeBaseSearch: true
      },
      contentSections: ['troubleshooting', 'tickets', 'knowledge_base', 'contact'],
      interactions: ['create_ticket', 'search_kb', 'troubleshoot', 'escalate']
    });

    // Trading Markets Panel
    this.panelDefinitions.set('trading-markets', {
      name: 'Trading Markets Panel',
      description: 'Market analysis, trading tools, and portfolio management',
      category: 'finance',
      features: ['market_data', 'trading_tools', 'portfolio_tracking', 'alerts'],
      requiredServices: ['panelFeaturesService'],
      defaultConfig: {
        defaultMarkets: ['stocks', 'crypto', 'forex'],
        alertThreshold: 5, // 5% price change
        portfolioDisplay: 'chart',
        riskAnalysis: true
      },
      contentSections: ['markets', 'portfolio', 'tools', 'alerts'],
      interactions: ['view_market', 'analyze_stock', 'set_alert', 'track_portfolio']
    });

    // Community Panel
    this.panelDefinitions.set('community', {
      name: 'Community Panel',
      description: 'Community engagement, events, and discussions',
      category: 'social',
      features: ['events', 'discussions', 'polls', 'announcements'],
      requiredServices: ['userManagementService'],
      defaultConfig: {
        showUpcomingEvents: true,
        enablePolls: true,
        moderationLevel: 'medium',
        eventReminders: true
      },
      contentSections: ['events', 'discussions', 'polls', 'announcements'],
      interactions: ['join_event', 'start_discussion', 'create_poll', 'make_announcement']
    });

    // Add more panel definitions...
    this.addRemainingPanelDefinitions();

    this.logger.log(`Initialized ${this.panelDefinitions.size} panel definitions`);
  }

  private addRemainingPanelDefinitions(): void {
    // Creative Showcase Panel
    this.panelDefinitions.set('creative-showcase', {
      name: 'Creative Showcase Panel',
      description: 'Share and discover creative content',
      category: 'creative',
      features: ['content_sharing', 'galleries', 'feedback', 'contests'],
      requiredServices: ['userManagementService'],
      defaultConfig: {
        allowedFileTypes: ['image', 'video', 'audio'],
        moderationRequired: true,
        votingEnabled: true,
        featuredContent: true
      },
      contentSections: ['gallery', 'submit', 'contests', 'featured'],
      interactions: ['submit_content', 'vote', 'comment', 'share']
    });

    // Educational Resources Panel
    this.panelDefinitions.set('educational-resources', {
      name: 'Educational Resources Panel',
      description: 'Learning materials, courses, and educational content',
      category: 'education',
      features: ['courses', 'tutorials', 'resources', 'progress_tracking'],
      requiredServices: ['panelFeaturesService', 'userManagementService'],
      defaultConfig: {
        difficultyLevels: ['beginner', 'intermediate', 'advanced'],
        trackProgress: true,
        certificates: true,
        recommendationEngine: true
      },
      contentSections: ['courses', 'resources', 'progress', 'certificates'],
      interactions: ['enroll_course', 'access_resource', 'track_progress', 'get_certificate']
    });

    // Gaming Entertainment Panel
    this.panelDefinitions.set('gaming-entertainment', {
      name: 'Gaming Entertainment Panel',
      description: 'Gaming content, tournaments, and entertainment',
      category: 'entertainment',
      features: ['tournaments', 'leaderboards', 'game_stats', 'streaming'],
      requiredServices: ['userManagementService'],
      defaultConfig: {
        supportedGames: ['general'],
        showLeaderboards: true,
        tournamentNotifications: true,
        streamingIntegration: false
      },
      contentSections: ['tournaments', 'leaderboards', 'stats', 'streaming'],
      interactions: ['join_tournament', 'view_stats', 'start_stream', 'challenge_user']
    });

    // Networking Business Panel
    this.panelDefinitions.set('networking-business', {
      name: 'Networking Business Panel',
      description: 'Professional networking and business opportunities',
      category: 'business',
      features: ['networking', 'job_board', 'business_directory', 'partnerships'],
      requiredServices: ['userManagementService'],
      defaultConfig: {
        showProfiles: true,
        jobAlerts: true,
        partnershipMatching: true,
        industryFocus: 'all'
      },
      contentSections: ['network', 'jobs', 'directory', 'partnerships'],
      interactions: ['connect', 'apply_job', 'list_business', 'seek_partnership']
    });

    // Announcement Panel
    this.panelDefinitions.set('announcement', {
      name: 'Announcement Panel',
      description: 'Important announcements and notifications',
      category: 'communication',
      features: ['announcements', 'notifications', 'pinned_messages', 'scheduling'],
      requiredServices: ['userManagementService'],
      defaultConfig: {
        priority: 'medium',
        pinDuration: 24, // hours
        notifyUsers: true,
        scheduling: true
      },
      contentSections: ['current', 'scheduled', 'archived', 'notifications'],
      interactions: ['create_announcement', 'schedule', 'pin', 'notify']
    });
  }

  // === PANEL CREATION METHODS ===

  private async createSpecializedMethods(
    type: PanelType, 
    definition: PanelDefinition, 
    panel: BasePanel
  ): Promise<Record<string, Function>> {
    const methods: Record<string, Function> = {};

    // Add methods based on panel features
    for (const feature of definition.features) {
      switch (feature) {
        case 'ai_tools':
          methods.searchAITools = async (query: string) => {
            return await this.panelFeaturesService.getAITools(undefined, query);
          };
          methods.getToolRecommendations = async () => {
            return await this.panelFeaturesService.getAIToolRecommendations(
              panel.context.userId, 
              { categories: ['ai'], limit: 5 }
            );
          };
          break;

        case 'financial_calculator':
          methods.calculate = async (data: any) => {
            return await this.panelFeaturesService.calculateFinancials({
              ...data,
              userId: panel.context.userId
            });
          };
          break;

        case 'habit_tracking':
          methods.trackHabit = async (habitData: any) => {
            return await this.panelFeaturesService.trackHabit(
              panel.context.userId,
              habitData
            );
          };
          methods.getHabitAnalytics = async (habitId?: string) => {
            return await this.panelFeaturesService.getHabitAnalytics(
              panel.context.userId,
              habitId
            );
          };
          break;

        case 'troubleshooting':
          methods.getTroubleshooting = async (issue: any) => {
            return await this.panelFeaturesService.provideTroubleshooting(issue);
          };
          methods.searchTroubleshooting = async (query: string) => {
            return await this.panelFeaturesService.searchTroubleshootingGuides(query);
          };
          break;

        case 'ticket_system':
          methods.createTicket = async (ticketData: any) => {
            return await this.createSupportTicket(panel.context.userId, ticketData);
          };
          break;

        // Add more feature-specific methods as needed
      }
    }

    return methods;
  }

  private async initializePanel(panel: BasePanel, definition: PanelDefinition): Promise<void> {
    // Activate the panel
    await this.panelCoreService.activatePanel(panel.config, panel.context);
    
    // Set initial state
    panel.state = 'active';
    
    // Load panel template if available
    const template = this.panelTemplates.get(panel.type);
    if (template) {
      panel.template = template;
    }
  }

  private async activatePanel(panel: BasePanel): Promise<boolean> {
    try {
      const success = await this.panelCoreService.activatePanel(panel.config, panel.context);
      if (success) {
        panel.state = 'active';
        panel.lastUpdated = new Date();
      }
      return success;
    } catch (error) {
      this.logger.error(`Failed to activate panel ${panel.id}:`, error);
      return false;
    }
  }

  private async deactivatePanel(panel: BasePanel): Promise<void> {
    try {
      await this.panelCoreService.deactivatePanel(panel.id);
      panel.state = 'inactive';
      panel.lastUpdated = new Date();
    } catch (error) {
      this.logger.error(`Failed to deactivate panel ${panel.id}:`, error);
    }
  }

  private async generatePanelContent(panel: BasePanel): Promise<PanelContent> {
    try {
      // Generate base content using core service
      const baseContent = await this.panelCoreService.generatePanelContent(
        panel.config, 
        panel.context
      );

      // Enhance content based on panel type
      const enhancedContent = await this.enhanceContentForPanelType(panel, baseContent);

      return enhancedContent;
    } catch (error) {
      this.logger.error(`Failed to generate content for panel ${panel.id}:`, error);
      throw error;
    }
  }

  private async handlePanelInteraction(panel: BasePanel, interaction: any): Promise<any> {
    try {
      // Track the interaction
      await this.userManagementService.trackPanelInteraction(
        panel.context.userId,
        {
          panelId: panel.id,
          channelId: panel.context.channelId,
          interactionType: interaction.type,
          timestamp: new Date(),
          data: interaction.data
        }
      );

      // Handle interaction based on panel type
      return await this.processInteractionForPanelType(panel, interaction);
    } catch (error) {
      this.logger.error(`Failed to handle interaction for panel ${panel.id}:`, error);
      throw error;
    }
  }

  // === UTILITY METHODS ===

  private mergeConfiguration(
    config: PanelConfig, 
    userPreferences: any, 
    defaultConfig: any
  ): PanelConfig {
    return {
      ...defaultConfig,
      ...config,
      ...userPreferences,
      id: config.id, // Ensure ID is not overridden
    };
  }

  private async generateDefaultConfig(type: PanelType, context: ChannelContext): Promise<PanelConfig> {
    const definition = this.panelDefinitions.get(type);
    if (!definition) {
      throw new Error(`No definition found for panel type: ${type}`);
    }

    return {
      id: `${type}_${context.channelId}_${Date.now()}`,
      type,
      isEnabled: true,
      targetChannels: [context.channelId],
      ...definition.defaultConfig
    };
  }

  private async enhanceContentForPanelType(panel: BasePanel, baseContent: PanelContent): Promise<PanelContent> {
    const definition = this.panelDefinitions.get(panel.type);
    if (!definition) {
      return baseContent;
    }

    // Add panel-specific content sections
    for (const section of definition.contentSections) {
      // Implementation would add section-specific content
    }

    return baseContent;
  }

  private async processInteractionForPanelType(panel: BasePanel, interaction: any): Promise<any> {
    const definition = this.panelDefinitions.get(panel.type);
    if (!definition || !definition.interactions.includes(interaction.type)) {
      throw new Error(`Unsupported interaction type: ${interaction.type} for panel: ${panel.type}`);
    }

    // Process interaction based on type
    switch (interaction.type) {
      case 'tool_search':
        return await this.panelFeaturesService.getAITools(undefined, interaction.query);
      
      case 'calculate':
        return await this.panelFeaturesService.calculateFinancials({
          ...interaction.data,
          userId: panel.context.userId
        });
      
      case 'track_habit':
        return await this.panelFeaturesService.trackHabit(
          panel.context.userId,
          interaction.data
        );
      
      // Add more interaction handlers
      default:
        return { success: true, message: 'Interaction processed' };
    }
  }

  private async createSupportTicket(userId: string, ticketData: any): Promise<any> {
    // Implementation for creating support tickets
    return {
      id: `ticket_${Date.now()}`,
      userId,
      status: 'open',
      priority: ticketData.priority || 'medium',
      subject: ticketData.subject,
      description: ticketData.description,
      createdAt: new Date()
    };
  }
}

// === TYPE DEFINITIONS ===

type PanelType = 
  | 'ai-mastery' 
  | 'wealth-creation' 
  | 'personal-growth' 
  | 'technical-support'
  | 'trading-markets' 
  | 'community' 
  | 'creative-showcase' 
  | 'educational-resources'
  | 'gaming-entertainment' 
  | 'networking-business' 
  | 'announcement';

interface PanelDefinition {
  name: string;
  description: string;
  category: string;
  features: string[];
  requiredServices: string[];
  defaultConfig: Record<string, any>;
  contentSections: string[];
  interactions: string[];
}

interface PanelTemplate {
  layout: string;
  components: any[];
  styling: Record<string, any>;
}