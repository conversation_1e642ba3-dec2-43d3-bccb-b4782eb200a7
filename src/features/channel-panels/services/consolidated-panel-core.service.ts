import { Injectable, Logger } from '@nestjs/common';
import { PanelConfig, PanelContent, ChannelContext, PanelState, PanelEvent } from '../interfaces/panel.interface';
import { DatabaseService } from '@/core/database';

/**
 * Consolidated Panel Core Service
 * Replaces: PanelActivationService, PanelStateService, PanelAnalyticsService, PanelContentManagerService
 * 
 * This service consolidates all core panel functionality that was previously spread
 * across 4 separate services, reducing complexity and improving maintainability.
 */
@Injectable()
export class ConsolidatedPanelCoreService {
  private readonly logger = new Logger(ConsolidatedPanelCoreService.name);
  private readonly panelStates = new Map<string, PanelState>();
  private readonly panelMetrics = new Map<string, any>();

  constructor(
    private readonly databaseService: DatabaseService,
  ) {}

  // === PANEL ACTIVATION (replaces PanelActivationService) ===
  
  /**
   * Activates a panel in the specified channel context
   */
  async activatePanel(config: PanelConfig, context: ChannelContext): Promise<boolean> {
    try {
      this.logger.log(`Activating panel ${config.id} in channel ${context.channelId}`);
      
      // Check if panel should be activated based on context
      if (!this.shouldActivatePanel(config, context)) {
        return false;
      }

      // Update panel state
      await this.updatePanelState(config.id, {
        status: 'active',
        channelId: context.channelId,
        lastActivity: new Date()
      });

      // Track activation analytics
      await this.trackEvent({
        id: `activation_${config.id}_${Date.now()}`,
        eventType: 'activation',
        panelId: config.id,
        channelId: context.channelId,
        timestamp: new Date(),
        data: { config, context }
      });

      return true;
    } catch (error) {
      this.logger.error(`Failed to activate panel ${config.id}:`, error);
      return false;
    }
  }

  /**
   * Determines if a panel should be activated based on configuration and context
   */
  shouldActivatePanel(config: PanelConfig, context: ChannelContext): boolean {
    // Check if panel is enabled
    if (!config.isEnabled) {
      return false;
    }

    // Check target channels
    if (config.targetChannels?.includes(context.channelId)) {
      return true;
    }

    // Check target categories
    if (config.targetCategories?.includes(context.categoryId)) {
      return true;
    }

    // Check channel patterns
    if (config.channelPatterns?.some(pattern => pattern.test(context.channelName))) {
      return true;
    }

    return false;
  }

  // === PANEL STATE MANAGEMENT (replaces PanelStateService) ===

  /**
   * Updates the state of a panel
   */
  async updatePanelState(panelId: string, state: Partial<PanelState>): Promise<void> {
    try {
      const currentState = this.panelStates.get(panelId) || {
        panelId,
        channelId: '',
        status: 'inactive',
        lastActivity: new Date(),
        errorCount: 0,
        deployedAt: new Date(),
        updatedAt: new Date(),
        metadata: {}
      };
      const newState = { ...currentState, ...state, updatedAt: new Date() };
      
      this.panelStates.set(panelId, newState);
      
      // Persist to database
      await this.databaseService.query(
        `INSERT INTO panel_states (panel_id, state, updated_at) 
         VALUES ($1, $2, $3) 
         ON CONFLICT (panel_id) 
         DO UPDATE SET state = $2, updated_at = $3`,
        [panelId, JSON.stringify(newState), new Date()]
      );

      this.logger.debug(`Updated state for panel ${panelId}`);
    } catch (error) {
      this.logger.error(`Failed to update panel state for ${panelId}:`, error);
    }
  }

  /**
   * Retrieves the current state of a panel
   */
  async getPanelState(panelId: string): Promise<PanelState | null> {
    try {
      // Check memory cache first
      if (this.panelStates.has(panelId)) {
        return this.panelStates.get(panelId);
      }

      // Fallback to database
      const result = await this.databaseService.query(
        'SELECT state FROM panel_states WHERE panel_id = $1',
        [panelId]
      );

      if (result.length > 0) {
        const state = JSON.parse(result[0].state);
        this.panelStates.set(panelId, state);
        return state;
      }

      return null;
    } catch (error) {
      this.logger.error(`Failed to get panel state for ${panelId}:`, error);
      return null;
    }
  }

  // === ANALYTICS & METRICS (replaces PanelAnalyticsService) ===

  /**
   * Tracks panel events for analytics
   */
  async trackEvent(event: PanelEvent): Promise<void> {
    try {
      // Store in memory for quick access
      const panelMetrics = this.panelMetrics.get(event.panelId) || {
        totalEvents: 0,
        eventsByType: {},
        lastActivity: null
      };

      panelMetrics.totalEvents++;
      panelMetrics.eventsByType[event.eventType] = (panelMetrics.eventsByType[event.eventType] || 0) + 1;
      panelMetrics.lastActivity = event.timestamp;

      this.panelMetrics.set(event.panelId, panelMetrics);

      // Persist to database
      await this.databaseService.query(
        `INSERT INTO panel_analytics (panel_id, event_type, event_data, created_at) 
         VALUES ($1, $2, $3, $4)`,
        [event.panelId, event.eventType, JSON.stringify(event), event.timestamp]
      );

      this.logger.debug(`Tracked event ${event.eventType} for panel ${event.panelId}`);
    } catch (error) {
      this.logger.error(`Failed to track event for panel ${event.panelId}:`, error);
    }
  }

  /**
   * Retrieves analytics for a panel
   */
  async getPanelAnalytics(panelId: string): Promise<any> {
    try {
      // Return cached metrics if available
      if (this.panelMetrics.has(panelId)) {
        return this.panelMetrics.get(panelId);
      }

      // Query database for historical data
      const result = await this.databaseService.query(
        `SELECT event_type, COUNT(*) as count, MAX(created_at) as last_event
         FROM panel_analytics 
         WHERE panel_id = $1 
         GROUP BY event_type`,
        [panelId]
      );

      const analytics = {
        totalEvents: result.reduce((sum, row) => sum + parseInt(row.count), 0),
        eventsByType: result.reduce((acc, row) => {
          acc[row.event_type] = parseInt(row.count);
          return acc;
        }, {}),
        lastActivity: result.length > 0 ? new Date(Math.max(...result.map((r: any) => new Date(r.last_event).getTime()))) : null
      };

      this.panelMetrics.set(panelId, analytics);
      return analytics;
    } catch (error) {
      this.logger.error(`Failed to get analytics for panel ${panelId}:`, error);
      return null;
    }
  }

  // === CONTENT MANAGEMENT (replaces PanelContentManagerService) ===

  /**
   * Generates panel content based on configuration and context
   */
  async generatePanelContent(config: PanelConfig, context: ChannelContext): Promise<PanelContent> {
    try {
      this.logger.debug(`Generating content for panel ${config.id}`);

      // Track content generation
      await this.trackEvent({
        id: `content_gen_${config.id}_${Date.now()}`,
        eventType: 'update',
        panelId: config.id,
        channelId: context.channelId,
        timestamp: new Date(),
        data: { config, context }
      });

      // Generate base content structure
      const content: PanelContent = {
        embeds: [],
        components: [],
        metadata: {
          panelId: config.id,
          generated: new Date(),
          version: '1.0.0'
        }
      };

      // This would be extended by specific panel implementations
      return content;
    } catch (error) {
      this.logger.error(`Failed to generate content for panel ${config.id}:`, error);
      throw error;
    }
  }

  /**
   * Updates existing panel content
   */
  async updatePanelContent(panelId: string, content: Partial<PanelContent>): Promise<void> {
    try {
      // Store content update in database
      await this.databaseService.query(
        `INSERT INTO panel_content (panel_id, content, updated_at) 
         VALUES ($1, $2, $3) 
         ON CONFLICT (panel_id) 
         DO UPDATE SET content = $2, updated_at = $3`,
        [panelId, JSON.stringify(content), new Date()]
      );

      // Track content update
      await this.trackEvent({
        id: `content_update_${panelId}_${Date.now()}`,
        eventType: 'update',
        panelId,
        channelId: '', // Channel context may not be available
        timestamp: new Date(),
        data: { contentKeys: Object.keys(content) }
      });

      this.logger.debug(`Updated content for panel ${panelId}`);
    } catch (error) {
      this.logger.error(`Failed to update content for panel ${panelId}:`, error);
    }
  }

  // === UTILITY METHODS ===

  /**
   * Gets comprehensive panel information
   */
  async getPanelInfo(panelId: string): Promise<{
    state: PanelState | null;
    analytics: any;
    isActive: boolean;
  }> {
    const [state, analytics] = await Promise.all([
      this.getPanelState(panelId),
      this.getPanelAnalytics(panelId)
    ]);

    return {
      state,
      analytics,
      isActive: state?.status === 'active'
    };
  }

  /**
   * Deactivates a panel
   */
  async deactivatePanel(panelId: string): Promise<void> {
    await this.updatePanelState(panelId, {
      status: 'inactive'
    });

    await this.trackEvent({
      id: `deactivation_${panelId}_${Date.now()}`,
      eventType: 'deactivation',
      panelId,
      channelId: '',
      timestamp: new Date(),
      data: {}
    });

    this.logger.log(`Deactivated panel ${panelId}`);
  }

  // === METHODS FOR UNIFIED PANEL BASE ===

  /**
   * Get activation reasons for debugging
   */
  getActivationReasons(config: PanelConfig, context: ChannelContext): string[] {
    const reasons: string[] = [];

    if (!config.isEnabled) {
      reasons.push('Panel is disabled');
      return reasons;
    }

    if (config.targetChannels?.includes(context.channelId)) {
      reasons.push('Channel ID matches target channels');
    }

    if (config.targetCategories?.includes(context.categoryId)) {
      reasons.push('Category ID matches target categories');
    }

    if (config.channelPatterns?.some(pattern => pattern.test(context.channelName))) {
      reasons.push('Channel name matches patterns');
    }

    if (reasons.length === 0) {
      reasons.push('No activation conditions met');
    }

    return reasons;
  }

  /**
   * Validate panel configuration
   */
  validatePanelConfig(config: PanelConfig): string[] {
    const issues: string[] = [];

    if (!config.id) {
      issues.push('Panel ID is required');
    }

    if (!config.targetChannels?.length && !config.targetCategories?.length && !config.channelPatterns?.length) {
      issues.push('At least one target (channels, categories, or patterns) must be specified');
    }

    return issues;
  }

  /**
   * Track panel view for analytics
   */
  async trackPanelView(userId: string, panelId: string, context: ChannelContext, sessionId?: string): Promise<void> {
    await this.trackEvent({
      id: `view_${panelId}_${userId}_${Date.now()}`,
      eventType: 'interaction',
      panelId,
      channelId: context.channelId,
      timestamp: new Date(),
      userId,
      data: { action: 'view', sessionId }
    });
  }

  /**
   * Track panel interaction
   */
  async trackInteraction(
    userId: string,
    panelId: string,
    context: ChannelContext,
    interactionData: any,
    sessionId?: string
  ): Promise<void> {
    await this.trackEvent({
      id: `interaction_${panelId}_${userId}_${Date.now()}`,
      eventType: 'interaction',
      panelId,
      channelId: context.channelId,
      timestamp: new Date(),
      userId,
      data: { ...interactionData, sessionId }
    });
  }

  /**
   * Track performance metrics
   */
  async trackPerformance(panelId: string, context: ChannelContext, metrics: any): Promise<void> {
    await this.trackEvent({
      id: `performance_${panelId}_${Date.now()}`,
      eventType: 'update',
      panelId,
      channelId: context.channelId,
      timestamp: new Date(),
      data: { type: 'performance', metrics }
    });
  }

  /**
   * Track errors
   */
  async trackError(
    userId: string,
    panelId: string,
    context: ChannelContext,
    errorData: any,
    sessionId?: string
  ): Promise<void> {
    await this.trackEvent({
      id: `error_${panelId}_${userId}_${Date.now()}`,
      eventType: 'error',
      panelId,
      channelId: context.channelId,
      timestamp: new Date(),
      userId,
      data: { ...errorData, sessionId }
    });
  }

  /**
   * Get panel insights
   */
  async getPanelInsights(
    panelId: string,
    timeRange: { start: Date; end: Date },
    guildId?: string
  ): Promise<any> {
    try {
      const result = await this.databaseService.query(
        `SELECT event_type, COUNT(*) as count, 
                DATE_TRUNC('day', created_at) as day
         FROM panel_analytics 
         WHERE panel_id = $1 AND created_at BETWEEN $2 AND $3
         GROUP BY event_type, DATE_TRUNC('day', created_at)
         ORDER BY day DESC`,
        [panelId, timeRange.start, timeRange.end]
      );

      return {
        panelId,
        timeRange,
        events: result,
        summary: {
          totalEvents: result.reduce((sum, row) => sum + parseInt(row.count), 0),
          eventsByType: result.reduce((acc, row) => {
            acc[row.event_type] = (acc[row.event_type] || 0) + parseInt(row.count);
            return acc;
          }, {})
        }
      };
    } catch (error) {
      this.logger.error(`Failed to get panel insights for ${panelId}:`, error);
      return null;
    }
  }
}