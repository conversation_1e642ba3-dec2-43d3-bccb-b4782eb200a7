import { Injectable, Logger } from '@nestjs/common';
import { RedisDatabaseService } from '@/core/database';
import { v4 as uuidv4 } from 'uuid';

// Redis Entity Types
export interface AITool {
  id: string;
  name: string;
  description: string;
  category: string;
  rating: number;
  pricing: string;
  features: string[];
  url: string;
  tags: string[];
  isActive: boolean;
  addedBy: string;
  votes: number;
  viewCount: number;
  lastUpdated: string;
  createdAt: string;
  updatedAt: string;
}

export interface AIToolBookmark {
  id: string;
  userId: string;
  toolId: number;
  notes?: string;
  tags: string[];
  rating?: number;
  createdAt: string;
  updatedAt: string;
}

export interface AITutorial {
  id: string;
  title: string;
  description: string;
  content: string;
  difficulty: string;
  estimatedTime: number;
  category: string;
  tags: string[];
  isPublished: boolean;
  authorId: string;
  rating: number;
  viewCount: number;
  completionCount: number;
  prerequisites: string[];
  resources: any[];
  createdAt: string;
  updatedAt: string;
}

export interface TutorialProgress {
  id: string;
  userId: string;
  tutorialId: number;
  progress: number;
  currentSection: number;
  completedSections: number[];
  timeSpent: number;
  isCompleted: boolean;
  completedAt?: string;
  lastAccessedAt?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface AINews {
  id: string;
  title: string;
  content: string;
  summary: string;
  category: string;
  importance: string;
  tags: string[];
  sourceUrl?: string;
  imageUrl?: string;
  publishedAt: string;
  isBreaking: boolean;
  views: number;
  likes: number;
  authorId: string;
  createdAt: string;
  updatedAt: string;
}

export interface AIUserPreferences {
  id: string;
  userId: string;
  preferredCategories: string[];
  difficultyLevel: string;
  notificationSettings: any;
  learningGoals: string[];
  weeklyTimeCommitment: number;
  preferredContentTypes: string[];
  languagePreference: string;
  timezone: string;
  createdAt: string;
  updatedAt: string;
}

export interface AIToolWithBookmark extends AITool {
  isBookmarked?: boolean;
  userRating?: number;
}

export interface AITutorialWithProgress extends AITutorial {
  userProgress?: TutorialProgress;
  isCompleted?: boolean;
  progressPercentage?: number;
}

export interface LearningStats {
  totalTools: number;
  bookmarkedTools: number;
  completedTutorials: number;
  totalTutorials: number;
  averageRating: number;
  timeSpentLearning: number; // in minutes
  learningStreak: number; // days
  favoriteCategories: string[];
}

@Injectable()
export class AIMasteryDatabaseService {
  private readonly logger = new Logger(AIMasteryDatabaseService.name);
  
  // Entity type constants
  private readonly AI_TOOL = 'ai_tool';
  private readonly AI_TOOL_BOOKMARK = 'ai_tool_bookmark';
  private readonly AI_TUTORIAL = 'ai_tutorial';
  private readonly TUTORIAL_PROGRESS = 'tutorial_progress';
  private readonly AI_NEWS = 'ai_news';
  private readonly AI_USER_PREFERENCES = 'ai_user_preferences';
  private readonly USER = 'user';

  constructor(private readonly redisDb: RedisDatabaseService) {}

  // Redis entity options
  private getToolOptions() {
    return {
      indexes: ['category', 'isActive', 'addedBy', 'rating'],
      relationships: {
        bookmarks: 'ai_tool_bookmark'
      }
    };
  }

  private getBookmarkOptions() {
    return {
      indexes: ['userId', 'toolId']
    };
  }

  private getTutorialOptions() {
    return {
      indexes: ['category', 'difficulty', 'isPublished', 'authorId'],
      relationships: {
        progress: 'tutorial_progress'
      }
    };
  }

  private getProgressOptions() {
    return {
      indexes: ['userId', 'tutorialId', 'isCompleted']
    };
  }

  private getNewsOptions() {
    return {
      indexes: ['category', 'importance', 'isBreaking', 'authorId']
    };
  }

  private getPreferencesOptions() {
    return {
      indexes: ['userId']
    };
  }

  private getUserOptions() {
    return {
      indexes: ['discordId', 'isActive']
    };
  }

  // AI Tools Management
  async createTool(toolData: Omit<AITool, 'id' | 'createdAt' | 'updatedAt'>): Promise<AITool> {
    try {
      const tool = await this.redisDb.create<AITool>(
        this.AI_TOOL,
        {
          ...toolData,
          votes: toolData.votes || 0,
          viewCount: toolData.viewCount || 0,
          lastUpdated: new Date().toISOString()
        },
        this.getToolOptions()
      );
      
      this.logger.log(`Created AI tool: ${tool.name} (${tool.id})`);
      return tool;
    } catch (error) {
      this.logger.error('Failed to create AI tool:', error);
      throw error;
    }
  }

  async getTools(category?: string, userId?: string, limit: number = 20): Promise<AIToolWithBookmark[]> {
    try {
      let tools: AITool[];
      
      if (category) {
        tools = await this.redisDb.findByIndex<AITool>(
          this.AI_TOOL,
          'category',
          category,
          { limit: 100 }
        );
      } else {
        const result = await this.redisDb.findMany<AITool>(
          this.AI_TOOL,
          { limit: 100 }
        );
        tools = result.data;
      }

      // Filter for active tools only
      const activeTools = tools.filter((tool: any) => tool.isActive);

      // Sort by votes and rating
      activeTools.sort((a, b) => {
        if (b.votes !== a.votes) return b.votes - a.votes;
        return b.rating - a.rating;
      });

      // Limit results
      const limitedTools = activeTools.slice(0, limit);

      // Enrich with bookmark data if userId provided
      const toolsWithBookmarks: AIToolWithBookmark[] = await Promise.all(
        limitedTools.map(async (tool) => {
          let isBookmarked = false;
          let userRating: number | undefined;
          
          if (userId) {
            const userBookmarks = await this.redisDb.findByIndex<AIToolBookmark>(
              this.AI_TOOL_BOOKMARK,
              'userId',
              userId
            );
            
            const bookmark = userBookmarks.find(b => b.toolId.toString() === tool.id);
            isBookmarked = !!bookmark;
            userRating = bookmark?.rating;
          }
          
          return {
            ...tool,
            isBookmarked,
            userRating
          };
        })
      );

      return toolsWithBookmarks;
    } catch (error) {
      this.logger.error('Failed to get AI tools:', error);
      throw error;
    }
  }

  async searchTools(searchTerm: string, userId?: string, limit: number = 10): Promise<AIToolWithBookmark[]> {
    try {
      const tools = await this.redisDb.search<AITool>(
        this.AI_TOOL,
        searchTerm,
        ['name', 'description', 'tags'],
        { limit }
      );

      // Filter for active tools and sort by votes
      const activeTools = tools
        .filter((tool: any) => tool.isActive)
        .sort((a, b) => b.votes - a.votes);

      // Enrich with bookmark data if userId provided
      const toolsWithBookmarks: AIToolWithBookmark[] = await Promise.all(
        activeTools.map(async (tool) => {
          let isBookmarked = false;
          let userRating: number | undefined;
          
          if (userId) {
            const userBookmarks = await this.redisDb.findByIndex<AIToolBookmark>(
              this.AI_TOOL_BOOKMARK,
              'userId',
              userId
            );
            
            const bookmark = userBookmarks.find(b => b.toolId.toString() === tool.id);
            isBookmarked = !!bookmark;
            userRating = bookmark?.rating;
          }
          
          return {
            ...tool,
            isBookmarked,
            userRating
          };
        })
      );

      return toolsWithBookmarks;
    } catch (error) {
      this.logger.error('Failed to search AI tools:', error);
      throw error;
    }
  }

  async bookmarkTool(userId: string, toolId: number, notes?: string): Promise<{ success: boolean; message: string }> {
    try {
      // Check if already bookmarked
      const userBookmarks = await this.redisDb.findByIndex<AIToolBookmark>(
        this.AI_TOOL_BOOKMARK,
        'userId',
        userId
      );
      
      const existingBookmark = userBookmarks.find(b => b.toolId === toolId);
      if (existingBookmark) {
        return { success: false, message: 'Tool is already bookmarked!' };
      }

      await this.redisDb.create<AIToolBookmark>(
        this.AI_TOOL_BOOKMARK,
        {
          userId,
          toolId,
          notes,
          tags: []
        },
        this.getBookmarkOptions()
      );

      this.logger.log(`User ${userId} bookmarked tool ${toolId}`);
      return { success: true, message: 'Tool bookmarked successfully!' };
    } catch (error) {
      this.logger.error('Failed to bookmark tool:', error);
      return { success: false, message: 'Failed to bookmark tool. Please try again.' };
    }
  }

  // Tutorials Management
  async createTutorial(tutorialData: Omit<AITutorial, 'id' | 'createdAt' | 'updatedAt'>): Promise<AITutorial> {
    try {
      const tutorial = await this.redisDb.create<AITutorial>(
        this.AI_TUTORIAL,
        {
          ...tutorialData,
          rating: tutorialData.rating || 0,
          viewCount: tutorialData.viewCount || 0,
          completionCount: tutorialData.completionCount || 0
        },
        this.getTutorialOptions()
      );
      
      this.logger.log(`Created AI tutorial: ${tutorial.title} (${tutorial.id})`);
      return tutorial;
    } catch (error) {
      this.logger.error('Failed to create AI tutorial:', error);
      throw error;
    }
  }

  async getTutorials(difficulty?: string, userId?: string, limit: number = 20): Promise<AITutorialWithProgress[]> {
    try {
      let tutorials: AITutorial[];
      
      if (difficulty) {
        tutorials = await this.redisDb.findByIndex<AITutorial>(
          this.AI_TUTORIAL,
          'difficulty',
          difficulty,
          { limit: 100 }
        );
      } else {
        const result = await this.redisDb.findMany<AITutorial>(
          this.AI_TUTORIAL,
          { limit: 100 }
        );
        tutorials = result.data;
      }

      // Filter for published tutorials only
      const publishedTutorials = tutorials.filter((tutorial: any) => tutorial.isPublished);

      // Sort by rating and view count
      publishedTutorials.sort((a, b) => {
        if (b.rating !== a.rating) return b.rating - a.rating;
        return b.viewCount - a.viewCount;
      });

      // Limit results
      const limitedTutorials = publishedTutorials.slice(0, limit);

      // Enrich with progress data if userId provided
      const tutorialsWithProgress: AITutorialWithProgress[] = await Promise.all(
        limitedTutorials.map(async (tutorial) => {
          let userProgress: TutorialProgress | undefined;
          let isCompleted = false;
          let progressPercentage = 0;
          
          if (userId) {
            const userProgressList = await this.redisDb.findByIndex<TutorialProgress>(
              this.TUTORIAL_PROGRESS,
              'userId',
              userId
            );
            
            userProgress = userProgressList.find(p => p.tutorialId.toString() === tutorial.id);
            if (userProgress) {
              isCompleted = userProgress.isCompleted;
              progressPercentage = userProgress.progress;
            }
          }
          
          return {
            ...tutorial,
            userProgress,
            isCompleted,
            progressPercentage
          };
        })
      );

      return tutorialsWithProgress;
    } catch (error) {
      this.logger.error('Failed to get AI tutorials:', error);
      throw error;
    }
  }

  async startTutorial(userId: string, tutorialId: number): Promise<{ success: boolean; message: string }> {
    try {
      // Check if already started
      const userProgressList = await this.redisDb.findByIndex<TutorialProgress>(
        this.TUTORIAL_PROGRESS,
        'userId',
        userId
      );
      
      const existingProgress = userProgressList.find(p => p.tutorialId === tutorialId);
      if (existingProgress) {
        return { success: false, message: 'Tutorial already started!' };
      }

      await this.redisDb.create<TutorialProgress>(
        this.TUTORIAL_PROGRESS,
        {
          userId,
          tutorialId,
          progress: 0,
          currentSection: 0,
          completedSections: [],
          timeSpent: 0,
          isCompleted: false
        },
        this.getProgressOptions()
      );

      // Increment view count
      await this.redisDb.increment(this.AI_TUTORIAL, tutorialId.toString(), 'viewCount', 1);

      this.logger.log(`User ${userId} started tutorial ${tutorialId}`);
      return { success: true, message: 'Tutorial started successfully!' };
    } catch (error) {
      this.logger.error('Failed to start tutorial:', error);
      return { success: false, message: 'Failed to start tutorial. Please try again.' };
    }
  }

  async updateTutorialProgress(
    userId: string, 
    tutorialId: number, 
    progress: number,
    currentSection: number,
    timeSpent: number
  ): Promise<void> {
    try {
      const isCompleted = progress >= 100;
      
      // Find existing progress
      const userProgressList = await this.redisDb.findByIndex<TutorialProgress>(
        this.TUTORIAL_PROGRESS,
        'userId',
        userId
      );
      
      const existingProgress = userProgressList.find(p => p.tutorialId === tutorialId);
      if (!existingProgress) {
        throw new Error('Tutorial progress not found');
      }

      const updateData: Partial<TutorialProgress> = {
        progress,
        currentSection,
        timeSpent,
        isCompleted,
        lastAccessedAt: new Date().toISOString()
      };
      
      if (isCompleted && !existingProgress.isCompleted) {
        updateData.completedAt = new Date().toISOString();
      }
      
      await this.redisDb.update<TutorialProgress>(
        this.TUTORIAL_PROGRESS,
        existingProgress.id,
        updateData,
        this.getProgressOptions()
      );

      if (isCompleted && !existingProgress.isCompleted) {
        // Increment completion count
        await this.redisDb.increment(this.AI_TUTORIAL, tutorialId.toString(), 'completionCount', 1);
      }

      this.logger.log(`Updated tutorial progress for user ${userId}: ${progress}%`);
    } catch (error) {
      this.logger.error('Failed to update tutorial progress:', error);
      throw error;
    }
  }

  // AI News Management
  async createNews(newsData: Omit<AINews, 'id' | 'createdAt' | 'updatedAt'>): Promise<AINews> {
    try {
      const news = await this.redisDb.create<AINews>(
        this.AI_NEWS,
        {
          ...newsData,
          views: newsData.views || 0,
          likes: newsData.likes || 0,
          isBreaking: newsData.isBreaking || false
        },
        this.getNewsOptions()
      );
      
      this.logger.log(`Created AI news: ${news.title} (${news.id})`);
      return news;
    } catch (error) {
      this.logger.error('Failed to create AI news:', error);
      throw error;
    }
  }

  async getNews(category?: string, importance?: string, limit: number = 20): Promise<AINews[]> {
    try {
      let news: AINews[];
      
      if (category && importance) {
        // Get by category first, then filter by importance
        const categoryNews = await this.redisDb.findByIndex<AINews>(
          this.AI_NEWS,
          'category',
          category,
          { limit: 100 }
        );
        news = categoryNews.filter((n: any) => n.importance === importance);
      } else if (category) {
        news = await this.redisDb.findByIndex<AINews>(
          this.AI_NEWS,
          'category',
          category,
          { limit: 100 }
        );
      } else if (importance) {
        news = await this.redisDb.findByIndex<AINews>(
          this.AI_NEWS,
          'importance',
          importance,
          { limit: 100 }
        );
      } else {
        const result = await this.redisDb.findMany<AINews>(
          this.AI_NEWS,
          { limit: 100 }
        );
        news = result.data;
      }

      // Sort by breaking news first, then by published date
      news.sort((a, b) => {
        if (a.isBreaking && !b.isBreaking) return -1;
        if (!a.isBreaking && b.isBreaking) return 1;
        return new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime();
      });

      return news.slice(0, limit);
    } catch (error) {
      this.logger.error('Failed to get AI news:', error);
      throw error;
    }
  }

  // User Preferences Management
  async getUserPreferences(userId: string): Promise<AIUserPreferences | null> {
    try {
      const userPreferences = await this.redisDb.findByIndex<AIUserPreferences>(
        this.AI_USER_PREFERENCES,
        'userId',
        userId
      );

      return userPreferences[0] || null;
    } catch (error) {
      this.logger.error('Failed to get user preferences:', error);
      throw error;
    }
  }

  async updateUserPreferences(userId: string, preferencesData: Partial<AIUserPreferences>): Promise<void> {
    try {
      const existing = await this.getUserPreferences(userId);
      
      if (existing) {
        await this.redisDb.update<AIUserPreferences>(
          this.AI_USER_PREFERENCES,
          existing.id,
          {
            ...preferencesData,
            updatedAt: new Date().toISOString()
          },
          this.getPreferencesOptions()
        );
      } else {
        await this.redisDb.create<AIUserPreferences>(
          this.AI_USER_PREFERENCES,
          {
            userId,
            preferredCategories: [],
            difficultyLevel: 'beginner',
            notificationSettings: {},
            learningGoals: [],
            weeklyTimeCommitment: 0,
            preferredContentTypes: [],
            languagePreference: 'en',
            timezone: 'UTC',
            ...preferencesData
          },
          this.getPreferencesOptions()
        );
      }

      this.logger.log(`Updated AI preferences for user ${userId}`);
    } catch (error) {
      this.logger.error('Failed to update user preferences:', error);
      throw error;
    }
  }

  // Analytics and Statistics
  async getLearningStats(userId: string): Promise<LearningStats> {
    try {
      // Get total tools
      const toolsResult = await this.redisDb.findMany<AITool>(
        this.AI_TOOL,
        { limit: 1000 }
      );
      const totalTools = toolsResult.data.filter((tool: any) => tool.isActive).length;

      // Get bookmarked tools
      const userBookmarks = await this.redisDb.findByIndex<AIToolBookmark>(
        this.AI_TOOL_BOOKMARK,
        'userId',
        userId
      );
      const bookmarkedTools = userBookmarks.length;

      // Get tutorial stats
      const tutorialsResult = await this.redisDb.findMany<AITutorial>(
        this.AI_TUTORIAL,
        { limit: 1000 }
      );
      const totalTutorials = tutorialsResult.data.filter((tutorial: any) => tutorial.isPublished).length;

      const userProgress = await this.redisDb.findByIndex<TutorialProgress>(
        this.TUTORIAL_PROGRESS,
        'userId',
        userId
      );
      
      const completedTutorials = userProgress.filter((p: any) => p.isCompleted).length;
      const totalTimeSpent = userProgress.reduce((sum, p) => sum + p.timeSpent, 0);

      // Get user preferences
      const preferences = await this.getUserPreferences(userId);

      return {
        totalTools,
        bookmarkedTools,
        completedTutorials,
        totalTutorials,
        averageRating: 0, // TODO: Calculate from tutorial ratings
        timeSpentLearning: totalTimeSpent,
        learningStreak: 0, // TODO: Calculate learning streak
        favoriteCategories: preferences?.preferredCategories || []
      };
    } catch (error) {
      this.logger.error('Failed to get learning stats:', error);
      throw error;
    }
  }

  // User Management
  async ensureUser(discordId: string, username: string): Promise<void> {
    try {
      const existingUsers = await this.redisDb.findByIndex<any>(
        this.USER,
        'discordId',
        discordId
      );

      if (existingUsers.length === 0) {
        await this.redisDb.create<any>(
          this.USER,
          {
            discordId,
            username,
            isActive: true,
            lastActivityAt: new Date().toISOString(),
            preferences: {},
            profile: {}
          },
          this.getUserOptions()
        );
        
        this.logger.log(`Created new user: ${username} (${discordId})`);
      } else if (existingUsers[0].username !== username) {
        await this.redisDb.update<any>(
          this.USER,
          existingUsers[0].id,
          { 
            username,
            lastActivityAt: new Date().toISOString()
          },
          this.getUserOptions()
        );
      }
    } catch (error) {
      this.logger.error('Failed to ensure user:', error);
      throw error;
    }
  }
}