import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Client } from 'discord.js';
import { EnhancedChannelPanelOrchestratorService } from '../enhanced-channel-panel-orchestrator.service';

@Injectable()
export class AutoDeploymentService implements OnModuleInit {
  private readonly logger = new Logger(AutoDeploymentService.name);
  private readonly AUTO_DEPLOY_ENABLED = true;
  private readonly DEPLOYMENT_DELAY = 10000; // 10 seconds after startup

  constructor(
    private readonly client: Client,
    private readonly orchestrator: EnhancedChannelPanelOrchestratorService,
    private readonly configService: ConfigService,
    // Auto-cleanup now handled through orchestrator
  ) {}

  async onModuleInit() {
    if (!this.AUTO_DEPLOY_ENABLED) {
      this.logger.log('🚫 Auto-deployment disabled');
      return;
    }

    this.logger.log('🔄 Auto-deployment service initialized, waiting for Discord client...');
    
    // Wait for Discord client to be ready
    this.client.once('ready', () => {
      setTimeout(() => {
        this.performAutoDeployment();
      }, this.DEPLOYMENT_DELAY);
    });
  }

  private async performAutoDeployment(): Promise<void> {
    this.logger.log('🚀 Starting automatic panel deployment...');

    try {
      // Get guild ID from environment or use the first available guild
      const guildId = this.configService.get<string>('GUILD_ID') || 
                     this.client.guilds.cache.first()?.id;

      if (!guildId) {
        this.logger.error('❌ No guild found for auto-deployment');
        return;
      }

      this.logger.log(`🎯 Auto-deploying panels in guild: ${guildId}`);

      // Health check before deployment
      const health = await this.orchestrator.getHealthStatus();
      this.logger.log(`🏥 Orchestrator health: ${health.status}`);

      if (health.status === 'unhealthy') {
        this.logger.error('❌ Orchestrator unhealthy, skipping auto-deployment');
        this.logger.error('Errors:', health.errors);
        return;
      }

      // Dynamically discover all text channels in the guild
      const guild = this.client.guilds.cache.get(guildId);
      if (!guild) {
        this.logger.error(`❌ Guild ${guildId} not found`);
        return;
      }

      // Get all text channels, excluding threads and voice channels
      const textChannels = guild.channels.cache
        .filter((channel: any) => channel.isTextBased() && channel.type === 0) // 0 = GuildText
        .values();

      const validChannelIds: string[] = [];
      const channelsByCategory: Record<string, string[]> = {};

      for (const channel of textChannels) {
        const channelId = channel.id;
        const categoryName = channel.parent?.name || 'uncategorized';
        
        // Validate channel exists and is accessible
        try {
          const discordChannel = await guild.channels.fetch(channelId);
          if (discordChannel && discordChannel.isTextBased()) {
            validChannelIds.push(channelId);
            
            if (!channelsByCategory[categoryName]) {
              channelsByCategory[categoryName] = [];
            }
            channelsByCategory[categoryName].push(channelId);
            
            this.logger.debug(`✅ Valid channel: #${channel.name} (${categoryName})`);
          }
        } catch (error) {
          this.logger.warn(`⚠️ Skipping inaccessible channel: ${channelId} (${channel.name})`);
        }
      }

      this.logger.log(`📋 Deploying panels to ${validChannelIds.length} channels across ${Object.keys(channelsByCategory).length} categories`);

      if (validChannelIds.length === 0) {
        this.logger.warn('❌ No valid channels found for deployment');
        return;
      }

      const result = await this.orchestrator.deployPanelsToChannels(validChannelIds);

      // Log results by category
      for (const [category, channels] of Object.entries(channelsByCategory)) {
        const successful = channels.filter((id: any) => result.successful.some(s => s.channelId === id));
        const failed = channels.filter((id: any) => result.failed.some(f => f.channelId === id));
        
        this.logger.log(`📊 ${category}: ${successful.length}/${channels.length} panels deployed successfully`);
        
        if (failed.length > 0) {
          this.logger.warn(`⚠️ ${category} failed deployments:`, failed);
        }
      }

      // Overall statistics
      this.logger.log(`✅ Auto-deployment completed!`);
      this.logger.log(`   📈 Successful: ${result.successful.length}`);
      this.logger.log(`   ❌ Failed: ${result.failed.length}`);

      if (result.failed.length > 0) {
        const failedChannelIds = result.failed.map((f: any) => f.channelId);
        this.logger.warn('❌ Failed channel deployments:', failedChannelIds);
        // Retry failed deployments after a delay
        setTimeout(() => {
          this.retryFailedDeployments(failedChannelIds);
        }, 30000); // Retry after 30 seconds
      }

      // Log deployment statistics
      const stats = await this.orchestrator.getOrchestratorStats();
      this.logger.log('📊 Final Deployment Statistics:');
      this.logger.log(`   🎯 Total active deployments: ${stats.deployments?.activeDeployments || 0}`);
      this.logger.log(`   📋 Panel stats:`, stats.panels);

      // Post-deployment cleanup through orchestrator
      try {
        this.orchestrator.invalidateContentCache();
        this.logger.log('✅ Post-deployment cleanup completed');
      } catch (cleanupError) {
        this.logger.warn('⚠️ Post-deployment cleanup failed:', cleanupError);
      }

      // Schedule periodic updates
      this.schedulePeriodicUpdates();

    } catch (error) {
      this.logger.error('❌ Auto-deployment failed:', error);
      
      // Trigger error recovery through orchestrator
      try {
        await this.orchestrator.triggerRecovery({ dryRun: false });
      } catch (recoveryError) {
        this.logger.warn('⚠️ Error recovery failed:', recoveryError);
      }
    }
  }

  private async retryFailedDeployments(failedChannels: string[]): Promise<void> {
    if (failedChannels.length === 0) return;

    this.logger.log(`🔄 Retrying deployment for ${failedChannels.length} failed channels...`);

    try {
      const result = await this.orchestrator.deployPanelsToChannels(failedChannels);
      
      this.logger.log(`✅ Retry completed: ${result.successful.length} recovered, ${result.failed.length} still failed`);
      
      if (result.failed.length > 0) {
        this.logger.warn('❌ Channels that still failed after retry:', result.failed);
        
        // Trigger recovery after retry if we still have failures
        if (result.failed.length > 2) {
          try {
            await this.orchestrator.triggerRecovery({ dryRun: false });
          } catch (recoveryError) {
            this.logger.warn('⚠️ Post-retry recovery failed:', recoveryError);
          }
        }
      }
    } catch (error) {
      this.logger.error('❌ Retry deployment failed:', error);
    }
  }

  private schedulePeriodicUpdates(): void {
    const UPDATE_INTERVAL = 6 * 60 * 60 * 1000; // 6 hours

    this.logger.log(`⏰ Scheduling periodic panel updates every ${UPDATE_INTERVAL / (60 * 60 * 1000)} hours`);

    setInterval(async () => {
      this.logger.log('🔄 Starting periodic panel updates...');
      
      try {
        await this.orchestrator.updateAllPanels();
        
        const stats = await this.orchestrator.getOrchestratorStats();
        this.logger.log(`✅ Periodic update completed: ${stats.deployments?.activeDeployments || 0} panels updated`);
      } catch (error) {
        this.logger.error('❌ Periodic update failed:', error);
      }
    }, UPDATE_INTERVAL);
  }

  /**
   * Manual trigger for re-deployment (for admin use)
   */
  async triggerRedeployment(): Promise<void> {
    this.logger.log('🔄 Manual redeployment triggered...');
    await this.performAutoDeployment();
  }

  /**
   * Get deployment status
   */
  async getDeploymentStatus(): Promise<{
    isEnabled: boolean;
    health: any;
    stats: any;
  }> {
    return {
      isEnabled: this.AUTO_DEPLOY_ENABLED,
      health: await this.orchestrator.getHealthStatus(),
      stats: await this.orchestrator.getOrchestratorStats(),
    };
  }

  /**
   * Deploy panels to a specific category
   */
  async deployToCategory(categoryName: string): Promise<void> {
    const categoryChannels: Record<string, string[]> = {
      announcements: ['1396352345046257665', '1396352364843241592', '1394499361454555207'],
      community: ['1396529696442810390', '1396529869730353283', '1396352417976553553', '1396530178079064154', '1396536876801720330'],
      aiMastery: ['1396594755064041482', '1396594735988084817', '1396530075054116934', '1396529967935787090', '1396594777985777725'],
      wealthCreation: ['1396594819224309852', '1396594859330113586', '1396594839298244689', '1396537062202806274'],
      personalGrowth: ['1396594969015873616', '1396594948434006046', '1396594928846413844'],
      networkingBusiness: ['1396595067150016532', '1396595103518289952', '1396595124535160932'],
    };

    const channels = categoryChannels[categoryName];
    if (!channels) {
      this.logger.error(`❌ Unknown category: ${categoryName}`);
      return;
    }

    this.logger.log(`🎯 Deploying panels to ${categoryName} category (${channels.length} channels)`);
    
    const result = await this.orchestrator.deployPanelsToChannels(channels);
    
    this.logger.log(`✅ Category deployment completed: ${result.successful.length}/${channels.length} successful`);
    
    if (result.failed.length > 0) {
      this.logger.warn(`❌ Failed deployments in ${categoryName}:`, result.failed);
    }
  }
}