import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DatabaseService } from '@/core/database';

/**
 * Panel Features Service
 * Consolidates functionality from:
 * - AIToolsService
 * - FinancialCalculatorService  
 * - HabitTrackerService
 * - TroubleshootingService
 * 
 * Provides unified feature capabilities for all panel types.
 */
@Injectable()
export class PanelFeaturesService {
  private readonly logger = new Logger(PanelFeaturesService.name);
  private readonly aiToolsCache = new Map<string, AITool[]>();
  private readonly habitData = new Map<string, HabitProgress>();
  private readonly troubleshootingKB = new Map<string, TroubleshootingGuide>();

  constructor(
    private readonly configService: ConfigService,
    private readonly databaseService: DatabaseService,
  ) {
    this.initializeFeatures();
  }

  // === AI TOOLS (replaces AIToolsService) ===

  /**
   * Get AI tools based on category or search query
   */
  async getAITools(category?: string, query?: string): Promise<AITool[]> {
    try {
      this.logger.debug(`Getting AI tools for category: ${category}, query: ${query}`);

      // Check cache first
      const cacheKey = `${category || 'all'}_${query || ''}`;
      if (this.aiToolsCache.has(cacheKey)) {
        return this.aiToolsCache.get(cacheKey)!;
      }

      // Build query based on parameters
      let sqlQuery = 'SELECT * FROM ai_tools WHERE 1=1';
      const params: any[] = [];

      if (category) {
        sqlQuery += ' AND category = $' + (params.length + 1);
        params.push(category);
      }

      if (query) {
        sqlQuery += ' AND (name ILIKE $' + (params.length + 1) + ' OR description ILIKE $' + (params.length + 1) + ')';
        params.push(`%${query}%`);
      }

      sqlQuery += ' ORDER BY popularity DESC, name ASC';

      const result = await this.databaseService.query(sqlQuery, params);
      
      const tools: AITool[] = result.map((row: any) => ({
        id: row.id,
        name: row.name,
        description: row.description,
        category: row.category,
        url: row.url,
        pricing: row.pricing,
        features: JSON.parse(row.features || '[]'),
        rating: parseFloat(row.rating),
        popularity: parseInt(row.popularity),
        tags: JSON.parse(row.tags || '[]')
      }));

      // Cache results
      this.aiToolsCache.set(cacheKey, tools);
      
      return tools;
    } catch (error) {
      this.logger.error('Failed to get AI tools:', error);
      return [];
    }
  }

  /**
   * Get AI tool recommendations based on user preferences
   */
  async getAIToolRecommendations(userId: string, preferences: ToolPreferences): Promise<AITool[]> {
    try {
      // Get user's previous tool interactions
      const userHistory = await this.databaseService.query(
        'SELECT tool_category, COUNT(*) as usage_count FROM user_tool_interactions WHERE user_id = $1 GROUP BY tool_category',
        [userId]
      );

      // Build recommendation query based on preferences and history
      const recommendedCategories = userHistory.map((row: any) => row.tool_category);
      
      if (preferences.categories) {
        recommendedCategories.push(...preferences.categories);
      }

      const tools = await this.getAITools();
      
      // Filter and score tools based on preferences
      return tools
        .filter((tool: any) => {
          if (preferences.priceRange && !this.isWithinPriceRange(tool.pricing, preferences.priceRange)) {
            return false;
          }
          if (preferences.minRating && tool.rating < preferences.minRating) {
            return false;
          }
          return true;
        })
        .sort((a, b) => {
          // Prioritize tools in user's preferred categories
          const aInPreferred = recommendedCategories.includes(a.category) ? 1 : 0;
          const bInPreferred = recommendedCategories.includes(b.category) ? 1 : 0;
          
          if (aInPreferred !== bInPreferred) {
            return bInPreferred - aInPreferred;
          }
          
          // Then by rating and popularity
          return (b.rating * b.popularity) - (a.rating * a.popularity);
        })
        .slice(0, preferences.limit || 10);

    } catch (error) {
      this.logger.error('Failed to get AI tool recommendations:', error);
      return [];
    }
  }

  // === FINANCIAL CALCULATOR (replaces FinancialCalculatorService) ===

  /**
   * Calculate financial metrics based on input data
   */
  async calculateFinancials(data: FinancialData): Promise<CalculationResult> {
    try {
      this.logger.debug(`Calculating financials for type: ${data.calculationType}`);

      const result: CalculationResult = {
        calculationType: data.calculationType,
        inputs: data,
        outputs: {},
        timestamp: new Date(),
        formulas: []
      };

      switch (data.calculationType) {
        case 'compound_interest':
          result.outputs = this.calculateCompoundInterest(data);
          break;
        
        case 'loan_payment':
          result.outputs = this.calculateLoanPayment(data);
          break;
        
        case 'investment_return':
          result.outputs = this.calculateInvestmentReturn(data);
          break;
        
        case 'retirement_planning':
          result.outputs = this.calculateRetirementPlanning(data);
          break;
        
        case 'debt_payoff':
          result.outputs = this.calculateDebtPayoff(data);
          break;
        
        default:
          throw new Error(`Unknown calculation type: ${data.calculationType}`);
      }

      // Store calculation for user history
      await this.storeCalculation(data.userId, result);

      return result;
    } catch (error) {
      this.logger.error('Financial calculation failed:', error);
      throw error;
    }
  }

  private calculateCompoundInterest(data: FinancialData): any {
    const { principal, rate, time, compoundFrequency = 12 } = data;
    const amount = principal * Math.pow(1 + (rate / compoundFrequency), compoundFrequency * time);
    const interest = amount - principal;

    return {
      finalAmount: Math.round(amount * 100) / 100,
      totalInterest: Math.round(interest * 100) / 100,
      principal,
      rate: rate * 100, // Convert to percentage
      years: time
    };
  }

  private calculateLoanPayment(data: FinancialData): any {
    const { principal, rate, time } = data;
    const monthlyRate = rate / 12;
    const numPayments = time * 12;
    
    const monthlyPayment = principal * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) / 
                          (Math.pow(1 + monthlyRate, numPayments) - 1);
    
    const totalPaid = monthlyPayment * numPayments;
    const totalInterest = totalPaid - principal;

    return {
      monthlyPayment: Math.round(monthlyPayment * 100) / 100,
      totalPaid: Math.round(totalPaid * 100) / 100,
      totalInterest: Math.round(totalInterest * 100) / 100,
      loanAmount: principal
    };
  }

  // === HABIT TRACKER (replaces HabitTrackerService) ===

  /**
   * Track habit progress for a user
   */
  async trackHabit(userId: string, habit: HabitData): Promise<HabitProgress> {
    try {
      this.logger.debug(`Tracking habit ${habit.name} for user ${userId}`);

      // Get existing progress or create new
      let progress = this.habitData.get(`${userId}_${habit.id}`) || {
        userId,
        habitId: habit.id,
        habitName: habit.name,
        currentStreak: 0,
        longestStreak: 0,
        totalCompletions: 0,
        completionRate: 0,
        lastCompleted: null,
        createdAt: new Date(),
        entries: []
      };

      // Record the habit completion
      const entry: HabitEntry = {
        date: new Date(),
        completed: habit.completed,
        notes: habit.notes,
        value: habit.value // For quantifiable habits
      };

      progress.entries.push(entry);

      if (habit.completed) {
        progress.totalCompletions++;
        
        // Update streak
        if (this.isConsecutiveDay(progress.lastCompleted)) {
          progress.currentStreak++;
        } else {
          progress.currentStreak = 1;
        }
        
        progress.longestStreak = Math.max(progress.longestStreak, progress.currentStreak);
        progress.lastCompleted = new Date();
      } else {
        // Reset current streak if not completed
        progress.currentStreak = 0;
      }

      // Calculate completion rate (last 30 days)
      const last30Days = progress.entries.filter((e: any) => 
        e.date >= new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      );
      progress.completionRate = last30Days.length > 0 ? 
        (last30Days.filter((e: any) => e.completed).length / last30Days.length) * 100 : 0;

      // Cache updated progress
      this.habitData.set(`${userId}_${habit.id}`, progress);

      // Persist to database
      await this.databaseService.query(
        `INSERT INTO habit_tracking (user_id, habit_id, progress_data, updated_at)
         VALUES ($1, $2, $3, $4)
         ON CONFLICT (user_id, habit_id)
         DO UPDATE SET progress_data = $3, updated_at = $4`,
        [userId, habit.id, JSON.stringify(progress), new Date()]
      );

      return progress;
    } catch (error) {
      this.logger.error('Failed to track habit:', error);
      throw error;
    }
  }

  /**
   * Get habit analytics for a user
   */
  async getHabitAnalytics(userId: string, habitId?: string): Promise<HabitAnalytics> {
    try {
      let query = 'SELECT * FROM habit_tracking WHERE user_id = $1';
      const params = [userId];

      if (habitId) {
        query += ' AND habit_id = $2';
        params.push(habitId);
      }

      const result = await this.databaseService.query(query, params);
      
      const habits = result.map((row: any) => JSON.parse(row.progress_data));
      
      return {
        totalHabits: habits.length,
        activeStreaks: habits.filter((h: any) => h.currentStreak > 0).length,
        averageCompletionRate: habits.reduce((sum, h) => sum + h.completionRate, 0) / habits.length,
        longestStreak: Math.max(...habits.map((h: any) => h.longestStreak)),
        habits: habitId ? habits.filter((h: any) => h.habitId === habitId) : habits
      };
    } catch (error) {
      this.logger.error('Failed to get habit analytics:', error);
      throw error;
    }
  }

  // === TROUBLESHOOTING (replaces TroubleshootingService) ===

  /**
   * Provide troubleshooting guidance for technical issues
   */
  async provideTroubleshooting(issue: TechnicalIssue): Promise<TroubleshootingGuide> {
    try {
      this.logger.debug(`Providing troubleshooting for: ${issue.category}`);

      // Check cache first
      const cacheKey = `${issue.category}_${issue.subCategory || ''}`;
      if (this.troubleshootingKB.has(cacheKey)) {
        return this.troubleshootingKB.get(cacheKey)!;
      }

      // Query knowledge base
      const result = await this.databaseService.query(
        `SELECT * FROM troubleshooting_guides 
         WHERE category = $1 AND ($2 IS NULL OR sub_category = $2)
         ORDER BY relevance_score DESC`,
        [issue.category, issue.subCategory]
      );

      if (result.length === 0) {
        return this.generateGenericTroubleshootingGuide(issue);
      }

      const guide: TroubleshootingGuide = {
        id: result[0].id,
        title: result[0].title,
        category: result[0].category,
        subCategory: result[0].sub_category,
        symptoms: JSON.parse(result[0].symptoms),
        diagnosticSteps: JSON.parse(result[0].diagnostic_steps),
        solutions: JSON.parse(result[0].solutions),
        commonCauses: JSON.parse(result[0].common_causes),
        preventionTips: JSON.parse(result[0].prevention_tips),
        relatedIssues: JSON.parse(result[0].related_issues || '[]'),
        difficulty: result[0].difficulty,
        estimatedTime: result[0].estimated_time,
        createdAt: result[0].created_at,
        updatedAt: result[0].updated_at
      };

      // Cache the guide
      this.troubleshootingKB.set(cacheKey, guide);

      return guide;
    } catch (error) {
      this.logger.error('Failed to provide troubleshooting:', error);
      throw error;
    }
  }

  /**
   * Search troubleshooting guides by keywords
   */
  async searchTroubleshootingGuides(query: string): Promise<TroubleshootingGuide[]> {
    try {
      const result = await this.databaseService.query(
        `SELECT * FROM troubleshooting_guides 
         WHERE title ILIKE $1 OR symptoms::text ILIKE $1 OR solutions::text ILIKE $1
         ORDER BY relevance_score DESC
         LIMIT 10`,
        [`%${query}%`]
      );

      return result.map((row: any) => ({
        id: row.id,
        title: row.title,
        category: row.category,
        subCategory: row.sub_category,
        symptoms: JSON.parse(row.symptoms),
        diagnosticSteps: JSON.parse(row.diagnostic_steps),
        solutions: JSON.parse(row.solutions),
        commonCauses: JSON.parse(row.common_causes),
        preventionTips: JSON.parse(row.prevention_tips),
        relatedIssues: JSON.parse(row.related_issues || '[]'),
        difficulty: row.difficulty,
        estimatedTime: row.estimated_time,
        createdAt: row.created_at,
        updatedAt: row.updated_at
      }));
    } catch (error) {
      this.logger.error('Failed to search troubleshooting guides:', error);
      return [];
    }
  }

  // === UTILITY METHODS ===

  private async initializeFeatures(): Promise<void> {
    this.logger.log('Initializing panel features service...');
    
    // Load cached data
    await this.loadCachedData();
    
    this.logger.log('Panel features service initialized');
  }

  private async loadCachedData(): Promise<void> {
    try {
      // Load frequently accessed AI tools
      const popularTools = await this.getAITools();
      this.aiToolsCache.set('popular', popularTools.slice(0, 20));
      
      // Load recent habit data
      // Implementation would load recent habit data here
      
    } catch (error) {
      this.logger.error('Failed to load cached data:', error);
    }
  }

  private isWithinPriceRange(pricing: string, priceRange: { min: number; max: number }): boolean {
    // Parse pricing string and check if within range
    if (pricing.toLowerCase().includes('free')) {
      return priceRange.min === 0;
    }
    
    // Extract numeric price (simplified implementation)
    const price = parseFloat(pricing.replace(/[^0-9.]/g, ''));
    return price >= priceRange.min && price <= priceRange.max;
  }

  private calculateInvestmentReturn(data: FinancialData): any {
    // Implementation for investment return calculation
    const { principal, rate, time } = data;
    const futureValue = principal * Math.pow(1 + rate, time);
    return {
      futureValue: Math.round(futureValue * 100) / 100,
      totalReturn: Math.round((futureValue - principal) * 100) / 100,
      annualizedReturn: rate * 100
    };
  }

  private calculateRetirementPlanning(data: FinancialData): any {
    // Implementation for retirement planning calculation
    return {
      targetAmount: data.targetAmount || 1000000,
      monthlyContribution: data.monthlyContribution || 500,
      yearsToRetirement: data.time || 30
    };
  }

  private calculateDebtPayoff(data: FinancialData): any {
    // Implementation for debt payoff calculation
    return {
      totalDebt: data.principal,
      payoffTime: data.time,
      totalInterest: data.principal * data.rate * data.time
    };
  }

  private async storeCalculation(userId: string, result: CalculationResult): Promise<void> {
    await this.databaseService.query(
      'INSERT INTO financial_calculations (user_id, calculation_data, created_at) VALUES ($1, $2, $3)',
      [userId, JSON.stringify(result), new Date()]
    );
  }

  private isConsecutiveDay(lastCompleted: Date | null): boolean {
    if (!lastCompleted) return false;
    
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    
    return lastCompleted.toDateString() === yesterday.toDateString();
  }

  private generateGenericTroubleshootingGuide(issue: TechnicalIssue): TroubleshootingGuide {
    return {
      id: 'generic',
      title: `General troubleshooting for ${issue.category}`,
      category: issue.category,
      subCategory: issue.subCategory,
      symptoms: ['Issue not working as expected'],
      diagnosticSteps: [
        'Check basic requirements',
        'Restart the application/service',
        'Check logs for errors',
        'Verify configuration settings'
      ],
      solutions: [
        'Follow the diagnostic steps above',
        'Consult official documentation',
        'Search community forums',
        'Contact support if issue persists'
      ],
      commonCauses: ['Configuration issues', 'Network problems', 'Software bugs'],
      preventionTips: ['Keep software updated', 'Regular maintenance', 'Monitor logs'],
      relatedIssues: [],
      difficulty: 'medium',
      estimatedTime: '15-30 minutes',
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }
}

// === TYPE DEFINITIONS ===

interface AITool {
  id: string;
  name: string;
  description: string;
  category: string;
  url: string;
  pricing: string;
  features: string[];
  rating: number;
  popularity: number;
  tags: string[];
}

interface ToolPreferences {
  categories?: string[];
  priceRange?: { min: number; max: number };
  minRating?: number;
  limit?: number;
}

interface FinancialData {
  calculationType: string;
  userId?: string;
  principal: number;
  rate: number;
  time: number;
  compoundFrequency?: number;
  monthlyContribution?: number;
  targetAmount?: number;
}

interface CalculationResult {
  calculationType: string;
  inputs: FinancialData;
  outputs: any;
  timestamp: Date;
  formulas: string[];
}

interface HabitData {
  id: string;
  name: string;
  completed: boolean;
  notes?: string;
  value?: number;
}

interface HabitProgress {
  userId: string;
  habitId: string;
  habitName: string;
  currentStreak: number;
  longestStreak: number;
  totalCompletions: number;
  completionRate: number;
  lastCompleted: Date | null;
  createdAt: Date;
  entries: HabitEntry[];
}

interface HabitEntry {
  date: Date;
  completed: boolean;
  notes?: string;
  value?: number;
}

interface HabitAnalytics {
  totalHabits: number;
  activeStreaks: number;
  averageCompletionRate: number;
  longestStreak: number;
  habits: HabitProgress[];
}

interface TechnicalIssue {
  category: string;
  subCategory?: string;
  description?: string;
  severity?: 'low' | 'medium' | 'high' | 'critical';
}

interface TroubleshootingGuide {
  id: string;
  title: string;
  category: string;
  subCategory?: string;
  symptoms: string[];
  diagnosticSteps: string[];
  solutions: string[];
  commonCauses: string[];
  preventionTips: string[];
  relatedIssues: string[];
  difficulty: 'easy' | 'medium' | 'hard';
  estimatedTime: string;
  createdAt: Date;
  updatedAt: Date;
}