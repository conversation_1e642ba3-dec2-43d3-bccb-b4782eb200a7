import { Injectable, Logger } from '@nestjs/common';
import { RedisDatabaseService } from '@/core/database';
import { v4 as uuidv4 } from 'uuid';

// Redis Entity Types
export interface Announcement {
  id: string;
  title: string;
  content: string;
  authorId: string;
  type: string;
  priority: string;
  guildId: string;
  targetChannels?: string[];
  targetRoles?: string[];
  targetUsers?: string[];
  scheduledFor?: string;
  publishedAt?: string;
  expiresAt?: string;
  status: string;
  isPinned: boolean;
  allowComments: boolean;
  requiresAcknowledgment: boolean;
  embedData?: any;
  attachments?: string[];
  tags: string[];
  viewCount: number;
  reactionCount: number;
  commentCount: number;
  acknowledgmentCount: number;
  totalRecipients: number;
  createdAt: string;
  updatedAt: string;
}

export interface AnnouncementDelivery {
  id: string;
  announcementId: string;
  channelId: string;
  messageId?: string;
  isSuccessful: boolean;
  errorMessage?: string;
  deliveredAt: string;
  createdAt: string;
  updatedAt: string;
}

export interface AnnouncementSubscription {
  id: string;
  userId: string;
  guildId: string;
  isEnabled: boolean;
  subscribedTypes: string[];
  notificationChannel?: string;
  directMessage: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AnnouncementReaction {
  id: string;
  announcementId: string;
  userId: string;
  emoji: string;
  createdAt: string;
  updatedAt: string;
}

export interface AnnouncementComment {
  id: string;
  announcementId: string;
  userId: string;
  content: string;
  parentId?: string;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AnnouncementAcknowledgment {
  id: string;
  announcementId: string;
  userId: string;
  channelId?: string;
  messageId?: string;
  acknowledgedAt: string;
  createdAt: string;
  updatedAt: string;
}

export interface AnnouncementTemplate {
  id: string;
  name: string;
  description?: string;
  templateData: any;
  guildId: string;
  creatorId: string;
  isPublic: boolean;
  isActive: boolean;
  category?: string;
  usageCount: number;
  tags: string[];
  createdAt: string;
  updatedAt: string;
}

export interface AnnouncementWithStats extends Announcement {
  deliveryCount?: number;
  acknowledgeRate?: number;
}

export interface AnnouncementMetrics {
  totalAnnouncements: number;
  activeAnnouncements: number;
  scheduledAnnouncements: number;
  totalDeliveries: number;
  totalEngagements: number;
  averageEngagementRate: number;
  popularTypes: Array<{ type: string; count: number }>;
  recentActivity: Array<{
    date: string;
    announcements: number;
    engagements: number;
  }>;
}

@Injectable()
export class AnnouncementDatabaseService {
  private readonly logger = new Logger(AnnouncementDatabaseService.name);
  
  // Entity type constants
  private readonly ANNOUNCEMENT = 'announcement';
  private readonly ANNOUNCEMENT_DELIVERY = 'announcement_delivery';
  private readonly ANNOUNCEMENT_SUBSCRIPTION = 'announcement_subscription';
  private readonly ANNOUNCEMENT_REACTION = 'announcement_reaction';
  private readonly ANNOUNCEMENT_COMMENT = 'announcement_comment';
  private readonly ANNOUNCEMENT_ACKNOWLEDGMENT = 'announcement_acknowledgment';
  private readonly ANNOUNCEMENT_TEMPLATE = 'announcement_template';
  private readonly USER = 'user';

  constructor(private readonly redisDb: RedisDatabaseService) {}

  // Redis entity options
  private getAnnouncementOptions() {
    return {
      indexes: ['guildId', 'authorId', 'type', 'priority', 'status', 'isPinned'],
      relationships: {
        deliveries: 'announcement_delivery',
        reactions: 'announcement_reaction',
        comments: 'announcement_comment',
        acknowledgments: 'announcement_acknowledgment'
      }
    };
  }

  private getDeliveryOptions() {
    return {
      indexes: ['announcementId', 'channelId', 'isSuccessful']
    };
  }

  private getSubscriptionOptions() {
    return {
      indexes: ['userId', 'guildId', 'isEnabled']
    };
  }

  private getReactionOptions() {
    return {
      indexes: ['announcementId', 'userId', 'emoji']
    };
  }

  private getCommentOptions() {
    return {
      indexes: ['announcementId', 'userId', 'parentId', 'isDeleted']
    };
  }

  private getAcknowledgmentOptions() {
    return {
      indexes: ['announcementId', 'userId']
    };
  }

  private getTemplateOptions() {
    return {
      indexes: ['guildId', 'creatorId', 'isPublic', 'isActive', 'category']
    };
  }

  private getUserOptions() {
    return {
      indexes: ['discordId', 'isActive']
    };
  }

  // Announcement Management
  async createAnnouncement(announcementData: Omit<Announcement, 'id' | 'createdAt' | 'updatedAt'>): Promise<Announcement> {
    try {
      const announcement = await this.redisDb.create<Announcement>(
        this.ANNOUNCEMENT,
        {
          ...announcementData,
          viewCount: 0,
          reactionCount: 0,
          commentCount: 0,
          acknowledgmentCount: 0,
          totalRecipients: 0,
          targetChannels: announcementData.targetChannels || [],
          targetRoles: announcementData.targetRoles || [],
          targetUsers: announcementData.targetUsers || [],
          attachments: announcementData.attachments || [],
          tags: announcementData.tags || []
        },
        this.getAnnouncementOptions()
      );
      
      this.logger.log(`Created announcement: ${announcement.title} (${announcement.id})`);
      return announcement;
    } catch (error) {
      this.logger.error('Failed to create announcement:', error);
      throw error;
    }
  }

  async getAnnouncements(
    guildId: string,
    filters?: {
      type?: string;
      priority?: string;
      status?: string;
      authorId?: string;
    },
    limit: number = 20
  ): Promise<AnnouncementWithStats[]> {
    try {
      // Get guild announcements
      let announcements = await this.redisDb.findByIndex<Announcement>(
        this.ANNOUNCEMENT,
        'guildId',
        guildId,
        { limit: limit * 2 } // Get more for filtering
      );

      // Apply filters
      if (filters?.type) {
        announcements = announcements.filter((a: any) => a.type === filters.type);
      }
      if (filters?.priority) {
        announcements = announcements.filter((a: any) => a.priority === filters.priority);
      }
      if (filters?.status) {
        announcements = announcements.filter((a: any) => a.status === filters.status);
      }
      if (filters?.authorId) {
        announcements = announcements.filter((a: any) => a.authorId === filters.authorId);
      }

      // Sort by creation date (newest first)
      announcements.sort((a, b) => 
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );

      // Limit results
      const limitedAnnouncements = announcements.slice(0, limit);

      // Enrich with stats
      const announcementsWithStats: AnnouncementWithStats[] = await Promise.all(
        limitedAnnouncements.map(async (announcement) => {
          const deliveries = await this.redisDb.findByIndex<AnnouncementDelivery>(
            this.ANNOUNCEMENT_DELIVERY,
            'announcementId',
            announcement.id
          );
          
          const deliveryCount = deliveries.length;
          const acknowledgeRate = announcement.totalRecipients > 0 
            ? Math.round((announcement.acknowledgmentCount / announcement.totalRecipients) * 100 * 100) / 100
            : 0;
          
          return {
            ...announcement,
            deliveryCount,
            acknowledgeRate
          };
        })
      );

      return announcementsWithStats;
    } catch (error) {
      this.logger.error('Failed to get announcements:', error);
      throw error;
    }
  }

  async scheduleAnnouncement(
    announcementId: string,
    scheduledFor: Date
  ): Promise<{ success: boolean; message: string }> {
    try {
      await this.redisDb.update<Announcement>(
        this.ANNOUNCEMENT,
        announcementId,
        {
          scheduledFor: scheduledFor.toISOString(),
          status: 'scheduled',
          updatedAt: new Date().toISOString()
        },
        this.getAnnouncementOptions()
      );

      this.logger.log(`Scheduled announcement ${announcementId} for ${scheduledFor}`);
      return { success: true, message: 'Announcement scheduled successfully!' };
    } catch (error) {
      this.logger.error('Failed to schedule announcement:', error);
      return { success: false, message: 'Failed to schedule announcement. Please try again.' };
    }
  }

  async publishAnnouncement(announcementId: string): Promise<{ success: boolean; message: string }> {
    try {
      await this.redisDb.update<Announcement>(
        this.ANNOUNCEMENT,
        announcementId,
        {
          status: 'published',
          publishedAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        this.getAnnouncementOptions()
      );

      this.logger.log(`Published announcement ${announcementId}`);
      return { success: true, message: 'Announcement published successfully!' };
    } catch (error) {
      this.logger.error('Failed to publish announcement:', error);
      return { success: false, message: 'Failed to publish announcement. Please try again.' };
    }
  }

  // Delivery Management
  async recordDelivery(deliveryData: Omit<AnnouncementDelivery, 'id' | 'createdAt' | 'updatedAt'>): Promise<AnnouncementDelivery> {
    try {
      const delivery = await this.redisDb.create<AnnouncementDelivery>(
        this.ANNOUNCEMENT_DELIVERY,
        {
          ...deliveryData,
          deliveredAt: new Date().toISOString()
        },
        this.getDeliveryOptions()
      );
      
      return delivery;
    } catch (error) {
      this.logger.error('Failed to record delivery:', error);
      throw error;
    }
  }

  async getDeliveryStatus(announcementId: string): Promise<{
    total: number;
    successful: number;
    failed: number;
    channels: Array<{ channelId: string; isSuccessful: boolean; messageId?: string }>;
  }> {
    try {
      const deliveries = await this.redisDb.findByIndex<AnnouncementDelivery>(
        this.ANNOUNCEMENT_DELIVERY,
        'announcementId',
        announcementId
      );

      const successful = deliveries.filter((d: any) => d.isSuccessful).length;
      const failed = deliveries.length - successful;

      return {
        total: deliveries.length,
        successful,
        failed,
        channels: deliveries.map((d: any) => ({
          channelId: d.channelId,
          isSuccessful: d.isSuccessful,
          messageId: d.messageId
        }))
      };
    } catch (error) {
      this.logger.error('Failed to get delivery status:', error);
      throw error;
    }
  }

  // Subscription Management
  async createSubscription(subscriptionData: Omit<AnnouncementSubscription, 'id' | 'createdAt' | 'updatedAt'>): Promise<AnnouncementSubscription> {
    try {
      // Check if subscription already exists
      const userSubscriptions = await this.redisDb.findByIndex<AnnouncementSubscription>(
        this.ANNOUNCEMENT_SUBSCRIPTION,
        'userId',
        subscriptionData.userId
      );
      
      const existing = userSubscriptions.find(s => s.guildId === subscriptionData.guildId);

      if (existing) {
        // Update existing subscription
        const updated = await this.redisDb.update<AnnouncementSubscription>(
          this.ANNOUNCEMENT_SUBSCRIPTION,
          existing.id,
          {
            ...subscriptionData,
            updatedAt: new Date().toISOString()
          },
          this.getSubscriptionOptions()
        );
        
        return updated!;
      }

      const subscription = await this.redisDb.create<AnnouncementSubscription>(
        this.ANNOUNCEMENT_SUBSCRIPTION,
        {
          ...subscriptionData,
          subscribedTypes: subscriptionData.subscribedTypes || []
        },
        this.getSubscriptionOptions()
      );
      
      this.logger.log(`Created subscription for user ${subscriptionData.userId}`);
      return subscription;
    } catch (error) {
      this.logger.error('Failed to create subscription:', error);
      throw error;
    }
  }

  async getUserSubscription(userId: string, guildId: string): Promise<AnnouncementSubscription | null> {
    try {
      const userSubscriptions = await this.redisDb.findByIndex<AnnouncementSubscription>(
        this.ANNOUNCEMENT_SUBSCRIPTION,
        'userId',
        userId
      );
      
      const subscription = userSubscriptions.find(s => s.guildId === guildId);
      return subscription || null;
    } catch (error) {
      this.logger.error('Failed to get user subscription:', error);
      throw error;
    }
  }

  // Reaction Management
  async addReaction(reactionData: Omit<AnnouncementReaction, 'id' | 'createdAt' | 'updatedAt'>): Promise<{ success: boolean; message: string }> {
    try {
      // Check if reaction already exists
      const announcementReactions = await this.redisDb.findByIndex<AnnouncementReaction>(
        this.ANNOUNCEMENT_REACTION,
        'announcementId',
        reactionData.announcementId
      );
      
      const existing = announcementReactions.find(r => 
        r.userId === reactionData.userId && r.emoji === reactionData.emoji
      );

      if (existing) {
        return { success: false, message: 'Reaction already exists!' };
      }

      await this.redisDb.create<AnnouncementReaction>(
        this.ANNOUNCEMENT_REACTION,
        reactionData,
        this.getReactionOptions()
      );

      // Update reaction count
      await this.redisDb.increment(
        this.ANNOUNCEMENT, 
        reactionData.announcementId, 
        'reactionCount', 
        1
      );

      return { success: true, message: 'Reaction added successfully!' };
    } catch (error) {
      this.logger.error('Failed to add reaction:', error);
      return { success: false, message: 'Failed to add reaction. Please try again.' };
    }
  }

  // Comment Management
  async addComment(commentData: Omit<AnnouncementComment, 'id' | 'createdAt' | 'updatedAt'>): Promise<AnnouncementComment> {
    try {
      const comment = await this.redisDb.create<AnnouncementComment>(
        this.ANNOUNCEMENT_COMMENT,
        {
          ...commentData,
          isDeleted: false
        },
        this.getCommentOptions()
      );

      // Update comment count
      await this.redisDb.increment(
        this.ANNOUNCEMENT, 
        commentData.announcementId, 
        'commentCount', 
        1
      );

      this.logger.log(`Added comment to announcement ${commentData.announcementId}`);
      return comment;
    } catch (error) {
      this.logger.error('Failed to add comment:', error);
      throw error;
    }
  }

  async getComments(announcementId: string, limit: number = 50): Promise<AnnouncementComment[]> {
    try {
      const allComments = await this.redisDb.findByIndex<AnnouncementComment>(
        this.ANNOUNCEMENT_COMMENT,
        'announcementId',
        announcementId
      );
      
      // Filter out deleted comments
      const activeComments = allComments.filter((c: any) => !c.isDeleted);
      
      // Sort by creation date
      activeComments.sort((a, b) => 
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
      );
      
      return activeComments.slice(0, limit);
    } catch (error) {
      this.logger.error('Failed to get comments:', error);
      throw error;
    }
  }

  // Acknowledgment Management
  async acknowledgeAnnouncement(
    announcementId: string,
    userId: string,
    channelId?: string,
    messageId?: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Check if already acknowledged
      const acknowledgments = await this.redisDb.findByIndex<AnnouncementAcknowledgment>(
        this.ANNOUNCEMENT_ACKNOWLEDGMENT,
        'announcementId',
        announcementId
      );
      
      const existing = acknowledgments.find(a => a.userId === userId);
      if (existing) {
        return { success: false, message: 'Announcement already acknowledged!' };
      }

      await this.redisDb.create<AnnouncementAcknowledgment>(
        this.ANNOUNCEMENT_ACKNOWLEDGMENT,
        {
          announcementId,
          userId,
          channelId,
          messageId,
          acknowledgedAt: new Date().toISOString()
        },
        this.getAcknowledgmentOptions()
      );

      // Update acknowledgment count
      await this.redisDb.increment(
        this.ANNOUNCEMENT, 
        announcementId, 
        'acknowledgmentCount', 
        1
      );

      return { success: true, message: 'Announcement acknowledged successfully!' };
    } catch (error) {
      this.logger.error('Failed to acknowledge announcement:', error);
      return { success: false, message: 'Failed to acknowledge announcement. Please try again.' };
    }
  }

  // Template Management
  async createTemplate(templateData: Omit<AnnouncementTemplate, 'id' | 'createdAt' | 'updatedAt'>): Promise<AnnouncementTemplate> {
    try {
      const template = await this.redisDb.create<AnnouncementTemplate>(
        this.ANNOUNCEMENT_TEMPLATE,
        {
          ...templateData,
          usageCount: 0,
          tags: templateData.tags || []
        },
        this.getTemplateOptions()
      );
      
      this.logger.log(`Created announcement template: ${template.name} (${template.id})`);
      return template;
    } catch (error) {
      this.logger.error('Failed to create template:', error);
      throw error;
    }
  }

  async getTemplates(guildId: string, creatorId?: string): Promise<AnnouncementTemplate[]> {
    try {
      let templates = await this.redisDb.findByIndex<AnnouncementTemplate>(
        this.ANNOUNCEMENT_TEMPLATE,
        'guildId',
        guildId,
        { limit: 100 }
      );
      
      // Filter by creator if specified
      if (creatorId) {
        templates = templates.filter((t: any) => t.creatorId === creatorId);
      }
      
      // Filter for active templates only
      templates = templates.filter((t: any) => t.isActive);
      
      // Sort by usage count
      templates.sort((a, b) => b.usageCount - a.usageCount);

      return templates;
    } catch (error) {
      this.logger.error('Failed to get templates:', error);
      throw error;
    }
  }

  // Analytics
  async getAnnouncementMetrics(guildId: string, days: number = 30): Promise<AnnouncementMetrics> {
    try {
      const dateFrom = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
      
      // Get all guild announcements
      const allAnnouncements = await this.redisDb.findByIndex<Announcement>(
        this.ANNOUNCEMENT,
        'guildId',
        guildId,
        { limit: 1000 }
      );
      
      // Filter by date range
      const recentAnnouncements = allAnnouncements.filter((a: any) => 
        new Date(a.createdAt) >= dateFrom
      );
      
      const totalAnnouncements = recentAnnouncements.length;
      const activeAnnouncements = recentAnnouncements.filter((a: any) => a.status === 'published').length;
      const scheduledAnnouncements = recentAnnouncements.filter((a: any) => a.status === 'scheduled').length;
      
      const totalDeliveries = recentAnnouncements.reduce((sum, a) => sum + a.totalRecipients, 0);
      const totalEngagements = recentAnnouncements.reduce((sum, a) => 
        sum + a.reactionCount + a.commentCount, 0
      );
      
      // Count by type
      const typeCount: Record<string, number> = {};
      recentAnnouncements.forEach(a => {
        typeCount[a.type] = (typeCount[a.type] || 0) + 1;
      });
      
      const popularTypes = Object.entries(typeCount)
        .map(([type, count]) => ({ type, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 5);
      
      const averageEngagementRate = totalDeliveries > 0 
        ? Math.round((totalEngagements / totalDeliveries) * 100 * 100) / 100
        : 0;

      return {
        totalAnnouncements,
        activeAnnouncements,
        scheduledAnnouncements,
        totalDeliveries,
        totalEngagements,
        averageEngagementRate,
        popularTypes,
        recentActivity: [] // TODO: Implement daily activity tracking
      };
    } catch (error) {
      this.logger.error('Failed to get announcement metrics:', error);
      throw error;
    }
  }

  // User Management
  async ensureUser(discordId: string, username: string): Promise<void> {
    try {
      const existingUsers = await this.redisDb.findByIndex<any>(
        this.USER,
        'discordId',
        discordId
      );

      if (existingUsers.length === 0) {
        await this.redisDb.create<any>(
          this.USER,
          {
            discordId,
            username,
            isActive: true,
            lastActivityAt: new Date().toISOString(),
            preferences: {},
            profile: {}
          },
          this.getUserOptions()
        );
        
        this.logger.log(`Created new user: ${username} (${discordId})`);
      } else if (existingUsers[0].username !== username) {
        await this.redisDb.update<any>(
          this.USER,
          existingUsers[0].id,
          { 
            username,
            lastActivityAt: new Date().toISOString()
          },
          this.getUserOptions()
        );
      }
    } catch (error) {
      this.logger.error('Failed to ensure user:', error);
      throw error;
    }
  }
}