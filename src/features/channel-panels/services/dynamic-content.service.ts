import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';

import { firstValueFrom } from 'rxjs';
import { DatabaseService } from '@/core/database';
import { eq, and, or, desc, asc, count, sql } from 'drizzle-orm';
import {
  dynamicContentSources,
  dynamicContentCache,
  panelContentMappings,
  contentFreshnessTracking,
  DynamicContentSource,
  NewDynamicContentSource,
  DynamicContentCache,
  NewDynamicContentCache,
  PanelContentMapping,
  ContentFreshnessTracking,
} from '@/core/database';

interface ContentRefreshResult {
  success: boolean;
  data?: any;
  error?: string;
  cached: boolean;
  size: number;
  checksum: string;
}

interface RateLimitState {
  requests: number;
  windowStart: number;
  retryAfter?: number;
}

@Injectable()
export class DynamicContentService {
  private readonly logger = new Logger(DynamicContentService.name);
  private readonly rateLimitStates = new Map<string, RateLimitState>();
  private readonly refreshQueue = new Set<string>();
  private readonly memoryCache = new Map<string, { data: any; expires: number }>();

  constructor(
    private readonly databaseService: DatabaseService,
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Register a new dynamic content source
   */
  async registerContentSource(source: NewDynamicContentSource): Promise<string | null> {
    try {
      const db = this.databaseService.getDb();
      
      const created = await db
        .insert(dynamicContentSources)
        .values(source)
        .returning();

      const createdSource = created[0];
      this.logger.log(`Registered content source: ${createdSource.sourceId}`);
      
      return createdSource.sourceId;
    } catch (error) {
      this.logger.error(`Failed to register content source: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Get fresh content for a panel
   */
  async getContentForPanel(panelId: string): Promise<any[]> {
    try {
      const db = this.databaseService.getDb();
      
      // Get all content mappings for this panel
      const mappings = await db
        .select()
        .from(panelContentMappings)
        .where(
          and(
            eq(panelContentMappings.panelId, panelId),
            eq(panelContentMappings.isActive, true)
          )
        )
        .orderBy(desc(panelContentMappings.priority));

      const contentResults = [];

      for (const mapping of mappings) {
        const content = await this.getContentFromSource(mapping.sourceId);
        if (content) {
          const transformedContent = this.transformContent(content, mapping.mapping);
          contentResults.push({
            sourceId: mapping.sourceId,
            content: transformedContent,
            mapping: mapping.mapping,
          });
        }
      }

      return contentResults;
    } catch (error) {
      this.logger.error(`Failed to get content for panel ${panelId}: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * Get content from a specific source with caching
   */
  async getContentFromSource(sourceId: string): Promise<any> {
    try {
      // Check memory cache first
      const memoryCached = this.memoryCache.get(sourceId);
      if (memoryCached && Date.now() < memoryCached.expires) {
        return memoryCached.data;
      }

      const db = this.databaseService.getDb();
      
      // Get source configuration
      const sources = await db
        .select()
        .from(dynamicContentSources)
        .where(
          and(
            eq(dynamicContentSources.sourceId, sourceId),
            eq(dynamicContentSources.isActive, true)
          )
        )
        .limit(1);

      if (sources.length === 0) {
        this.logger.warn(`Content source not found: ${sourceId}`);
        return null;
      }

      const source = sources[0];

      // Check database cache
      const cached = await this.getCachedContent(sourceId);
      if (cached && !cached.isStale) {
        // Update memory cache
        this.memoryCache.set(sourceId, {
          data: cached.data,
          expires: cached.expiresAt.getTime(),
        });
        return cached.data;
      }

      // Fetch fresh content
      const freshContent = await this.fetchFreshContent(source);
      if (freshContent.success) {
        // Update cache
        await this.updateCache(sourceId, freshContent.data, source);
        
        // Update memory cache
        const cacheConfig = source.config.caching;
        if (cacheConfig) {
          this.memoryCache.set(sourceId, {
            data: freshContent.data,
            expires: Date.now() + (cacheConfig.ttl * 1000),
          });
        }

        return freshContent.data;
      }

      // Return stale cache if fresh fetch failed
      if (cached) {
        this.logger.warn(`Using stale cache for source ${sourceId} due to fetch failure`);
        return cached.data;
      }

      return null;
    } catch (error) {
      this.logger.error(`Failed to get content from source ${sourceId}: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Fetch fresh content from external source
   */
  private async fetchFreshContent(source: DynamicContentSource): Promise<ContentRefreshResult> {
    try {
      // Check rate limits
      if (!this.checkRateLimit(source.sourceId, source.config.rateLimit)) {
        return {
          success: false,
          error: 'Rate limit exceeded',
          cached: false,
          size: 0,
          checksum: '',
        };
      }

      const config = source.config;
      let data: any;

      switch (source.type) {
        case 'api':
          data = await this.fetchFromAPI(config);
          break;
        case 'rss':
          data = await this.fetchFromRSS(config.url!);
          break;
        case 'database':
          data = await this.fetchFromDatabase(config);
          break;
        default:
          throw new Error(`Unsupported source type: ${source.type}`);
      }

      // Transform data if mapping is provided
      if (config.transformation) {
        data = this.applyTransformation(data, config.transformation);
      }

      // Apply filters
      if (config.transformation?.filters) {
        data = this.applyFilters(data, config.transformation.filters);
      }

      const serialized = JSON.stringify(data);
      const checksum = this.generateChecksum(serialized);

      // Update rate limit state
      this.updateRateLimit(source.sourceId);

      return {
        success: true,
        data,
        cached: false,
        size: serialized.length,
        checksum,
      };
    } catch (error) {
      this.logger.error(`Failed to fetch content from ${source.sourceId}: ${error.message}`, error.stack);
      
      // Update error count
      await this.updateErrorCount(source.sourceId, error.message);

      return {
        success: false,
        error: error.message,
        cached: false,
        size: 0,
        checksum: '',
      };
    }
  }

  /**
   * Fetch data from API
   */
  private async fetchFromAPI(config: any): Promise<any> {
    const response = await firstValueFrom(
      this.httpService.request({
        url: config.url,
        method: config.method || 'GET',
        headers: config.headers,
        params: config.params,
        auth: config.authentication ? this.buildAuth(config.authentication) : undefined,
      })
    );

    return response.data;
  }

  /**
   * Fetch data from RSS feed
   */
  private async fetchFromRSS(url: string): Promise<any> {
    // For now, treat RSS as a simple HTTP GET
    // In a real implementation, you'd use an RSS parser
    const response = await firstValueFrom(
      this.httpService.get(url)
    );

    return response.data;
  }

  /**
   * Fetch data from database
   */
  private async fetchFromDatabase(config: any): Promise<any> {
    // This would execute a database query based on the config
    // Implementation depends on your specific database schema
    throw new Error('Database source not yet implemented');
  }

  /**
   * Transform content based on mapping configuration
   */
  private transformContent(data: any, mapping: any): any {
    if (!mapping || !data) return data;

    const { contentType, fieldMappings, conditions, template, maxItems, sortBy } = mapping;

    let processedData = Array.isArray(data) ? data : [data];

    // Apply conditions
    if (conditions && conditions.length > 0) {
      processedData = processedData.filter((item: any) => {
        return conditions.every((condition: any) => {
          const fieldValue = this.getNestedValue(item, condition.field);
          return this.evaluateCondition(fieldValue, condition.operator, condition.value);
        });
      });
    }

    // Apply field mappings
    if (fieldMappings && fieldMappings.length > 0) {
      processedData = processedData.map((item: any) => {
        const mappedItem: any = {};
        fieldMappings.forEach((mapping: any) => {
          const sourceValue = this.getNestedValue(item, mapping.sourceField);
          mappedItem[mapping.targetField] = this.applyTransformer(
            sourceValue,
            mapping.transformer,
            mapping.params
          );
        });
        return mappedItem;
      });
    }

    // Apply sorting
    if (sortBy) {
      processedData.sort((a, b) => {
        const aValue = this.getNestedValue(a, sortBy.field);
        const bValue = this.getNestedValue(b, sortBy.field);
        
        if (sortBy.order === 'desc') {
          return bValue > aValue ? 1 : bValue < aValue ? -1 : 0;
        }
        return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
      });
    }

    // Limit items
    if (maxItems && processedData.length > maxItems) {
      processedData = processedData.slice(0, maxItems);
    }

    // Apply template if provided
    if (template) {
      processedData = processedData.map((item: any) => ({
        ...item,
        rendered: this.applyTemplate(template, item),
      }));
    }

    return processedData;
  }

  /**
   * Update content cache
   */
  private async updateCache(
    sourceId: string,
    data: any,
    source: DynamicContentSource
  ): Promise<void> {
    try {
      const db = this.databaseService.getDb();
      const serialized = JSON.stringify(data);
      const checksum = this.generateChecksum(serialized);
      const cacheConfig = source.config.caching;
      
      if (!cacheConfig) return;

      const cacheKey = cacheConfig.key || sourceId;
      const expiresAt = new Date(Date.now() + (cacheConfig.ttl * 1000));

      const cacheData = {
        cacheKey,
        sourceId,
        data,
        metadata: {
          size: serialized.length,
          checksum,
          contentType: 'application/json',
          lastModified: new Date().toISOString(),
        },
        expiresAt,
        accessCount: { total: 1, thisHour: 1, thisDay: 1, lastAccessed: new Date().toISOString() },
        tags: [],
        isStale: false,
      } as any;

      // Upsert cache entry
      await db
        .insert(dynamicContentCache)
        .values(cacheData)
        .onConflictDoUpdate({
          target: dynamicContentCache.cacheKey,
          set: {
            data: cacheData.data,
            metadata: cacheData.metadata,
            expiresAt: cacheData.expiresAt,
            accessCount: cacheData.accessCount,
            isStale: false,
            updatedAt: new Date(),
          } as any,
        });

      // Update source last fetched time
      await db
        .update(dynamicContentSources)
        .set({
          lastFetched: new Date(),
          lastSuccessful: new Date(),
          updatedAt: new Date(),
        } as any)
        .where(eq(dynamicContentSources.sourceId, sourceId));

    } catch (error) {
      this.logger.error(`Failed to update cache for ${sourceId}: ${error.message}`, error.stack);
    }
  }

  /**
   * Get cached content
   */
  private async getCachedContent(sourceId: string): Promise<DynamicContentCache | null> {
    try {
      const db = this.databaseService.getDb();
      
      const cached = await db
        .select()
        .from(dynamicContentCache)
        .where(eq(dynamicContentCache.sourceId, sourceId))
        .orderBy(desc(dynamicContentCache.createdAt))
        .limit(1);

      if (cached.length === 0) return null;

      const cacheEntry = cached[0];

      // Update access count
      const currentAccess = cacheEntry.accessCount || { total: 0, thisHour: 0, thisDay: 0, lastAccessed: new Date().toISOString() };
      await db
        .update(dynamicContentCache)
        .set({
          accessCount: {
            ...currentAccess,
            total: currentAccess.total + 1,
            thisHour: currentAccess.thisHour + 1,
            thisDay: currentAccess.thisDay + 1,
            lastAccessed: new Date().toISOString(),
          },
          updatedAt: new Date(),
        } as any)
        .where(eq(dynamicContentCache.id, cacheEntry.id));

      return cacheEntry;
    } catch (error) {
      this.logger.error(`Failed to get cached content for ${sourceId}: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Check content freshness and mark stale if needed
   */
  async checkContentFreshness(sourceId: string): Promise<boolean> {
    try {
      const db = this.databaseService.getDb();
      
      // Get freshness tracking info
      const tracking = await db
        .select()
        .from(contentFreshnessTracking)
        .where(eq(contentFreshnessTracking.sourceId, sourceId))
        .limit(1);

      if (tracking.length === 0) {
        // No tracking info, assume content is fresh
        return true;
      }

      const track = tracking[0];
      const now = new Date();
      const timeSinceCheck = now.getTime() - track.lastChecked.getTime();
      
      // Use adaptive refresh based on change frequency
      const changeFreq = track.changeFrequency;
      const expectedInterval = changeFreq?.average || 3600000; // Default 1 hour

      if (timeSinceCheck > expectedInterval * 1.5) {
        // Mark content as potentially stale
        await db
          .update(dynamicContentCache)
          .set({ isStale: true } as any)
          .where(eq(dynamicContentCache.sourceId, sourceId));
        
        return false;
      }

      return true;
    } catch (error) {
      this.logger.error(`Failed to check content freshness for ${sourceId}: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Refresh all stale content sources
   */
  @Cron(CronExpression.EVERY_5_MINUTES)
  async refreshStaleContent() {
    try {
      const db = this.databaseService.getDb();
      
      // Get sources that need refresh
      const now = new Date();
      const staleSources = await db
        .select()
        .from(dynamicContentSources)
        .where(
          and(
            eq(dynamicContentSources.isActive, true),
            lt(dynamicContentSources.lastFetched, new Date(now.getTime() - 5 * 60 * 1000))
          )
        );

      for (const source of staleSources) {
        if (!this.refreshQueue.has(source.sourceId)) {
          this.refreshQueue.add(source.sourceId);
          
          // Refresh in background
          this.fetchFreshContent(source)
            .then(result => {
              if (result.success) {
                this.updateCache(source.sourceId, result.data, source);
              }
            })
            .finally(() => {
              this.refreshQueue.delete(source.sourceId);
            });
        }
      }

      this.logger.debug(`Initiated refresh for ${staleSources.length} stale content sources`);
    } catch (error) {
      this.logger.error(`Failed to refresh stale content: ${error.message}`, error.stack);
    }
  }

  /**
   * Helper methods
   */
  private checkRateLimit(sourceId: string, rateLimitConfig?: any): boolean {
    if (!rateLimitConfig) return true;

    const now = Date.now();
    const state = this.rateLimitStates.get(sourceId);
    
    if (!state) {
      this.rateLimitStates.set(sourceId, {
        requests: 1,
        windowStart: now,
      });
      return true;
    }

    // Check if we're in a new window
    if (now - state.windowStart >= rateLimitConfig.window * 1000) {
      state.requests = 1;
      state.windowStart = now;
      return true;
    }

    // Check if we've exceeded the limit
    if (state.requests >= rateLimitConfig.requests) {
      return false;
    }

    state.requests++;
    return true;
  }

  private updateRateLimit(sourceId: string): void {
    const state = this.rateLimitStates.get(sourceId);
    if (state) {
      state.requests++;
    }
  }

  private generateChecksum(data: string): string {
    // Simple hash function for checksum
    let hash = 0;
    for (let i = 0; i < data.length; i++) {
      const char = data.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString();
  }

  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  private evaluateCondition(value: any, operator: string, target: any): boolean {
    switch (operator) {
      case 'equals': return value === target;
      case 'contains': return String(value).includes(String(target));
      case 'greater': return Number(value) > Number(target);
      case 'less': return Number(value) < Number(target);
      case 'regex': return new RegExp(target).test(String(value));
      default: return false;
    }
  }

  private applyTransformer(value: any, transformer?: string, params?: any): any {
    if (!transformer) return value;

    switch (transformer) {
      case 'date':
        return new Date(value).toISOString();
      case 'currency':
        return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value);
      case 'percentage':
        return `${(value * 100).toFixed(2)}%`;
      case 'truncate':
        return String(value).slice(0, params?.length || 100);
      case 'uppercase':
        return String(value).toUpperCase();
      case 'lowercase':
        return String(value).toLowerCase();
      case 'markdown':
        return `**${value}**`; // Simple markdown formatting
      default:
        return value;
    }
  }

  private applyTemplate(template: string, data: any): string {
    return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return this.getNestedValue(data, key) || match;
    });
  }

  private applyTransformation(data: any, transformation: any): any {
    if (transformation.jsonPath) {
      // Apply JSONPath extraction (simplified)
      return this.getNestedValue(data, transformation.jsonPath);
    }
    return data;
  }

  private applyFilters(data: any[], filters: any[]): any[] {
    return data.filter((item: any) => {
      return filters.every(filter => {
        const value = this.getNestedValue(item, filter.field);
        return this.evaluateCondition(value, filter.operator, filter.value);
      });
    });
  }

  private buildAuth(authConfig: any): any {
    switch (authConfig.type) {
      case 'bearer':
        return { Authorization: `Bearer ${authConfig.credentials.token}` };
      case 'api-key':
        return { [authConfig.credentials.header || 'X-API-Key']: authConfig.credentials.key };
      case 'basic':
        return {
          username: authConfig.credentials.username,
          password: authConfig.credentials.password,
        };
      default:
        return undefined;
    }
  }

  private async updateErrorCount(sourceId: string, errorMessage: string): Promise<void> {
    try {
      const db = this.databaseService.getDb();
      
      await db
        .update(dynamicContentSources)
        .set({
          errorCount: {
            consecutive: 1, // This would need proper increment logic
            total: 1,
            lastError: {
              message: errorMessage,
              timestamp: new Date().toISOString(),
            },
          },
          updatedAt: new Date(),
        } as any)
        .where(eq(dynamicContentSources.sourceId, sourceId));
    } catch (error) {
      this.logger.error(`Failed to update error count: ${error.message}`, error.stack);
    }
  }
}