# Clean Architecture Discord Panel System

A production-ready Discord bot panel system built with clean architecture principles, featuring proper separation of concerns, dependency injection, and extensible design patterns.

## 🏗️ Architecture Overview

This system implements clean architecture with clear boundaries between layers:

```
┌─────────────────────────────────────────────────────────────┐
│                    PRESENTATION LAYER                       │
│  ┌─────────────────────┐  ┌─────────────────────────────────┐ │
│  │ Discord Interaction │  │    Panel Factories              │ │
│  │     Handlers        │  │ (Create Panel Instances)        │ │
│  └─────────────────────┘  └─────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                   │
┌─────────────────────────────────────────────────────────────┐
│                   ORCHESTRATION LAYER                       │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │           Panel Orchestrator                            │ │
│  │  (Coordinates all components & handles routing)        │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                   │
┌─────────────────────────────────────────────────────────────┐
│                    APPLICATION LAYER                        │
│  ┌─────────────────┐  ┌──────────────┐  ┌─────────────────┐ │
│  │ Action Handlers │  │    State     │  │ Content         │ │
│  │ (Process User   │  │  Management  │  │ Providers       │ │
│  │  Interactions)  │  │   (User      │  │ (Supply Real    │ │
│  │                 │  │  Sessions)   │  │    Data)        │ │
│  └─────────────────┘  └──────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                   │
┌─────────────────────────────────────────────────────────────┐
│                 INFRASTRUCTURE LAYER                        │
│  ┌─────────────────┐  ┌──────────────┐  ┌─────────────────┐ │
│  │    Database     │  │    Cache     │  │   Discord API   │ │
│  │  (TypeORM)      │  │   (Memory/   │  │  (discord.js)   │ │
│  │                 │  │   Redis)     │  │                 │ │
│  └─────────────────┘  └──────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🎯 Core Design Principles

### Single Responsibility Principle
- **Action Handlers**: Handle specific panel interactions only
- **Content Providers**: Supply data for specific content types only  
- **State Manager**: Manages user session state only
- **Panel Factory**: Creates panel instances only
- **Orchestrator**: Coordinates components only

### Dependency Injection
- All components are injected through NestJS DI container
- Easy to mock and unit test in isolation
- Flexible configuration through providers

### Interface Segregation
- Small, focused interfaces (`IActionHandler`, `IContentProvider`, etc.)  
- Components only depend on what they actually use
- Clear contracts between layers

### Open/Closed Principle
- Easy to add new panel types without modifying existing code
- Extensible through new action handlers and content providers
- Closed for modification, open for extension

## 📁 Project Structure

```
src/features/channel-panels/
├── core/                           # Core business logic
│   ├── interfaces/                 # Architecture contracts
│   │   └── panel-contracts.interface.ts
│   ├── actions/                    # Action handlers
│   │   ├── base-action-handler.ts
│   │   ├── announcement-action-handler.ts
│   │   ├── community-hub-action-handler.ts
│   │   └── ai-coding-action-handler.ts
│   ├── content/                    # Content providers
│   │   ├── base-content-provider.ts
│   │   └── announcement-content-provider.ts
│   ├── state/                      # State management
│   │   └── panel-state-manager.ts
│   ├── orchestration/              # Coordination layer
│   │   └── panel-orchestrator.ts
│   └── factories/                  # Panel creation
│       └── default-panel-factory.ts
├── handlers/                       # Discord interaction handlers
│   └── channel-panel-interaction.handler.ts
├── examples/                       # Usage examples
│   └── clean-architecture-usage.example.ts
├── clean-panel-system.module.ts   # Main DI module
└── README.md                       # This file
```

## 🚀 Quick Start

### 1. Import the Module

```typescript
import { CleanPanelSystemModule } from './features/channel-panels/clean-panel-system.module';

@Module({
  imports: [
    CleanPanelSystemModule.forRoot({
      enableAnalytics: true,
      cacheStrategy: 'memory',
      stateCleanupInterval: 3600000, // 1 hour
      maxStatesPerUser: 10
    })
  ]
})
export class AppModule {}
```

### 2. Deploy Panels to a Channel  

```typescript
@Injectable()
export class MyService {
  constructor(
    private readonly panelOrchestrator: IPanelOrchestrator
  ) {}

  async deployPanels(channelId: string, guildId: string) {
    const config: PanelConfiguration = {
      panelType: 'announcement',
      panelId: `announcement-${channelId}`,
      channelId,
      guildId,
      title: '📢 Announcements',
      description: 'Stay updated with latest news',
      isEnabled: true,
      customSettings: {
        allowSubscriptions: true,
        defaultCategories: ['general', 'updates']
      }
    };

    await this.panelOrchestrator.deployPanel(config);
  }
}
```

### 3. Handle Interactions Automatically  

The system automatically handles Discord interactions through the `ChannelPanelInteractionHandler`. No additional setup required - just ensure your custom IDs follow the pattern: `panelType_actionId|data`

## 📋 Supported Panel Types

### 1. Announcement Center (`announcement`)
- **Actions**: Subscribe, View History, Configure Settings, Get Help
- **Features**: Subscription management, notification preferences, announcement history
- **Custom ID Pattern**: `announcement_subscribe`, `announcement_view_history`

### 2. Community Hub (`community`) 
- **Actions**: View Guidelines, Browse Events, View Leaderboard, Submit Feedback
- **Features**: Event participation, community rankings, feedback system
- **Custom ID Pattern**: `community_view_guidelines`, `community_browse_events`

### 3. AI Coding & Development (`ai-coding`)
- **Actions**: Browse Projects, View Resources, Get Help, Showcase Project  
- **Features**: Project showcase, resource library, coding help, collaboration
- **Custom ID Pattern**: `ai_coding_browse_projects`, `ai_coding_get_help`

### 4. Premium Community (`premium`)
- **Actions**: View Benefits, Exclusive Events, Priority Support, Resources
- **Features**: Premium member benefits, exclusive content access
- **Custom ID Pattern**: `premium_view_benefits`, `premium_exclusive_events`

### 5. Goal Tracking & Achievement (`goal-tracking`)
- **Actions**: Set Goals, Track Progress, Find Accountability, Celebrate Wins
- **Features**: Goal management, progress tracking, accountability partnerships  
- **Custom ID Pattern**: `goals_set_goals`, `goals_track_progress`

## 🔧 Extending the System

### Adding a New Panel Type

1. **Create Action Handler**:
```typescript
@Injectable()
export class CustomActionHandler extends BaseActionHandler {
  readonly handlerId = 'custom-handler';
  readonly supportedPanelTypes = ['custom'];
  readonly supportedActions = ['custom_action'];

  protected async executeAction(context: ActionContext): Promise<InteractionResult> {
    // Implementation
  }
}
```

2. **Create Content Provider**:
```typescript
@Injectable() 
export class CustomContentProvider extends BaseContentProvider<CustomData> {
  readonly providerId = 'custom-provider';
  readonly supportedContentTypes = ['custom-data'];

  protected async fetchContent(request: ContentRequest): Promise<ContentResponse<CustomData>> {
    // Implementation
  }
}
```

3. **Register in Module**:
```typescript
@Module({
  providers: [
    // ... existing providers
    CustomActionHandler,
    CustomContentProvider
  ]
})
export class CleanPanelSystemModule {}
```

4. **Add to Factory**: Update `DefaultPanelFactory.supportedPanelTypes` and add content generation method.

## 🧪 Testing

### Unit Testing Action Handlers
```typescript
describe('AnnouncementActionHandler', () => {
  let handler: AnnouncementActionHandler;
  let mockContentProvider: jest.Mocked<IContentProvider>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        AnnouncementActionHandler,
        { provide: 'CONTENT_PROVIDER', useValue: mockContentProvider }
      ]
    }).compile();

    handler = module.get<AnnouncementActionHandler>(AnnouncementActionHandler);
  });

  it('should handle subscribe action', async () => {
    const context = createMockActionContext('subscribe');
    const result = await handler.handleAction(context);
    
    expect(result.success).toBe(true);
    expect(result.renderData).toBeDefined();
  });
});
```

### Integration Testing
```typescript
describe('Panel System Integration', () => {
  let orchestrator: IPanelOrchestrator;
  
  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [CleanPanelSystemModule]
    }).compile();

    orchestrator = module.get<IPanelOrchestrator>('PANEL_ORCHESTRATOR');
  });

  it('should deploy and handle interactions', async () => {
    const config = createMockPanelConfig();
    await orchestrator.deployPanel(config);
    
    const mockInteraction = createMockInteraction();
    await orchestrator.handleInteraction(mockInteraction);
    
    // Assertions
  });
});
```

## 📊 Monitoring & Analytics

### Panel Status Monitoring
```typescript
const status = await panelOrchestrator.getPanelStatus('announcement-123');
console.log(`Panel health: ${status.health}`);
console.log(`Active users: ${status.activeUsers}`);
console.log(`Total interactions: ${status.totalInteractions}`);
```

### Cache Performance
```typescript
const cacheStats = stateManager.getCacheStats();
console.log(`Cache hit rate: ${cacheStats.cacheHitRate}`);
console.log(`Total cached states: ${cacheStats.totalCachedStates}`);
```

### Content Provider Statistics  
```typescript
const providerStats = contentProvider.getCacheStats();
console.log(`Expired entries: ${providerStats.expiredEntries}`);
console.log(`Oldest entry: ${providerStats.oldestEntry}`);
```

## 🔒 Security & Best Practices

### Input Validation
- All user inputs are sanitized in `BaseActionHandler.sanitizeInput()`
- Discord markdown characters are stripped to prevent injection
- Maximum input lengths are enforced

### Permission Checking
- Actions can require specific permissions via `PanelAction.requiresPermission`
- User permissions are checked in `BaseActionHandler.validateAction()`
- Admin-only actions are properly protected

### Error Handling
- All errors are properly logged with context
- User-friendly error messages are returned to Discord
- System errors don't expose internal details

### Rate Limiting
- Action cooldowns prevent spam via `PanelAction.cooldownSeconds`
- State cleanup prevents memory leaks
- Cache limits prevent unbounded growth

## 🚀 Production Deployment

### Database Setup
1. Uncomment TypeORM entities in module imports
2. Create database migrations for panel state and configuration tables  
3. Configure database connection in your main app module

### Redis Caching (Optional)
1. Implement `persistContent()` in content providers to use Redis
2. Update cache strategy to 'redis' or 'hybrid'
3. Configure Redis connection details

### Environment Configuration
```typescript
CleanPanelSystemModule.forRoot({
  enableAnalytics: process.env.NODE_ENV === 'production',
  cacheStrategy: process.env.CACHE_STRATEGY as 'memory' | 'redis',
  stateCleanupInterval: parseInt(process.env.CLEANUP_INTERVAL || '3600000'),
  maxStatesPerUser: parseInt(process.env.MAX_STATES_PER_USER || '10')
})
```

## 🤝 Contributing

1. Follow the established architecture patterns
2. Implement proper interfaces and contracts
3. Add comprehensive unit tests
4. Update this README with new features
5. Ensure all new code follows clean architecture principles

## 📄 License

This panel system is part of the EnergeX Discord bot project.

---

**Built with Clean Architecture principles for maximum maintainability, testability, and extensibility.**