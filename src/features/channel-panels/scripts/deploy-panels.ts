/**
 * Panel Deployment Script
 * 
 * This script can be used to deploy channel panels to Discord channels.
 * It should be executed after the bot is running and connected to Discord.
 */

import { NestFactory } from '@nestjs/core';
import { AppModule } from '../../../app.module';
import { EnhancedChannelPanelOrchestratorService } from '../enhanced-channel-panel-orchestrator.service';

async function deployPanels() {
  console.log('🚀 Starting panel deployment...');
  
  try {
    // Create NestJS application context
    const app = await NestFactory.createApplicationContext(AppModule);
    
    // Get the orchestrator service
    const orchestrator = app.get(EnhancedChannelPanelOrchestratorService);
    
    // Wait for Discord client to be ready
    console.log('⏳ Waiting for Discord client to be ready...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Check orchestrator health
    const health = await orchestrator.getHealthStatus();
    console.log('🏥 Orchestrator health:', health);
    
    if (health.status === 'unhealthy') {
      console.error('❌ Orchestrator is unhealthy, aborting deployment');
      console.error('Errors:', health.errors);
      return;
    }
    
    // Channel IDs for deployment
    const channelIds = [
      // ANNOUNCEMENTS
      '1396352345046257665', // 📢-announcements
      '1396352364843241592', // 📰-news
      '1394499361454555207', // 📋-rules
      
      // COMMUNITY
      '1396529696442810390', // welcome
      '1396529869730353283', // introductions
      '1396352417976553553', // 🖼-media
      '1396530178079064154', // links-dump
      '1396536876801720330', // 💎-premium-chat
      
      // AI MASTERY
      '1396594755064041482', // 🛠-ai-tools
      '1396594735988084817', // 🎓-ai-tutorials
      '1396530075054116934', // ai-news
      '1396529967935787090', // ai-coding
      '1396594777985777725', // ⚙️-automation
      
      // WEALTH CREATION
      '1396594819224309852', // 💵-money-strategies
      '1396594859330113586', // 🏆-success-stories
      '1396594839298244689', // 🚀-entrepreneurship
      '1396537062202806274', // 💳-subscriptions
      
      // PERSONAL GROWTH
      '1396594969015873616', // 🧠-mindset-coaching
      '1396594948434006046', // 🎯-goal-tracking
      '1396594928846413844', // ⚡-productivity
      
      // NETWORKING & BUSINESS
      '1396595067150016532', // 💼-business-talks
      '1396595103518289952', // ⚔️-war-room
      '1396595124535160932', // 🤝-networking
    ];
    
    console.log(`📋 Deploying panels to ${channelIds.length} channels...`);
    
    // Deploy panels to all channels
    const result = await orchestrator.deployPanelsToChannels(channelIds);
    
    console.log('✅ Deployment completed!');
    console.log(`   Successful: ${result.successful.length}`);
    console.log(`   Failed: ${result.failed.length}`);
    
    if (result.failed.length > 0) {
      console.log('❌ Failed deployments:', result.failed);
    }
    
    // Show deployment statistics  
    const stats = orchestrator.getDeploymentStats();
    console.log('📊 Deployment Statistics:');
    console.log(`   Total deployments: ${stats.totalDeployments}`);
    console.log(`   Active deployments: ${stats.activeDeployments}`);
    console.log('   Panel distribution:', stats.panelStats);
    
    await app.close();
    console.log('🏁 Panel deployment script completed');
    
  } catch (error) {
    console.error('❌ Panel deployment failed:', error);
    process.exit(1);
  }
}

// Export for use as a module
export { deployPanels };

// Run directly if this file is executed
if (require.main === module) {
  deployPanels();
}