import { Modu<PERSON> } from '@nestjs/common';
import { AIAutomationService } from './ai-automation.service';
import { AutoModerationService } from './auto-moderation.service';
import { SmartNotificationService } from './smart-notification.service';
import { DatabaseModule } from '../../core/database/database.module';
import { DiscordModule } from '../../discord/discord.module';
import { AgentsModule } from '../../agents/agents.module';

@Module({
  imports: [DatabaseModule, DiscordModule, AgentsModule],
  providers: [
    AIAutomationService,
    AutoModerationService,
    SmartNotificationService,
  ],
  exports: [
    AIAutomationService,
    AutoModerationService,
    SmartNotificationService,
  ],
})
export class AIAutomationModule {}