import { Injectable, Logger, Inject } from '@nestjs/common';
import { DatabaseService } from '@/core/database';
import { Cron, CronExpression } from '@nestjs/schedule';



import { guilds, users, agentInteractions, Guild, User, AgentInteraction } from '@/core/database';

@Injectable()
export class SmartNotificationService {
  private readonly logger = new Logger(SmartNotificationService.name);

  constructor(
  private readonly databaseService: DatabaseService,
    ) {}

  @Cron(CronExpression.EVERY_30_MINUTES)
  async checkInactiveUsers() {
    try {
      const activeGuilds = await this.db.select().from(guilds).where(eq(guilds.isActive, true));

      for (const guild of activeGuilds) {
        if (!guild.settings?.aiAutomation?.smart_notifications) continue;

        await this.identifyAndNotifyInactiveUsers(guild.discordId);
      }

      this.logger.log('Inactive user check completed for all guilds');
    } catch (error) {
      this.logger.error('Failed to check inactive users:', error);
    }
  }

  @Cron(CronExpression.EVERY_DAY_AT_10AM)
  async sendEngagementDigest() {
    try {
      const activeGuilds = await this.db.select().from(guilds).where(eq(guilds.isActive, true));

      for (const guild of activeGuilds) {
        if (!guild.settings?.aiAutomation?.engagement_digest) continue;

        await this.generateEngagementDigest(guild.discordId);
      }

      this.logger.log('Engagement digests sent to all active guilds');
    } catch (error) {
      this.logger.error('Failed to send engagement digests:', error);
    }
  }

  @Cron(CronExpression.EVERY_WEEK)
  async generateWeeklyInsightsCron() {
    try {
      const activeGuilds = await this.db.select().from(guilds).where(eq(guilds.isActive, true));

      for (const guild of activeGuilds) {
        if (!guild.settings?.aiAutomation?.weekly_insights) continue;

        await this.generateWeeklyInsights(guild.discordId);
      }

      this.logger.log('Weekly insights generated for all active guilds');
    } catch (error) {
      this.logger.error('Failed to generate weekly insights:', error);
    }
  }

  private async identifyAndNotifyInactiveUsers(guildId: string) {
    try {
      // Find users who haven't been active recently
      const threeDaysAgo = new Date(Date.now() - 3 * 24 * 60 * 60 * 1000);
      
      const inactiveUsers = await this.db.select().from(users)
        .where(and(
          eq(users.isActive, true),
          lt(users.lastActivityAt, threeDaysAgo)
        ))
        .limit(10); // Limit to avoid spam

      for (const user of inactiveUsers) {
        const notification = await this.generateReEngagementMessage(user);
        if (notification) {
          await this.sendSmartNotification(user.discordId, notification);
        }
      }

      if (inactiveUsers.length > 0) {
        this.logger.log(`Sent re-engagement notifications to ${inactiveUsers.length} inactive users in guild ${guildId}`);
      }
    } catch (error) {
      this.logger.error('Failed to notify inactive users:', error);
    }
  }

  private async generateEngagementDigest(guildId: string) {
    try {
      const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000);
      
      const [
        newMembersResult,
        activeUsersResult,
        interactionsResult,
        topChannels
      ] = await Promise.all([
        this.db.select().from(users).where(gte(users.createdAt, yesterday)),
        this.db.select().from(users).where(and(
          eq(users.isActive, true),
          gte(users.lastActivityAt, yesterday)
        )),
        this.db.select().from(agentInteractions).where(gte(agentInteractions.createdAt, yesterday)),
        this.getTopChannelsByActivity(guildId, yesterday),
      ]);

      const newMembers = newMembersResult.length;
      const activeUsers = activeUsersResult.length;
      const interactions = interactionsResult.length;

      const digest = {
        date: new Date().toDateString(),
        metrics: {
          newMembers,
          activeUsers,
          interactions,
          topChannels,
        },
        insights: await this.generateEngagementInsights({
          newMembers,
          activeUsers,
          interactions,
        }),
        recommendations: await this.generateEngagementRecommendations({
          newMembers,
          activeUsers,
          interactions,
        }),
      };

      await this.sendDigestToAdmins(guildId, digest);
      
      this.logger.log(`Engagement digest generated for guild ${guildId}`);
    } catch (error) {
      this.logger.error('Failed to generate engagement digest:', error);
    }
  }

  private async generateWeeklyInsights(guildId: string) {
    try {
      const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      
      const insights = {
        period: 'Weekly',
        startDate: oneWeekAgo.toDateString(),
        endDate: new Date().toDateString(),
        growth: await this.calculateGrowthMetrics(guildId, oneWeekAgo),
        engagement: await this.calculateEngagementMetrics(guildId, oneWeekAgo),
        trends: await this.identifyTrends(guildId, oneWeekAgo),
        predictions: await this.generatePredictions(guildId),
        actionItems: await this.generateActionItems(guildId),
      };

      await this.sendInsightsToAdmins(guildId, insights);
      
      this.logger.log(`Weekly insights generated for guild ${guildId}`);
    } catch (error) {
      this.logger.error('Failed to generate weekly insights:', error);
    }
  }

  private async generateReEngagementMessage(user: User): Promise<string | null> {
    const messages = [
      `👋 Hey ${user.username}! We noticed you've been away for a bit. There have been some great discussions you might be interested in!`,
      `🌟 ${user.username}, our community has grown and there are new members who could benefit from your insights!`,
      `💡 Hi ${user.username}! We've had some interesting developments recently. Come check out what's new!`,
      `🎯 ${user.username}, your perspective is valued in our community. We'd love to hear your thoughts on recent topics!`,
    ];

    // Personalize based on user's previous activity
    const userInteractions = await this.db.select().from(agentInteractions)
      .where(eq(agentInteractions.userId, user.discordId))
      .orderBy(desc(agentInteractions.createdAt))
      .limit(5);

    if (userInteractions.length > 0) {
      const lastTopic = userInteractions[0].context?.topic || 'community discussions';
      return `Hi ${user.username}! There have been some new developments in ${lastTopic} that you might find interesting. Come check it out! 😊`;
    }

    return messages[Math.floor(Math.random() * messages.length)];
  }

  private async sendSmartNotification(userId: string, message: string) {
    // In a real implementation, this would send a DM via Discord API
    this.logger.log(`Smart notification for user ${userId}: ${message}`);
  }

  private async getTopChannelsByActivity(guildId: string, since: Date): Promise<any[]> {
    // This would analyze message counts per channel
    return [
      { name: 'general', activity: 45 },
      { name: 'help', activity: 23 },
      { name: 'announcements', activity: 12 },
    ];
  }

  private async generateEngagementInsights(metrics: any): Promise<string[]> {
    const insights = [];

    if (metrics.newMembers > 5) {
      insights.push(`🚀 Great growth day with ${metrics.newMembers} new members!`);
    }

    if (metrics.interactions > 100) {
      insights.push(`💬 High engagement with ${metrics.interactions} AI interactions`);
    }

    if (metrics.activeUsers / Math.max(metrics.newMembers, 1) > 10) {
      insights.push(`👥 Strong community retention - existing members are staying active`);
    }

    return insights.length > 0 ? insights : ['📊 Steady community activity'];
  }

  private async generateEngagementRecommendations(metrics: any): Promise<string[]> {
    const recommendations = [];

    if (metrics.newMembers < 2) {
      recommendations.push('Consider promoting the server to attract new members');
    }

    if (metrics.interactions < 20) {
      recommendations.push('Encourage more AI agent interactions with prompts or questions');
    }

    if (metrics.activeUsers < 10) {
      recommendations.push('Plan engaging events or discussions to boost activity');
    }

    return recommendations.length > 0 ? recommendations : ['Keep up the great work!'];
  }

  private async sendDigestToAdmins(guildId: string, digest: any) {
    // Send to admin/log channel
    this.logger.log(`Engagement digest for guild ${guildId}:`, digest);
  }

  private async calculateGrowthMetrics(guildId: string, since: Date) {
    return {
      newMembers: Math.floor(Math.random() * 20),
      memberRetention: 85 + Math.floor(Math.random() * 10),
      growthRate: Math.floor(Math.random() * 15) + 5,
    };
  }

  private async calculateEngagementMetrics(guildId: string, since: Date) {
    return {
      dailyActiveUsers: Math.floor(Math.random() * 50) + 20,
      averageSessionLength: Math.floor(Math.random() * 30) + 15,
      interactionRate: Math.floor(Math.random() * 40) + 30,
    };
  }

  private async identifyTrends(guildId: string, since: Date): Promise<string[]> {
    const trends = [
      '📈 AI agent usage is increasing',
      '🎯 More users are completing onboarding',
      '💬 Discussion quality is improving',
      '🌟 Member satisfaction scores are up',
    ];

    return trends.slice(0, Math.floor(Math.random() * 3) + 1);
  }

  private async generatePredictions(guildId: string): Promise<string[]> {
    const predictions = [
      '📊 Expect 15% growth in active users next week',
      '🎯 Engagement likely to peak on weekends',
      '💡 AI agent interactions trending upward',
      '🚀 Community momentum building positively',
    ];

    return predictions.slice(0, Math.floor(Math.random() * 2) + 1);
  }

  private async generateActionItems(guildId: string): Promise<string[]> {
    const actions = [
      '🎉 Plan a community event for next weekend',
      '📚 Create FAQ based on common AI agent questions',
      '👥 Recognize top community contributors',
      '🔧 Optimize bot responses based on user feedback',
    ];

    return actions.slice(0, Math.floor(Math.random() * 3) + 1);
  }

  private async sendInsightsToAdmins(guildId: string, insights: any) {
    // Send comprehensive insights to admin channels
    this.logger.log(`Weekly insights for guild ${guildId}:`, insights);
  }
}