import { Injectable, Logger } from '@nestjs/common';
import { User as DiscordUser, Message } from 'discord.js';
import { Context, On, Options, SlashCommand, SlashCommandContext, UserOption } from 'necord';
import { RedisDatabaseService } from '../../core/database/redis-database.service';
import { ChannelFilterService } from '../../core/services/channel-filter.service';

class LevelDto {
  @UserOption({ name: 'user', description: 'User to check level for', required: false })
  user?: DiscordUser;
}

@Injectable()
export class LevelingService {
  private readonly logger = new Logger(LevelingService.name);
  private readonly xpCooldowns = new Map<string, number>();

  constructor(
    private readonly redisDatabaseService: RedisDatabaseService,
    private readonly channelFilterService: ChannelFilterService,
  ) {}

  @On('messageCreate')
  async handleMessage(@Context() [message]: [Message]) {
    // Use centralized channel filtering service
    if (!this.channelFilterService.shouldProcessMessage(message)) {
      return;
    }

    const userId = message.author.id;
    const now = Date.now();
    const cooldown = this.xpCooldowns.get(userId);

    // 1 minute cooldown for XP gain
    if (cooldown && now - cooldown < 60000) return;

    this.xpCooldowns.set(userId, now);
    await this.addXP(userId, this.getRandomXP());
  }

  @SlashCommand({ name: 'level', description: 'Check current level and XP (yours or another user\'s)' })
  async onLevelCommand(@Context() [interaction]: SlashCommandContext, @Options() options: LevelDto) {
    try {
      const targetUser = options.user || interaction.user;
      const userKey = `user:${targetUser.id}`;
      const redis = this.redisDatabaseService.redis.getClient();
      const userStr = await redis.get(userKey);
      const user = userStr ? JSON.parse(userStr) : null;
      const isOwnLevel = targetUser === interaction.user;

      if (!user || !user.experience) {
        await interaction.reply({
          content: `📊 ${isOwnLevel ? 'You need' : `${targetUser.tag} needs`} to send some messages first to start gaining XP!`,
          ephemeral: true,
        });
        return;
      }

      const level = this.calculateLevel(user.experience);
      const xpForNext = this.getXPForLevel(level + 1);
      const xpForCurrent = this.getXPForLevel(level);
      const progress = user.experience - xpForCurrent;
      const needed = xpForNext - xpForCurrent;

      await interaction.reply({
        content: `📊 **${isOwnLevel ? 'Your' : `${targetUser.tag}'s`} Level Stats**\n\n🎯 Level: **${level}**\n⭐ Total XP: **${user.experience}**\n📈 Progress: **${progress}/${needed}** XP to next level${isOwnLevel ? '\n\nKeep chatting to gain more XP! 🚀' : ''}`,
        ephemeral: true,
      });
      this.logger.log(`Level checked: ${targetUser.tag} (level ${level}) by ${interaction.user.tag}`);
    } catch (error) {
      this.logger.error('Level command failed:', error);
      await interaction.reply({ content: '❌ Failed to get level information.', ephemeral: true });
    }
  }

  @SlashCommand({ name: 'rank', description: 'View server leaderboard or specific user rank' })
  async onRankCommand(@Context() [interaction]: SlashCommandContext, @Options() options: LevelDto) {
    const targetUser = options.user;
    await interaction.reply({
      content: `🏆 Server leaderboard${targetUser ? ` and ${targetUser.tag}'s rank` : ''} is coming soon! Keep gaining XP.`,
      ephemeral: true,
    });
  }

  private async addXP(userId: string, amount: number) {
    try {
      const userKey = `user:${userId}`;
      const redis = this.redisDatabaseService.redis.getClient();
      const userStr = await redis.get(userKey);
      let user = userStr ? JSON.parse(userStr) : null;

      if (!user) {
        user = {
          discordId: userId,
          username: 'Unknown',
          experience: amount,
          balance: 0,
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        await redis.set(userKey, JSON.stringify(user));
      } else {
        const oldLevel = this.calculateLevel(user.experience || 0);
        const newExperience = (user.experience || 0) + amount;
        const newLevel = this.calculateLevel(newExperience);

        // Level up notification would go here
        if (newLevel > oldLevel) {
          this.logger.log(`User ${userId} leveled up to ${newLevel}!`);
        }

        user.experience = newExperience;
        user.updatedAt = new Date().toISOString();
        await redis.set(userKey, JSON.stringify(user));
      }
    } catch (error) {
      this.logger.error(`Failed to add XP to user ${userId}:`, error);
    }
  }

  private getRandomXP(): number {
    return Math.floor(Math.random() * 15) + 5; // 5-20 XP
  }

  private calculateLevel(xp: number): number {
    return Math.floor(Math.sqrt(xp / 100));
  }

  private getXPForLevel(level: number): number {
    return level * level * 100;
  }
}