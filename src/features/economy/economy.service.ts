import { RedisDatabaseService } from '@/core/database/redis-database.service';
import { User } from '@/core/database/schema';
import { Injectable, Logger } from '@nestjs/common';
import { User as DiscordUser } from 'discord.js';
import { Context, Options, SlashCommand, SlashCommandContext, UserOption } from 'necord';

class BalanceDto {
  @UserOption({ name: 'user', description: 'Select a user to check their coin balance (leave empty for yourself)', required: false })
  user?: DiscordUser;
}

@Injectable()
export class EconomyService {
  private readonly logger = new Logger(EconomyService.name);

  constructor(
    private readonly redisDatabaseService: RedisDatabaseService,
  ) {}

  @SlashCommand({ name: 'balance', description: '💰 Check coin balance for yourself or another user' })
  async onBalanceCommand(@Context() [interaction]: SlashCommandContext, @Options() options: BalanceDto) {
    try {
      const targetUser = options.user || interaction.user;
      const user = await this.redisDatabaseService.findUserByDiscordId(targetUser.id);
      const balance = user?.balance || user?.preferences?.balance || 0;
      const isOwnBalance = targetUser === interaction.user;
      
      await interaction.reply({
        content: `💰 **${isOwnBalance ? 'Your' : `${targetUser.tag}'s`} Balance**\n\n🪙 Coins: **${balance}**${isOwnBalance ? '\n\nEarn coins by participating in the community! 🎯' : ''}`,
        flags: 64, // MessageFlags.Ephemeral
      });
      this.logger.log(`Balance checked: ${targetUser.tag} (${balance} coins) by ${interaction.user.tag}`);
    } catch (error) {
      this.logger.error('Balance command failed:', error);
      await interaction.reply({ content: '❌ Failed to get balance information.', flags: 64 });
    }
  }

  @SlashCommand({
    name: 'daily',
    description: '🎁 Claim your daily coin reward (resets every 24 hours)',
  })
  // @ts-ignore: Necord decorator compatibility issue
  async onDailyCommand(@Context() [interaction]: SlashCommandContext) {
    try {
      // Get user from Redis database
      let user = await this.redisDatabaseService.findUserByDiscordId(interaction.user.id);

      if (!user) {
        // Create user with minimal required fields
        const newUser: Partial<User> = {
          discordId: interaction.user.id,
          username: interaction.user.username,
          isActive: true,
          experience: 0,
          balance: 0,
          preferences: {}
        };
        user = await this.redisDatabaseService.createUser(newUser);
      }

      const now = new Date();
      const userPrefs = user.preferences || {};
      const currentBalance = userPrefs.balance || 0;
      const lastDaily = userPrefs.lastDaily ? new Date(userPrefs.lastDaily) : null;
      
      // Check if user already claimed today
      if (lastDaily && this.isSameDay(now, lastDaily)) {
        const nextDaily = new Date(lastDaily);
        nextDaily.setDate(nextDaily.getDate() + 1);
        nextDaily.setHours(0, 0, 0, 0);
        
        await interaction.reply({
          content: `🎁 You've already claimed your daily reward today!\n⏰ Next daily available: <t:${Math.floor(nextDaily.getTime() / 1000)}:R>`,
          flags: 64, // MessageFlags.Ephemeral
        });
        return;
      }

      // Calculate streak
      let streak = userPrefs.dailyStreak || 0;
      if (lastDaily && this.isConsecutiveDay(now, lastDaily)) {
        streak += 1;
      } else {
        streak = 1; // Reset or start streak
      }

      // Calculate reward based on streak (base 100, bonus for streak)
      const baseReward = 100;
      const streakBonus = Math.min(streak * 25, 500); // Max 500 bonus at 20 day streak
      const totalReward = baseReward + streakBonus;
      const newBalance = currentBalance + totalReward;

      // Update user preferences with balance and daily info
      const updatedPreferences = {
        ...userPrefs,
        balance: newBalance,
        lastDaily: now.toISOString(),
        dailyStreak: streak,
      };

      // Update user preferences with new balance and daily info
      await this.redisDatabaseService.updateUser(user.id, {
        preferences: updatedPreferences
      });

      this.logger.log(`${interaction.user.tag} claimed daily reward: ${totalReward} coins (streak: ${streak})`);

      let rewardMessage = `🎁 **Daily Reward Claimed!**\n\n💰 **+${totalReward} coins**\n🪙 **New balance:** ${newBalance} coins`;

      if (streak > 1) {
        rewardMessage += `\n🔥 **${streak} day streak!** (+${streakBonus} bonus coins)`;
      }

      if (streak >= 7) {
        rewardMessage += `\n🏆 **Amazing streak! Keep it up!**`;
      }

      await interaction.reply({
        content: rewardMessage,
        flags: 64, // MessageFlags.Ephemeral
      });
    } catch (error) {
      this.logger.error('Daily command failed:', error);
      await interaction.reply({
        content: '❌ Failed to claim daily reward.',
        flags: 64, // MessageFlags.Ephemeral
      });
    }
  }

  private isSameDay(date1: Date, date2: Date): boolean {
    return date1.getFullYear() === date2.getFullYear() &&
           date1.getMonth() === date2.getMonth() &&
           date1.getDate() === date2.getDate();
  }

  private isConsecutiveDay(today: Date, lastDaily: Date): boolean {
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    return this.isSameDay(yesterday, lastDaily);
  }

  @SlashCommand({
    name: 'shop',
    description: '🛒 Browse and purchase items from the server shop',
  })
  // @ts-ignore: Necord decorator compatibility issue
  async onShopCommand(@Context() [interaction]: SlashCommandContext) {
    await interaction.reply({
      content: '🛒 Server shop is coming soon! Exciting items await.',
      flags: 64, // MessageFlags.Ephemeral
    });
  }
}