import { Module } from '@nestjs/common';
import { WelcomeModule } from './welcome/welcome.module';
import { MusicModule } from './music/music.module';
import { GamingModule } from './gaming/gaming.module';
import { LevelingModule } from './leveling/leveling.module';
import { EconomyModule } from './economy/economy.module';
import { ModerationModule } from './moderation/moderation.module';
import { UtilityModule } from './utility/utility.module';
import { StarboardModule } from './starboard/starboard.module';
import { RoleAccessModule } from './role-access/role-access.module';
import { DevOnDemandModule } from './dev-on-demand/dev-on-demand.module';
import { AIAutomationModule } from './ai-automation/ai-automation.module';
import { ReactionRoleModule } from './reaction-role/reaction-role.module';
import { UserCommandModule } from './user-command/user-command.module';
import { AIChannelModule } from './ai-channel/ai-channel.module';
import { AutomationModule } from './automation/automation.module';
import { DynamicPanelsModule } from './dynamic-panels/dynamic-panels.module';
import { ChannelPanelsModule } from './channel-panels/channel-panels.module';

@Module({
  imports: [
    WelcomeModule,
    MusicModule,
    GamingModule,
    LevelingModule,
    EconomyModule,
    ModerationModule,
    UtilityModule,
    StarboardModule,
    RoleAccessModule,
    DevOnDemandModule,
    AIAutomationModule,
    AIChannelModule,
    ReactionRoleModule,
    UserCommandModule,
    AutomationModule,
    DynamicPanelsModule,
    ChannelPanelsModule,
  ],
})
export class FeaturesModule {}