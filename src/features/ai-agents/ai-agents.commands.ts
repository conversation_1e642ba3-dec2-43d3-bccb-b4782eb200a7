import { Injectable, Logger } from '@nestjs/common';
import { Context, SlashCommand, SlashCommandContext, StringOption, Options } from 'necord';
import { Em<PERSON><PERSON><PERSON>er, ActionRowBuilder, ButtonBuilder, ButtonStyle } from 'discord.js';
import { SpecializedAgentsService, AgentSpecialty } from './specialized-agents.service';
import { TierManagementService } from '../dev-on-demand/services/tier-management.service';

export class AgentConsultDto {
  @StringOption({
    name: 'agent',
    description: 'Which AI agent to consult',
    required: true,
    choices: [
      { name: '📊 Marcus - Business Advisor', value: 'business_advisor' },
      { name: '🤖 Aria - AI Mastery Coach', value: 'ai_mastery_coach' },
      { name: '💻 Dev - Coding Mentor', value: 'coding_mentor' },
      { name: '🧠 Sage - Mindset Coach', value: 'mindset_coach' },
      { name: '💰 Felix - Wealth Strategist', value: 'wealth_strategist' },
      { name: '⚡ Zen - Productivity Optimizer', value: 'productivity_optimizer' },
    ],
  })
  agent: AgentSpecialty;

  @StringOption({
    name: 'question',
    description: 'Your question or challenge',
    required: true,
  })
  question: string;

  @StringOption({
    name: 'context',
    description: 'Additional context (optional)',
    required: false,
  })
  context?: string;
}

@Injectable()
export class AiAgentsCommands {
  private readonly logger = new Logger(AiAgentsCommands.name);

  constructor(
    private readonly agentsService: SpecializedAgentsService,
    private readonly tierService: TierManagementService,
  ) {}

  @SlashCommand({
    name: 'ai-agents',
    description: 'Access your personal AI specialist team',
  })
  async onAiAgentsCommand(@Context() [interaction]: SlashCommandContext) {
    if (!interaction.guild) {
      await interaction.reply({
        content: '❌ This command can only be used in a server.',
        ephemeral: true,
      });
      return;
    }

    try {
      // Check if user has access to AI agents
      const canAccess = await this.tierService.canUserAccessFeature(
        interaction.user.id,
        interaction.guild.id,
        'aiAgentAccess'
      );

      if (!canAccess) {
        const upgradeEmbed = new EmbedBuilder()
          .setColor(0xFF6B6B)
          .setTitle('🔒 AI Agents - Premium Feature')
          .setDescription('Access to our specialized AI team requires a membership upgrade.')
          .addFields([
            {
              name: '🆓 Free Tier',
              value: 'Limited community access only',
              inline: true
            },
            {
              name: '🚀 AI Explorer ($29.99/mo)',
              value: 'Access to AI Mastery Coach and Mindset Coach',
              inline: true
            },
            {
              name: '💎 Wealth Builder ($49.99/mo)',
              value: 'Full AI team access + business guidance',
              inline: true
            }
          ])
          .setFooter({ text: 'Upgrade your membership to unlock AI agent consultations' });

        const upgradeButton = new ActionRowBuilder<ButtonBuilder>()
          .addComponents(
            new ButtonBuilder()
              .setLabel('Upgrade Membership')
              .setURL('https://whop.com/dev-on-demand-community')
              .setStyle(ButtonStyle.Link)
              .setEmoji('⬆️')
          );

        await interaction.reply({
          embeds: [upgradeEmbed],
          components: [upgradeButton],
          ephemeral: true,
        });
        return;
      }

      // Get user's available agents and create selection interface
      const { embed, components } = await this.agentsService.createAgentSelectionEmbed(
        interaction.user.id,
        interaction.guild.id
      );

      // Add usage stats
      const usageStats = await this.agentsService.getAgentUsageStats(
        interaction.user.id,
        interaction.guild.id
      );

      embed.addFields([
        {
          name: '📊 Your Usage This Month',
          value: usageStats.monthlyLimit === -1 
            ? `${usageStats.currentUsage} queries used (Unlimited)`
            : `${usageStats.currentUsage}/${usageStats.monthlyLimit} queries used`,
          inline: true
        }
      ]);

      if (usageStats.favoriteAgent) {
        embed.addFields([
          {
            name: '⭐ Most Consulted',
            value: this.agentsService['agentPersonalities'][usageStats.favoriteAgent].name.split(' - ')[0],
            inline: true
          }
        ]);
      }

      await interaction.reply({
        embeds: [embed],
        components,
        ephemeral: true,
      });

    } catch (error) {
      this.logger.error('Failed to handle ai-agents command:', error);
      await interaction.reply({
        content: '❌ Failed to load AI agents. Please try again.',
        ephemeral: true,
      });
    }
  }

  @SlashCommand({
    name: 'consult',
    description: 'Quick consultation with a specific AI agent',
  })
  async onConsultCommand(
    @Context() [interaction]: SlashCommandContext,
    @Options() { agent, question, context }: AgentConsultDto
  ) {
    if (!interaction.guild) {
      await interaction.reply({
        content: '❌ This command can only be used in a server.',
        ephemeral: true,
      });
      return;
    }

    try {
      await interaction.deferReply({ ephemeral: true });

      // Get consultation response
      const response = await this.agentsService.consultAgent(
        interaction.user.id,
        interaction.guild.id,
        agent,
        question,
        context ? { additionalContext: context } : undefined
      );

      if (!response) {
        await interaction.editReply({
          content: '❌ Unable to process your consultation. You may have exceeded your usage limit or lack access to this agent.',
        });
        return;
      }

      const agentPersonality = this.agentsService['agentPersonalities'][agent];
      
      const responseEmbed = new EmbedBuilder()
        .setColor(0x00AE86)
        .setTitle(`${this.getAgentEmoji(agent)} ${agentPersonality.name}`)
        .setDescription(response.content)
        .addFields([
          {
            name: '📝 Your Question',
            value: question.length > 100 ? question.substring(0, 100) + '...' : question,
            inline: false
          }
        ]);

      if (context) {
        responseEmbed.addFields([
          {
            name: '🔍 Context Provided',
            value: context.length > 100 ? context.substring(0, 100) + '...' : context,
            inline: false
          }
        ]);
      }

      if (response.actionItems && response.actionItems.length > 0) {
        responseEmbed.addFields([
          {
            name: '✅ Action Items',
            value: response.actionItems.map((item, index) => `${index + 1}. ${item}`).join('\n'),
            inline: false
          }
        ]);
      }

      if (response.nextSteps && response.nextSteps.length > 0) {
        responseEmbed.addFields([
          {
            name: '👣 Next Steps',
            value: response.nextSteps.map((step, index) => `${index + 1}. ${step}`).join('\n'),
            inline: false
          }
        ]);
      }

      const actionRow = new ActionRowBuilder<ButtonBuilder>()
        .addComponents(
          new ButtonBuilder()
            .setCustomId(`follow_up_${agent}`)
            .setLabel('Ask Follow-up')
            .setStyle(ButtonStyle.Secondary)
            .setEmoji('🔄'),
          new ButtonBuilder()
            .setCustomId(`rate_consultation_${agent}`)
            .setLabel('Rate Consultation')
            .setStyle(ButtonStyle.Primary)
            .setEmoji('⭐')
        );

      if (response.resources && response.resources.length > 0) {
        responseEmbed.addFields([
          {
            name: '📚 Recommended Resources',
            value: response.resources.map((resource: any) => 
              `• [${resource.title}](${resource.url}) - ${resource.description}`
            ).join('\n'),
            inline: false
          }
        ]);
      }

      responseEmbed.setFooter({ 
        text: `Consultation provided by ${agentPersonality.name} • ${new Date().toLocaleString()}` 
      });

      await interaction.editReply({
        embeds: [responseEmbed],
        components: [actionRow],
      });

    } catch (error) {
      this.logger.error('Failed to handle consult command:', error);
      await interaction.editReply({
        content: '❌ Failed to process consultation. Please try again.',
      });
    }
  }

  @SlashCommand({
    name: 'agent-stats',
    description: 'View your AI agent usage statistics',
  })
  async onAgentStatsCommand(@Context() [interaction]: SlashCommandContext) {
    if (!interaction.guild) {
      await interaction.reply({
        content: '❌ This command can only be used in a server.',
        ephemeral: true,
      });
      return;
    }

    try {
      const usageStats = await this.agentsService.getAgentUsageStats(
        interaction.user.id,
        interaction.guild.id
      );

      const availableAgents = await this.agentsService.getAvailableAgents(
        interaction.user.id,
        interaction.guild.id
      );

      const statsEmbed = new EmbedBuilder()
        .setColor(0x3B82F6)
        .setTitle('📊 Your AI Agent Statistics')
        .setDescription('Overview of your AI consultation activity')
        .addFields([
          {
            name: '🔢 Usage This Month',
            value: usageStats.monthlyLimit === -1 
              ? `${usageStats.currentUsage} consultations (Unlimited plan)`
              : `${usageStats.currentUsage} / ${usageStats.monthlyLimit} consultations used`,
            inline: true
          },
          {
            name: '🤖 Available Agents',
            value: `${availableAgents.length} specialists accessible`,
            inline: true
          },
          {
            name: '📅 Resets On',
            value: usageStats.resetDate.toLocaleDateString(),
            inline: true
          }
        ]);

      if (usageStats.favoriteAgent) {
        const agentPersonality = this.agentsService['agentPersonalities'][usageStats.favoriteAgent];
        statsEmbed.addFields([
          {
            name: '⭐ Most Consulted Agent',
            value: `${this.getAgentEmoji(usageStats.favoriteAgent)} ${agentPersonality.name}`,
            inline: false
          }
        ]);
      }

      // Add available agents list
      const agentsList = availableAgents.map((agentType: any) => {
        const agent = this.agentsService['agentPersonalities'][agentType];
        return `${this.getAgentEmoji(agentType)} **${agent.name}** - ${agent.expertise[0]}`;
      }).join('\n');

      if (agentsList) {
        statsEmbed.addFields([
          {
            name: '👥 Your AI Team',
            value: agentsList,
            inline: false
          }
        ]);
      }

      const upgradeButton = new ActionRowBuilder<ButtonBuilder>()
        .addComponents(
          new ButtonBuilder()
            .setLabel('Upgrade Membership')
            .setURL('https://whop.com/dev-on-demand-community')
            .setStyle(ButtonStyle.Link)
            .setEmoji('⬆️'),
          new ButtonBuilder()
            .setCustomId('consult_new_agent')
            .setLabel('New Consultation')
            .setStyle(ButtonStyle.Primary)
            .setEmoji('🤖')
        );

      await interaction.reply({
        embeds: [statsEmbed],
        components: [upgradeButton],
        ephemeral: true,
      });

    } catch (error) {
      this.logger.error('Failed to handle agent-stats command:', error);
      await interaction.reply({
        content: '❌ Failed to load agent statistics. Please try again.',
        ephemeral: true,
      });
    }
  }

  private getAgentEmoji(agentType: AgentSpecialty): string {
    const emojiMap = {
      business_advisor: '📊',
      ai_mastery_coach: '🤖',
      coding_mentor: '💻',
      mindset_coach: '🧠',
      wealth_strategist: '💰',
      productivity_optimizer: '⚡'
    };
    return emojiMap[agentType] || '🤖';
  }
}