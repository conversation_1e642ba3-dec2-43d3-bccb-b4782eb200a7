import { Injectable, Logger, Inject } from '@nestjs/common';



import { TierManagementService } from '../dev-on-demand/services/tier-management.service';
import { Embed<PERSON><PERSON>er, ActionRowBuilder, ButtonBuilder, ButtonStyle, ModalBuilder, TextInputBuilder, TextInputStyle } from 'discord.js';

export type AgentSpecialty = 'business_advisor' | 'ai_mastery_coach' | 'coding_mentor' | 'mindset_coach' | 'wealth_strategist' | 'productivity_optimizer';

export interface AgentPersonality {
  name: string;
  description: string;
  expertise: string[];
  personality_traits: string[];
  communication_style: string;
  response_format: 'detailed' | 'concise' | 'step_by_step' | 'conversational';
}

export interface AgentResponse {
  content: string;
  actionItems?: string[];
  resources?: Array<{
    title: string;
    url: string;
    description: string;
  }>;
  followUpQuestions?: string[];
  nextSteps?: string[];
}

@Injectable()
export class SpecializedAgentsService {
  private readonly logger = new Logger(SpecializedAgentsService.name);

  private readonly agentPersonalities: Record<AgentSpecialty, AgentPersonality> = {
    business_advisor: {
      name: 'Marcus - Business Strategy Advisor',
      description: 'AI-powered business strategist with expertise in startups, scaling, and market analysis',
      expertise: ['Business Strategy', 'Market Analysis', 'Startup Guidance', 'Revenue Optimization', 'Competitive Analysis'],
      personality_traits: ['Analytical', 'Strategic', 'Results-Oriented', 'Pragmatic', 'Data-Driven'],
      communication_style: 'Professional yet approachable, focuses on actionable insights',
      response_format: 'step_by_step'
    },
    ai_mastery_coach: {
      name: 'Aria - AI Mastery Coach',
      description: 'Expert in AI tools, automation, and productivity enhancement through artificial intelligence',
      expertise: ['AI Tool Integration', 'Automation Workflows', 'Prompt Engineering', 'AI Productivity', 'Machine Learning Applications'],
      personality_traits: ['Innovative', 'Tech-Savvy', 'Patient', 'Encouraging', 'Detail-Oriented'],
      communication_style: 'Enthusiastic and educational, breaks down complex AI concepts simply',
      response_format: 'detailed'
    },
    coding_mentor: {
      name: 'Dev - Technical Coding Mentor',
      description: 'Experienced software engineer providing guidance on coding, architecture, and best practices',
      expertise: ['Full-Stack Development', 'System Architecture', 'Code Review', 'Performance Optimization', 'DevOps'],
      personality_traits: ['Methodical', 'Encouraging', 'Problem-Solver', 'Quality-Focused', 'Collaborative'],
      communication_style: 'Clear and structured, provides practical examples and code snippets',
      response_format: 'step_by_step'
    },
    mindset_coach: {
      name: 'Sage - Mindset & Growth Coach',
      description: 'Personal development expert focused on mental resilience, goal achievement, and mindset optimization',
      expertise: ['Mindset Training', 'Goal Setting', 'Habit Formation', 'Stress Management', 'Personal Growth'],
      personality_traits: ['Empathetic', 'Motivational', 'Wise', 'Supportive', 'Intuitive'],
      communication_style: 'Warm and encouraging, asks reflective questions and provides gentle guidance',
      response_format: 'conversational'
    },
    wealth_strategist: {
      name: 'Felix - Wealth Creation Strategist',
      description: 'Financial advisor specializing in wealth building, investment strategies, and passive income',
      expertise: ['Investment Strategies', 'Passive Income', 'Financial Planning', 'Asset Allocation', 'Risk Management'],
      personality_traits: ['Knowledgeable', 'Conservative', 'Forward-Thinking', 'Practical', 'Wealth-Minded'],
      communication_style: 'Professional and informative, focuses on long-term wealth building strategies',
      response_format: 'detailed'
    },
    productivity_optimizer: {
      name: 'Zen - Productivity Optimizer',
      description: 'Efficiency expert helping optimize workflows, time management, and personal systems',
      expertise: ['Time Management', 'Workflow Optimization', 'System Design', 'Focus Techniques', 'Life Organization'],
      personality_traits: ['Organized', 'Efficient', 'Systematic', 'Calm', 'Solution-Focused'],
      communication_style: 'Clear and actionable, provides specific systems and frameworks',
      response_format: 'step_by_step'
    }
  };

  constructor(
    private readonly tierService: TierManagementService,
  ) {}

  async getAvailableAgents(userId: string, guildId: string): Promise<AgentSpecialty[]> {
    try {
      const userFeatures = await this.tierService.getUserTierFeatures(userId, guildId);
      
      if (!userFeatures?.aiAgentAccess) {
        return [];
      }

      // Basic tier gets access to 2 agents
      let availableAgents: AgentSpecialty[] = ['ai_mastery_coach', 'mindset_coach'];

      // AI Explorer tier adds productivity and basic business advice
      if (userFeatures.productivityAutomation) {
        availableAgents.push('productivity_optimizer');
      }

      // Wealth Builder tier adds business and wealth agents
      if (userFeatures.businessStrategyAccess) {
        availableAgents.push('business_advisor', 'wealth_strategist');
      }

      // Dev Premium and Enterprise get full access
      if (userFeatures.developerNetworkAccess) {
        availableAgents.push('coding_mentor');
      }

      return availableAgents;
    } catch (error) {
      this.logger.error(`Failed to get available agents for user ${userId}:`, error);
      return ['ai_mastery_coach']; // Fallback to basic agent
    }
  }

  async consultAgent(
    userId: string, 
    guildId: string, 
    agentType: AgentSpecialty, 
    query: string,
    context?: Record<string, any>
  ): Promise<AgentResponse | null> {
    try {
      // Check if user has access to this agent
      const availableAgents = await this.getAvailableAgents(userId, guildId);
      if (!availableAgents.includes(agentType)) {
        throw new Error(`User does not have access to ${agentType} agent`);
      }

      // Update usage tracking
      const canUse = await this.tierService.updateUserUsage(userId, guildId, 'aiAgentQueries');
      if (!canUse) {
        throw new Error('User has exceeded their AI agent query limit for this billing period');
      }

      const agent = this.agentPersonalities[agentType];
      
      // Generate AI response based on agent specialty and personality
      const response = await this.generateAgentResponse(agent, query, context);
      
      this.logger.log(`${agent.name} consulted by user ${userId} in guild ${guildId}`);
      
      return response;
    } catch (error) {
      this.logger.error(`Failed to consult ${agentType} agent:`, error);
      return null;
    }
  }

  async createAgentSelectionEmbed(userId: string, guildId: string): Promise<{ embed: EmbedBuilder; components: ActionRowBuilder<ButtonBuilder>[] }> {
    const availableAgents = await this.getAvailableAgents(userId, guildId);
    
    const embed = new EmbedBuilder()
      .setColor(0x00AE86)
      .setTitle('🤖 AI Agent Consultation')
      .setDescription('Select an AI specialist to help you achieve your goals')
      .setFooter({ text: `You have access to ${availableAgents.length} specialized agents` });

    const components: ActionRowBuilder<ButtonBuilder>[] = [];
    let currentRow = new ActionRowBuilder<ButtonBuilder>();
    let buttonCount = 0;

    for (const agentType of availableAgents) {
      const agent = this.agentPersonalities[agentType];
      
      embed.addFields([{
        name: `${this.getAgentEmoji(agentType)} ${agent.name}`,
        value: `${agent.description}\n**Expertise:** ${agent.expertise.slice(0, 3).join(', ')}`,
        inline: true
      }]);

      currentRow.addComponents(
        new ButtonBuilder()
          .setCustomId(`consult_agent_${agentType}`)
          .setLabel(agent.name.split(' - ')[0])
          .setEmoji(this.getAgentEmoji(agentType))
          .setStyle(ButtonStyle.Primary)
      );

      buttonCount++;
      
      if (buttonCount === 5 || agentType === availableAgents[availableAgents.length - 1]) {
        components.push(currentRow);
        currentRow = new ActionRowBuilder<ButtonBuilder>();
        buttonCount = 0;
      }
    }

    return { embed, components };
  }

  async createConsultationModal(agentType: AgentSpecialty): Promise<ModalBuilder> {
    const agent = this.agentPersonalities[agentType];
    
    return new ModalBuilder()
      .setCustomId(`agent_consultation_${agentType}`)
      .setTitle(`Consult ${agent.name.split(' - ')[0]}`)
      .addComponents(
        new ActionRowBuilder<TextInputBuilder>().addComponents(
          new TextInputBuilder()
            .setCustomId('consultation_query')
            .setLabel('What would you like help with?')
            .setStyle(TextInputStyle.Paragraph)
            .setPlaceholder(`Ask ${agent.name.split(' - ')[0]} anything about ${agent.expertise[0].toLowerCase()}...`)
            .setRequired(true)
            .setMaxLength(2000)
        ),
        new ActionRowBuilder<TextInputBuilder>().addComponents(
          new TextInputBuilder()
            .setCustomId('consultation_context')
            .setLabel('Additional Context (Optional)')
            .setStyle(TextInputStyle.Paragraph)
            .setPlaceholder('Provide any relevant background information...')
            .setRequired(false)
            .setMaxLength(1000)
        )
      );
  }

  private async generateAgentResponse(
    agent: AgentPersonality, 
    query: string, 
    context?: Record<string, any>
  ): Promise<AgentResponse> {
    // In a real implementation, this would call an AI service like OpenAI
    // For now, we'll generate contextual responses based on agent type
    
    const responses = this.getAgentResponseTemplates(agent);
    const baseResponse = responses[Math.floor(Math.random() * responses.length)];
    
    // Customize response based on query and context
    const customizedResponse: AgentResponse = {
      content: this.customizeResponse(baseResponse.content, query, agent),
      actionItems: baseResponse.actionItems,
      resources: baseResponse.resources,
      followUpQuestions: baseResponse.followUpQuestions,
      nextSteps: baseResponse.nextSteps
    };

    return customizedResponse;
  }

  private customizeResponse(template: string, query: string, agent: AgentPersonality): string {
    return template
      .replace('{query}', query)
      .replace('{agent_name}', agent.name.split(' - ')[0])
      .replace('{expertise}', agent.expertise[0]);
  }

  private getAgentResponseTemplates(agent: AgentPersonality): AgentResponse[] {
    // Return different response templates based on agent specialty
    switch (agent.name.toLowerCase().includes('business')) {
      case true:
        return [
          {
            content: `Great question about {query}! As {agent_name}, I can help you break this down strategically. Based on my expertise in {expertise}, here's what I recommend...`,
            actionItems: [
              'Conduct market research on your target audience',
              'Analyze competitor strategies and positioning',
              'Define your unique value proposition',
              'Create a go-to-market strategy'
            ],
            resources: [
              {
                title: 'Lean Canvas Template',
                url: 'https://leanstack.com/leancanvas',
                description: 'Tool for mapping out your business model'
              }
            ],
            followUpQuestions: [
              'What\'s your target market size?',
              'Who are your main competitors?',
              'What\'s your unique competitive advantage?'
            ],
            nextSteps: [
              'Schedule a follow-up consultation in 1 week',
              'Implement the action items above',
              'Gather data on market response'
            ]
          }
        ];
      default:
        return [
          {
            content: `Thank you for your question about {query}. As {agent_name}, I'm here to guide you through this challenge with my expertise in {expertise}. Let me provide you with a comprehensive approach...`,
            actionItems: [
              'Take time to reflect on your current situation',
              'Identify the key challenges and opportunities',
              'Create a structured plan to move forward'
            ],
            followUpQuestions: [
              'What specific outcome are you hoping to achieve?',
              'What obstacles have you encountered so far?',
              'What resources do you currently have available?'
            ],
            nextSteps: [
              'Implement the suggested action items',
              'Monitor your progress over the next week',
              'Schedule a follow-up consultation as needed'
            ]
          }
        ];
    }
  }

  private getAgentEmoji(agentType: AgentSpecialty): string {
    const emojiMap = {
      business_advisor: '📊',
      ai_mastery_coach: '🤖',
      coding_mentor: '💻',
      mindset_coach: '🧠',
      wealth_strategist: '💰',
      productivity_optimizer: '⚡'
    };
    return emojiMap[agentType] || '🤖';
  }

  async getAgentUsageStats(userId: string, guildId: string): Promise<{
    currentUsage: number;
    monthlyLimit: number;
    resetDate: Date;
    favoriteAgent?: AgentSpecialty;
  }> {
    try {
      const membership = await this.tierService.getUserMembership(userId, guildId);
      if (!membership) {
        return { currentUsage: 0, monthlyLimit: 0, resetDate: new Date() };
      }

      return {
        currentUsage: membership.usageStats.aiAgentQueries,
        monthlyLimit: -1, // Would get from tier limits
        resetDate: new Date(membership.usageStats.lastResetDate),
        favoriteAgent: 'ai_mastery_coach' // Would track from usage data
      };
    } catch (error) {
      this.logger.error(`Failed to get agent usage stats for user ${userId}:`, error);
      return { currentUsage: 0, monthlyLimit: 10, resetDate: new Date() };
    }
  }
}