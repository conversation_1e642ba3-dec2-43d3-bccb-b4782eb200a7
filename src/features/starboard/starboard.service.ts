import { Injectable, Logger } from '@nestjs/common';
import { On, Context } from 'necord';
import { MessageReaction, User, TextChannel, EmbedBuilder } from 'discord.js';
import { RedisDatabaseService } from '../../core/database/redis-database.service';

@Injectable()
export class StarboardService {
  private readonly logger = new Logger(StarboardService.name);
  private readonly processedMessages = new Set<string>();

  constructor(
    private readonly redisDatabaseService: RedisDatabaseService,
  ) {}

  @On('messageReactionAdd')
  async handleReactionAdd(@Context() [reaction, user]: [MessageReaction, User]) {
    if (user.bot) return;
    if (reaction.emoji.name !== '⭐') return;

    try {
      const guildConfig = await this.getStarboardConfig(reaction.message.guild?.id || '');
      
      if (!guildConfig.enabled || !guildConfig.channelId) {
        return;
      }

      const messageId = reaction.message.id;
      const starCount = reaction.count || 0;
      const requiredStars = guildConfig.threshold || 3;

      if (starCount >= requiredStars && !this.processedMessages.has(messageId)) {
        await this.addToStarboard(reaction, guildConfig);
        this.processedMessages.add(messageId);
      }
    } catch (error) {
      this.logger.error('Failed to handle star reaction:', error);
    }
  }

  private async addToStarboard(reaction: MessageReaction, guildConfig: any) {
    try {
      const starboardChannel = reaction.message.guild?.channels.cache.get(
        guildConfig.channelId!
      ) as TextChannel;

      if (!starboardChannel) return;

      const message = reaction.message;
      const author = message.author;

      if (!author) return;

      const embed = new EmbedBuilder()
        .setAuthor({
          name: author.tag,
          iconURL: author.displayAvatarURL(),
        })
        .setDescription(message.content || '*[No text content]*')
        .setColor(0xFFD700)
        .setTimestamp(message.createdAt)
        .addFields([
          {
            name: 'Original Message',
            value: `[Jump to message](${message.url})`,
            inline: true,
          },
          {
            name: 'Channel',
            value: `${message.channel}`,
            inline: true,
          },
        ]);

      // Add image if present
      const attachment = message.attachments.first();
      if (attachment && attachment.contentType?.startsWith('image/')) {
        embed.setImage(attachment.url);
      }

      await starboardChannel.send({
        content: `⭐ **${reaction.count}** | ${message.channel}`,
        embeds: [embed],
      });

      this.logger.log(`Added message ${message.id} to starboard with ${reaction.count} stars`);
    } catch (error) {
      this.logger.error('Failed to add message to starboard:', error);
    }
  }

  async updateStarboardConfig(
    guildId: string,
    config: {
      enabled?: boolean;
      channelId?: string;
      threshold?: number;
    }
  ) {
    try {
      const redis = this.redisDatabaseService.redis.getClient();
      const configKey = `starboard_config:${guildId}`;
      
      // Get current config
      const currentConfigStr = await redis.get(configKey);
      let currentConfig = currentConfigStr ? JSON.parse(currentConfigStr) : {
        enabled: true,
        channelId: null,
        threshold: 3
      };

      // Update config with provided values
      if (config.enabled !== undefined) currentConfig.enabled = config.enabled;
      if (config.channelId) currentConfig.channelId = config.channelId;
      if (config.threshold !== undefined) currentConfig.threshold = config.threshold;

      // Save updated config
      await redis.set(configKey, JSON.stringify(currentConfig));
      
      return {
        message: 'Starboard configuration updated successfully',
        config: currentConfig,
      };
    } catch (error) {
      this.logger.error(`Failed to update starboard config for guild ${guildId}:`, error);
      throw error;
    }
  }

  async getStarboardConfig(guildId: string) {
    try {
      const redis = this.redisDatabaseService.redis.getClient();
      const configKey = `starboard_config:${guildId}`;
      const configStr = await redis.get(configKey);

      if (!configStr) {
        return {
          enabled: true, // Enable by default
          channelId: null,
          threshold: 3,
        };
      }

      const config = JSON.parse(configStr);
      return {
        enabled: config.enabled !== false, // Default to true unless explicitly disabled
        channelId: config.channelId,
        threshold: config.threshold || 3,
      };
    } catch (error) {
      this.logger.error(`Failed to get starboard config for guild ${guildId}:`, error);
      throw error;
    }
  }
}