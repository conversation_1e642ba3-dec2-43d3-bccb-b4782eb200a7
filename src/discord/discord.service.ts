import { Inject, Injectable, Logger, OnModuleInit, Optional } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Client } from 'discord.js';



import { aiAgentConfigs, guilds } from '@/core/database';
import { eq, and, or, desc, asc, count, sql } from 'drizzle-orm';

type Feature = 'welcome-message' | 'music' | 'gaming' | 'reaction-role' | 'meme' | 'user-command' | 'leveling' | 'moderation' | 'economy' | 'utility' | 'starboard' | 'whop' | 'ai-agents';

@Injectable()
export class DiscordService implements OnModuleInit {
  private readonly logger = new Logger(DiscordService.name);

  constructor(
    @Optional() @Inject(Client) private readonly client: Client | null,
    private readonly configService: ConfigService,
    private readonly databaseService: DatabaseService,
  ) {}

  async onModuleInit() {
    this.logger.log('Discord module initializing...');
    if (this.client) {
      await this.initializeBot();
    } else {
      this.logger.warn('⚠️ Discord bot token not provided - running in OAuth-only mode');
      this.logger.log('✅ Discord module initialized in OAuth-only mode');
    }
  }

  private async initializeBot() {
    try {
      if (!this.client) {
        this.logger.warn('⚠️ Discord client not available - skipping bot initialization');
        return;
      }

      this.logger.log('🔑 Discord token configured, attempting bot login...');

      // Set up basic client event handlers
      this.client.on('ready', () => {
        this.logger.log(`✅ Discord bot connected successfully as ${this.client?.user?.tag}`);
        this.logger.log(`🎯 Bot is active in ${this.client?.guilds.cache.size} guilds`);
      });

      this.client.on('error', (error) => {
        this.logger.error('Discord client error:', error);
      });

      this.client.on('warn', (warning) => {
        this.logger.warn('Discord client warning:', warning);
      });

      // Bot is automatically logged in by Necord
      this.logger.log('🚀 Discord bot initialization completed');
    } catch (error) {
      this.logger.error('❌ Failed to initialize Discord bot:', error);
      throw error;
    }
  }

  /**
   * Get enabled features for a guild
   */
  async getEnabledFeatures(guildId: string): Promise<Feature[]> {
    const features: Feature[] = [];
    
    try {
      // Get guild configuration
      const guildResults = await this.db.select().from(guilds).where(eq(guilds.discordId, guildId));
      const guild = guildResults[0];

      if (!guild) {
        return features;
      }

      // Check AI agents configuration
      const aiAgentConfigResults = await this.db.select().from(aiAgentConfigs).where(eq(aiAgentConfigs.guildId, guild.discordId));
      const enabledAiAgentConfigs = aiAgentConfigResults.filter((config: any) => config.enabled);
      
      if (enabledAiAgentConfigs.length > 0) {
        features.push('ai-agents');
      }

      // Check welcome system
      if (guild.welcomeEnabled) {
        features.push('welcome-message');
      }

      // Check starboard system
      if (guild.starboardEnabled) {
        features.push('starboard');
      }

      // Check features from settings JSON
      if (guild.settings) {
        const settings = guild.settings;
        
        if (settings.music?.enabled) features.push('music');
        if (settings.moderation?.enabled) features.push('moderation');
        if (settings.economy?.enabled) features.push('economy');
        if (settings.leveling?.enabled) features.push('leveling');
        if (settings.gaming?.enabled) features.push('gaming');
        if (settings.utility?.enabled) features.push('utility');
        if (settings.meme?.enabled) features.push('meme');
        if (settings.userCommand?.enabled) features.push('user-command');
        if (settings.reactionRole?.enabled) features.push('reaction-role');
        if (settings.whop?.enabled) features.push('whop');
      }

      // Check features from features JSON
      if (guild.features) {
        Object.entries(guild.features).forEach(([feature, config]: [string, any]) => {
          if (config?.enabled && !features.includes(feature as Feature)) {
            features.push(feature as Feature);
          }
        });
      }
      
      return features;
    } catch (error) {
      this.logger.error(`Failed to get enabled features for guild ${guildId}:`, error);
      return [];
    }
  }

  /**
   * Check if bot is ready and connected
   */
  isReady(): boolean {
    return this.client?.isReady() ?? false;
  }

  /**
   * Get guild count
   */
  getGuildCount(): number {
    return this.client?.guilds.cache.size ?? 0;
  }

  /**
   * Get user count across all guilds
   */
  getUserCount(): number {
    return this.client?.users.cache.size ?? 0;
  }

  async sendMessage(channelId: string, content: string): Promise<boolean> {
    try {
      if (!this.client?.isReady()) return false;
      const channel = await this.client.channels.fetch(channelId);
      if (channel?.isTextBased() && 'send' in channel) {
        await channel.send(content);
        return true;
      }
      return false;
    } catch (error) {
      this.logger.error(`Failed to send message to channel ${channelId}:`, error);
      return false;
    }
  }

  /**
   * Get guild information
   */
  async getGuildInfo(guildId: string) {
    if (!this.client || !this.client.isReady()) {
      this.logger.warn(`Bot not available for guild ${guildId}`);
      return { id: guildId, name: 'Discord Server', icon: null, memberCount: 0, owner: null, features: [] };
    }

    const guild = this.client.guilds.cache.get(guildId);
    if (!guild) {
      this.logger.warn(`Guild ${guildId} not in bot cache`);
      return { id: guildId, name: 'Discord Server', icon: null, memberCount: 0, owner: null, features: [] };
    }

    return {
      id: guild.id,
      name: guild.name,
      icon: guild.iconURL(),
      memberCount: guild.memberCount,
      owner: guild.ownerId,
      features: guild.features,
    };
  }

  getClient(): Client {
    return this.client;
  }

  /**
   * Health check for Discord connection
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    connected: boolean;
    guilds: number;
    users: number;
    ping: number;
  }> {
    try {
      if (!this.client) {
        return {
          status: 'unhealthy',
          connected: false,
          guilds: 0,
          users: 0,
          ping: -1,
        };
      }

      const connected = this.isReady();
      const guilds = this.getGuildCount();
      const users = this.getUserCount();
      const ping = this.client.ws.ping;

      return {
        status: connected ? 'healthy' : 'unhealthy',
        connected,
        guilds,
        users,
        ping,
      };
    } catch (error) {
      this.logger.error('Discord health check failed:', error);
      return {
        status: 'unhealthy',
        connected: false,
        guilds: 0,
        users: 0,
        ping: -1,
      };
    }
  }
}