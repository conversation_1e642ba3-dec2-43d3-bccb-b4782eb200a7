import { Injectable, Logger, Inject, Optional } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Client, PermissionsBitField, GuildMember } from 'discord.js';

@Injectable()
export class DiscordUtilsService {
  private readonly logger = new Logger(DiscordUtilsService.name);
  private readonly permissionCache = new Map<string, { permissions: boolean; expires: number }>();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  constructor(
    @Optional() @Inject(Client) private readonly client: Client | null,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Get user ID from Discord OAuth access token
   */
  async getUserID(accessToken: string): Promise<string> {
    try {
      const response = await fetch('https://discord.com/api/v10/users/@me', {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Discord API returned ${response.status}`);
      }

      const user = await response.json();
      return user.id;
    } catch (error) {
      this.logger.error('Failed to get user ID from Discord:', error);
      throw new Error('Failed to verify Discord user');
    }
  }

  /**
   * Check user guild permissions via OAuth token
   */
  async checkUserGuildPermissions(accessToken: string, guildId: string): Promise<boolean> {
    const cacheKey = `${accessToken.substring(0, 10)}-${guildId}`;
    const cached = this.permissionCache.get(cacheKey);
    
    if (cached && cached.expires > Date.now()) {
      return cached.permissions;
    }

    try {
      // Get user's guilds with permissions
      const response = await fetch('https://discord.com/api/v10/users/@me/guilds', {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      if (!response.ok) {
        if (response.status === 429) {
          throw new Error('Rate limited by Discord API');
        }
        throw new Error(`Discord API returned ${response.status}`);
      }

      const guilds = await response.json();
      const guild = guilds.find((g: any) => g.id === guildId);
      
      if (!guild) {
        this.permissionCache.set(cacheKey, { permissions: false, expires: Date.now() + this.CACHE_DURATION });
        return false;
      }

      // Check if user has administrator or manage guild permissions
      const permissions = BigInt(guild.permissions);
      const hasPermissions = 
        (permissions & PermissionsBitField.Flags.Administrator) === PermissionsBitField.Flags.Administrator ||
        (permissions & PermissionsBitField.Flags.ManageGuild) === PermissionsBitField.Flags.ManageGuild;

      this.permissionCache.set(cacheKey, { permissions: hasPermissions, expires: Date.now() + this.CACHE_DURATION });
      return hasPermissions;
    } catch (error) {
      this.logger.error('Failed to check user guild permissions:', error);
      throw error;
    }
  }

  /**
   * Check permissions using bot's guild cache (more accurate)
   */
  async checkBotGuildPermissions(userId: string, guildId: string): Promise<boolean> {
    if (!this.client?.isReady()) {
      return false;
    }

    try {
      const guild = this.client?.guilds.cache.get(guildId);
      if (!guild) {
        throw new Error('Guild not found');
      }

      const member = await guild.members.fetch(userId);
      if (!member) {
        throw new Error('Member not found');
      }

      return member.permissions.has([PermissionsBitField.Flags.Administrator, PermissionsBitField.Flags.ManageGuild]);
    } catch (error) {
      this.logger.error(`Failed to check bot guild permissions for user ${userId} in guild ${guildId}:`, error);
      throw error;
    }
  }

  /**
   * Get guild member information
   */
  async getGuildMember(guildId: string, userId: string): Promise<GuildMember | null> {
    if (!this.client?.isReady()) {
      return null;
    }

    try {
      const guild = this.client?.guilds.cache.get(guildId);
      if (!guild) {
        return null;
      }

      const member = await guild.members.fetch(userId);
      return member;
    } catch (error) {
      this.logger.error(`Failed to get guild member ${userId} from guild ${guildId}:`, error);
      return null;
    }
  }

  /**
   * Send DM to user
   */
  async sendDirectMessage(userId: string, content: string): Promise<boolean> {
    try {
      const user = await this.client?.users.fetch(userId);
      if (!user) {
        return false;
      }

      await user.send(content);
      return true;
    } catch (error) {
      this.logger.error(`Failed to send DM to user ${userId}:`, error);
      return false;
    }
  }

  /**
   * Get guild channels
   */
  async getGuildChannels(guildId: string) {
    if (!this.client?.isReady()) {
      return [];
    }

    try {
      const guild = this.client.guilds.cache.get(guildId);
      if (!guild) {
        return [];
      }

      const channels = Array.from(guild.channels.cache.values())
        .filter((channel: any) => channel.isTextBased())
        .map((channel: any) => ({
          id: channel.id,
          name: channel.name,
          type: channel.type,
        }));

      return channels;
    } catch (error) {
      this.logger.error(`Failed to get channels for guild ${guildId}:`, error);
      throw error;
    }
  }

  /**
   * Get guild roles
   */
  async getGuildRoles(guildId: string) {
    if (!this.client?.isReady()) {
      return [];
    }

    try {
      const guild = this.client.guilds.cache.get(guildId);
      if (!guild) {
        return [];
      }

      const roles = Array.from(guild.roles.cache.values())
        .filter((role: any) => !role.managed && role.name !== '@everyone')
        .map((role: any) => ({
          id: role.id,
          name: role.name,
          color: role.hexColor,
          position: role.position,
        }))
        .sort((a, b) => b.position - a.position);

      return roles;
    } catch (error) {
      this.logger.error(`Failed to get roles for guild ${guildId}:`, error);
      throw error;
    }
  }

  /**
   * Clear permission cache
   */
  clearPermissionCache(): void {
    this.permissionCache.clear();
    this.logger.log('Permission cache cleared');
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    const now = Date.now();
    const validEntries = Array.from(this.permissionCache.entries())
      .filter(([, data]) => data.expires > now);
    
    return {
      totalEntries: this.permissionCache.size,
      validEntries: validEntries.length,
      expiredEntries: this.permissionCache.size - validEntries.length,
    };
  }
}