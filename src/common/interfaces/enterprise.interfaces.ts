/**
 * Enterprise-grade interfaces and types for multi-tenant, scalable Discord bot platform
 */

// Organization & Multi-Tenancy
export interface Organization {
  id: string;
  name: string;
  slug: string;
  tier: OrganizationTier;
  status: OrganizationStatus;
  settings: OrganizationSettings;
  limits: ResourceLimits;
  billing: BillingInfo;
  createdAt: Date;
  updatedAt: Date;
}

export enum OrganizationTier {
  FREE = 'free',
  STARTER = 'starter',
  PROFESSIONAL = 'professional',
  ENTERPRISE = 'enterprise',
  CUSTOM = 'custom'
}

export enum OrganizationStatus {
  ACTIVE = 'active',
  SUSPENDED = 'suspended',
  TRIAL = 'trial',
  CANCELLED = 'cancelled'
}

export interface OrganizationSettings {
  allowedDomains: string[];
  ssoEnabled: boolean;
  auditLogRetention: number; // days
  dataRetention: number; // days
  customBranding: boolean;
  apiAccess: boolean;
  webhookEndpoints: string[];
}

export interface ResourceLimits {
  maxGuilds: number;
  maxUsers: number;
  maxApiCalls: number; // per hour
  maxStorage: number; // MB
  maxBandwidth: number; // MB per month
  maxConcurrentConnections: number;
  features: string[];
}

export interface BillingInfo {
  subscriptionId?: string;
  planId: string;
  status: 'active' | 'past_due' | 'cancelled' | 'trialing';
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  trialEnd?: Date;
  paymentMethod?: string;
}

// Performance & Monitoring
export interface PerformanceMetrics {
  timestamp: Date;
  organizationId: string;
  guildId?: string;
  metrics: {
    responseTime: number;
    throughput: number;
    errorRate: number;
    cpuUsage: number;
    memoryUsage: number;
    activeConnections: number;
    queueDepth: number;
  };
}

export interface HealthCheck {
  service: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: Date;
  responseTime: number;
  details?: Record<string, any>;
  dependencies?: HealthCheck[];
}

// Security & Audit
export interface AuditLog {
  id: string;
  organizationId: string;
  userId: string;
  action: string;
  resource: string;
  resourceId: string;
  details: Record<string, any>;
  ipAddress: string;
  userAgent: string;
  timestamp: Date;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export interface SecurityEvent {
  id: string;
  type: SecurityEventType;
  organizationId: string;
  userId?: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  metadata: Record<string, any>;
  resolved: boolean;
  timestamp: Date;
}

export enum SecurityEventType {
  FAILED_LOGIN = 'failed_login',
  SUSPICIOUS_ACTIVITY = 'suspicious_activity',
  RATE_LIMIT_EXCEEDED = 'rate_limit_exceeded',
  UNAUTHORIZED_ACCESS = 'unauthorized_access',
  DATA_BREACH_ATTEMPT = 'data_breach_attempt',
  PRIVILEGE_ESCALATION = 'privilege_escalation'
}

// API & Rate Limiting
export interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  skipSuccessfulRequests: boolean;
  skipFailedRequests: boolean;
  keyGenerator: (req: any) => string;
  onLimitReached?: (req: any, res: any) => void;
}

export interface ApiKey {
  id: string;
  organizationId: string;
  name: string;
  key: string; // hashed
  permissions: string[];
  rateLimit: RateLimitConfig;
  lastUsed?: Date;
  expiresAt?: Date;
  isActive: boolean;
  createdBy: string;
  createdAt: Date;
}

// Event System
export interface DomainEvent {
  id: string;
  type: string;
  aggregateId: string;
  aggregateType: string;
  version: number;
  data: Record<string, any>;
  metadata: EventMetadata;
  timestamp: Date;
}

export interface EventMetadata {
  userId?: string;
  organizationId?: string;
  correlationId?: string;
  causationId?: string;
  source: string;
  version: string;
}

// Cache & Storage
export interface CacheConfig {
  ttl: number; // seconds
  maxSize: number;
  strategy: 'lru' | 'lfu' | 'fifo';
  namespace: string;
}

export interface StorageProvider {
  type: 'local' | 's3' | 'gcs' | 'azure';
  config: Record<string, any>;
  encryption: boolean;
  compression: boolean;
}

// Feature Flags
export interface FeatureFlag {
  key: string;
  name: string;
  description: string;
  enabled: boolean;
  rolloutPercentage: number;
  conditions: FeatureFlagCondition[];
  organizationOverrides: Record<string, boolean>;
  userOverrides: Record<string, boolean>;
  createdAt: Date;
  updatedAt: Date;
}

export interface FeatureFlagCondition {
  type: 'organization_tier' | 'user_role' | 'guild_size' | 'custom';
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'in' | 'not_in';
  value: any;
}

// Queue & Job Processing
export interface JobDefinition {
  id: string;
  name: string;
  type: string;
  priority: number;
  data: Record<string, any>;
  options: JobOptions;
  organizationId?: string;
  createdAt: Date;
  scheduledFor?: Date;
}

export interface JobOptions {
  attempts: number;
  backoff: 'fixed' | 'exponential';
  delay: number;
  timeout: number;
  removeOnComplete: number;
  removeOnFail: number;
}

// Configuration Management
export interface ConfigurationSchema {
  key: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  required: boolean;
  default?: any;
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    enum?: any[];
  };
  description: string;
  category: string;
  sensitive: boolean;
}

export interface TenantConfiguration {
  organizationId: string;
  config: Record<string, any>;
  schema: ConfigurationSchema[];
  version: number;
  lastModified: Date;
  modifiedBy: string;
}

// Notification System
export interface NotificationTemplate {
  id: string;
  name: string;
  type: 'email' | 'sms' | 'webhook' | 'discord' | 'slack';
  subject?: string;
  body: string;
  variables: string[];
  organizationId?: string;
  isActive: boolean;
}

export interface NotificationDelivery {
  id: string;
  templateId: string;
  recipient: string;
  channel: string;
  status: 'pending' | 'sent' | 'delivered' | 'failed' | 'bounced';
  attempts: number;
  lastAttempt?: Date;
  deliveredAt?: Date;
  error?: string;
  metadata: Record<string, any>;
}

// Integration Framework
export interface Integration {
  id: string;
  name: string;
  type: string;
  organizationId: string;
  config: Record<string, any>;
  credentials: Record<string, any>; // encrypted
  status: 'active' | 'inactive' | 'error';
  lastSync?: Date;
  syncInterval?: number;
  webhookUrl?: string;
  isActive: boolean;
}

export interface WebhookEvent {
  id: string;
  integrationId: string;
  event: string;
  payload: Record<string, any>;
  signature: string;
  timestamp: Date;
  processed: boolean;
  attempts: number;
  lastAttempt?: Date;
  error?: string;
}
