import { Logger, ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app.module';

async function bootstrap() {
  const logger = new Logger('Bootstrap');
  const app = await NestFactory.create(AppModule);

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  // API prefix for REST endpoints
  app.setGlobalPrefix('api');

  // Swagger documentation
  const config = new DocumentBuilder()
    .setTitle('Discord Bot API')
    .setDescription('REST API for Discord bot management')
    .setVersion('1.0')
    .addBearerAuth()
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('docs', app, document);

  // Dynamic CORS configuration
  const isDevelopment = process.env.NODE_ENV !== 'production';
  
  const dynamicCorsOptions = {
    origin: (origin: string | undefined, callback: (error: Error | null, allow?: boolean) => void) => {
      // Allow requests with no origin (mobile apps, curl, etc.)
      if (!origin) return callback(null, true);
      
      // Development: allow any localhost/127.0.0.1 origin
      if (isDevelopment) {
        const isLocalhost = origin.includes('localhost') || 
                           origin.includes('127.0.0.1') || 
                           origin.includes('0.0.0.0');
        if (isLocalhost) return callback(null, true);
      }
      
      // Production: check against configured origins
      const allowedOrigins = [
        process.env.WEB_URL,
        process.env.APP_URL,
        process.env.FRONTEND_URL,
        'https://discordbot-energex-jkhvk.sevalla.app',
        'https://discordbot-energex-backend-nqzv2.sevalla.app',
      ].filter(Boolean);
      
      if (allowedOrigins.includes(origin)) {
        return callback(null, true);
      }
      
      // Log rejected origins for debugging
      logger.warn(`CORS: Rejected origin: ${origin}`);
      callback(new Error('Not allowed by CORS'));
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'X-CSRF-Token'],
    optionsSuccessStatus: 200, // For legacy browser support
  };
  
  app.enableCors(dynamicCorsOptions);

  const port = process.env.PORT || 8080;
  const host = process.env.HOST || '0.0.0.0';

  await app.listen(port, host);

  logger.log(`🚀 Application is running on: http://${host}:${port}`);
  logger.log(`📚 Swagger docs: http://${host}:${port}/docs`);
  logger.log(`🔗 Health check: http://${host}:${port}/api/health`);
  logger.log(`🤖 Discord bot is initializing...`);
}

bootstrap().catch((error) => {
  console.error('Failed to start application:', error);
  process.exit(1);
});