import { Injectable, Logger } from '@nestjs/common';
import { 
  IChannelService, 
  IDiscordService, 
  IMembershipService,
  ChannelTemplate, 
  MembershipTier,
  ChannelPermission 
} from './interfaces';

@Injectable()
export class ChannelService implements IChannelService {
  private readonly logger = new Logger(ChannelService.name);

  constructor(
    private readonly discordService: IDiscordService,
    private readonly membershipService: IMembershipService,
  ) {}

  async updateUserAccess(userId: string, guildId: string, tier: MembershipTier): Promise<void> {
    try {
      this.logger.log(`Updating channel access for user ${userId} to tier ${tier}`);

      // Get channels user should have access to
      const accessibleChannels = await this.membershipService.getTierChannels(guildId, tier);
      
      // Update permissions for each channel
      for (const channelId of accessibleChannels) {
        await this.grantChannelAccess(userId, channelId);
      }

      this.logger.log(`Channel access updated for user ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to update channel access for user ${userId}:`, error);
      throw error;
    }
  }

  async createTierChannels(guildId: string, tier: MembershipTier): Promise<string[]> {
    try {
      const templates = await this.getChannelTemplates(guildId, tier);
      const createdChannels: string[] = [];

      for (const template of templates) {
        if (template.autoCreate) {
          const channelId = await this.discordService.createChannel(guildId, {
            name: template.name,
            type: 'text',
            parent: template.category,
            permissions: template.permissions
          });
          
          createdChannels.push(channelId);
          this.logger.log(`Created tier channel: ${template.name} (${channelId})`);
        }
      }

      return createdChannels;
    } catch (error) {
      this.logger.error(`Failed to create tier channels for ${tier}:`, error);
      return [];
    }
  }

  async removeTierAccess(userId: string, guildId: string, oldTier: MembershipTier): Promise<void> {
    try {
      this.logger.log(`Removing ${oldTier} tier access for user ${userId}`);

      const restrictedChannels = await this.getTierRestrictedChannels(guildId, oldTier);
      
      for (const channelId of restrictedChannels) {
        await this.revokeChannelAccess(userId, channelId);
      }

      this.logger.log(`Removed tier access for user ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to remove tier access for user ${userId}:`, error);
      throw error;
    }
  }

  async getChannelTemplates(guildId: string, tier: MembershipTier): Promise<ChannelTemplate[]> {
    // In production, these would be stored in database
    const templates: ChannelTemplate[] = [
      {
        id: 'basic-welcome',
        name: 'welcome',
        description: 'Welcome channel for all members',
        requiredTier: 'basic',
        category: 'community',
        permissions: [
          {
            id: '@everyone',
            type: 'role',
            allow: ['view_channel', 'read_message_history'],
            deny: ['send_messages']
          }
        ],
        autoCreate: false
      },
      {
        id: 'premium-chat',
        name: '💎-premium-chat',
        description: 'Exclusive chat for premium members',
        requiredTier: 'premium',
        category: 'premium',
        permissions: [
          {
            id: '@everyone',
            type: 'role',
            allow: [],
            deny: ['view_channel']
          },
          {
            id: 'premium-role-id',
            type: 'role',
            allow: ['view_channel', 'send_messages', 'read_message_history'],
            deny: []
          }
        ],
        autoCreate: true
      },
      {
        id: 'ai-advanced',
        name: '🤖-ai-advanced',
        description: 'Advanced AI discussions',
        requiredTier: 'premium',
        category: 'ai-mastery',
        permissions: [
          {
            id: '@everyone',
            type: 'role',
            allow: [],
            deny: ['view_channel']
          },
          {
            id: 'premium-role-id',
            type: 'role',
            allow: ['view_channel', 'send_messages', 'read_message_history'],
            deny: []
          }
        ],
        autoCreate: true
      },
      {
        id: 'enterprise-solutions',
        name: '🏢-enterprise-solutions',
        description: 'Enterprise-level solutions and consulting',
        requiredTier: 'enterprise',
        category: 'enterprise',
        permissions: [
          {
            id: '@everyone',
            type: 'role',
            allow: [],
            deny: ['view_channel']
          },
          {
            id: 'enterprise-role-id',
            type: 'role',
            allow: ['view_channel', 'send_messages', 'read_message_history'],
            deny: []
          }
        ],
        autoCreate: true
      }
    ];

    return templates.filter((template: any) => this.canAccessTier(tier, template.requiredTier));
  }

  async createPrivateChannel(guildId: string, name: string, members: string[]): Promise<string> {
    try {
      // Create private channel with restricted permissions
      const channelId = await this.discordService.createChannel(guildId, {
        name: name.toLowerCase().replace(/[^a-z0-9-]/g, '-'),
        type: 'text',
        permissions: [
          {
            id: '@everyone',
            type: 'role',
            allow: [],
            deny: ['view_channel']
          },
          ...members.map((memberId: any) => ({
            id: memberId,
            type: 'member' as const,
            allow: ['view_channel', 'send_messages', 'read_message_history'],
            deny: []
          }))
        ]
      });

      this.logger.log(`Created private channel ${name} (${channelId}) for ${members.length} members`);
      return channelId;
    } catch (error) {
      this.logger.error(`Failed to create private channel ${name}:`, error);
      throw error;
    }
  }

  async archiveChannel(channelId: string): Promise<void> {
    try {
      // Archive by removing all permissions except administrators
      await this.discordService.updateChannelPermissions(channelId, [
        {
          id: '@everyone',
          type: 'role',
          allow: [],
          deny: ['view_channel', 'send_messages']
        }
      ]);

      this.logger.log(`Archived channel ${channelId}`);
    } catch (error) {
      this.logger.error(`Failed to archive channel ${channelId}:`, error);
      throw error;
    }
  }

  async getAccessibleChannels(userId: string, guildId: string): Promise<string[]> {
    try {
      // Get user's tier
      const userContext = await this.membershipService.getUserContext(userId, guildId);
      if (!userContext) {
        return [];
      }

      // Get channels for user's tier
      return await this.membershipService.getTierChannels(guildId, userContext.tier);
    } catch (error) {
      this.logger.error(`Failed to get accessible channels for user ${userId}:`, error);
      return [];
    }
  }

  private async grantChannelAccess(userId: string, channelId: string): Promise<void> {
    try {
      const permissions: ChannelPermission[] = [
        {
          id: userId,
          type: 'member',
          allow: ['view_channel', 'send_messages', 'read_message_history'],
          deny: []
        }
      ];

      await this.discordService.updateChannelPermissions(channelId, permissions);
    } catch (error) {
      this.logger.warn(`Failed to grant channel access to ${userId} for channel ${channelId}`);
    }
  }

  private async revokeChannelAccess(userId: string, channelId: string): Promise<void> {
    try {
      const permissions: ChannelPermission[] = [
        {
          id: userId,
          type: 'member',
          allow: [],
          deny: ['view_channel', 'send_messages', 'read_message_history']
        }
      ];

      await this.discordService.updateChannelPermissions(channelId, permissions);
    } catch (error) {
      this.logger.warn(`Failed to revoke channel access from ${userId} for channel ${channelId}`);
    }
  }

  private async getTierRestrictedChannels(guildId: string, tier: MembershipTier): Promise<string[]> {
    // Get channels that this tier shouldn't have access to
    const allChannels = await this.membershipService.getTierChannels(guildId, 'enterprise');
    const allowedChannels = await this.membershipService.getTierChannels(guildId, tier);
    
    return allChannels.filter((channelId: any) => !allowedChannels.includes(channelId));
  }

  private canAccessTier(userTier: MembershipTier, requiredTier: MembershipTier): boolean {
    const tierLevels = {
      basic: 1,
      premium: 2,
      enterprise: 3
    };
    
    return tierLevels[userTier] >= tierLevels[requiredTier];
  }
}