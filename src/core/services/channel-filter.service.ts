import { Injectable, Logger } from '@nestjs/common';
import { Channel, Message, GuildChannel } from 'discord.js';

@Injectable()
export class ChannelFilterService {
  private readonly logger = new Logger(ChannelFilterService.name);

  // Staff and restricted categories that should not receive bot responses
  private readonly EXCLUDED_CATEGORIES = {
    'STAFF': '1394712469867466905',
    'DEVELOPMENT': '1394721385774973071', 
    'SUPPORT': '1396352237315424278'
  };

  // Additional channel name patterns to exclude
  private readonly EXCLUDED_CHANNEL_PATTERNS = [
    /staff/i,
    /admin/i,
    /mod/i,
    /moderator/i,
    /management/i,
    /private/i,
    /restricted/i,
    /dev/i,
    /development/i,
    /support-staff/i,
    /team-only/i
  ];

  /**
   * Check if a channel is restricted (staff, admin, etc.) and should not receive bot responses
   */
  public isChannelRestricted(channel: Channel): boolean {
    // Handle DMs - usually allowed unless specifically restricted
    if (!('guild' in channel) || !channel.guild) {
      return false;
    }

    const guildChannel = channel as GuildChannel;

    // Check if channel is in an excluded category
    if (guildChannel.parent && this.isCategoryExcluded(guildChannel.parent.id)) {
      this.logger.debug(`Channel ${guildChannel.name} is in excluded category: ${guildChannel.parent.name}`);
      return true;
    }

    // Check channel name patterns
    if (this.hasRestrictedChannelName(guildChannel.name)) {
      this.logger.debug(`Channel ${guildChannel.name} matches restricted pattern`);
      return true;
    }

    return false;
  }

  /**
   * Check if a message should be processed by the bot (not in restricted channels)
   */
  public shouldProcessMessage(message: Message): boolean {
    // Always ignore bot messages
    if (message.author.bot) {
      return false;
    }

    // Check if channel is restricted
    if (this.isChannelRestricted(message.channel)) {
      this.logger.debug(`Skipping message processing in restricted channel: ${message.channel.id}`);
      return false;
    }

    return true;
  }

  /**
   * Check if a category is excluded from bot automation
   */
  private isCategoryExcluded(categoryId: string): boolean {
    return Object.values(this.EXCLUDED_CATEGORIES).includes(categoryId);
  }

  /**
   * Check if channel name matches any restricted patterns
   */
  private hasRestrictedChannelName(channelName: string): boolean {
    return this.EXCLUDED_CHANNEL_PATTERNS.some(pattern => pattern.test(channelName));
  }

  /**
   * Get list of excluded category IDs for external use
   */
  public getExcludedCategories(): Record<string, string> {
    return { ...this.EXCLUDED_CATEGORIES };
  }

  /**
   * Add temporary exclusion for a specific channel (for emergency situations)
   */
  private temporaryExclusions = new Set<string>();

  public addTemporaryExclusion(channelId: string, duration: number = 3600000): void {
    this.temporaryExclusions.add(channelId);
    this.logger.warn(`Temporarily excluding channel ${channelId} from bot responses`);
    
    // Auto-remove after duration (default 1 hour)
    setTimeout(() => {
      this.temporaryExclusions.delete(channelId);
      this.logger.log(`Removed temporary exclusion for channel ${channelId}`);
    }, duration);
  }

  public isTemporarilyExcluded(channelId: string): boolean {
    return this.temporaryExclusions.has(channelId);
  }
}