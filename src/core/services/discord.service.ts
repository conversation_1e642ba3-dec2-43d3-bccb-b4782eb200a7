import { Injectable, Logger } from '@nestjs/common';
import { Client, Guild, GuildMember, TextChannel, VoiceChannel, CategoryChannel, PermissionFlagsBits } from 'discord.js';
import { 
  IDiscordService, 
  ChannelConfig, 
  ChannelPermission, 
  MessageConfig 
} from './interfaces';

@Injectable()
export class DiscordService implements IDiscordService {
  private readonly logger = new Logger(DiscordService.name);

  constructor(private readonly client: Client) {}

  async assignRole(userId: string, roleId: string): Promise<void> {
    try {
      const guilds = this.client.guilds.cache;
      
      for (const guild of guilds.values()) {
        const member = await guild.members.fetch(userId).catch(() => null);
        if (member) {
          const role = guild.roles.cache.get(roleId);
          if (role && !member.roles.cache.has(roleId)) {
            await member.roles.add(role);
            this.logger.log(`Assigned role ${roleId} to user ${userId} in guild ${guild.id}`);
          }
        }
      }
    } catch (error) {
      this.logger.error(`Failed to assign role ${roleId} to user ${userId}:`, error);
      throw error;
    }
  }

  async removeRole(userId: string, roleId: string): Promise<void> {
    try {
      const guilds = this.client.guilds.cache;
      
      for (const guild of guilds.values()) {
        const member = await guild.members.fetch(userId).catch(() => null);
        if (member) {
          const role = guild.roles.cache.get(roleId);
          if (role && member.roles.cache.has(roleId)) {
            await member.roles.remove(role);
            this.logger.log(`Removed role ${roleId} from user ${userId} in guild ${guild.id}`);
          }
        }
      }
    } catch (error) {
      this.logger.error(`Failed to remove role ${roleId} from user ${userId}:`, error);
      throw error;
    }
  }

  async createChannel(guildId: string, config: ChannelConfig): Promise<string> {
    try {
      const guild = this.client.guilds.cache.get(guildId);
      if (!guild) {
        throw new Error(`Guild ${guildId} not found`);
      }

      const channelOptions: any = {
        name: config.name,
        type: this.getChannelType(config.type),
        topic: config.topic,
        parent: config.parent
      };

      const channel = await guild.channels.create(channelOptions);

      if (config.permissions && config.permissions.length > 0) {
        await this.updateChannelPermissions(channel.id, config.permissions);
      }

      this.logger.log(`Created channel ${channel.id} (${config.name}) in guild ${guildId}`);
      return channel.id;
    } catch (error) {
      this.logger.error(`Failed to create channel in guild ${guildId}:`, error);
      throw error;
    }
  }

  async updateChannelPermissions(channelId: string, permissions: ChannelPermission[]): Promise<void> {
    try {
      const channel = this.client.channels.cache.get(channelId);
      if (!channel || !('permissionOverwrites' in channel)) {
        throw new Error(`Channel ${channelId} not found or not a guild channel`);
      }

      for (const permission of permissions) {
        const allowPerms = this.convertPermissions(permission.allow);
        const denyPerms = this.convertPermissions(permission.deny);

        await channel.permissionOverwrites.set([{
          id: permission.id,
          allow: allowPerms,
          deny: denyPerms
        }]);
      }

      this.logger.log(`Updated permissions for channel ${channelId}`);
    } catch (error) {
      this.logger.error(`Failed to update channel permissions for ${channelId}:`, error);
      throw error;
    }
  }

  async sendMessage(channelId: string, config: MessageConfig): Promise<string> {
    try {
      const channel = this.client.channels.cache.get(channelId) as TextChannel;
      if (!channel || !channel.isTextBased()) {
        throw new Error(`Text channel ${channelId} not found`);
      }

      const message = await channel.send({
        content: config.content,
        embeds: config.embeds,
        components: config.components
      });

      return message.id;
    } catch (error) {
      this.logger.error(`Failed to send message to channel ${channelId}:`, error);
      throw error;
    }
  }

  async sendDirectMessage(userId: string, config: MessageConfig): Promise<void> {
    try {
      const user = await this.client.users.fetch(userId);
      await user.send({
        content: config.content,
        embeds: config.embeds,
        components: config.components
      });

      this.logger.log(`Sent DM to user ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to send DM to user ${userId}:`, error);
      throw error;
    }
  }

  async createPrivateThread(channelId: string, name: string, members: string[]): Promise<string> {
    try {
      const channel = this.client.channels.cache.get(channelId) as TextChannel;
      if (!channel || !channel.isTextBased()) {
        throw new Error(`Text channel ${channelId} not found`);
      }

      const thread = await channel.threads.create({
        name,
        type: 12, // GUILD_PRIVATE_THREAD
        invitable: false
      });

      // Add members to the private thread
      for (const memberId of members) {
        try {
          await thread.members.add(memberId);
        } catch (memberError) {
          this.logger.warn(`Failed to add member ${memberId} to thread ${thread.id}`);
        }
      }

      this.logger.log(`Created private thread ${thread.id} (${name}) in channel ${channelId}`);
      return thread.id;
    } catch (error) {
      this.logger.error(`Failed to create private thread in channel ${channelId}:`, error);
      throw error;
    }
  }

  async getUserRoles(guildId: string, userId: string): Promise<string[]> {
    try {
      const guild = this.client.guilds.cache.get(guildId);
      if (!guild) {
        throw new Error(`Guild ${guildId} not found`);
      }

      const member = await guild.members.fetch(userId);
      return member.roles.cache.map((role: any) => role.id);
    } catch (error) {
      this.logger.error(`Failed to get user roles for ${userId} in guild ${guildId}:`, error);
      return [];
    }
  }

  async getGuildMember(guildId: string, userId: string): Promise<any> {
    try {
      const guild = this.client.guilds.cache.get(guildId);
      if (!guild) {
        throw new Error(`Guild ${guildId} not found`);
      }

      const member = await guild.members.fetch(userId);
      return {
        id: member.id,
        username: member.user.username,
        displayName: member.displayName,
        joinedAt: member.joinedAt,
        roles: member.roles.cache.map((role: any) => role.id),
        permissions: member.permissions.toArray()
      };
    } catch (error) {
      this.logger.error(`Failed to get guild member ${userId} in guild ${guildId}:`, error);
      return null;
    }
  }

  private getChannelType(type: string): number {
    const typeMap = {
      'text': 0,
      'voice': 2,
      'category': 4
    };
    return ((typeMap as any)[type]) || 0;
  }

  private convertPermissions(permissions: string[]): bigint {
    let permissionBits = 0n;
    
    for (const permission of permissions) {
      switch (permission.toLowerCase()) {
        case 'view_channel':
          permissionBits |= PermissionFlagsBits.ViewChannel;
          break;
        case 'send_messages':
          permissionBits |= PermissionFlagsBits.SendMessages;
          break;
        case 'read_message_history':
          permissionBits |= PermissionFlagsBits.ReadMessageHistory;
          break;
        case 'connect':
          permissionBits |= PermissionFlagsBits.Connect;
          break;
        case 'speak':
          permissionBits |= PermissionFlagsBits.Speak;
          break;
        case 'manage_channels':
          permissionBits |= PermissionFlagsBits.ManageChannels;
          break;
        case 'manage_roles':
          permissionBits |= PermissionFlagsBits.ManageRoles;
          break;
      }
    }
    
    return permissionBits;
  }
}