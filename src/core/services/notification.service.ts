import { Injectable, Logger } from '@nestjs/common';
import { 
  INotificationService, 
  IDiscordService,
  NotificationTemplate, 
  NotificationContext, 
  MembershipTier,
  MessageConfig 
} from './interfaces';

@Injectable()
export class NotificationService implements INotificationService {
  private readonly logger = new Logger(NotificationService.name);

  constructor(private readonly discordService: IDiscordService) {}

  async sendWelcomeMessage(userId: string, guildId: string, tier: MembershipTier): Promise<void> {
    try {
      const welcomeMessage = this.buildWelcomeMessage(tier);
      await this.discordService.sendDirectMessage(userId, welcomeMessage);
      
      this.logger.log(`Sent welcome message to user ${userId} (${tier} tier)`);
    } catch (error) {
      this.logger.error(`Failed to send welcome message to user ${userId}:`, error);
    }
  }

  async sendTierChangeNotification(context: NotificationContext): Promise<void> {
    try {
      const notification = this.buildTierChangeMessage(context);
      await this.discordService.sendDirectMessage(context.userId, notification);
      
      this.logger.log(`Sent tier change notification to user ${context.userId} (${context.previousTier} → ${context.tier})`);
    } catch (error) {
      this.logger.error(`Failed to send tier change notification to user ${context.userId}:`, error);
    }
  }

  async sendFeatureUnlockNotification(userId: string, features: string[]): Promise<void> {
    try {
      const notification = this.buildFeatureUnlockMessage(features);
      await this.discordService.sendDirectMessage(userId, notification);
      
      this.logger.log(`Sent feature unlock notification to user ${userId} for ${features.length} features`);
    } catch (error) {
      this.logger.error(`Failed to send feature unlock notification to user ${userId}:`, error);
    }
  }

  async sendProjectMatchNotification(clientId: string, developerId: string, projectId: string): Promise<void> {
    try {
      // Notify client
      const clientMessage = this.buildProjectMatchMessage('client', projectId, developerId);
      await this.discordService.sendDirectMessage(clientId, clientMessage);

      // Notify developer
      const developerMessage = this.buildProjectMatchMessage('developer', projectId, clientId);
      await this.discordService.sendDirectMessage(developerId, developerMessage);
      
      this.logger.log(`Sent project match notifications for project ${projectId}`);
    } catch (error) {
      this.logger.error(`Failed to send project match notifications for project ${projectId}:`, error);
    }
  }

  async sendCustomNotification(channelId: string, template: MessageConfig): Promise<void> {
    try {
      await this.discordService.sendMessage(channelId, template);
      this.logger.log(`Sent custom notification to channel ${channelId}`);
    } catch (error) {
      this.logger.error(`Failed to send custom notification to channel ${channelId}:`, error);
    }
  }

  async getNotificationTemplates(guildId: string, type: string): Promise<NotificationTemplate[]> {
    // In production, these would be stored in database
    const templates: NotificationTemplate[] = [
      {
        id: 'welcome-basic',
        name: 'Basic Welcome',
        type: 'welcome',
        template: this.buildWelcomeMessage('basic'),
        triggers: ['membership.created'],
        targetTiers: ['basic']
      },
      {
        id: 'welcome-premium',
        name: 'Premium Welcome',
        type: 'welcome',
        template: this.buildWelcomeMessage('premium'),
        triggers: ['membership.created'],
        targetTiers: ['premium']
      },
      {
        id: 'welcome-enterprise',
        name: 'Enterprise Welcome',
        type: 'welcome',
        template: this.buildWelcomeMessage('enterprise'),
        triggers: ['membership.created'],
        targetTiers: ['enterprise']
      }
    ];

    return templates.filter((template: any) => template.type === type);
  }

  private buildWelcomeMessage(tier: MembershipTier): MessageConfig {
    const tierMessages = {
      basic: {
        title: '🎉 Welcome to EnergeX - Basic Tier!',
        description: 'Welcome to the EnergeX community! You\'re now part of an exclusive group focused on AI mastery, wealth creation, and personal growth.',
        fields: [
          {
            name: '🤖 AI Mastery',
            value: '• Access to GPT-3.5 Turbo\n• Basic AI tutorials\n• Automation guidance',
            inline: true
          },
          {
            name: '📚 Community Access',
            value: '• General discussions\n• Community support\n• Basic resources',
            inline: true
          },
          {
            name: '🚀 Next Steps',
            value: '• Explore the AI tools channels\n• Join community discussions\n• Consider upgrading for more features',
            inline: false
          }
        ],
        color: 0x3498db
      },
      premium: {
        title: '💎 Welcome to EnergeX - Premium Tier!',
        description: 'Welcome to Premium! You now have access to advanced AI models, wealth strategies, and personal growth coaching.',
        fields: [
          {
            name: '🤖 Advanced AI Access',
            value: '• GPT-4o Mini & Claude 3 Haiku\n• Advanced tutorials\n• Higher usage limits',
            inline: true
          },
          {
            name: '💰 Wealth Creation',
            value: '• SaaS development strategies\n• AI automation services\n• Investment guidance',
            inline: true
          },
          {
            name: '🌱 Personal Growth',
            value: '• 1-on-1 coaching sessions\n• Goal tracking systems\n• Accountability partners',
            inline: true
          },
          {
            name: '⚡ Dev On Demand',
            value: '• Submit project requests\n• Connect with developers\n• Priority support',
            inline: false
          }
        ],
        color: 0xf39c12
      },
      enterprise: {
        title: '🏢 Welcome to EnergeX - Enterprise Tier!',
        description: 'Welcome to Enterprise! You have access to our most advanced features, including unlimited AI access and dedicated development support.',
        fields: [
          {
            name: '🚀 Unlimited AI Access',
            value: '• GPT-4o & Claude 3.5 Sonnet\n• Unlimited usage\n• Custom model access',
            inline: true
          },
          {
            name: '💼 Business Solutions',
            value: '• AI consulting frameworks\n• White-label solutions\n• Custom development',
            inline: true
          },
          {
            name: '👨‍💻 Dev On Demand Pro',
            value: '• Become a developer\n• Access premium projects\n• Technical consulting',
            inline: true
          },
          {
            name: '🎯 Executive Support',
            value: '• Personal success strategist\n• Executive coaching\n• Custom growth plans',
            inline: false
          }
        ],
        color: 0x9b59b6
      }
    };

    const messageData = tierMessages[tier];
    
    return {
      embeds: [{
        title: messageData.title,
        description: messageData.description,
        fields: messageData.fields,
        color: messageData.color,
        thumbnail: {
          url: 'https://your-bot-domain.com/energex-logo.png'
        },
        footer: {
          text: 'EnergeX Community • AI Mastery • Wealth Creation • Personal Growth'
        },
        timestamp: new Date().toISOString()
      }]
    };
  }

  private buildTierChangeMessage(context: NotificationContext): MessageConfig {
    const isUpgrade = this.isUpgrade(context.previousTier, context.tier);
    const emoji = isUpgrade ? '🎉' : '📉';
    const action = isUpgrade ? 'upgraded' : 'changed';
    
    return {
      embeds: [{
        title: `${emoji} Membership ${action.charAt(0).toUpperCase() + action.slice(1)}!`,
        description: `Your membership tier has been ${action} from **${context.previousTier}** to **${context.tier}**.`,
        fields: [
          {
            name: '✨ What\'s New',
            value: this.getNewFeatures(context.previousTier, context.tier),
            inline: false
          }
        ],
        color: isUpgrade ? 0x2ecc71 : 0xe67e22,
        timestamp: new Date().toISOString()
      }]
    };
  }

  private buildFeatureUnlockMessage(features: string[]): MessageConfig {
    return {
      embeds: [{
        title: '🔓 New Features Unlocked!',
        description: 'Your membership upgrade has unlocked new features:',
        fields: [
          {
            name: '🆕 Available Now',
            value: features.map((feature: any) => `• ${this.formatFeatureName(feature)}`).join('\n'),
            inline: false
          }
        ],
        color: 0x2ecc71,
        timestamp: new Date().toISOString()
      }]
    };
  }

  private buildProjectMatchMessage(recipient: 'client' | 'developer', projectId: string, otherUserId: string): MessageConfig {
    if (recipient === 'client') {
      return {
        embeds: [{
          title: '🎯 Developer Matched to Your Project!',
          description: `Great news! A developer has been matched to your project **${projectId}**.`,
          fields: [
            {
              name: 'Next Steps',
              value: '• Check the private project channel\n• Discuss requirements with your developer\n• Review project timeline and milestones',
              inline: false
            }
          ],
          color: 0x2ecc71,
          timestamp: new Date().toISOString()
        }]
      };
    } else {
      return {
        embeds: [{
          title: '💼 New Project Assignment!',
          description: `You've been matched to project **${projectId}**.`,
          fields: [
            {
              name: 'Next Steps',
              value: '• Join the private project channel\n• Review project requirements\n• Begin initial client consultation',
              inline: false
            }
          ],
          color: 0x3498db,
          timestamp: new Date().toISOString()
        }]
      };
    }
  }

  private isUpgrade(oldTier?: MembershipTier, newTier?: MembershipTier): boolean {
    if (!oldTier || !newTier) return false;
    
    const tierLevels = { basic: 1, premium: 2, enterprise: 3 };
    return tierLevels[newTier] > tierLevels[oldTier];
  }

  private getNewFeatures(oldTier?: MembershipTier, newTier?: MembershipTier): string {
    if (!oldTier || !newTier) return 'New membership features available!';
    
    if (oldTier === 'basic' && newTier === 'premium') {
      return '• Advanced AI models\n• Wealth creation strategies\n• Personal growth coaching\n• Dev On Demand client access';
    }
    
    if (oldTier === 'premium' && newTier === 'enterprise') {
      return '• Unlimited AI access\n• Developer marketplace access\n• Executive coaching\n• Custom solutions';
    }
    
    if (oldTier === 'basic' && newTier === 'enterprise') {
      return '• Full AI model access\n• Complete wealth & growth programs\n• Developer marketplace\n• Enterprise support';
    }
    
    return 'Access to new tier features!';
  }

  private formatFeatureName(feature: string): string {
    return feature
      .replace(/_/g, ' ')
      .replace(/\b\w/g, char => char.toUpperCase());
  }
}