import { Injectable, Logger } from '@nestjs/common';
import { 
  IAIAgentService, 
  AgentType, 
  AgentResponse, 
  MessageContext, 
  MembershipTier 
} from './interfaces';

@Injectable()
export class AIAgentService implements IAIAgentService {
  private readonly logger = new Logger(AIAgentService.name);

  async routeMessage(context: MessageContext): Promise<AgentResponse> {
    try {
      // Determine appropriate agent
      const agentType = await this.selectAgent(
        context.userContext.tier, 
        context.content, 
        context.channelId
      );

      // Process with selected agent
      return await this.processWithAgent(agentType, context);
    } catch (error) {
      this.logger.error('Failed to route message:', error);
      return {
        content: "I'm sorry, I'm having trouble processing your request right now. Please try again later."
      };
    }
  }

  async selectAgent(userTier: MembershipTier, topic: string, channelId: string): Promise<AgentType> {
    const topicLower = topic.toLowerCase();
    
    // Channel-based routing
    if (channelId.includes('ai-tools') || channelId.includes('ai-tutorials')) {
      return 'ai-mastery';
    }
    
    if (channelId.includes('money') || channelId.includes('wealth') || channelId.includes('business')) {
      return 'wealth-creation';
    }
    
    if (channelId.includes('growth') || channelId.includes('mindset') || channelId.includes('goals')) {
      return 'personal-growth';
    }
    
    if (channelId.includes('dev') || channelId.includes('project')) {
      return 'dev-support';
    }

    // Content-based routing
    const aiKeywords = ['ai', 'artificial intelligence', 'chatgpt', 'claude', 'automation', 'gpt', 'llm'];
    const wealthKeywords = ['money', 'income', 'profit', 'business', 'revenue', 'wealth', 'entrepreneur'];
    const growthKeywords = ['mindset', 'goals', 'personal', 'development', 'coaching', 'habits'];
    const devKeywords = ['code', 'programming', 'development', 'tech', 'project', 'build'];

    if (aiKeywords.some(keyword => topicLower.includes(keyword))) {
      return 'ai-mastery';
    }
    
    if (wealthKeywords.some(keyword => topicLower.includes(keyword))) {
      return 'wealth-creation';
    }
    
    if (growthKeywords.some(keyword => topicLower.includes(keyword))) {
      return 'personal-growth';
    }
    
    if (devKeywords.some(keyword => topicLower.includes(keyword))) {
      return 'dev-support';
    }

    // Default to intake specialist for new users or unclear requests
    return 'intake-specialist';
  }

  async processWithAgent(agentType: AgentType, context: MessageContext): Promise<AgentResponse> {
    // Check if user has access to this agent
    const hasAccess = await this.isAgentAvailable(agentType, context.userContext.tier);
    
    if (!hasAccess) {
      return this.getUpgradePrompt(agentType, context.userContext.tier);
    }

    switch (agentType) {
      case 'ai-mastery':
        return await this.processAIMasteryAgent(context);
      case 'wealth-creation':
        return await this.processWealthCreationAgent(context);
      case 'personal-growth':
        return await this.processPersonalGrowthAgent(context);
      case 'dev-support':
        return await this.processDevSupportAgent(context);
      case 'intake-specialist':
        return await this.processIntakeSpecialist(context);
      default:
        return {
          content: "I'm not sure how to help with that. Could you please rephrase your question?"
        };
    }
  }

  async getAvailableAgents(tier: MembershipTier): Promise<AgentType[]> {
    const agentAccess: Record<MembershipTier, AgentType[]> = {
      basic: ['intake-specialist', 'ai-mastery'],
      premium: ['intake-specialist', 'ai-mastery', 'wealth-creation', 'personal-growth'],
      enterprise: ['intake-specialist', 'ai-mastery', 'wealth-creation', 'personal-growth', 'dev-support']
    };
    
    return agentAccess[tier] || agentAccess.basic;
  }

  async isAgentAvailable(agentType: AgentType, tier: MembershipTier): Promise<boolean> {
    const availableAgents = await this.getAvailableAgents(tier);
    return availableAgents.includes(agentType);
  }

  private async processAIMasteryAgent(context: MessageContext): Promise<AgentResponse> {
    const availableModels = context.userContext.features.filter((f: any) => 
      f.includes('gpt') || f.includes('claude')
    );

    return {
      content: `🤖 **AI Mastery Assistant**

I'm here to help you master AI tools and automation! Based on your ${context.userContext.tier} membership, you have access to: ${availableModels.join(', ')}.

${this.getAIGuidance(context.content, context.userContext.tier)}`
    };
  }

  private async processWealthCreationAgent(context: MessageContext): Promise<AgentResponse> {
    return {
      content: `💰 **Wealth Creation Coach**

Let's build your path to financial freedom! As a ${context.userContext.tier} member, I can help you with:

${this.getWealthGuidance(context.content, context.userContext.tier)}`
    };
  }

  private async processPersonalGrowthAgent(context: MessageContext): Promise<AgentResponse> {
    return {
      content: `🌱 **Personal Growth Mentor**

Ready to level up your life? Here's how I can support your growth journey:

${this.getGrowthGuidance(context.content, context.userContext.tier)}`
    };
  }

  private async processDevSupportAgent(context: MessageContext): Promise<AgentResponse> {
    if (context.userContext.tier !== 'enterprise') {
      return this.getUpgradePrompt('dev-support', context.userContext.tier);
    }

    return {
      content: `⚡ **Dev On Demand Support**

Enterprise-level development support activated! I can help you with:

- Project planning and architecture
- Developer matching for your projects
- Technical consultation
- Code reviews and optimization

What development challenge can I help you solve?`,
      actionRequired: {
        type: 'create_channel',
        data: { name: `dev-support-${context.userId}` }
      }
    };
  }

  private async processIntakeSpecialist(context: MessageContext): Promise<AgentResponse> {
    return {
      content: `👋 **Welcome! I'm your Intake Specialist**

I help new members find exactly what they need. Based on your message, I can connect you with the right specialist:

- 🤖 **AI Mastery** - Learn automation and AI tools
- 💰 **Wealth Creation** - Build income streams
- 🌱 **Personal Growth** - Develop mindset and habits
- ⚡ **Dev On Demand** - Get custom development (Premium+)

What's your main goal right now?`,
      shouldEscalate: true,
      nextAgent: this.determineNextAgent(context.content)
    };
  }

  private getAIGuidance(content: string, tier: MembershipTier): string {
    const guidance = {
      basic: "I can help you get started with ChatGPT and basic automation. What specific AI task would you like to learn?",
      premium: "I can teach you advanced prompt engineering, GPT-4 strategies, and Claude techniques. What's your AI goal?",
      enterprise: "I can provide custom AI solutions, model fine-tuning guidance, and enterprise automation. What complex challenge can I solve?"
    };
    
    return guidance[tier];
  }

  private getWealthGuidance(content: string, tier: MembershipTier): string {
    const guidance = {
      basic: "- AI freelancing opportunities\n- Content creation strategies\n- Basic monetization methods",
      premium: "- SaaS development plans\n- AI automation services\n- Course creation guidance\n- Investment strategies",
      enterprise: "- AI consulting frameworks\n- White-label solutions\n- Advanced investment strategies\n- Business scaling systems"
    };
    
    return guidance[tier];
  }

  private getGrowthGuidance(content: string, tier: MembershipTier): string {
    const guidance = {
      basic: "- Goal setting frameworks\n- Basic habit formation\n- Mindset coaching",
      premium: "- Advanced goal tracking\n- 1-on-1 coaching sessions\n- Accountability partnerships\n- Success mindset development",
      enterprise: "- Personal success strategist\n- Executive coaching\n- Custom growth plans\n- Leadership development"
    };
    
    return guidance[tier];
  }

  private getUpgradePrompt(agentType: AgentType, currentTier: MembershipTier): AgentResponse {
    const requiredTier = this.getRequiredTier(agentType);
    
    return {
      content: `🔒 **Upgrade Required**

The ${agentType.replace('-', ' ')} specialist requires a ${requiredTier} membership. 

Your current tier: ${currentTier}
Required tier: ${requiredTier}

Upgrade now to unlock this feature and many more! Visit our Whop store to upgrade.`
    };
  }

  private getRequiredTier(agentType: AgentType): MembershipTier {
    const tierRequirements: Record<AgentType, MembershipTier> = {
      'intake-specialist': 'basic',
      'ai-mastery': 'basic',
      'wealth-creation': 'premium',
      'personal-growth': 'premium',
      'dev-support': 'enterprise'
    };
    
    return tierRequirements[agentType] || 'basic';
  }

  private determineNextAgent(content: string): AgentType | undefined {
    const topicLower = content.toLowerCase();
    
    if (topicLower.includes('ai') || topicLower.includes('automation')) {
      return 'ai-mastery';
    }
    
    if (topicLower.includes('money') || topicLower.includes('business')) {
      return 'wealth-creation';
    }
    
    if (topicLower.includes('growth') || topicLower.includes('mindset')) {
      return 'personal-growth';
    }
    
    if (topicLower.includes('dev') || topicLower.includes('project')) {
      return 'dev-support';
    }
    
    return undefined;
  }
}