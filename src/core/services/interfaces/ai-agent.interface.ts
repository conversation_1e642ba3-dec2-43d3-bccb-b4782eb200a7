import { MembershipTier, UserContext } from './membership.interface';

export type AgentType = 'ai-mastery' | 'wealth-creation' | 'personal-growth' | 'dev-support' | 'intake-specialist';

export interface AgentResponse {
  content: string;
  shouldEscalate?: boolean;
  nextAgent?: AgentType;
  actionRequired?: {
    type: 'create_channel' | 'assign_role' | 'schedule_followup';
    data: any;
  };
}

export interface MessageContext {
  content: string;
  userId: string;
  guildId: string;
  channelId: string;
  userContext: UserContext;
  conversationHistory?: string[];
}

export interface IAIAgentService {
  routeMessage(context: MessageContext): Promise<AgentResponse>;
  selectAgent(userTier: MembershipTier, topic: string, channelId: string): Promise<AgentType>;
  processWithAgent(agentType: AgentType, context: MessageContext): Promise<AgentResponse>;
  getAvailableAgents(tier: MembershipTier): Promise<AgentType[]>;
  isAgentAvailable(agentType: AgentType, tier: MembershipTier): Promise<boolean>;
}