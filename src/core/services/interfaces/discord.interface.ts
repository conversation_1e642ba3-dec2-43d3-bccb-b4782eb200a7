export interface ChannelConfig {
  name: string;
  type: 'text' | 'voice' | 'category';
  parent?: string;
  permissions?: ChannelPermission[];
  topic?: string;
}

export interface ChannelPermission {
  id: string;
  type: 'role' | 'member';
  allow: string[];
  deny: string[];
}

export interface MessageConfig {
  content?: string;
  embeds?: any[];
  components?: any[];
}

export interface IDiscordService {
  assignRole(userId: string, roleId: string): Promise<void>;
  removeRole(userId: string, roleId: string): Promise<void>;
  createChannel(guildId: string, config: ChannelConfig): Promise<string>;
  updateChannelPermissions(channelId: string, permissions: ChannelPermission[]): Promise<void>;
  sendMessage(channelId: string, config: MessageConfig): Promise<string>;
  sendDirectMessage(userId: string, config: MessageConfig): Promise<void>;
  createPrivateThread(channelId: string, name: string, members: string[]): Promise<string>;
  getUserRoles(guildId: string, userId: string): Promise<string[]>;
  getGuildMember(guildId: string, userId: string): Promise<any>;
}