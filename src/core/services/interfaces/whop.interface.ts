export type MembershipTier = 'basic' | 'premium' | 'enterprise';

export interface WhopMembership {
  id: string;
  userId: string;
  tier: MembershipTier;
  status: 'active' | 'cancelled' | 'expired';
  features: string[];
  limits: {
    aiModels: string[];
    dailyTokens: number;
    requestsPerHour: number;
  };
  subscriptionId?: string;
  expiresAt?: Date;
}

export interface WhopWebhookPayload {
  type: 'membership.created' | 'membership.updated' | 'membership.cancelled';
  data: {
    membership: WhopMembership;
    user: {
      id: string;
      discordId?: string;
      email: string;
    };
  };
}

export interface IWhopService {
  validateMembership(userId: string): Promise<WhopMembership | null>;
  getUserTier(userId: string): Promise<MembershipTier>;
  processWebhook(payload: WhopWebhookPayload): Promise<void>;
  getUserFeatures(userId: string): Promise<string[]>;
  checkAccess(userId: string, feature: string): Promise<boolean>;
  createPaymentEscrow(requestId: string, clientId: string, developerId: string, amount: number): Promise<any>;
  completeMilestone(escrowId: string, milestoneId: string, userId: string): Promise<boolean>;
  verifyClientAccess(userId: string): Promise<boolean>;
  verifyDeveloperAccess(userId: string): Promise<boolean>;
}