import {
  Controller,
  Get,
  Post,
  Delete,
  Put,
  Param,
  Body,
  Query,
  HttpStatus,
  HttpException,
  UseGuards,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBody,
  ApiBearerAuth,
} from '@nestjs/swagger';

import { NgrokService } from '../services/ngrok.service';
import { NgrokDiscordService } from '../services/ngrok-discord.service';
import { NgrokHealthMonitorService } from '../services/ngrok-health-monitor.service';
import { NgrokPersistenceService } from '../services/ngrok-persistence.service';
import { NgrokLoggingService } from '../services/ngrok-logging.service';
import {
  TunnelConfig,
  TunnelStatus,
  NgrokHealthResult,
  TunnelEvent,
  NgrokDiscordIntegration,
  TunnelConfigSchema,
} from '../interfaces/ngrok.interface';

// DTOs for API requests
class CreateTunnelDto {
  id: string;
  proto: 'http' | 'https' | 'tcp' | 'tls' = 'http';
  addr: string | number;
  domain?: string;
  subdomain?: string;
  region?: 'us' | 'eu' | 'ap' | 'au' | 'sa' | 'jp' | 'in';
  basicAuth?: string;
  oauthProvider?: 'google' | 'github' | 'microsoft';
  oauthAllowDomains?: string[];
  ipRestrictions?: string[];
  circuitBreaker?: number;
  compression?: boolean;
  websocketTcpConverter?: boolean;
  metadata?: Record<string, string>;
}

class UpdateTunnelDto {
  domain?: string;
  subdomain?: string;
  basicAuth?: string;
  ipRestrictions?: string[];
  metadata?: Record<string, string>;
}

@ApiTags('ngrok')
@Controller('ngrok')
@ApiBearerAuth()
export class NgrokController {
  private readonly logger = new Logger(NgrokController.name);

  constructor(
    private readonly ngrokService: NgrokService,
    private readonly ngrokDiscordService: NgrokDiscordService,
    private readonly ngrokHealthMonitorService: NgrokHealthMonitorService,
    private readonly ngrokPersistenceService: NgrokPersistenceService,
    private readonly ngrokLoggingService: NgrokLoggingService,
    private readonly configService: ConfigService,
  ) {}

  @Get('status')
  @ApiOperation({ summary: 'Get overall ngrok service status' })
  @ApiResponse({ status: 200, description: 'Service status retrieved successfully' })
  async getStatus() {
    try {
      const isEnabled = this.ngrokService.isEnabled();
      const config = this.ngrokService.getConfig();
      const health = isEnabled ? await this.ngrokService.getHealthStatus() : null;

      return {
        enabled: isEnabled,
        initialized: isEnabled,
        environment: config.environment,
        region: config.region,
        health,
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error('Failed to get ngrok status', error);
      throw new HttpException(
        'Failed to retrieve ngrok status',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('health')
  @ApiOperation({ summary: 'Get detailed health status of all tunnels' })
  @ApiResponse({ status: 200, description: 'Health status retrieved successfully' })
  async getHealth(): Promise<NgrokHealthResult> {
    try {
      if (!this.ngrokService.isEnabled()) {
        throw new HttpException('NgrokService is not enabled', HttpStatus.SERVICE_UNAVAILABLE);
      }

      return await this.ngrokService.getHealthStatus();
    } catch (error) {
      this.logger.error('Failed to get ngrok health status', error);
      throw new HttpException(
        'Failed to retrieve health status',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('tunnels')
  @ApiOperation({ summary: 'Get all active tunnels' })
  @ApiResponse({ status: 200, description: 'Tunnels retrieved successfully' })
  async getAllTunnels(): Promise<TunnelStatus[]> {
    try {
      return await this.ngrokService.getAllTunnelStatuses();
    } catch (error) {
      this.logger.error('Failed to get tunnels', error);
      throw new HttpException(
        'Failed to retrieve tunnels',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('tunnels/:id')
  @ApiOperation({ summary: 'Get specific tunnel status' })
  @ApiParam({ name: 'id', description: 'Tunnel ID' })
  @ApiResponse({ status: 200, description: 'Tunnel status retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Tunnel not found' })
  async getTunnel(@Param('id') tunnelId: string): Promise<TunnelStatus> {
    try {
      const tunnel = await this.ngrokService.getTunnelStatus(tunnelId);
      if (!tunnel) {
        throw new HttpException(`Tunnel '${tunnelId}' not found`, HttpStatus.NOT_FOUND);
      }
      return tunnel;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error(`Failed to get tunnel '${tunnelId}'`, error);
      throw new HttpException(
        'Failed to retrieve tunnel',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('tunnels')
  @ApiOperation({ summary: 'Create a new tunnel' })
  @ApiBody({ type: CreateTunnelDto })
  @ApiResponse({ status: 201, description: 'Tunnel created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid tunnel configuration' })
  @ApiResponse({ status: 409, description: 'Tunnel with this ID already exists' })
  async createTunnel(@Body() createTunnelDto: CreateTunnelDto): Promise<{ url: string; tunnel: TunnelStatus }> {
    try {
      // Validate the configuration using Zod
      const validatedConfig = TunnelConfigSchema.parse(createTunnelDto);

      // Create the tunnel
      const url = await this.ngrokService.createTunnel(validatedConfig);
      
      // Get the tunnel status
      const tunnel = await this.ngrokService.getTunnelStatus(validatedConfig.id);
      
      if (!tunnel) {
        throw new HttpException(
          'Tunnel created but status unavailable',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }

      return { url, tunnel };
    } catch (error) {
      if (error.name === 'ZodError') {
        throw new HttpException(
          `Invalid tunnel configuration: ${error.errors.map((e: any) => e.message).join(', ')}`,
          HttpStatus.BAD_REQUEST,
        );
      }
      if (error.message?.includes('already exists')) {
        throw new HttpException(error.message, HttpStatus.CONFLICT);
      }
      
      this.logger.error('Failed to create tunnel', error);
      throw new HttpException(
        'Failed to create tunnel',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Put('tunnels/:id/restart')
  @ApiOperation({ summary: 'Restart a tunnel' })
  @ApiParam({ name: 'id', description: 'Tunnel ID' })
  @ApiResponse({ status: 200, description: 'Tunnel restarted successfully' })
  @ApiResponse({ status: 404, description: 'Tunnel not found' })
  async restartTunnel(@Param('id') tunnelId: string): Promise<{ url: string; tunnel: TunnelStatus }> {
    try {
      const url = await this.ngrokService.restartTunnel(tunnelId);
      const tunnel = await this.ngrokService.getTunnelStatus(tunnelId);
      
      if (!tunnel) {
        throw new HttpException(
          'Tunnel restarted but status unavailable',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }

      return { url, tunnel };
    } catch (error) {
      if (error.message?.includes('not found')) {
        throw new HttpException(`Tunnel '${tunnelId}' not found`, HttpStatus.NOT_FOUND);
      }
      
      this.logger.error(`Failed to restart tunnel '${tunnelId}'`, error);
      throw new HttpException(
        'Failed to restart tunnel',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete('tunnels/:id')
  @ApiOperation({ summary: 'Close a tunnel' })
  @ApiParam({ name: 'id', description: 'Tunnel ID' })
  @ApiResponse({ status: 200, description: 'Tunnel closed successfully' })
  @ApiResponse({ status: 404, description: 'Tunnel not found' })
  async closeTunnel(@Param('id') tunnelId: string): Promise<{ message: string }> {
    try {
      await this.ngrokService.closeTunnel(tunnelId);
      return { message: `Tunnel '${tunnelId}' closed successfully` };
    } catch (error) {
      if (error.message?.includes('not found')) {
        throw new HttpException(`Tunnel '${tunnelId}' not found`, HttpStatus.NOT_FOUND);
      }
      
      this.logger.error(`Failed to close tunnel '${tunnelId}'`, error);
      throw new HttpException(
        'Failed to close tunnel',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete('tunnels')
  @ApiOperation({ summary: 'Close all tunnels' })
  @ApiResponse({ status: 200, description: 'All tunnels closed successfully' })
  async closeAllTunnels(): Promise<{ message: string }> {
    try {
      await this.ngrokService.closeAllTunnels();
      return { message: 'All tunnels closed successfully' };
    } catch (error) {
      this.logger.error('Failed to close all tunnels', error);
      throw new HttpException(
        'Failed to close all tunnels',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('events')
  @ApiOperation({ summary: 'Get tunnel events' })
  @ApiQuery({ name: 'limit', required: false, description: 'Maximum number of events to return' })
  @ApiResponse({ status: 200, description: 'Events retrieved successfully' })
  async getEvents(@Query('limit') limit?: string): Promise<TunnelEvent[]> {
    try {
      const eventLimit = limit ? parseInt(limit) : 100;
      return this.ngrokService.getEvents(eventLimit);
    } catch (error) {
      this.logger.error('Failed to get events', error);
      throw new HttpException(
        'Failed to retrieve events',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('urls')
  @ApiOperation({ summary: 'Get all tunnel URLs' })
  @ApiResponse({ status: 200, description: 'URLs retrieved successfully' })
  async getTunnelUrls(): Promise<Record<string, string>> {
    try {
      return this.ngrokService.getAllTunnelUrls();
    } catch (error) {
      this.logger.error('Failed to get tunnel URLs', error);
      throw new HttpException(
        'Failed to retrieve tunnel URLs',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // Discord integration endpoints
  @Get('discord/status')
  @ApiOperation({ summary: 'Get Discord integration status' })
  @ApiResponse({ status: 200, description: 'Discord status retrieved successfully' })
  async getDiscordStatus(): Promise<NgrokDiscordIntegration | { enabled: false }> {
    try {
      const status = this.ngrokDiscordService.getDiscordIntegrationStatus();
      return status || { enabled: false };
    } catch (error) {
      this.logger.error('Failed to get Discord status', error);
      throw new HttpException(
        'Failed to retrieve Discord status',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('discord/setup')
  @ApiOperation({ summary: 'Setup Discord webhook integration' })
  @ApiResponse({ status: 201, description: 'Discord integration setup successfully' })
  async setupDiscordIntegration(): Promise<NgrokDiscordIntegration> {
    try {
      return await this.ngrokDiscordService.setupDiscordWebhooks();
    } catch (error) {
      this.logger.error('Failed to setup Discord integration', error);
      throw new HttpException(
        'Failed to setup Discord integration',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Put('discord/update')
  @ApiOperation({ summary: 'Update Discord webhook URLs' })
  @ApiResponse({ status: 200, description: 'Discord URLs updated successfully' })
  async updateDiscordUrls(): Promise<{ message: string; integration: NgrokDiscordIntegration | null }> {
    try {
      await this.ngrokDiscordService.updateDiscordWebhookUrls();
      const integration = this.ngrokDiscordService.getDiscordIntegrationStatus();
      
      return {
        message: 'Discord webhook URLs updated successfully',
        integration,
      };
    } catch (error) {
      this.logger.error('Failed to update Discord URLs', error);
      throw new HttpException(
        'Failed to update Discord URLs',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('discord/tunnel')
  @ApiOperation({ summary: 'Create dedicated Discord tunnel' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        port: { type: 'number', description: 'Port for Discord tunnel' },
      },
    },
  })
  @ApiResponse({ status: 201, description: 'Discord tunnel created successfully' })
  async createDiscordTunnel(@Body() body?: { port?: number }): Promise<{ url: string; message: string }> {
    try {
      const url = await this.ngrokDiscordService.createDiscordTunnel(body?.port);
      
      return {
        url,
        message: 'Discord tunnel created successfully',
      };
    } catch (error) {
      this.logger.error('Failed to create Discord tunnel', error);
      throw new HttpException(
        'Failed to create Discord tunnel',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('discord/health')
  @ApiOperation({ summary: 'Get Discord webhook health status' })
  @ApiResponse({ status: 200, description: 'Discord health status retrieved successfully' })
  async getDiscordHealth() {
    try {
      return await this.ngrokDiscordService.getWebhookHealth();
    } catch (error) {
      this.logger.error('Failed to get Discord health', error);
      throw new HttpException(
        'Failed to retrieve Discord health status',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('config')
  @ApiOperation({ summary: 'Get ngrok configuration (sensitive data redacted)' })
  @ApiResponse({ status: 200, description: 'Configuration retrieved successfully' })
  async getConfig() {
    try {
      const config = this.ngrokService.getConfig();
      
      // Redact sensitive information
      return {
        ...config,
        authToken: config.authToken ? '***REDACTED***' : undefined,
        apiKey: config.apiKey ? '***REDACTED***' : undefined,
      };
    } catch (error) {
      this.logger.error('Failed to get configuration', error);
      throw new HttpException(
        'Failed to retrieve configuration',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // Health monitoring endpoints
  @Get('health/detailed')
  @ApiOperation({ summary: 'Get detailed health monitoring status' })
  @ApiResponse({ status: 200, description: 'Detailed health status retrieved successfully' })
  async getDetailedHealth(): Promise<NgrokHealthResult> {
    try {
      return await this.ngrokHealthMonitorService.performHealthCheck();
    } catch (error) {
      this.logger.error('Failed to get detailed health status', error);
      throw new HttpException(
        'Failed to retrieve detailed health status',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('health/tunnel/:id')
  @ApiOperation({ summary: 'Get health status for specific tunnel' })
  @ApiParam({ name: 'id', description: 'Tunnel ID' })
  @ApiResponse({ status: 200, description: 'Tunnel health status retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Tunnel not found' })
  async getTunnelHealth(@Param('id') tunnelId: string) {
    try {
      return await this.ngrokHealthMonitorService.getTunnelHealth(tunnelId);
    } catch (error) {
      if (error.message?.includes('not found')) {
        throw new HttpException(`Tunnel '${tunnelId}' not found`, HttpStatus.NOT_FOUND);
      }
      
      this.logger.error(`Failed to get health for tunnel '${tunnelId}'`, error);
      throw new HttpException(
        'Failed to retrieve tunnel health status',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('health/history')
  @ApiOperation({ summary: 'Get health check history' })
  @ApiQuery({ name: 'limit', required: false, description: 'Maximum number of history entries to return' })
  @ApiResponse({ status: 200, description: 'Health history retrieved successfully' })
  async getHealthHistory(@Query('limit') limit?: string) {
    try {
      const historyLimit = limit ? parseInt(limit) : 10;
      return this.ngrokHealthMonitorService.getHealthHistory(historyLimit);
    } catch (error) {
      this.logger.error('Failed to get health history', error);
      throw new HttpException(
        'Failed to retrieve health history',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('health/metrics')
  @ApiOperation({ summary: 'Get tunnel health metrics' })
  @ApiQuery({ name: 'tunnelId', required: false, description: 'Specific tunnel ID (returns all if not specified)' })
  @ApiResponse({ status: 200, description: 'Health metrics retrieved successfully' })
  async getHealthMetrics(@Query('tunnelId') tunnelId?: string) {
    try {
      return this.ngrokHealthMonitorService.getTunnelMetrics(tunnelId);
    } catch (error) {
      this.logger.error('Failed to get health metrics', error);
      throw new HttpException(
        'Failed to retrieve health metrics',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('health/monitoring/start')
  @ApiOperation({ summary: 'Start health monitoring' })
  @ApiResponse({ status: 200, description: 'Health monitoring started successfully' })
  async startHealthMonitoring() {
    try {
      await this.ngrokHealthMonitorService.startMonitoring();
      return { message: 'Health monitoring started successfully' };
    } catch (error) {
      this.logger.error('Failed to start health monitoring', error);
      throw new HttpException(
        'Failed to start health monitoring',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('health/monitoring/stop')
  @ApiOperation({ summary: 'Stop health monitoring' })
  @ApiResponse({ status: 200, description: 'Health monitoring stopped successfully' })
  async stopHealthMonitoring() {
    try {
      await this.ngrokHealthMonitorService.stopMonitoring();
      return { message: 'Health monitoring stopped successfully' };
    } catch (error) {
      this.logger.error('Failed to stop health monitoring', error);
      throw new HttpException(
        'Failed to stop health monitoring',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('health/monitoring/status')
  @ApiOperation({ summary: 'Get health monitoring status' })
  @ApiResponse({ status: 200, description: 'Health monitoring status retrieved successfully' })
  async getHealthMonitoringStatus() {
    try {
      return {
        isMonitoring: this.ngrokHealthMonitorService.isMonitoring(),
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error('Failed to get health monitoring status', error);
      throw new HttpException(
        'Failed to retrieve health monitoring status',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // Persistence and recovery endpoints
  @Post('persistence/save')
  @ApiOperation({ summary: 'Force save current tunnel state' })
  @ApiResponse({ status: 200, description: 'State saved successfully' })
  async saveState() {
    try {
      await this.ngrokPersistenceService.saveState();
      return { message: 'Tunnel state saved successfully' };
    } catch (error) {
      this.logger.error('Failed to save state', error);
      throw new HttpException(
        'Failed to save tunnel state',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('persistence/recover')
  @ApiOperation({ summary: 'Recover tunnels from persistent state' })
  @ApiResponse({ status: 200, description: 'Recovery completed' })
  async recoverTunnels() {
    try {
      const result = await this.ngrokPersistenceService.recoverTunnels();
      return {
        message: 'Recovery completed',
        ...result,
      };
    } catch (error) {
      this.logger.error('Failed to recover tunnels', error);
      throw new HttpException(
        'Failed to recover tunnels',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('persistence/backup')
  @ApiOperation({ summary: 'Create manual backup of current state' })
  @ApiResponse({ status: 200, description: 'Backup created successfully' })
  async createBackup() {
    try {
      await this.ngrokPersistenceService.forceBackup();
      return { message: 'Backup created successfully' };
    } catch (error) {
      this.logger.error('Failed to create backup', error);
      throw new HttpException(
        'Failed to create backup',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('persistence/backups')
  @ApiOperation({ summary: 'List available backups' })
  @ApiResponse({ status: 200, description: 'Backup list retrieved successfully' })
  async getBackupList() {
    try {
      const backups = await this.ngrokPersistenceService.getBackupList();
      return {
        backups,
        count: backups.length,
      };
    } catch (error) {
      this.logger.error('Failed to get backup list', error);
      throw new HttpException(
        'Failed to retrieve backup list',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('persistence/restore/:backupName')
  @ApiOperation({ summary: 'Restore from specific backup' })
  @ApiParam({ name: 'backupName', description: 'Name of the backup file to restore from' })
  @ApiResponse({ status: 200, description: 'Restore completed' })
  async restoreFromBackup(@Param('backupName') backupName: string) {
    try {
      // Construct full backup path (basic security check)
      if (backupName.includes('..') || backupName.includes('/') || backupName.includes('\\')) {
        throw new HttpException('Invalid backup name', HttpStatus.BAD_REQUEST);
      }

      const config = this.ngrokPersistenceService.getConfig();
      const backupPath = `${config.statePath}.backup.${backupName}`;

      const result = await this.ngrokPersistenceService.restoreFromBackup(backupPath);
      
      return {
        message: 'Restore completed',
        backupName,
        ...result,
      };
    } catch (error) {
      this.logger.error(`Failed to restore from backup: ${backupName}`, error);
      throw new HttpException(
        'Failed to restore from backup',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('persistence/status')
  @ApiOperation({ summary: 'Get persistence service status' })
  @ApiResponse({ status: 200, description: 'Persistence status retrieved successfully' })
  async getPersistenceStatus() {
    try {
      const config = this.ngrokPersistenceService.getConfig();
      return {
        enabled: this.ngrokPersistenceService.isPersistenceEnabled(),
        recoveryAttempts: this.ngrokPersistenceService.getRecoveryAttempts(),
        maxRecoveryAttempts: config.maxRecoveryAttempts,
        statePath: config.statePath,
        backupInterval: config.backupInterval,
        autoRecover: config.autoRecover,
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error('Failed to get persistence status', error);
      throw new HttpException(
        'Failed to retrieve persistence status',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('persistence/reset-recovery-attempts')
  @ApiOperation({ summary: 'Reset recovery attempts counter' })
  @ApiResponse({ status: 200, description: 'Recovery attempts counter reset successfully' })
  async resetRecoveryAttempts() {
    try {
      this.ngrokPersistenceService.resetRecoveryAttempts();
      return { message: 'Recovery attempts counter reset successfully' };
    } catch (error) {
      this.logger.error('Failed to reset recovery attempts', error);
      throw new HttpException(
        'Failed to reset recovery attempts',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // Logging and monitoring endpoints
  @Get('logs/metrics')
  @ApiOperation({ summary: 'Get comprehensive logging metrics' })
  @ApiResponse({ status: 200, description: 'Logging metrics retrieved successfully' })
  async getLoggingMetrics() {
    try {
      return this.ngrokLoggingService.getMetrics();
    } catch (error) {
      this.logger.error('Failed to get logging metrics', error);
      throw new HttpException(
        'Failed to retrieve logging metrics',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('logs/config')
  @ApiOperation({ summary: 'Get logging configuration' })
  @ApiResponse({ status: 200, description: 'Logging configuration retrieved successfully' })
  async getLoggingConfig() {
    try {
      const config = this.ngrokLoggingService.getConfig();
      
      // Redact sensitive information
      return {
        ...config,
        remoteLogging: {
          ...config.remoteLogging,
          apiKey: config.remoteLogging.apiKey ? '***REDACTED***' : undefined,
        },
      };
    } catch (error) {
      this.logger.error('Failed to get logging config', error);
      throw new HttpException(
        'Failed to retrieve logging configuration',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('logs/export')
  @ApiOperation({ summary: 'Export logs within date range' })
  @ApiQuery({ name: 'startDate', required: false, description: 'Start date for log export (ISO string)' })
  @ApiQuery({ name: 'endDate', required: false, description: 'End date for log export (ISO string)' })
  @ApiResponse({ status: 200, description: 'Logs exported successfully' })
  async exportLogs(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    try {
      const start = startDate ? new Date(startDate) : undefined;
      const end = endDate ? new Date(endDate) : undefined;
      
      const logs = await this.ngrokLoggingService.exportLogs(start, end);
      
      return {
        logs,
        count: logs.length,
        startDate: start,
        endDate: end,
        exportedAt: new Date(),
      };
    } catch (error) {
      this.logger.error('Failed to export logs', error);
      throw new HttpException(
        'Failed to export logs',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('logs/clear')
  @ApiOperation({ summary: 'Clear all logs' })
  @ApiResponse({ status: 200, description: 'Logs cleared successfully' })
  async clearLogs() {
    try {
      await this.ngrokLoggingService.clearLogs();
      return { message: 'All logs cleared successfully' };
    } catch (error) {
      this.logger.error('Failed to clear logs', error);
      throw new HttpException(
        'Failed to clear logs',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('logs/status')
  @ApiOperation({ summary: 'Get logging service status' })
  @ApiResponse({ status: 200, description: 'Logging status retrieved successfully' })
  async getLoggingStatus() {
    try {
      const config = this.ngrokLoggingService.getConfig();
      const metrics = this.ngrokLoggingService.getMetrics();
      
      return {
        enabled: this.ngrokLoggingService.isEnabled(),
        fileLogging: config.fileLogging,
        remoteLogging: config.remoteLogging.enabled,
        totalLogs: metrics.totalLogs,
        logLevel: config.logLevel,
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error('Failed to get logging status', error);
      throw new HttpException(
        'Failed to retrieve logging status',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('logs/test')
  @ApiOperation({ summary: 'Create test log entries for monitoring verification' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        level: { type: 'string', enum: ['debug', 'info', 'warn', 'error'] },
        message: { type: 'string' },
        category: { type: 'string' },
      },
    },
  })
  @ApiResponse({ status: 200, description: 'Test log entry created successfully' })
  async createTestLog(@Body() body: { level?: 'debug' | 'info' | 'warn' | 'error'; message?: string; category?: string }) {
    try {
      const { level = 'info', message = 'Test log entry', category = 'test' } = body;
      
      await this.ngrokLoggingService.log(level, category, message, {
        testEntry: true,
        timestamp: new Date(),
        requestSource: 'api',
      });
      
      return { message: 'Test log entry created successfully', level, category };
    } catch (error) {
      this.logger.error('Failed to create test log', error);
      throw new HttpException(
        'Failed to create test log',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('monitoring/overview')
  @ApiOperation({ summary: 'Get comprehensive monitoring overview' })
  @ApiResponse({ status: 200, description: 'Monitoring overview retrieved successfully' })
  async getMonitoringOverview() {
    try {
      const [health, persistence, logging] = await Promise.all([
        this.ngrokHealthMonitorService.performHealthCheck(),
        this.ngrokPersistenceService.getConfig(),
        this.ngrokLoggingService.getMetrics(),
      ]);

      const tunnels = await this.ngrokService.getAllTunnelStatuses();
      const discordStatus = this.ngrokDiscordService.getDiscordIntegrationStatus();

      return {
        system: {
          ngrokEnabled: this.ngrokService.isEnabled(),
          healthMonitoring: this.ngrokHealthMonitorService.isMonitoring(),
          persistenceEnabled: this.ngrokPersistenceService.isPersistenceEnabled(),
          loggingEnabled: this.ngrokLoggingService.isEnabled(),
        },
        tunnels: {
          total: tunnels.length,
          active: tunnels.filter((t: any) => t.status === 'connected').length,
          errors: tunnels.filter((t: any) => t.status === 'error').length,
        },
        health: {
          overall: health.overall,
          lastCheck: health.lastCheck,
          errorRate: health.metrics.errorRate,
        },
        discord: {
          enabled: !!discordStatus,
          lastUpdated: discordStatus?.lastUpdated,
        },
        logging: {
          totalLogs: logging.totalLogs,
          errorCount: logging.logsByLevel.error,
          avgErrorsPerHour: logging.errorsPerHour.reduce((a, b) => a + b, 0) / 24,
        },
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error('Failed to get monitoring overview', error);
      throw new HttpException(
        'Failed to retrieve monitoring overview',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}