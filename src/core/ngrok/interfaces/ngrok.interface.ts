import { z } from 'zod';

// Tunnel configuration schemas
export const TunnelConfigSchema = z.object({
  id: z.string(),
  proto: z.enum(['http', 'https', 'tcp', 'tls']).default('http'),
  addr: z.union([z.number(), z.string()]),
  domain: z.string().optional(),
  subdomain: z.string().optional(),
  region: z.enum(['us', 'eu', 'ap', 'au', 'sa', 'jp', 'in']).default('us'),
  basicAuth: z.string().optional(),
  oauthProvider: z.enum(['google', 'github', 'microsoft']).optional(),
  oauthAllowDomains: z.array(z.string()).optional(),
  ipRestrictions: z.array(z.string()).optional(),
  circuitBreaker: z.number().optional(),
  compression: z.boolean().default(true),
  websocketTcpConverter: z.boolean().default(false),
  metadata: z.record(z.string()).optional(),
});

export type TunnelConfig = z.infer<typeof TunnelConfigSchema>;

// Ngrok service configuration
export const NgrokServiceConfigSchema = z.object({
  enabled: z.boolean().default(false),
  authToken: z.string(),
  apiKey: z.string().optional(),
  region: z.enum(['us', 'eu', 'ap', 'au', 'sa', 'jp', 'in']).default('us'),
  environment: z.enum(['development', 'staging', 'production']).default('development'),
  retryAttempts: z.number().min(1).max(10).default(3),
  retryDelay: z.number().default(5000),
  healthCheckInterval: z.number().default(30000),
  autoRestart: z.boolean().default(true),
  persistTunnels: z.boolean().default(true),
  logLevel: z.enum(['debug', 'info', 'warn', 'error']).default('info'),
  defaultTunnel: TunnelConfigSchema.optional(),
  tunnels: z.array(TunnelConfigSchema).default([]),
});

export type NgrokServiceConfig = z.infer<typeof NgrokServiceConfigSchema>;

// Tunnel status and metrics
export interface TunnelStatus {
  id: string;
  status: 'connecting' | 'connected' | 'disconnected' | 'error' | 'retrying';
  url?: string;
  proto: string;
  addr: string | number;
  startedAt?: Date;
  lastHealthCheck?: Date;
  metrics: TunnelMetrics;
  errors: string[];
}

export interface TunnelMetrics {
  connectionsTotal: number;
  connectionsActive: number;
  bytesIn: number;
  bytesOut: number;
  requestsPerSecond: number;
  avgResponseTime: number;
  errorRate: number;
  uptime: number;
}

// Tunnel events
export interface TunnelEvent {
  id: string;
  tunnelId: string;
  type: 'started' | 'stopped' | 'error' | 'reconnected' | 'health_check';
  timestamp: Date;
  message: string;
  metadata?: Record<string, any>;
}

// Health check result
export interface NgrokHealthResult {
  overall: 'healthy' | 'warning' | 'critical';
  tunnels: TunnelHealthStatus[];
  metrics: NgrokServiceMetrics;
  lastCheck: Date;
  errors: string[];
}

export interface TunnelHealthStatus {
  id: string;
  status: 'healthy' | 'warning' | 'critical';
  url?: string;
  responseTime?: number;
  uptime: number;
  lastError?: string;
}

export interface NgrokServiceMetrics {
  tunnelsTotal: number;
  tunnelsActive: number;
  tunnelsConnecting: number;
  tunnelsError: number;
  totalUptime: number;
  restartCount: number;
  avgResponseTime: number;
  totalRequests: number;
  errorRate: number;
}

// Discord integration specific interfaces
export interface DiscordWebhookConfig {
  enabled: boolean;
  endpointPath: string;
  publicKey: string;
  verifySignature: boolean;
  allowedIPs: string[];
  rateLimit: {
    requests: number;
    windowMs: number;
  };
}

export interface NgrokDiscordIntegration {
  webhookUrl?: string;
  interactionsUrl?: string;
  oauthRedirectUrl?: string;
  tunnelId: string;
  lastUpdated: Date;
}

// Error types
export class NgrokError extends Error {
  constructor(
    message: string,
    public code: string,
    public tunnelId?: string,
    public retryable: boolean = true
  ) {
    super(message);
    this.name = 'NgrokError';
  }
}

export class TunnelError extends NgrokError {
  constructor(
    message: string,
    tunnelId: string,
    public statusCode?: number,
    retryable: boolean = true
  ) {
    super(message, 'TUNNEL_ERROR', tunnelId, retryable);
    this.name = 'TunnelError';
  }
}

export class AuthenticationError extends NgrokError {
  constructor(message: string = 'Invalid ngrok authentication token') {
    super(message, 'AUTH_ERROR', undefined, false);
    this.name = 'AuthenticationError';
  }
}

export class ConfigurationError extends NgrokError {
  constructor(message: string) {
    super(message, 'CONFIG_ERROR', undefined, false);
    this.name = 'ConfigurationError';
  }
}

// Service interfaces
export interface INgrokService {
  initialize(): Promise<void>;
  createTunnel(config: TunnelConfig): Promise<string>;
  closeTunnel(tunnelId: string): Promise<void>;
  closeAllTunnels(): Promise<void>;
  getTunnelStatus(tunnelId: string): Promise<TunnelStatus | null>;
  getAllTunnelStatuses(): Promise<TunnelStatus[]>;
  restartTunnel(tunnelId: string): Promise<string>;
  getHealthStatus(): Promise<NgrokHealthResult>;
  isEnabled(): boolean;
}

export interface INgrokTunnelManager {
  createHttpTunnel(port: number, options?: Partial<TunnelConfig>): Promise<string>;
  createTcpTunnel(port: number, options?: Partial<TunnelConfig>): Promise<string>;
  createTlsTunnel(port: number, certPath: string, keyPath: string, options?: Partial<TunnelConfig>): Promise<string>;
  monitorTunnel(tunnelId: string): Promise<void>;
  restartTunnel(tunnelId: string): Promise<string>;
  getTunnelMetrics(tunnelId: string): Promise<TunnelMetrics>;
}

export interface INgrokHealthMonitor {
  startMonitoring(): Promise<void>;
  stopMonitoring(): Promise<void>;
  performHealthCheck(): Promise<NgrokHealthResult>;
  getTunnelHealth(tunnelId: string): Promise<TunnelHealthStatus>;
  isMonitoring(): boolean;
}

export interface INgrokDiscordService {
  setupDiscordWebhooks(): Promise<NgrokDiscordIntegration>;
  updateDiscordWebhookUrls(): Promise<void>;
  verifyDiscordWebhook(signature: string, timestamp: string, body: string): boolean;
  getDiscordIntegrationStatus(): NgrokDiscordIntegration | null;
}

// Configuration defaults
export const DEFAULT_NGROK_CONFIG: Partial<NgrokServiceConfig> = {
  enabled: false,
  region: 'us',
  environment: 'development',
  retryAttempts: 3,
  retryDelay: 5000,
  healthCheckInterval: 30000,
  autoRestart: true,
  persistTunnels: true,
  logLevel: 'info',
  tunnels: [],
};

export const DEFAULT_TUNNEL_CONFIG: Partial<TunnelConfig> = {
  proto: 'http',
  region: 'us',
  compression: true,
  websocketTcpConverter: false,
};

export const DEFAULT_DISCORD_WEBHOOK_CONFIG: DiscordWebhookConfig = {
  enabled: false,
  endpointPath: '/discord/webhook',
  publicKey: '',
  verifySignature: true,
  allowedIPs: [
    '*************/24',
    '*************/24', 
    '*************/24'
  ],
  rateLimit: {
    requests: 100,
    windowMs: 300000, // 5 minutes
  },
};