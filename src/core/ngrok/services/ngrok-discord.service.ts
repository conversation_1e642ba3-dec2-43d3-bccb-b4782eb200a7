import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { createVerify } from 'crypto';
import {
  INgrokDiscordService,
  NgrokDiscordIntegration,
  DiscordWebhookConfig,
  DEFAULT_DISCORD_WEBHOOK_CONFIG,
} from '../interfaces/ngrok.interface';
import { NgrokService } from './ngrok.service';

@Injectable()
export class NgrokDiscordService implements INgrokDiscordService {
  private readonly logger = new Logger(NgrokDiscordService.name);
  private readonly webhookConfig: DiscordWebhookConfig;
  private discordIntegration: NgrokDiscordIntegration | null = null;

  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
    private readonly ngrokService: NgrokService,
  ) {
    this.webhookConfig = this.loadWebhookConfig();
  }

  private loadWebhookConfig(): DiscordWebhookConfig {
    return {
      ...DEFAULT_DISCORD_WEBHOOK_CONFIG,
      enabled: this.configService.get<boolean>('DISCORD_WEBHOOK_ENABLED', false),
      endpointPath: this.configService.get<string>('DISCORD_WEBHOOK_PATH', '/discord/webhook'),
      publicKey: this.configService.get<string>('DISCORD_PUBLIC_KEY', ''),
      verifySignature: this.configService.get<boolean>('DISCORD_VERIFY_SIGNATURE', true),
      allowedIPs: this.configService.get<string>('DISCORD_ALLOWED_IPS', '*************/24,*************/24,*************/24')
        .split(',').map((ip: any) => ip.trim()),
      rateLimit: {
        requests: this.configService.get<number>('DISCORD_RATE_LIMIT_REQUESTS', 100),
        windowMs: this.configService.get<number>('DISCORD_RATE_LIMIT_WINDOW', 300000),
      },
    };
  }

  async setupDiscordWebhooks(): Promise<NgrokDiscordIntegration> {
    if (!this.webhookConfig.enabled) {
      throw new Error('Discord webhook integration is disabled');
    }

    if (!this.ngrokService.isEnabled()) {
      throw new Error('NgrokService is not enabled');
    }

    this.logger.log('Setting up Discord webhook integration with ngrok...');

    try {
      // Get or create the default tunnel
      const defaultTunnelUrl = this.ngrokService.getTunnelUrl('default');
      
      if (!defaultTunnelUrl) {
        throw new Error('No default ngrok tunnel available for Discord integration');
      }

      // Construct webhook URLs
      const webhookUrl = `${defaultTunnelUrl}${this.webhookConfig.endpointPath}`;
      const interactionsUrl = `${defaultTunnelUrl}/discord/interactions`;
      const oauthRedirectUrl = `${defaultTunnelUrl}/auth/discord/callback`;

      this.discordIntegration = {
        webhookUrl,
        interactionsUrl,
        oauthRedirectUrl,
        tunnelId: 'default',
        lastUpdated: new Date(),
      };

      this.logger.log(`Discord webhook URLs configured:
        - Webhook: ${webhookUrl}
        - Interactions: ${interactionsUrl}
        - OAuth Redirect: ${oauthRedirectUrl}`);

      // Emit event for Discord bot to update its configurations
      this.eventEmitter.emit('discord.webhook.urls.updated', this.discordIntegration);

      // Optionally auto-update Discord application settings
      if (this.configService.get<boolean>('DISCORD_AUTO_UPDATE_URLS', false)) {
        await this.updateDiscordApplicationUrls();
      }

      return this.discordIntegration;

    } catch (error) {
      this.logger.error('Failed to setup Discord webhook integration', error);
      throw error;
    }
  }

  async updateDiscordWebhookUrls(): Promise<void> {
    if (!this.discordIntegration) {
      await this.setupDiscordWebhooks();
      return;
    }

    const currentTunnelUrl = this.ngrokService.getTunnelUrl(this.discordIntegration.tunnelId);
    
    if (!currentTunnelUrl) {
      throw new Error(`Tunnel '${this.discordIntegration.tunnelId}' is not available`);
    }

    // Check if URL has changed
    const newWebhookUrl = `${currentTunnelUrl}${this.webhookConfig.endpointPath}`;
    
    if (newWebhookUrl !== this.discordIntegration.webhookUrl) {
      this.logger.log(`Updating Discord webhook URLs - tunnel URL changed to: ${currentTunnelUrl}`);
      
      this.discordIntegration = {
        ...this.discordIntegration,
        webhookUrl: newWebhookUrl,
        interactionsUrl: `${currentTunnelUrl}/discord/interactions`,
        oauthRedirectUrl: `${currentTunnelUrl}/auth/discord/callback`,
        lastUpdated: new Date(),
      };

      this.eventEmitter.emit('discord.webhook.urls.updated', this.discordIntegration);
      
      if (this.configService.get<boolean>('DISCORD_AUTO_UPDATE_URLS', false)) {
        await this.updateDiscordApplicationUrls();
      }
    }
  }

  private async updateDiscordApplicationUrls(): Promise<void> {
    const clientId = this.configService.get<string>('DISCORD_CLIENT_ID');
    const botToken = this.configService.get<string>('DISCORD_BOT_TOKEN');

    if (!clientId || !botToken || !this.discordIntegration) {
      this.logger.warn('Missing Discord credentials for automatic URL updates');
      return;
    }

    try {
      // Update Discord application settings via API
      const response = await fetch(`https://discord.com/api/v10/applications/${clientId}`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bot ${botToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          interactions_endpoint_url: this.discordIntegration.interactionsUrl,
        }),
      });

      if (!response.ok) {
        throw new Error(`Discord API error: ${response.status} ${response.statusText}`);
      }

      this.logger.log('Discord application URLs updated successfully');

    } catch (error) {
      this.logger.error('Failed to update Discord application URLs', error);
      // Don't throw - this is a convenience feature
    }
  }

  verifyDiscordWebhook(signature: string, timestamp: string, body: string): boolean {
    if (!this.webhookConfig.verifySignature) {
      return true; // Skip verification if disabled
    }

    if (!this.webhookConfig.publicKey) {
      this.logger.warn('Discord public key not configured, skipping signature verification');
      return true;
    }

    try {
      // Verify timestamp to prevent replay attacks
      const timestampMs = parseInt(timestamp) * 1000;
      const now = Date.now();
      const fiveMinutes = 5 * 60 * 1000;
      
      if (Math.abs(now - timestampMs) > fiveMinutes) {
        this.logger.warn('Discord webhook timestamp too old, rejecting request');
        return false;
      }

      // Verify Ed25519 signature
      const verify = createVerify('sha256');
      verify.update(timestamp + body);
      
      const publicKey = `-----BEGIN PUBLIC KEY-----\n${this.webhookConfig.publicKey}\n-----END PUBLIC KEY-----`;
      const isValid = verify.verify(publicKey, signature, 'hex');

      if (!isValid) {
        this.logger.warn('Discord webhook signature verification failed');
      }

      return isValid;

    } catch (error) {
      this.logger.error('Discord webhook verification error', error);
      return false;
    }
  }

  getDiscordIntegrationStatus(): NgrokDiscordIntegration | null {
    return this.discordIntegration ? { ...this.discordIntegration } : null;
  }

  async validateDiscordWebhookAccess(clientIp: string, userAgent?: string): Promise<boolean> {
    // Check IP allowlist
    if (this.webhookConfig.allowedIPs.length > 0) {
      const isAllowed = this.webhookConfig.allowedIPs.some(allowedIp => {
        if (allowedIp.includes('/')) {
          // CIDR notation
          return this.isIpInCidr(clientIp, allowedIp);
        }
        return clientIp === allowedIp;
      });

      if (!isAllowed) {
        this.logger.warn(`Discord webhook access denied for IP: ${clientIp}`);
        return false;
      }
    }

    // Additional validation based on User-Agent
    if (userAgent && !userAgent.includes('Discord')) {
      this.logger.warn(`Suspicious User-Agent for Discord webhook: ${userAgent}`);
      // Note: Don't reject based on User-Agent alone as Discord may change it
    }

    return true;
  }

  private isIpInCidr(ip: string, cidr: string): boolean {
    // Simple CIDR check implementation
    // For production, consider using a more robust library like 'ip-range-check'
    try {
      const [network, prefixLength] = cidr.split('/');
      const networkParts = network.split('.').map(Number);
      const ipParts = ip.split('.').map(Number);
      
      const prefixLen = parseInt(prefixLength);
      const bytesToCheck = Math.floor(prefixLen / 8);
      const remainingBits = prefixLen % 8;
      
      // Check full bytes
      for (let i = 0; i < bytesToCheck; i++) {
        if (networkParts[i] !== ipParts[i]) {
          return false;
        }
      }
      
      // Check remaining bits
      if (remainingBits > 0 && bytesToCheck < 4) {
        const mask = 0xFF << (8 - remainingBits);
        const networkByte = networkParts[bytesToCheck] & mask;
        const ipByte = ipParts[bytesToCheck] & mask;
        
        if (networkByte !== ipByte) {
          return false;
        }
      }
      
      return true;
    } catch (error) {
      this.logger.error(`Invalid CIDR notation: ${cidr}`, error);
      return false;
    }
  }

  async createDiscordTunnel(port?: number): Promise<string> {
    const discordPort = port || this.configService.get<number>('DISCORD_PORT', 3001);
    
    const tunnelConfig = {
      id: 'discord-webhooks',
      proto: 'http' as const,
      addr: discordPort,
      domain: this.configService.get<string>('DISCORD_NGROK_DOMAIN'),
      subdomain: this.configService.get<string>('DISCORD_NGROK_SUBDOMAIN'),
      region: this.configService.get('NGROK_REGION', 'us') as any,
      basicAuth: this.configService.get<string>('DISCORD_BASIC_AUTH'),
      ipRestrictions: this.webhookConfig.allowedIPs,
      metadata: {
        service: 'discord-webhooks',
        environment: this.configService.get('NODE_ENV', 'development'),
        version: '1.0.0',
      },
    };

    const url = await this.ngrokService.createTunnel(tunnelConfig);
    
    this.logger.log(`Discord-specific tunnel created: ${url}`);
    
    // Update integration if this becomes the primary tunnel
    if (!this.discordIntegration || this.discordIntegration.tunnelId === 'default') {
      this.discordIntegration = {
        webhookUrl: `${url}${this.webhookConfig.endpointPath}`,
        interactionsUrl: `${url}/discord/interactions`,
        oauthRedirectUrl: `${url}/auth/discord/callback`,
        tunnelId: 'discord-webhooks',
        lastUpdated: new Date(),
      };

      this.eventEmitter.emit('discord.webhook.urls.updated', this.discordIntegration);
    }

    return url;
  }

  getWebhookConfig(): DiscordWebhookConfig {
    return { ...this.webhookConfig };
  }

  async getWebhookHealth(): Promise<{
    status: 'healthy' | 'warning' | 'error';
    tunnelAvailable: boolean;
    webhookUrl?: string;
    lastUpdated?: Date;
    errors: string[];
  }> {
    const errors: string[] = [];
    
    if (!this.webhookConfig.enabled) {
      return {
        status: 'warning',
        tunnelAvailable: false,
        errors: ['Discord webhook integration is disabled'],
      };
    }

    if (!this.discordIntegration) {
      return {
        status: 'error',
        tunnelAvailable: false,
        errors: ['Discord integration not initialized'],
      };
    }

    const tunnelAvailable = !!this.ngrokService.getTunnelUrl(this.discordIntegration.tunnelId);
    
    if (!tunnelAvailable) {
      errors.push(`Tunnel '${this.discordIntegration.tunnelId}' is not available`);
    }

    if (!this.webhookConfig.publicKey && this.webhookConfig.verifySignature) {
      errors.push('Discord public key not configured but signature verification is enabled');
    }

    const status = errors.length === 0 ? 'healthy' : 
                   tunnelAvailable ? 'warning' : 'error';

    return {
      status,
      tunnelAvailable,
      webhookUrl: this.discordIntegration.webhookUrl,
      lastUpdated: this.discordIntegration.lastUpdated,
      errors,
    };
  }
}