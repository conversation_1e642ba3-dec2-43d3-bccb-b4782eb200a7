import { Controller, Post, Get, Body } from '@nestjs/common';
import {
  DiscordWebhookEndpoint,
  DiscordInteraction,
  DiscordWebhook,
  ClientIp,
  NgrokTunnel,
  DiscordInteractionPayload,
  DiscordInteractionResponse,
  DISCORD_INTERACTION_TYPES,
  DISCORD_RESPONSE_TYPES,
} from '../index';

/**
 * Example Discord webhook controller showing how to use ngrok integration
 * This controller demonstrates various Discord webhook patterns with ngrok tunnels
 */
@Controller('discord')
export class ExampleDiscordWebhookController {

  /**
   * Basic Discord webhook endpoint
   * Automatically handles signature verification and IP validation
   */
  @Post('webhook')
  @DiscordWebhookEndpoint({
    description: 'Main Discord webhook endpoint',
    requireNgrok: true,
  })
  async handleWebhook(
    @Body() payload: DiscordInteractionPayload,
    @DiscordWebhook() webhook: any,
    @ClientIp() clientIp: string,
    @NgrokTunnel() tunnel: any,
  ): Promise<DiscordInteractionResponse> {
    console.log('Discord webhook received:', {
      type: payload.type,
      interactionId: payload.id,
      clientIp,
      tunnel: tunnel.host,
      verified: webhook.isVerified,
    });

    // Handle ping
    if (payload.type === DISCORD_INTERACTION_TYPES.PING) {
      return {
        type: DISCORD_RESPONSE_TYPES.PONG,
      };
    }

    // Handle slash command
    if (payload.type === DISCORD_INTERACTION_TYPES.APPLICATION_COMMAND) {
      return {
        type: DISCORD_RESPONSE_TYPES.CHANNEL_MESSAGE_WITH_SOURCE,
        data: {
          content: `Hello! Command received through ngrok tunnel: ${tunnel.host}`,
        },
      };
    }

    return {
      type: DISCORD_RESPONSE_TYPES.CHANNEL_MESSAGE_WITH_SOURCE,
      data: {
        content: 'Interaction processed successfully!',
      },
    };
  }

  /**
   * Discord slash command endpoint
   * Uses specialized decorator for command interactions
   */
  @Post('interactions')
  @DiscordInteraction({
    type: 'command',
    description: 'Discord slash command handler',
  })
  async handleSlashCommand(
    @Body() interaction: DiscordInteractionPayload,
  ): Promise<DiscordInteractionResponse> {
    const commandName = interaction.data?.name;

    switch (commandName) {
      case 'ping':
        return {
          type: DISCORD_RESPONSE_TYPES.CHANNEL_MESSAGE_WITH_SOURCE,
          data: {
            content: 'Pong! 🏓',
          },
        };

      case 'info':
        return {
          type: DISCORD_RESPONSE_TYPES.CHANNEL_MESSAGE_WITH_SOURCE,
          data: {
            embeds: [{
              title: 'Bot Information',
              description: 'This bot is running through an ngrok tunnel!',
              color: 0x00ff00,
              fields: [
                {
                  name: 'Tunnel Status',
                  value: 'Active ✅',
                  inline: true,
                },
                {
                  name: 'Environment',
                  value: process.env.NODE_ENV || 'development',
                  inline: true,
                },
              ],
            }],
          },
        };

      default:
        return {
          type: DISCORD_RESPONSE_TYPES.CHANNEL_MESSAGE_WITH_SOURCE,
          data: {
            content: `Unknown command: ${commandName}`,
            flags: 64, // EPHEMERAL
          },
        };
    }
  }

  /**
   * Discord button/component interaction endpoint
   */
  @Post('components')
  @DiscordInteraction({
    type: 'component',
    description: 'Discord component interaction handler',
  })
  async handleComponentInteraction(
    @Body() interaction: DiscordInteractionPayload,
  ): Promise<DiscordInteractionResponse> {
    const customId = interaction.data?.custom_id;

    switch (customId) {
      case 'test_button':
        return {
          type: DISCORD_RESPONSE_TYPES.UPDATE_MESSAGE,
          data: {
            content: 'Button clicked! ✨',
            components: [], // Remove the button
          },
        };

      case 'info_button':
        return {
          type: DISCORD_RESPONSE_TYPES.CHANNEL_MESSAGE_WITH_SOURCE,
          data: {
            content: 'This is additional information from the button click!',
            flags: 64, // EPHEMERAL
          },
        };

      default:
        return {
          type: DISCORD_RESPONSE_TYPES.CHANNEL_MESSAGE_WITH_SOURCE,
          data: {
            content: 'Unknown component interaction',
            flags: 64, // EPHEMERAL
          },
        };
    }
  }

  /**
   * OAuth callback endpoint for Discord authentication
   * Protected by ngrok but doesn't require Discord webhook verification
   */
  @Get('callback')
  @DiscordWebhookEndpoint({
    description: 'Discord OAuth callback',
    requireNgrok: true,
  })
  async handleOAuthCallback(
    @Body() query: any,
    @NgrokTunnel() tunnel: any,
  ) {
    // Handle OAuth callback logic here
    return {
      message: 'OAuth callback received',
      tunnel: tunnel.host,
      code: query.code,
    };
  }

  /**
   * Health check endpoint for Discord webhooks
   * Can be used to verify tunnel connectivity
   */
  @Get('health')
  async getDiscordHealth() {
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      message: 'Discord webhook service is running',
    };
  }
}

/**
 * Example usage in app module:
 * 
 * @Module({
 *   imports: [
 *     NgrokModule.forRoot({
 *       isGlobal: true,
 *       enableController: true,
 *       autoStart: true,
 *     }),
 *   ],
 *   controllers: [ExampleDiscordWebhookController],
 * })
 * export class AppModule {}
 */

/**
 * Example environment variables:
 * 
 * # Ngrok Configuration
 * NGROK_ENABLED=true
 * NGROK_AUTHTOKEN=your_ngrok_auth_token
 * NGROK_REGION=us
 * PORT=3000
 * 
 * # Discord Configuration
 * DISCORD_WEBHOOK_ENABLED=true
 * DISCORD_PUBLIC_KEY=your_discord_public_key
 * DISCORD_VERIFY_SIGNATURE=true
 * DISCORD_CLIENT_ID=your_discord_client_id
 * DISCORD_BOT_TOKEN=your_discord_bot_token
 * DISCORD_AUTO_UPDATE_URLS=true
 * 
 * # Security (optional)
 * NGROK_BASIC_AUTH=username:password
 * DISCORD_ALLOWED_IPS=*************/24,*************/24
 */

/**
 * Example Discord application setup:
 * 
 * 1. Create a Discord application at https://discord.com/developers/applications
 * 2. Get your application ID and public key
 * 3. Create a bot and get the bot token
 * 4. Set up slash commands:
 *    - /ping - Simple ping command
 *    - /info - Get bot information
 * 5. Configure interactions endpoint URL to your ngrok tunnel
 * 6. Add the bot to your server with appropriate permissions
 */