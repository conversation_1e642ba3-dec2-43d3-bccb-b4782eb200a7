// Core database services
export { DatabaseModule } from './database.module';
export { DatabaseService, REDIS_CONNECTION } from './database.service';
export { RedisQueryBuilder } from './query-builder';
export { RedisDatabaseModule } from './redis-database.module';
export { RedisDatabaseService } from './redis-database.service';
export { RedisEntityManagerService } from './redis-entity-manager.service';
export { RedisService } from './redis.service';

// Constants
export * from './database.constants';

// Repositories - comprehensive exports
export {
    AgentRepository, BaseRedisRepositoryImpl, GuildRepository, RepositoryFactory, SessionRepository, UserRepository
} from './repositories';

// Repository index
export * from './repositories/index';

// Types and interfaces - comprehensive exports
// Export only non-conflicting base interfaces
export type { CreateEntity, UpdateEntity } from './types/base.interface';

export type {
    RedisConfig,
    RedisOperationResult
} from './types/redis.types';

export type {
    RepositoryOptions
} from './types/repository.types';

// All types index (Redis-compatible entity interfaces and related types)
export * from './types';

// Utilities
export * from './utils';
export { RedisDataUtil, RedisMigrationUtil } from './utils';

// Do not re-export legacy Drizzle schema to avoid duplicate or missing symbol errors

// Entity class exports - Core entities
// NOTE: Commented out to avoid duplicate exports with types from schema
// If you need the entity classes, import them directly from './entities/'
// export { BaseEntity } from './entities/base.entity';
// export { User } from './entities/user.entity';
// export { Guild } from './entities/guild.entity';
// export { Organization } from './entities/organization.entity';
// export { Session } from './entities/session.entity';

// Avoid re-exporting entity classes here; consumers should import from './types'
// if they need Redis-compatible interfaces, or from './entities/*' directly when needed.