import { Injectable } from '@nestjs/common';
import { Redis } from 'ioredis';
import { BaseRedisRepositoryImpl } from './base-redis.repository';
import { RepositoryOptions } from '../types';

export interface Agent {
  id: string;
  type: string;
  userId: string;
  config: any;
  capabilities: string[];
  memory: Record<string, any>;
  context: any;
  state: string;
  createdAt: Date;
  lastInteraction: Date;
}

export interface AgentSession {
  agentId: string;
  userId: string;
  status: string;
  agentState: any;
  createdAt: Date;
  lastActivity: Date;
}

export interface AgentInteractionRecord {
  id: string;
  agentId: string;
  userId: string;
  eventType: string;
  eventData: any;
  createdAt: Date;
}

export interface AgentMemory {
  id: string;
  agentId: string;
  userId: string;
  interactionData: any;
  createdAt: Date;
}

/**
 * Agent repository with Redis implementation
 * Handles agent instances, sessions, interactions, and memory
 */
@Injectable()
export class AgentRepository extends BaseRedisRepositoryImpl<Agent> {
  constructor(redis: Redis) {
    const options: RepositoryOptions = {
      enableCaching: true,
      defaultTTL: 7200, // 2 hours
      enableSoftDelete: false,
      enableVersioning: false,
      enableAudit: true,
      compressionEnabled: false,
      serializationStrategy: 'json',
      indexFields: ['type', 'userId', 'state'],
      searchFields: ['type'],
    };

    super(redis, 'agent', null, options); // No predefined keys for flexible agent structure
  }

  // Agent Management

  /**
   * Find agents by user ID
   */
  async findByUserId(userId: string): Promise<Agent[]> {
    try {
      return await this.findByField('userId', userId);
    } catch (error) {
      this.logger.error(`Failed to find agents by user ID: ${userId}`, error);
      return [];
    }
  }

  /**
   * Find agents by type
   */
  async findByType(type: string): Promise<Agent[]> {
    try {
      return await this.findByField('type', type);
    } catch (error) {
      this.logger.error(`Failed to find agents by type: ${type}`, error);
      return [];
    }
  }

  /**
   * Find active agents
   */
  async findActiveAgents(): Promise<Agent[]> {
    try {
      return await this.findByField('state', 'active');
    } catch (error) {
      this.logger.error('Failed to find active agents', error);
      return [];
    }
  }

  /**
   * Update agent's last interaction time
   */
  async updateLastInteraction(agentId: string): Promise<Agent | null> {
    return await this.update(agentId, {
      lastInteraction: new Date(),
    });
  }

  // Session Management

  /**
   * Create agent session
   */
  async createSession(session: Omit<AgentSession, 'createdAt' | 'lastActivity'>): Promise<void> {
    const sessionData: AgentSession = {
      ...session,
      createdAt: new Date(),
      lastActivity: new Date(),
    };

    await this.redis.hset(
      'agent_sessions',
      session.agentId,
      JSON.stringify(sessionData)
    );
  }

  /**
   * Get agent session
   */
  async getSession(agentId: string): Promise<AgentSession | null> {
    try {
      const sessionDataStr = await this.redis.hget('agent_sessions', agentId);
      if (!sessionDataStr) {
        return null;
      }
      
      const sessionData = JSON.parse(sessionDataStr);
      // Convert date strings back to Date objects
      sessionData.createdAt = new Date(sessionData.createdAt);
      sessionData.lastActivity = new Date(sessionData.lastActivity);
      
      return sessionData;
    } catch (error) {
      this.logger.error(`Failed to get agent session: ${agentId}`, error);
      return null;
    }
  }

  /**
   * Update agent session
   */
  async updateSession(agentId: string, updates: Partial<AgentSession>): Promise<void> {
    const existingSession = await this.getSession(agentId);
    if (!existingSession) {
      throw new Error(`Agent session not found: ${agentId}`);
    }

    const updatedSession: AgentSession = {
      ...existingSession,
      ...updates,
      lastActivity: new Date(),
    };

    await this.redis.hset(
      'agent_sessions',
      agentId,
      JSON.stringify(updatedSession)
    );
  }

  /**
   * Remove agent session
   */
  async removeSession(agentId: string): Promise<void> {
    await this.redis.hdel('agent_sessions', agentId);
  }

  /**
   * Get all active sessions
   */
  async getActiveSessions(): Promise<AgentSession[]> {
    try {
      const sessionKeys = await this.redis.hkeys('agent_sessions');
      const sessions: AgentSession[] = [];

      for (const agentId of sessionKeys) {
        const session = await this.getSession(agentId);
        if (session && session.status === 'active') {
          sessions.push(session);
        }
      }

      return sessions;
    } catch (error) {
      this.logger.error('Failed to get active sessions', error);
      return [];
    }
  }

  // Memory Management

  /**
   * Store agent memory
   */
  async storeMemory(memory: Omit<AgentMemory, 'id' | 'createdAt'>): Promise<void> {
    const memoryId = `${memory.agentId}_${Date.now()}`;
    const memoryData: AgentMemory = {
      id: memoryId,
      ...memory,
      createdAt: new Date(),
    };

    await this.redis.hset(
      `agent_memory:${memory.agentId}`,
      memoryId,
      JSON.stringify(memoryData)
    );
  }

  /**
   * Get agent memory
   */
  async getMemory(agentId: string, limit = 100): Promise<AgentMemory[]> {
    try {
      const memoryKeys = await this.redis.hkeys(`agent_memory:${agentId}`);
      const memories: AgentMemory[] = [];

      // Sort keys by timestamp (newer first)
      const sortedKeys = memoryKeys.sort((a, b) => {
        const timestampA = parseInt(a.split('_').pop() || '0');
        const timestampB = parseInt(b.split('_').pop() || '0');
        return timestampB - timestampA;
      });

      const keysToFetch = sortedKeys.slice(0, limit);

      for (const key of keysToFetch) {
        const memoryDataStr = await this.redis.hget(`agent_memory:${agentId}`, key);
        if (memoryDataStr) {
          const memoryData = JSON.parse(memoryDataStr);
          memoryData.createdAt = new Date(memoryData.createdAt);
          memories.push(memoryData);
        }
      }

      return memories;
    } catch (error) {
      this.logger.error(`Failed to get agent memory: ${agentId}`, error);
      return [];
    }
  }

  /**
   * Clear agent memory
   */
  async clearMemory(agentId: string): Promise<void> {
    await this.redis.del(`agent_memory:${agentId}`);
  }

  // Interaction Tracking

  /**
   * Track agent interaction
   */
  async trackInteraction(interaction: Omit<AgentInteractionRecord, 'id' | 'createdAt'>): Promise<void> {
    const interactionId = `${interaction.agentId}_${Date.now()}`;
    const interactionData: AgentInteractionRecord = {
      id: interactionId,
      ...interaction,
      createdAt: new Date(),
    };

    await this.redis.hset(
      'agent_interactions',
      interactionId,
      JSON.stringify(interactionData)
    );
  }

  /**
   * Get agent interactions
   */
  async getInteractions(agentId: string, limit = 50): Promise<AgentInteractionRecord[]> {
    try {
      const interactionKeys = await this.redis.hkeys('agent_interactions');
      const interactions: AgentInteractionRecord[] = [];

      // Filter keys for this agent and sort by timestamp
      const agentInteractionKeys = interactionKeys
        .filter((key: any) => key.startsWith(`${agentId}_`))
        .sort((a, b) => {
          const timestampA = parseInt(a.split('_').pop() || '0');
          const timestampB = parseInt(b.split('_').pop() || '0');
          return timestampB - timestampA;
        })
        .slice(0, limit);

      for (const key of agentInteractionKeys) {
        const interactionDataStr = await this.redis.hget('agent_interactions', key);
        if (interactionDataStr) {
          const interactionData = JSON.parse(interactionDataStr);
          interactionData.createdAt = new Date(interactionData.createdAt);
          interactions.push(interactionData);
        }
      }

      return interactions;
    } catch (error) {
      this.logger.error(`Failed to get agent interactions: ${agentId}`, error);
      return [];
    }
  }

  /**
   * Get agent statistics
   */
  async getAgentStats(): Promise<{
    total: number;
    active: number;
    byType: Record<string, number>;
    totalInteractions: number;
    activeSessions: number;
  }> {
    try {
      const [allAgents, activeAgents, activeSessions] = await Promise.all([
        this.findAll({ limit: 10000 }),
        this.findActiveAgents(),
        this.getActiveSessions(),
      ]);

      const byType: Record<string, number> = {};
      for (const agent of allAgents) {
        byType[agent.type] = (byType[agent.type] || 0) + 1;
      }

      const interactionKeys = await this.redis.hkeys('agent_interactions');
      
      return {
        total: allAgents.length,
        active: activeAgents.length,
        byType,
        totalInteractions: interactionKeys.length,
        activeSessions: activeSessions.length,
      };
    } catch (error) {
      this.logger.error('Failed to get agent statistics', error);
      return {
        total: 0,
        active: 0,
        byType: {},
        totalInteractions: 0,
        activeSessions: 0,
      };
    }
  }

  /**
   * Cleanup expired sessions
   */
  async cleanupExpiredSessions(maxAgeHours = 24): Promise<number> {
    const cutoffTime = new Date(Date.now() - maxAgeHours * 60 * 60 * 1000);
    const sessionKeys = await this.redis.hkeys('agent_sessions');
    let cleanedCount = 0;

    for (const agentId of sessionKeys) {
      const session = await this.getSession(agentId);
      if (session && session.lastActivity < cutoffTime) {
        await this.removeSession(agentId);
        cleanedCount++;
      }
    }

    return cleanedCount;
  }

  /**
   * Cleanup old memory entries
   */
  async cleanupOldMemory(maxAgeHours = 72, maxEntriesPerAgent = 1000): Promise<number> {
    const cutoffTime = new Date(Date.now() - maxAgeHours * 60 * 60 * 1000);
    const agents = await this.findAll({ limit: 10000 });
    let cleanedCount = 0;

    for (const agent of agents) {
      const memoryKeys = await this.redis.hkeys(`agent_memory:${agent.id}`);
      
      // Sort by timestamp and remove old or excess entries
      const sortedKeys = memoryKeys.sort((a, b) => {
        const timestampA = parseInt(a.split('_').pop() || '0');
        const timestampB = parseInt(b.split('_').pop() || '0');
        return timestampB - timestampA; // Newest first
      });

      const keysToDelete: string[] = [];

      for (let i = 0; i < sortedKeys.length; i++) {
        const key = sortedKeys[i];
        const timestamp = parseInt(key.split('_').pop() || '0');
        const keyDate = new Date(timestamp);

        // Delete if too old or beyond max entries
        if (keyDate < cutoffTime || i >= maxEntriesPerAgent) {
          keysToDelete.push(key);
        }
      }

      if (keysToDelete.length > 0) {
        await this.redis.hdel(`agent_memory:${agent.id}`, ...keysToDelete);
        cleanedCount += keysToDelete.length;
      }
    }

    return cleanedCount;
  }
}