# Service Migration Pattern - PostgreSQL to Redis

## Overview

This document provides patterns for migrating existing database services from PostgreSQL/Drizzle ORM to Redis. Follow these patterns to ensure consistent, maintainable, and performant Redis-based services.

## Before & After Comparison

### PostgreSQL Service Pattern (Before)
```typescript
@Injectable()
export class CommunityDatabaseService {
  constructor(private readonly databaseService: DatabaseService) {}

  private get db() {
    return this.databaseService.getDb();
  }

  async createEvent(eventData: NewCommunityEvent): Promise<CommunityEvent> {
    const [event] = await this.db.insert(communityEvents)
      .values(eventData)
      .returning();
    return event;
  }

  async getUpcomingEvents(guildId: string, limit: number = 10): Promise<CommunityEvent[]> {
    return this.db
      .select()
      .from(communityEvents)
      .where(and(
        eq(communityEvents.guildId, guildId),
        eq(communityEvents.isActive, true),
        sql`${communityEvents.startDate} > NOW()`
      ))
      .orderBy(asc(communityEvents.startDate))
      .limit(limit);
  }
}
```

### Redis Service Pattern (After)
```typescript
@Injectable()
export class CommunityDatabaseService {
  constructor(private readonly redisService: RedisDatabaseService) {}

  async createEvent(eventData: NewCommunityEvent): Promise<CommunityEvent> {
    const eventId = `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const redis = this.redisService.getRedis();
    
    const event = {
      ...eventData,
      id: eventId,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    await redis.multi()
      .hset(`event:${eventId}`, this.serialize(event))
      .sadd('event:all', eventId)
      .zadd(`guild:${eventData.guildId}:events`, new Date(eventData.startDate).getTime(), eventId)
      .exec();

    return event;
  }

  async getUpcomingEvents(guildId: string, limit: number = 10): Promise<CommunityEvent[]> {
    const redis = this.redisService.getRedis();
    const now = Date.now();
    
    // Get future events for this guild, ordered by start date
    const eventIds = await redis.zrangebyscore(
      `guild:${guildId}:events`,
      now,
      '+inf',
      'LIMIT', 0, limit
    );

    if (eventIds.length === 0) return [];

    const pipeline = redis.pipeline();
    eventIds.forEach(id => pipeline.hgetall(`event:${id}`));
    
    const results = await pipeline.exec();
    return results
      ?.map(([err, data]) => err ? null : this.deserialize(data))
      .filter((event): event is CommunityEvent => 
        event !== null && event.isActive
      ) || [];
  }

  private serialize(data: any): Record<string, string> {
    const serialized: Record<string, string> = {};
    for (const [key, value] of Object.entries(data)) {
      if (value !== undefined && value !== null) {
        serialized[key] = typeof value === 'object' 
          ? JSON.stringify(value) 
          : String(value);
      }
    }
    return serialized;
  }

  private deserialize(data: Record<string, string>): any {
    const deserialized: any = {};
    for (const [key, value] of Object.entries(data)) {
      try {
        deserialized[key] = JSON.parse(value);
      } catch {
        deserialized[key] = this.parseValue(value);
      }
    }
    return deserialized;
  }

  private parseValue(value: string): any {
    if (value === 'true') return true;
    if (value === 'false') return false;
    if (value === 'null') return null;
    if (!isNaN(Number(value))) return Number(value);
    return value;
  }
}
```

## Migration Patterns

### 1. Simple CRUD Operations

#### PostgreSQL → Redis
```typescript
// Before: Simple insert
async create(data: NewEntity): Promise<Entity> {
  const [entity] = await this.db.insert(entities).values(data).returning();
  return entity;
}

// After: Redis hash with indexing
async create(data: NewEntity): Promise<Entity> {
  const id = this.generateId();
  const entity = { ...data, id, createdAt: new Date(), updatedAt: new Date() };
  
  const multi = this.redis.multi();
  multi.hset(`entity:${id}`, this.serialize(entity));
  multi.sadd('entity:all', id);
  
  // Add indexes
  if (entity.categoryId) {
    multi.sadd(`entity:category:${entity.categoryId}`, id);
  }
  
  await multi.exec();
  return entity;
}
```

### 2. Complex Queries with Joins

#### PostgreSQL → Redis
```typescript
// Before: JOIN query
async getEventsWithParticipants(guildId: string): Promise<EventWithParticipants[]> {
  return this.db
    .select({
      ...communityEvents,
      participantCount: sql`COUNT(${eventParticipants.id})`
    })
    .from(communityEvents)
    .leftJoin(eventParticipants, eq(communityEvents.id, eventParticipants.eventId))
    .where(eq(communityEvents.guildId, guildId))
    .groupBy(communityEvents.id);
}

// After: Multiple Redis operations with pipeline
async getEventsWithParticipants(guildId: string): Promise<EventWithParticipants[]> {
  const eventIds = await this.redis.smembers(`guild:${guildId}:events`);
  
  const pipeline = this.redis.pipeline();
  eventIds.forEach(id => {
    pipeline.hgetall(`event:${id}`);
    pipeline.scard(`event:${id}:participants`);
  });
  
  const results = await pipeline.exec();
  const events: EventWithParticipants[] = [];
  
  for (let i = 0; i < eventIds.length; i++) {
    const eventData = results[i * 2][1] as Record<string, string>;
    const participantCount = results[i * 2 + 1][1] as number;
    
    events.push({
      ...this.deserialize(eventData),
      participantCount
    });
  }
  
  return events;
}
```

### 3. Aggregations and Analytics

#### PostgreSQL → Redis
```typescript
// Before: SQL aggregation
async getGuildStats(guildId: string): Promise<GuildStats> {
  const result = await this.db
    .select({
      totalEvents: sql`COUNT(*)`,
      activeEvents: sql`COUNT(*) FILTER (WHERE is_active = true)`,
      avgParticipants: sql`AVG(current_participants)`
    })
    .from(communityEvents)
    .where(eq(communityEvents.guildId, guildId));
    
  return result[0];
}

// After: Redis aggregation with Lua script or multiple operations
async getGuildStats(guildId: string): Promise<GuildStats> {
  const eventIds = await this.redis.smembers(`guild:${guildId}:events`);
  
  if (eventIds.length === 0) {
    return { totalEvents: 0, activeEvents: 0, avgParticipants: 0 };
  }
  
  const pipeline = this.redis.pipeline();
  eventIds.forEach(id => pipeline.hgetall(`event:${id}`));
  
  const results = await pipeline.exec();
  const events = results?.map(([err, data]) => 
    err ? null : this.deserialize(data)
  ).filter(Boolean) || [];
  
  const activeEvents = events.filter(e => e.isActive);
  const totalParticipants = events.reduce((sum, e) => sum + (e.currentParticipants || 0), 0);
  
  return {
    totalEvents: events.length,
    activeEvents: activeEvents.length,
    avgParticipants: events.length > 0 ? totalParticipants / events.length : 0
  };
}
```

### 4. Search and Filtering

#### PostgreSQL → Redis
```typescript
// Before: SQL LIKE query
async searchEvents(guildId: string, query: string): Promise<CommunityEvent[]> {
  return this.db
    .select()
    .from(communityEvents)
    .where(and(
      eq(communityEvents.guildId, guildId),
      or(
        ilike(communityEvents.title, `%${query}%`),
        ilike(communityEvents.description, `%${query}%`)
      )
    ));
}

// After: Redis search with client-side filtering
async searchEvents(guildId: string, query: string): Promise<CommunityEvent[]> {
  const eventIds = await this.redis.smembers(`guild:${guildId}:events`);
  const events = await this.getEventsByIds(eventIds);
  
  const searchTerm = query.toLowerCase();
  return events.filter(event => 
    event.title?.toLowerCase().includes(searchTerm) ||
    event.description?.toLowerCase().includes(searchTerm)
  );
}

// Alternative: Use RediSearch module for full-text search
async searchEventsWithRediSearch(guildId: string, query: string): Promise<CommunityEvent[]> {
  // Requires RediSearch module
  const searchResult = await this.redis.call(
    'FT.SEARCH',
    'events_index',
    `@guild_id:{${guildId}} @title|description:${query}*`
  );
  
  // Parse search results and return events
  return this.parseSearchResults(searchResult);
}
```

### 5. Transactions and Consistency

#### PostgreSQL → Redis
```typescript
// Before: Database transaction
async joinEvent(eventId: string, userId: string): Promise<void> {
  await this.db.transaction(async (tx) => {
    await tx.insert(eventParticipants).values({ eventId, userId });
    await tx.update(communityEvents)
      .set({ currentParticipants: sql`${communityEvents.currentParticipants} + 1` })
      .where(eq(communityEvents.id, eventId));
  });
}

// After: Redis MULTI/EXEC transaction
async joinEvent(eventId: string, userId: string): Promise<void> {
  const multi = this.redis.multi();
  
  multi.sadd(`event:${eventId}:participants`, userId);
  multi.sadd(`user:${userId}:events`, eventId);
  multi.hincrby(`event:${eventId}`, 'currentParticipants', 1);
  multi.hset(`event:${eventId}`, 'updatedAt', new Date().toISOString());
  
  await multi.exec();
}
```

### 6. Time-based Operations

#### PostgreSQL → Redis
```typescript
// Before: Date filtering
async getRecentEvents(guildId: string, days: number = 7): Promise<CommunityEvent[]> {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - days);
  
  return this.db
    .select()
    .from(communityEvents)
    .where(and(
      eq(communityEvents.guildId, guildId),
      gte(communityEvents.createdAt, cutoffDate)
    ))
    .orderBy(desc(communityEvents.createdAt));
}

// After: Redis sorted sets with timestamps
async getRecentEvents(guildId: string, days: number = 7): Promise<CommunityEvent[]> {
  const cutoffTime = Date.now() - (days * 24 * 60 * 60 * 1000);
  
  // Get events created after cutoff time
  const eventIds = await this.redis.zrangebyscore(
    `guild:${guildId}:events_by_created`,
    cutoffTime,
    '+inf'
  );
  
  return this.getEventsByIds(eventIds);
}
```

## Data Structure Patterns

### 1. Entity Storage Patterns

#### Hash for Structured Data
```typescript
// Store user data as hash
await redis.hset('user:123', {
  username: 'john_doe',
  email: '<EMAIL>',
  experience: '1500',
  lastLogin: new Date().toISOString()
});
```

#### Sets for Collections
```typescript
// Store user achievements as set
await redis.sadd('user:123:achievements', 'first_message', 'level_10', 'helper');

// Store guild members as set
await redis.sadd('guild:456:members', 'user1', 'user2', 'user3');
```

#### Sorted Sets for Rankings
```typescript
// Store leaderboard with scores
await redis.zadd('leaderboard:experience', 1500, 'user1', 2300, 'user2', 850, 'user3');

// Store events by start date
await redis.zadd('guild:456:events', startTimestamp, eventId);
```

#### Lists for Activity Feeds
```typescript
// Store user activity feed
await redis.lpush('user:123:activity', JSON.stringify({
  type: 'message_sent',
  timestamp: Date.now(),
  channelId: '789'
}));

// Keep only last 100 activities
await redis.ltrim('user:123:activity', 0, 99);
```

### 2. Indexing Patterns

#### Secondary Indexes
```typescript
// Create username index
await redis.hset('user:username_index', username.toLowerCase(), userId);

// Create category index
await redis.sadd(`events:category:${category}`, eventId);

// Create status index
await redis.sadd(`tickets:status:${status}`, ticketId);
```

#### Composite Indexes
```typescript
// Guild + Category index
await redis.sadd(`events:guild:${guildId}:category:${category}`, eventId);

// Time-based index
const monthKey = `events:month:${new Date().toISOString().substr(0, 7)}`;
await redis.sadd(monthKey, eventId);
```

### 3. Caching Patterns

#### Time-based Cache
```typescript
// Cache with TTL
await redis.setex(`cache:user:${userId}`, 300, JSON.stringify(userData));

// Daily cache that expires at midnight
const secondsUntilMidnight = this.getSecondsUntilMidnight();
await redis.setex(`cache:daily:stats`, secondsUntilMidnight, JSON.stringify(stats));
```

#### Cache-aside Pattern
```typescript
async getUser(userId: string): Promise<User | null> {
  // Try cache first
  const cached = await this.redis.get(`cache:user:${userId}`);
  if (cached) return JSON.parse(cached);
  
  // Load from Redis storage
  const user = await this.redis.hgetall(`user:${userId}`);
  if (Object.keys(user).length === 0) return null;
  
  const parsedUser = this.deserialize(user);
  
  // Cache for 5 minutes
  await this.redis.setex(`cache:user:${userId}`, 300, JSON.stringify(parsedUser));
  
  return parsedUser;
}
```

## Performance Optimization

### 1. Batch Operations
```typescript
// Bad: Multiple individual operations
for (const userId of userIds) {
  await redis.hgetall(`user:${userId}`);
}

// Good: Pipeline for batch operations
const pipeline = redis.pipeline();
userIds.forEach(id => pipeline.hgetall(`user:${id}`));
const results = await pipeline.exec();
```

### 2. Lua Scripts for Complex Operations
```typescript
const luaScript = `
  local eventId = KEYS[1]
  local userId = KEYS[2]
  local maxParticipants = tonumber(ARGV[1])
  
  local currentCount = redis.call('SCARD', 'event:' .. eventId .. ':participants')
  if currentCount >= maxParticipants then
    return 0
  end
  
  redis.call('SADD', 'event:' .. eventId .. ':participants', userId)
  redis.call('HINCRBY', 'event:' .. eventId, 'currentParticipants', 1)
  return 1
`;

const result = await redis.eval(luaScript, 2, eventId, userId, maxParticipants.toString());
```

### 3. Memory Optimization
```typescript
// Use shorter field names for frequently accessed data
await redis.hset(`u:${userId}`, {
  n: username,        // name
  e: email,          // email  
  x: experience,     // experience
  l: lastLogin,      // last login
});

// Use binary flags for boolean combinations
const flags = 0 |
  (isActive ? 1 : 0) |
  (isPremium ? 2 : 0) |
  (isModerator ? 4 : 0);

await redis.hset(`user:${userId}`, 'flags', flags.toString());
```

## Error Handling and Resilience

### 1. Connection Resilience
```typescript
async executeWithRetry<T>(operation: () => Promise<T>, maxRetries: number = 3): Promise<T> {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      if (attempt === maxRetries) throw error;
      
      this.logger.warn(`Redis operation failed, attempt ${attempt}/${maxRetries}:`, error);
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
    }
  }
  
  throw new Error('Max retries exceeded');
}
```

### 2. Graceful Degradation
```typescript
async getUserData(userId: string): Promise<User | null> {
  try {
    return await this.redisService.users.findById(userId);
  } catch (error) {
    this.logger.error('Redis operation failed, using cache fallback:', error);
    
    // Fallback to local cache or default data
    return this.getCachedUserData(userId);
  }
}
```

## Testing Patterns

### 1. Redis Testing Setup
```typescript
describe('CommunityDatabaseService', () => {
  let service: CommunityDatabaseService;
  let redisService: RedisDatabaseService;
  
  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        CommunityDatabaseService,
        {
          provide: RedisDatabaseService,
          useValue: createMockRedisService(),
        },
      ],
    }).compile();
    
    service = module.get(CommunityDatabaseService);
    redisService = module.get(RedisDatabaseService);
  });
  
  afterEach(async () => {
    // Clean up test data
    await redisService.getRedis().flushdb();
  });
});
```

### 2. Integration Testing
```typescript
it('should create and retrieve event with participants', async () => {
  const eventData = {
    title: 'Test Event',
    guildId: 'test-guild',
    startDate: new Date(),
  };
  
  const event = await service.createEvent(eventData);
  expect(event.id).toBeDefined();
  
  await service.joinEvent(event.id, 'user1');
  await service.joinEvent(event.id, 'user2');
  
  const eventWithParticipants = await service.getEventWithParticipants(event.id);
  expect(eventWithParticipants.participantCount).toBe(2);
});
```

This migration pattern ensures consistent, performant, and maintainable Redis-based services while preserving the functionality of the original PostgreSQL implementation.