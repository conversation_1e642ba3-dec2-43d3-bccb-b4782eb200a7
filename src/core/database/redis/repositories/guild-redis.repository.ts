import { Injectable, Logger } from '@nestjs/common';
import { Guild } from '../../entities/guild.entity';
import type { GuildFeatures, GuildSettings, WelcomeRoles } from '../../types';
import { BaseRedisRepository, RedisEntityConfig } from '../base-redis.repository';

export interface RedisGuild extends Omit<Guild, 'id'> {
  id?: string;
  discordId: string;
  settings?: GuildSettings;
  features?: GuildFeatures;
  welcomeRoles?: WelcomeRoles;
}

@Injectable()
export class GuildRedisRepository extends BaseRedisRepository<RedisGuild> {
  protected config: RedisEntityConfig = {
    keyPrefix: 'guild',
    indexes: ['name', 'isActive', 'ownerDiscordId'],
    relationships: {
      ownerDiscordId: 'user',
    }
  };

  protected logger = new Logger(GuildRedisRepository.name);

  // Guild-specific operations
  async createGuild(guildData: CreateGuild): Promise<RedisGuild> {
    const id = guildData.discordId;
    const guild = await this.create(id, {
      ...guildData,
      id: undefined, // Remove auto-increment ID for Redis
      isActive: guildData.isActive ?? true,
      lastActivityAt: guildData.lastActivityAt || new Date(),
      settings: this.getDefaultSettings(),
      features: this.getDefaultFeatures(),
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    // Initialize guild member set
    await this.redis.sadd(this.key(id, 'members'), '');
    await this.redis.srem(this.key(id, 'members'), ''); // Remove empty string

    // Initialize guild channels set
    await this.redis.sadd(this.key(id, 'channels'), '');
    await this.redis.srem(this.key(id, 'channels'), '');

    // Initialize guild roles set
    await this.redis.sadd(this.key(id, 'roles'), '');
    await this.redis.srem(this.key(id, 'roles'), '');

    // Initialize activity counters
    await this.initializeActivityCounters(id);

    this.logger.log(`Created guild: ${guildData.name} (${id})`);
    return guild;
  }

  async findByDiscordId(discordId: string): Promise<RedisGuild | null> {
    return this.findById(discordId);
  }

  async updateLastActivity(discordId: string): Promise<void> {
    await this.redis.hset(this.key(discordId), {
      lastActivityAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    });
  }

  // Member management
  async addMember(guildId: string, userId: string): Promise<void> {
    const multi = this.redis.multi();
    
    multi.sadd(this.key(guildId, 'members'), userId);
    multi.sadd(`user:${userId}:guilds`, guildId);
    
    // Update member count
    multi.hincrby(this.key(guildId, 'stats'), 'memberCount', 1);
    
    await multi.exec();
    
    await this.addGuildActivity(guildId, {
      type: 'member_joined',
      userId,
    });

    this.logger.debug(`Added member ${userId} to guild ${guildId}`);
  }

  async removeMember(guildId: string, userId: string): Promise<void> {
    const multi = this.redis.multi();
    
    multi.srem(this.key(guildId, 'members'), userId);
    multi.srem(`user:${userId}:guilds`, guildId);
    
    // Update member count
    multi.hincrby(this.key(guildId, 'stats'), 'memberCount', -1);
    
    await multi.exec();
    
    await this.addGuildActivity(guildId, {
      type: 'member_left',
      userId,
    });

    this.logger.debug(`Removed member ${userId} from guild ${guildId}`);
  }

  async getMembers(guildId: string): Promise<string[]> {
    return this.redis.smembers(this.key(guildId, 'members'));
  }

  async getMemberCount(guildId: string): Promise<number> {
    return this.redis.scard(this.key(guildId, 'members'));
  }

  async isMember(guildId: string, userId: string): Promise<boolean> {
    return (await this.redis.sismember(this.key(guildId, 'members'), userId)) === 1;
  }

  // Settings management
  async updateSettings(guildId: string, settings: Partial<GuildSettings>): Promise<RedisGuild | null> {
    const guild = await this.findById(guildId);
    if (!guild) return null;

    const updatedSettings = {
      ...guild.settings,
      ...settings,
    };

    return this.update(guildId, { settings: updatedSettings });
  }

  async getSettings(guildId: string): Promise<GuildSettings | null> {
    const guild = await this.findById(guildId);
    return guild?.settings || null;
  }

  async updateFeatures(guildId: string, features: Partial<GuildFeatures>): Promise<RedisGuild | null> {
    const guild = await this.findById(guildId);
    if (!guild) return null;

    const updatedFeatures = {
      ...guild.features,
      ...features,
    };

    return this.update(guildId, { features: updatedFeatures });
  }

  async getFeatures(guildId: string): Promise<GuildFeatures | null> {
    const guild = await this.findById(guildId);
    return guild?.features || null;
  }

  async isFeatureEnabled(guildId: string, feature: keyof GuildFeatures): Promise<boolean> {
    const features = await this.getFeatures(guildId);
    const featureValue = features?.[feature];
    if (typeof featureValue === 'boolean') {
      return featureValue;
    }
    if (typeof featureValue === 'object' && featureValue !== null) {
      return (featureValue as any).enabled || false;
    }
    return false;
  }

  // Activity tracking
  async addGuildActivity(guildId: string, activity: any): Promise<void> {
    const activityData = {
      ...activity,
      id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: activity.timestamp || Date.now(),
    };

    await this.redis.lpush(
      this.key(guildId, 'activity'),
      JSON.stringify(activityData)
    );
    
    // Keep only last 1000 activities
    await this.redis.ltrim(this.key(guildId, 'activity'), 0, 999);

    // Update daily activity counter
    const today = new Date().toISOString().split('T')[0];
    const today = new Date().toISOString().split('T')[0];
    if (today) {
      await this.redis.hincrby(
        this.key(guildId, 'activity_daily'),
        today,
        1
      );
    }
  }

  async getGuildActivity(guildId: string, limit: number = 50): Promise<any[]> {
    const activities = await this.redis.lrange(this.key(guildId, 'activity'), 0, limit - 1);
    return activities.map((activity: any) => JSON.parse(activity));
  }

  async getDailyActivity(guildId: string, days: number = 7): Promise<Record<string, number>> {
    const activity = await this.redis.hgetall(this.key(guildId, 'activity_daily'));
    
    // Get last N days
    const result: Record<string, number> = {};
    const today = new Date();
    
    for (let i = 0; i < days; i++) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];
      if (dateStr) {
        result[dateStr] = parseInt(activity[dateStr] || '0');
      }
    }
    
    return result;
  }

  // Channel management
  async addChannel(guildId: string, channelId: string, channelData: any): Promise<void> {
    const multi = this.redis.multi();
    
    multi.sadd(this.key(guildId, 'channels'), channelId);
    multi.hset(this.key(guildId, 'channel_data'), channelId, JSON.stringify(channelData));
    
    await multi.exec();
    
    await this.addGuildActivity(guildId, {
      type: 'channel_created',
      channelId,
      channelData,
    });
  }

  async removeChannel(guildId: string, channelId: string): Promise<void> {
    const multi = this.redis.multi();
    
    multi.srem(this.key(guildId, 'channels'), channelId);
    multi.hdel(this.key(guildId, 'channel_data'), channelId);
    
    await multi.exec();
    
    await this.addGuildActivity(guildId, {
      type: 'channel_deleted',
      channelId,
    });
  }

  async getChannels(guildId: string): Promise<string[]> {
    return this.redis.smembers(this.key(guildId, 'channels'));
  }

  async getChannelData(guildId: string, channelId: string): Promise<any | null> {
    const data = await this.redis.hget(this.key(guildId, 'channel_data'), channelId);
    return data ? JSON.parse(data) : null;
  }

  // Role management
  async addRole(guildId: string, roleId: string, roleData: any): Promise<void> {
    const multi = this.redis.multi();
    
    multi.sadd(this.key(guildId, 'roles'), roleId);
    multi.hset(this.key(guildId, 'role_data'), roleId, JSON.stringify(roleData));
    
    await multi.exec();
    
    await this.addGuildActivity(guildId, {
      type: 'role_created',
      roleId,
      roleData,
    });
  }

  async removeRole(guildId: string, roleId: string): Promise<void> {
    const multi = this.redis.multi();
    
    multi.srem(this.key(guildId, 'roles'), roleId);
    multi.hdel(this.key(guildId, 'role_data'), roleId);
    
    await multi.exec();
    
    await this.addGuildActivity(guildId, {
      type: 'role_deleted',
      roleId,
    });
  }

  async getRoles(guildId: string): Promise<string[]> {
    return this.redis.smembers(this.key(guildId, 'roles'));
  }

  async getRoleData(guildId: string, roleId: string): Promise<any | null> {
    const data = await this.redis.hget(this.key(guildId, 'role_data'), roleId);
    return data ? JSON.parse(data) : null;
  }

  // Statistics and analytics
  async getGuildStats(guildId: string): Promise<{
    memberCount: number;
    channelCount: number;
    roleCount: number;
    totalActivity: number;
    averageDailyActivity: number;
    isActive: boolean;
    createdAt: Date | null;
    lastActivity: Date | null;
  }> {
    const guild = await this.findById(guildId);
    if (!guild) {
      throw new Error('Guild not found');
    }

    const [memberCount, channelCount, roleCount, dailyActivity] = await Promise.all([
      this.getMemberCount(guildId),
      this.redis.scard(this.key(guildId, 'channels')),
      this.redis.scard(this.key(guildId, 'roles')),
      this.getDailyActivity(guildId, 30), // Last 30 days
    ]);

    const totalActivity = Object.values(dailyActivity).reduce((sum, count) => sum + count, 0);
    const averageDailyActivity = totalActivity / 30;

    return {
      memberCount,
      channelCount,
      roleCount,
      totalActivity,
      averageDailyActivity,
      isActive: guild.isActive || false,
      createdAt: guild.createdAt ? new Date(guild.createdAt) : null,
      lastActivity: guild.lastActivityAt ? new Date(guild.lastActivityAt) : null,
    };
  }

  // Premium features
  async setPremiumStatus(guildId: string, isPremium: boolean, expiresAt?: Date): Promise<void> {
    const multi = this.redis.multi();
    
    multi.hset(this.key(guildId, 'premium'), {
      isPremium: isPremium ? '1' : '0',
      expiresAt: expiresAt ? expiresAt.toISOString() : '',
      updatedAt: new Date().toISOString(),
    });
    
    if (isPremium && expiresAt) {
      // Set TTL for premium expiration
      const ttl = Math.floor((expiresAt.getTime() - Date.now()) / 1000);
      multi.expire(this.key(guildId, 'premium'), ttl);
    }
    
    await multi.exec();
    
    await this.addGuildActivity(guildId, {
      type: isPremium ? 'premium_activated' : 'premium_deactivated',
      expiresAt,
    });

    this.logger.log(`${isPremium ? 'Activated' : 'Deactivated'} premium for guild ${guildId}`);
  }

  async isPremium(guildId: string): Promise<boolean> {
    const premiumData = await this.redis.hgetall(this.key(guildId, 'premium'));
    
    if (!premiumData.isPremium || premiumData.isPremium === '0') {
      return false;
    }

    if (premiumData.expiresAt) {
      const expiresAt = new Date(premiumData.expiresAt);
      if (expiresAt < new Date()) {
        // Expired, clean up
        await this.setPremiumStatus(guildId, false);
        return false;
      }
    }

    return true;
  }

  // Bulk operations
  async getActiveGuilds(limit?: number): Promise<RedisGuild[]> {
    const activeGuilds = await this.findByIndex('isActive', 'true');
    
    // Sort by last activity
    const sorted = activeGuilds.sort((a, b) => {
      const aTime = a.lastActivityAt ? new Date(a.lastActivityAt).getTime() : 0;
      const bTime = b.lastActivityAt ? new Date(b.lastActivityAt).getTime() : 0;
      return bTime - aTime;
    });

    return limit ? sorted.slice(0, limit) : sorted;
  }

  async searchGuilds(query: string, limit: number = 10): Promise<RedisGuild[]> {
    const searchTerm = query.toLowerCase();
    const allGuilds = await this.findAll();
    
    return allGuilds
      .filter((guild: any) => 
        guild.name?.toLowerCase().includes(searchTerm) ||
        guild.discordId.includes(searchTerm)
      )
      .slice(0, limit);
  }

  // Helper methods
  private getDefaultSettings(): GuildSettings {
    return {
      prefix: '!',
      language: 'en',
      timezone: 'UTC',
      moderation: {
        enabled: false,
        autoMod: false,
        logChannelId: undefined,
        logChannel: undefined,
        mutedRoleId: undefined,
        warningThreshold: 3,
      },
      welcome: {
        enabled: false,
        channel: null,
        message: 'Welcome to the server!',
      },
      leveling: {
        enabled: true,
        rewards: [],
      },
    };
  }

  private getDefaultFeatures(): GuildFeatures {
    return {
      aiAgents: false,
      starboard: false,
      automod: false,
      leveling: true,
      music: {
        enabled: false,
        maxQueueSize: 100,
        allowPlaylists: true,
        djRoleId: undefined,
      },
      tickets: false,
      polls: false,
      giveaways: false,
    };
  }

  private async initializeActivityCounters(guildId: string): Promise<void> {
    const multi = this.redis.multi();
    
    // Initialize daily activity counter
    const today = new Date().toISOString().split('T')[0];
    if (today) {
      multi.hset(this.key(guildId, 'activity_daily'), today, '0');
    }
    
    // Initialize stats counter
    multi.hset(this.key(guildId, 'stats'), {
      messageCount: '0',
      commandCount: '0',
      memberCount: '0',
    });
    
    await multi.exec();
  }

  // Clean up inactive guilds (soft delete)
  async markInactive(guildId: string): Promise<void> {
    await this.update(guildId, {
      isActive: false,
      updatedAt: new Date(),
    });

    this.logger.log(`Marked guild ${guildId} as inactive`);
  }

  async reactivateGuild(guildId: string): Promise<void> {
    await this.update(guildId, {
      isActive: true,
      lastActivityAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    });

    this.logger.log(`Reactivated guild ${guildId}`);
  }
}