import { Injectable, Logger, OnModuleD<PERSON>roy, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Redis from 'ioredis';

// Repository imports
import { GuildRedisRepository } from './repositories/guild-redis.repository';
import { UserRedisRepository } from './repositories/user-redis.repository';

@Injectable()
export class RedisDatabaseService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(RedisDatabaseService.name);
  private redis!: Redis;
  private isConnected = false;

  // Repository instances
  public readonly users!: UserRedisRepository;
  public readonly guilds!: GuildRedisRepository;

  constructor(private readonly configService: ConfigService) {
    // Redis and repositories will be initialized in onModuleInit
  }

  async onModuleInit() {
    try {
      await this.initializeRedis();
      await this.redis.ping();
      this.isConnected = true;

      // Initialize repositories after Redis connection
      this.users = new UserRedisRepository(this.redis);
      this.guilds = new GuildRedisRepository(this.redis);

      this.logger.log('Redis database service initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Redis database service:', error);
      throw error;
    }
  }

  async onModuleDestroy() {
    if (this.redis) {
      await this.redis.disconnect();
      this.logger.log('Redis database service disconnected');
    }
  }

  private initializeRedis() {
    const redisUrl = this.configService.get<string>('REDIS_URL');
    
    if (!redisUrl) {
      throw new Error('REDIS_URL is not configured');
    }

    try {
      this.redis = new Redis(redisUrl, {
        maxRetriesPerRequest: 3,
        lazyConnect: true,
        retryDelayOnFailover: 100,
        enableReadyCheck: true,
        // Connection pool settings
        family: 4,
        keepAlive: true,
        // Performance settings
        keyPrefix: '',
        // Error handling
        reconnectOnError: (err) => {
          this.logger.warn('Redis reconnection attempt:', err.message);
          return true;
        },
      });

      this.setupEventHandlers();
    } catch (error) {
      this.logger.error('Failed to initialize Redis connection:', error);
      throw error;
    }
  }

  private setupEventHandlers() {
    this.redis.on('connect', () => {
      this.logger.log('Connected to Redis');
      this.isConnected = true;
    });

    this.redis.on('ready', () => {
      this.logger.log('Redis connection ready');
    });

    this.redis.on('error', (error) => {
      this.logger.error('Redis connection error:', error);
      this.isConnected = false;
    });

    this.redis.on('close', () => {
      this.logger.warn('Redis connection closed');
      this.isConnected = false;
    });

    this.redis.on('reconnecting', (ms) => {
      this.logger.warn(`Redis reconnecting in ${ms}ms`);
    });
  }

  // Core database operations
  getRedis(): Redis {
    return this.redis;
  }

  async ping(): Promise<string> {
    return this.redis.ping();
  }

  async healthCheck(): Promise<{
    connected: boolean;
    responseTime: number;
    memoryUsage?: any;
    dbSize?: number;
  }> {
    const startTime = Date.now();
    
    try {
      await this.redis.ping();
      const responseTime = Date.now() - startTime;
      
      // Get memory info
      const memInfo = await this.redis.info('memory');
      const dbSize = await this.redis.dbsize();
      
      return {
        connected: this.isConnected,
        responseTime,
        memoryUsage: this.parseMemoryInfo(memInfo),
        dbSize,
      };
    } catch (error) {
      return {
        connected: false,
        responseTime: Date.now() - startTime,
      };
    }
  }

  private parseMemoryInfo(memInfo: string): any {
    const info: any = {};
    const lines = memInfo.split('\r\n');
    
    for (const line of lines) {
      if (line.includes(':')) {
        const [key, value] = line.split(':');
        if (key && value) {
          info[key] = value;
        }
      }
    }
    
    return info;
  }

  // Transaction support
  async transaction<T>(callback: (multi: any) => Promise<T>): Promise<T> {
    const multi = this.redis.multi();

    try {
      const result = await callback(multi);
      await multi.exec();
      return result;
    } catch (error) {
      multi.discard();
      throw error;
    }
  }

  // Advanced operations
  async pipeline<T>(callback: (pipeline: any) => Promise<T>): Promise<T> {
    const pipeline = this.redis.pipeline();

    try {
      const result = await callback(pipeline);
      await pipeline.exec();
      return result;
    } catch (error) {
      this.logger.error('Pipeline operation failed:', error);
      throw error;
    }
  }

  // Batch operations
  async batchGet(keys: string[]): Promise<(string | null)[]> {
    if (keys.length === 0) return [];
    
    const pipeline = this.redis.pipeline();
    keys.forEach(key => pipeline.get(key));
    
    const results = await pipeline.exec();
    return (results?.map(([err, result]) => err ? null : result as string) || []) as (string | null)[];
  }

  async batchSet(keyValues: Array<{ key: string; value: any; ttl?: number }>): Promise<void> {
    if (keyValues.length === 0) return;

    const pipeline = this.redis.pipeline();
    
    for (const { key, value, ttl } of keyValues) {
      const serialized = typeof value === 'string' ? value : JSON.stringify(value);
      
      if (ttl) {
        pipeline.setex(key, ttl, serialized);
      } else {
        pipeline.set(key, serialized);
      }
    }
    
    await pipeline.exec();
  }

  async batchDelete(keys: string[]): Promise<number> {
    if (keys.length === 0) return 0;
    
    // Delete in chunks to avoid blocking Redis
    const chunkSize = 100;
    let totalDeleted = 0;
    
    for (let i = 0; i < keys.length; i += chunkSize) {
      const chunk = keys.slice(i, i + chunkSize);
      const deleted = await this.redis.del(...chunk);
      totalDeleted += deleted;
    }
    
    return totalDeleted;
  }

  // Search operations
  async findKeys(pattern: string, limit?: number): Promise<string[]> {
    const keys: string[] = [];
    let cursor = '0';
    let count = 0;
    
    do {
      const result = await this.redis.scan(
        cursor,
        'MATCH',
        pattern,
        'COUNT',
        100
      );
      
      cursor = result[0];
      const foundKeys = result[1] as string[];
      
      for (const key of foundKeys) {
        if (limit && count >= limit) break;
        keys.push(key);
        count++;
      }
      
      if (limit && count >= limit) break;
    } while (cursor !== '0');
    
    return keys;
  }

  async deletePattern(pattern: string): Promise<number> {
    const keys = await this.findKeys(pattern);
    return keys.length > 0 ? this.batchDelete(keys) : 0;
  }

  // Cache operations with automatic serialization
  async cacheGet<T>(key: string): Promise<T | null> {
    try {
      const value = await this.redis.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      this.logger.error(`Failed to get cache key ${key}:`, error);
      return null;
    }
  }

  async cacheSet<T>(key: string, value: T, ttl?: number): Promise<boolean> {
    try {
      const serialized = JSON.stringify(value);
      
      if (ttl) {
        await this.redis.setex(key, ttl, serialized);
      } else {
        await this.redis.set(key, serialized);
      }
      
      return true;
    } catch (error) {
      this.logger.error(`Failed to set cache key ${key}:`, error);
      return false;
    }
  }

  async cacheDelete(key: string): Promise<boolean> {
    try {
      const result = await this.redis.del(key);
      return result === 1;
    } catch (error) {
      this.logger.error(`Failed to delete cache key ${key}:`, error);
      return false;
    }
  }

  async cacheExists(key: string): Promise<boolean> {
    try {
      const result = await this.redis.exists(key);
      return result === 1;
    } catch (error) {
      this.logger.error(`Failed to check existence of cache key ${key}:`, error);
      return false;
    }
  }

  // Lock operations for distributed locking
  async acquireLock(
    lockKey: string,
    ttl: number = 30,
    retryAttempts: number = 5,
    retryDelay: number = 100
  ): Promise<string | null> {
    const lockValue = `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    for (let attempt = 0; attempt < retryAttempts; attempt++) {
      const result = await this.redis.set(
        `lock:${lockKey}`,
        lockValue,
        'EX',
        ttl,
        'NX'
      );
      
      if (result === 'OK') {
        return lockValue;
      }
      
      if (attempt < retryAttempts - 1) {
        await new Promise(resolve => setTimeout(resolve, retryDelay));
      }
    }
    
    return null;
  }

  async releaseLock(lockKey: string, lockValue: string): Promise<boolean> {
    const script = `
      if redis.call('get', KEYS[1]) == ARGV[1] then
        return redis.call('del', KEYS[1])
      else
        return 0
      end
    `;
    
    try {
      const result = await this.redis.eval(script, 1, `lock:${lockKey}`, lockValue) as number;
      return result === 1;
    } catch (error) {
      this.logger.error(`Failed to release lock ${lockKey}:`, error);
      return false;
    }
  }

  // Performance monitoring
  async getPerformanceMetrics(): Promise<{
    info: any;
    slowlog: any[];
    commandStats: any;
    memoryUsage: any;
    keyspaceInfo: any;
  }> {
    const [info, slowlog, memInfo, keyspaceInfo] = await Promise.all([
      this.redis.info(),
      this.redis.call('SLOWLOG', 'GET', 10),
      this.redis.info('memory'),
      this.redis.info('keyspace'),
    ]);

    // Parse command statistics
    const commandStats = this.parseCommandStats(info);
    const memoryUsage = this.parseMemoryInfo(memInfo);
    const keyspace = this.parseKeyspaceInfo(keyspaceInfo);

    return {
      info: this.parseInfo(info),
      slowlog: slowlog as any[],
      commandStats,
      memoryUsage,
      keyspaceInfo: keyspace,
    };
  }

  private parseInfo(info: string): any {
    const parsed: any = {};
    const sections = info.split('\r\n# ');
    
    for (const section of sections) {
      const lines = section.split('\r\n');
      const sectionName = lines[0]?.replace('# ', '') || '';
      
      if (sectionName) {
        parsed[sectionName] = {};
        
        for (let i = 1; i < lines.length; i++) {
          const line = lines[i];
          if (line && line.includes(':')) {
            const [key, value] = line.split(':');
            if (key && value !== undefined) {
              parsed[sectionName][key] = value;
            }
          }
        }
      }
    }
    
    return parsed;
  }

  private parseCommandStats(info: string): any {
    const stats: any = {};
    const lines = info.split('\r\n');
    
    for (const line of lines) {
      if (line.startsWith('cmdstat_')) {
        const [cmd, statsStr] = line.split(':');
        if (cmd && statsStr) {
          const cmdName = cmd.replace('cmdstat_', '');

          const matches = statsStr.match(/calls=(\d+),usec=(\d+),usec_per_call=([\d.]+)/);
          if (matches && matches[1] && matches[2] && matches[3]) {
            stats[cmdName] = {
              calls: parseInt(matches[1]),
              usec: parseInt(matches[2]),
              usec_per_call: parseFloat(matches[3]),
            };
          }
        }
      }
    }
    
    return stats;
  }

  private parseKeyspaceInfo(keyspaceInfo: string): any {
    const parsed: any = {};
    const lines = keyspaceInfo.split('\r\n');
    
    for (const line of lines) {
      if (line.startsWith('db')) {
        const [db, info] = line.split(':');
        if (db && info) {
          const matches = info.match(/keys=(\d+),expires=(\d+),avg_ttl=(\d+)/);

          if (matches && matches[1] && matches[2] && matches[3]) {
            parsed[db] = {
              keys: parseInt(matches[1]),
              expires: parseInt(matches[2]),
              avg_ttl: parseInt(matches[3]),
            };
          }
        }
      }
    }
    
    return parsed;
  }

  // Data export/import for migrations
  async exportData(patterns: string[] = ['*']): Promise<Record<string, any>> {
    const exportData: Record<string, any> = {};
    
    for (const pattern of patterns) {
      const keys = await this.findKeys(pattern);
      
      for (const key of keys) {
        const type = await this.redis.type(key);
        
        switch (type) {
          case 'string':
            exportData[key] = {
              type: 'string',
              value: await this.redis.get(key),
            };
            break;
            
          case 'hash':
            exportData[key] = {
              type: 'hash',
              value: await this.redis.hgetall(key),
            };
            break;
            
          case 'list':
            exportData[key] = {
              type: 'list',
              value: await this.redis.lrange(key, 0, -1),
            };
            break;
            
          case 'set':
            exportData[key] = {
              type: 'set',
              value: await this.redis.smembers(key),
            };
            break;
            
          case 'zset':
            exportData[key] = {
              type: 'zset',
              value: await this.redis.zrange(key, 0, -1, 'WITHSCORES'),
            };
            break;
        }
      }
    }
    
    return exportData;
  }

  async importData(data: Record<string, any>): Promise<void> {
    const pipeline = this.redis.pipeline();
    
    for (const [key, keyData] of Object.entries(data)) {
      const { type, value } = keyData;
      
      switch (type) {
        case 'string':
          pipeline.set(key, value);
          break;
          
        case 'hash':
          pipeline.del(key);
          if (Object.keys(value).length > 0) {
            pipeline.hset(key, value);
          }
          break;
          
        case 'list':
          pipeline.del(key);
          if (value.length > 0) {
            pipeline.lpush(key, ...value.reverse());
          }
          break;
          
        case 'set':
          pipeline.del(key);
          if (value.length > 0) {
            pipeline.sadd(key, ...value);
          }
          break;
          
        case 'zset':
          pipeline.del(key);
          if (value.length > 0) {
            pipeline.zadd(key, ...value);
          }
          break;
      }
    }
    
    await pipeline.exec();
    this.logger.log(`Imported ${Object.keys(data).length} keys`);
  }

  // Cleanup operations
  async cleanup(options: {
    deleteExpired?: boolean;
    deleteEmptyCollections?: boolean;
    optimizeMemory?: boolean;
  } = {}): Promise<{
    expiredKeysDeleted: number;
    emptyCollectionsDeleted: number;
    memoryOptimized: boolean;
  }> {
    let expiredKeysDeleted = 0;
    let emptyCollectionsDeleted = 0;
    let memoryOptimized = false;

    if (options.deleteExpired) {
      // This is automatically handled by Redis, but we can check
      const expiredKeys = await this.findKeys('*');
      const beforeCount = expiredKeys.length;
      
      // Force expire check (Redis does this automatically, but we can trigger)
      await this.redis.eval('return redis.call("eval", "return 1", 0)', 0);
      
      const afterKeys = await this.findKeys('*');
      expiredKeysDeleted = beforeCount - afterKeys.length;
    }

    if (options.deleteEmptyCollections) {
      const collections = await this.findKeys('*:all');
      
      for (const collectionKey of collections) {
        const size = await this.redis.scard(collectionKey);
        if (size === 0) {
          await this.redis.del(collectionKey);
          emptyCollectionsDeleted++;
        }
      }
    }

    if (options.optimizeMemory) {
      try {
        await this.redis.call('MEMORY', 'PURGE');
        memoryOptimized = true;
      } catch (error) {
        this.logger.warn('Memory optimization not available in this Redis version');
      }
    }

    return {
      expiredKeysDeleted,
      emptyCollectionsDeleted,
      memoryOptimized,
    };
  }

  // Get connection status
  get connected(): boolean {
    return this.isConnected;
  }
}