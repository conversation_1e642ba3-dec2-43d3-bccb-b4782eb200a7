-- Panel Cleanup Tables Migration
-- Creates tables to support the panel cleanup and state management system
-- Run Date: 2024-01-XX

-- Create panel deployment tracking table (enhanced from existing)
CREATE TABLE IF NOT EXISTS panel_deployments (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Core deployment info
    channel_id VARCHAR(50) NOT NULL,
    guild_id VARCHAR(50) NOT NULL,
    panel_id VARCHAR(100) NOT NULL,
    message_id VARCHAR(50),
    
    -- Deployment state
    deployment_status VARCHAR(20) NOT NULL DEFAULT 'active',
    content_hash VARCHAR(64),
    last_update_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Configuration and context
    deployment_config JSO<PERSON><PERSON> DEFAULT '{}',
    channel_context JSONB DEFAULT '{}',
    discord_state JSONB DEFAULT '{}',
    
    -- Error handling
    error_state JSONB DEFAULT '{}',
    retry_count INTEGER DEFAULT 0,
    last_error_at TIMESTAMP WITH TIME ZONE,
    
    -- Status flags
    is_active BOOLEAN DEFAULT TRUE,
    needs_cleanup BOOLEAN DEFAULT FALSE,
    
    -- Performance metrics
    deployment_metrics JSONB DEFAULT '{}',
    interaction_count INTEGER DEFAULT 0,
    last_interaction_at TIMESTAMP WITH TIME ZONE,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    -- Constraints
    UNIQUE(channel_id, panel_id),
    CHECK (deployment_status IN ('active', 'inactive', 'failed', 'updating', 'scheduled_cleanup'))
);

-- Create indexes for efficient queries
CREATE INDEX IF NOT EXISTS idx_panel_deployments_channel_id ON panel_deployments(channel_id);
CREATE INDEX IF NOT EXISTS idx_panel_deployments_guild_id ON panel_deployments(guild_id);
CREATE INDEX IF NOT EXISTS idx_panel_deployments_panel_id ON panel_deployments(panel_id);
CREATE INDEX IF NOT EXISTS idx_panel_deployments_message_id ON panel_deployments(message_id);
CREATE INDEX IF NOT EXISTS idx_panel_deployments_status ON panel_deployments(deployment_status);
CREATE INDEX IF NOT EXISTS idx_panel_deployments_active ON panel_deployments(is_active);
CREATE INDEX IF NOT EXISTS idx_panel_deployments_cleanup ON panel_deployments(needs_cleanup);
CREATE INDEX IF NOT EXISTS idx_panel_deployments_updated_at ON panel_deployments(updated_at);

-- Create cleanup logs table for tracking cleanup operations
CREATE TABLE IF NOT EXISTS panel_cleanup_logs (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Cleanup session info
    cleanup_session_id VARCHAR(100) NOT NULL,
    cleanup_type VARCHAR(20) NOT NULL, -- 'manual', 'scheduled', 'emergency'
    triggered_by VARCHAR(100), -- user ID or 'system'
    
    -- Target info
    guild_id VARCHAR(50),
    channel_id VARCHAR(50),
    target_scope VARCHAR(50), -- 'channel', 'guild', 'all'
    
    -- Results
    messages_scanned INTEGER DEFAULT 0,
    duplicates_found INTEGER DEFAULT 0,
    duplicates_removed INTEGER DEFAULT 0,
    duplicates_preserved INTEGER DEFAULT 0,
    errors_count INTEGER DEFAULT 0,
    
    -- Timing
    started_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE,
    execution_time_ms INTEGER,
    
    -- Configuration used
    cleanup_config JSONB DEFAULT '{}',
    
    -- Results detail
    results_summary JSONB DEFAULT '{}',
    error_details JSONB DEFAULT '{}',
    
    -- Status
    status VARCHAR(20) DEFAULT 'running', -- 'running', 'completed', 'failed', 'cancelled'
    
    CHECK (cleanup_type IN ('manual', 'scheduled', 'emergency')),
    CHECK (status IN ('running', 'completed', 'failed', 'cancelled'))
);

-- Create indexes for cleanup logs
CREATE INDEX IF NOT EXISTS idx_cleanup_logs_session_id ON panel_cleanup_logs(cleanup_session_id);
CREATE INDEX IF NOT EXISTS idx_cleanup_logs_type ON panel_cleanup_logs(cleanup_type);
CREATE INDEX IF NOT EXISTS idx_cleanup_logs_guild_id ON panel_cleanup_logs(guild_id);
CREATE INDEX IF NOT EXISTS idx_cleanup_logs_channel_id ON panel_cleanup_logs(channel_id);
CREATE INDEX IF NOT EXISTS idx_cleanup_logs_created_at ON panel_cleanup_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_cleanup_logs_status ON panel_cleanup_logs(status);

-- Create panel versions table for version tracking
CREATE TABLE IF NOT EXISTS panel_versions (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Version identification
    panel_id VARCHAR(100) NOT NULL,
    channel_id VARCHAR(50) NOT NULL,
    version_number VARCHAR(20) NOT NULL, -- semantic version
    content_hash VARCHAR(64) NOT NULL,
    
    -- Version content
    panel_content JSONB NOT NULL,
    
    -- Change tracking
    changes_from_previous JSONB DEFAULT '[]',
    change_significance VARCHAR(10) DEFAULT 'patch', -- 'major', 'minor', 'patch'
    
    -- Metadata
    deployment_id INTEGER,
    generation_time_ms INTEGER,
    content_size_bytes INTEGER,
    metadata JSONB DEFAULT '{}',
    
    -- Status
    is_current BOOLEAN DEFAULT FALSE,
    
    FOREIGN KEY (deployment_id) REFERENCES panel_deployments(id) ON DELETE SET NULL,
    CHECK (change_significance IN ('major', 'minor', 'patch'))
);

-- Create indexes for versions
CREATE INDEX IF NOT EXISTS idx_panel_versions_panel_id ON panel_versions(panel_id);
CREATE INDEX IF NOT EXISTS idx_panel_versions_channel_id ON panel_versions(channel_id);
CREATE INDEX IF NOT EXISTS idx_panel_versions_hash ON panel_versions(content_hash);
CREATE INDEX IF NOT EXISTS idx_panel_versions_current ON panel_versions(is_current);
CREATE INDEX IF NOT EXISTS idx_panel_versions_created_at ON panel_versions(created_at);

-- Create panel health metrics table
CREATE TABLE IF NOT EXISTS panel_health_metrics (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Target
    guild_id VARCHAR(50),
    channel_id VARCHAR(50),
    panel_id VARCHAR(100),
    
    -- Health metrics
    active_deployments INTEGER DEFAULT 0,
    duplicate_deployments INTEGER DEFAULT 0,
    failed_deployments INTEGER DEFAULT 0,
    orphaned_messages INTEGER DEFAULT 0,
    
    -- Performance metrics
    avg_response_time_ms FLOAT DEFAULT 0,
    interaction_success_rate FLOAT DEFAULT 1.0,
    last_successful_interaction TIMESTAMP WITH TIME ZONE,
    last_failed_interaction TIMESTAMP WITH TIME ZONE,
    
    -- System health
    memory_usage_mb FLOAT DEFAULT 0,
    cache_hit_rate FLOAT DEFAULT 0,
    error_rate_24h FLOAT DEFAULT 0,
    
    -- Cleanup metrics
    cleanups_performed INTEGER DEFAULT 0,
    last_cleanup_at TIMESTAMP WITH TIME ZONE,
    time_since_last_cleanup_hours INTEGER DEFAULT 0,
    
    -- Status
    overall_health_score FLOAT DEFAULT 1.0, -- 0-1 scale
    health_status VARCHAR(20) DEFAULT 'healthy', -- 'healthy', 'warning', 'critical'
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    CHECK (health_status IN ('healthy', 'warning', 'critical')),
    CHECK (overall_health_score >= 0 AND overall_health_score <= 1),
    CHECK (interaction_success_rate >= 0 AND interaction_success_rate <= 1)
);

-- Create indexes for health metrics
CREATE INDEX IF NOT EXISTS idx_health_metrics_guild_id ON panel_health_metrics(guild_id);
CREATE INDEX IF NOT EXISTS idx_health_metrics_channel_id ON panel_health_metrics(channel_id);
CREATE INDEX IF NOT EXISTS idx_health_metrics_panel_id ON panel_health_metrics(panel_id);
CREATE INDEX IF NOT EXISTS idx_health_metrics_status ON panel_health_metrics(health_status);
CREATE INDEX IF NOT EXISTS idx_health_metrics_score ON panel_health_metrics(overall_health_score);
CREATE INDEX IF NOT EXISTS idx_health_metrics_created_at ON panel_health_metrics(created_at);

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply trigger to panel_deployments
DROP TRIGGER IF EXISTS update_panel_deployments_updated_at ON panel_deployments;
CREATE TRIGGER update_panel_deployments_updated_at
    BEFORE UPDATE ON panel_deployments
    FOR EACH ROW
    EXECUTE PROCEDURE update_updated_at_column();

-- Create views for common queries

-- Active deployments view
CREATE OR REPLACE VIEW active_panel_deployments AS
SELECT 
    id,
    channel_id,
    guild_id,
    panel_id,
    message_id,
    deployment_status,
    content_hash,
    created_at,
    updated_at,
    last_update_at,
    interaction_count,
    last_interaction_at
FROM panel_deployments 
WHERE is_active = TRUE 
  AND deployment_status = 'active';

-- Cleanup candidates view
CREATE OR REPLACE VIEW panel_cleanup_candidates AS
SELECT 
    pd.id,
    pd.channel_id,
    pd.guild_id,
    pd.panel_id,
    pd.message_id,
    pd.content_hash,
    pd.created_at,
    pd.updated_at,
    COUNT(*) OVER (PARTITION BY pd.channel_id, pd.panel_id) as duplicate_count
FROM panel_deployments pd
WHERE pd.is_active = TRUE
  AND (pd.needs_cleanup = TRUE 
       OR pd.deployment_status IN ('failed', 'inactive'))
ORDER BY pd.channel_id, pd.panel_id, pd.created_at DESC;

-- Health summary view
CREATE OR REPLACE VIEW panel_system_health AS
SELECT 
    COUNT(*) as total_deployments,
    COUNT(*) FILTER (WHERE deployment_status = 'active') as active_deployments,
    COUNT(*) FILTER (WHERE deployment_status = 'failed') as failed_deployments,
    COUNT(*) FILTER (WHERE needs_cleanup = TRUE) as needs_cleanup,
    AVG(interaction_count) as avg_interaction_count,
    COUNT(DISTINCT guild_id) as guilds_with_panels,
    COUNT(DISTINCT channel_id) as channels_with_panels,
    MAX(updated_at) as last_activity
FROM panel_deployments 
WHERE is_active = TRUE;

-- Add helpful comments
COMMENT ON TABLE panel_deployments IS 'Tracks all panel deployments with persistent state management';
COMMENT ON TABLE panel_cleanup_logs IS 'Logs all cleanup operations for auditing and metrics';
COMMENT ON TABLE panel_versions IS 'Tracks content versions and changes for panels';
COMMENT ON TABLE panel_health_metrics IS 'System health and performance metrics';

COMMENT ON VIEW active_panel_deployments IS 'Current active panel deployments';
COMMENT ON VIEW panel_cleanup_candidates IS 'Panels that may need cleanup (duplicates, failed, etc.)';
COMMENT ON VIEW panel_system_health IS 'Overall system health summary';

-- Insert initial system configuration
INSERT INTO panel_health_metrics (
    guild_id, 
    channel_id, 
    panel_id, 
    health_status,
    metadata
) VALUES (
    'system', 
    'system', 
    'cleanup_system', 
    'healthy',
    '{"initialized_at": "' || CURRENT_TIMESTAMP || '", "version": "1.0.0", "capabilities": ["duplication_prevention", "automated_cleanup", "health_monitoring", "recovery_operations"]}'
) ON CONFLICT DO NOTHING;

-- Create cleanup system status function
CREATE OR REPLACE FUNCTION get_cleanup_system_status()
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'total_deployments', COUNT(*),
        'active_deployments', COUNT(*) FILTER (WHERE deployment_status = 'active'),
        'failed_deployments', COUNT(*) FILTER (WHERE deployment_status = 'failed'),
        'cleanup_candidates', COUNT(*) FILTER (WHERE needs_cleanup = TRUE),
        'last_updated', MAX(updated_at),
        'system_health', CASE 
            WHEN COUNT(*) FILTER (WHERE deployment_status = 'failed') > COUNT(*) * 0.1 THEN 'critical'
            WHEN COUNT(*) FILTER (WHERE needs_cleanup = TRUE) > COUNT(*) * 0.05 THEN 'warning'
            ELSE 'healthy'
        END
    ) INTO result
    FROM panel_deployments 
    WHERE is_active = TRUE;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;