-- Create missing tables for consolidated panel services
-- Migration: 003_create_missing_panel_tables
-- Description: Create tables required by consolidated panel services after refactoring

-- First, let's ensure the panel_deployments table uses correct column names
-- The consolidated service expects 'status' column but migration has 'deployment_status'
DO $$
BEGIN
    -- Rename deployment_status to status if it exists
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'panel_deployments' 
        AND column_name = 'deployment_status'
    ) THEN
        ALTER TABLE panel_deployments RENAME COLUMN deployment_status TO status;
    END IF;
END $$;

-- Create panel_states table
CREATE TABLE IF NOT EXISTS panel_states (
    id SERIAL PRIMARY KEY,
    panel_id VARCHAR(100) NOT NULL UNIQUE,
    state JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create panel_analytics table
CREATE TABLE IF NOT EXISTS panel_analytics (
    id SERIAL PRIMARY KEY,
    panel_id VARCHAR(100) NOT NULL,
    event_type VARCHAR(50) NOT NULL,
    event_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create panel_content table
CREATE TABLE IF NOT EXISTS panel_content (
    id SERIAL PRIMARY KEY,
    panel_id VARCHAR(100) NOT NULL UNIQUE,
    content JSONB NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create panel_versions table
CREATE TABLE IF NOT EXISTS panel_versions (
    id SERIAL PRIMARY KEY,
    panel_id VARCHAR(100) NOT NULL,
    version VARCHAR(50) NOT NULL,
    changes JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create panel_errors table
CREATE TABLE IF NOT EXISTS panel_errors (
    id SERIAL PRIMARY KEY,
    panel_id VARCHAR(100) NOT NULL,
    error_message TEXT NOT NULL,
    error_stack TEXT,
    context JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create user_panel_states table
CREATE TABLE IF NOT EXISTS user_panel_states (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL UNIQUE,
    preferences JSONB DEFAULT '{}',
    panel_history JSONB DEFAULT '[]',
    active_interactions JSONB DEFAULT '[]',
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    settings JSONB DEFAULT '{}',
    notifications JSONB DEFAULT '{}',
    achievements JSONB DEFAULT '[]',
    stats JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create user_subscriptions table
CREATE TABLE IF NOT EXISTS user_subscriptions (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    plan_type VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL,
    features JSONB DEFAULT '[]',
    limits JSONB DEFAULT '{}',
    start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    end_date TIMESTAMP WITH TIME ZONE,
    renewal_date TIMESTAMP WITH TIME ZONE,
    payment_method VARCHAR(100),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create feature_usage table
CREATE TABLE IF NOT EXISTS feature_usage (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    feature VARCHAR(100) NOT NULL,
    usage_count INTEGER DEFAULT 1,
    usage_date DATE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    UNIQUE(user_id, feature, usage_date)
);

-- Create ai_tools table
CREATE TABLE IF NOT EXISTS ai_tools (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    url VARCHAR(500),
    pricing VARCHAR(200),
    features JSONB DEFAULT '[]',
    rating DECIMAL(3,2) DEFAULT 0.0,
    popularity INTEGER DEFAULT 0,
    tags JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create user_tool_interactions table
CREATE TABLE IF NOT EXISTS user_tool_interactions (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    tool_id INTEGER REFERENCES ai_tools(id),
    tool_category VARCHAR(100),
    interaction_type VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create habit_tracking table
CREATE TABLE IF NOT EXISTS habit_tracking (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    habit_id VARCHAR(100) NOT NULL,
    progress_data JSONB NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    UNIQUE(user_id, habit_id)
);

-- Create financial_calculations table
CREATE TABLE IF NOT EXISTS financial_calculations (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    calculation_data JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create troubleshooting_guides table
CREATE TABLE IF NOT EXISTS troubleshooting_guides (
    id SERIAL PRIMARY KEY,
    title VARCHAR(300) NOT NULL,
    category VARCHAR(100) NOT NULL,
    sub_category VARCHAR(100),
    symptoms JSONB DEFAULT '[]',
    diagnostic_steps JSONB DEFAULT '[]',
    solutions JSONB DEFAULT '[]',
    common_causes JSONB DEFAULT '[]',
    prevention_tips JSONB DEFAULT '[]',
    related_issues JSONB DEFAULT '[]',
    difficulty VARCHAR(20) DEFAULT 'medium',
    estimated_time VARCHAR(50),
    relevance_score INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create agent_memory table (if not exists)
CREATE TABLE IF NOT EXISTS agent_memory (
    id SERIAL PRIMARY KEY,
    agent_id VARCHAR(100) NOT NULL,
    user_id VARCHAR(50) NOT NULL,
    interaction_data JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create agent_sessions table
CREATE TABLE IF NOT EXISTS agent_sessions (
    id SERIAL PRIMARY KEY,
    agent_id VARCHAR(100) NOT NULL,
    user_id VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'active',
    agent_state JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create agent_interactions table (if not exists)
CREATE TABLE IF NOT EXISTS agent_interactions (
    id SERIAL PRIMARY KEY,
    agent_id VARCHAR(100) NOT NULL,
    user_id VARCHAR(50) NOT NULL,
    event_type VARCHAR(50) NOT NULL,
    event_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_panel_states_panel_id ON panel_states (panel_id);
CREATE INDEX IF NOT EXISTS idx_panel_analytics_panel_id ON panel_analytics (panel_id);
CREATE INDEX IF NOT EXISTS idx_panel_analytics_event_type ON panel_analytics (event_type);
CREATE INDEX IF NOT EXISTS idx_panel_analytics_created_at ON panel_analytics (created_at);
CREATE INDEX IF NOT EXISTS idx_panel_content_panel_id ON panel_content (panel_id);
CREATE INDEX IF NOT EXISTS idx_panel_versions_panel_id ON panel_versions (panel_id);
CREATE INDEX IF NOT EXISTS idx_panel_versions_version ON panel_versions (version);
CREATE INDEX IF NOT EXISTS idx_panel_errors_panel_id ON panel_errors (panel_id);
CREATE INDEX IF NOT EXISTS idx_panel_errors_created_at ON panel_errors (created_at);

CREATE INDEX IF NOT EXISTS idx_user_panel_states_user_id ON user_panel_states (user_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_user_id ON user_subscriptions (user_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_status ON user_subscriptions (status);
CREATE INDEX IF NOT EXISTS idx_feature_usage_user_id ON feature_usage (user_id);
CREATE INDEX IF NOT EXISTS idx_feature_usage_feature ON feature_usage (feature);
CREATE INDEX IF NOT EXISTS idx_feature_usage_date ON feature_usage (usage_date);

CREATE INDEX IF NOT EXISTS idx_ai_tools_category ON ai_tools (category);
CREATE INDEX IF NOT EXISTS idx_ai_tools_rating ON ai_tools (rating);
CREATE INDEX IF NOT EXISTS idx_ai_tools_popularity ON ai_tools (popularity);
CREATE INDEX IF NOT EXISTS idx_user_tool_interactions_user_id ON user_tool_interactions (user_id);
CREATE INDEX IF NOT EXISTS idx_user_tool_interactions_tool_category ON user_tool_interactions (tool_category);

CREATE INDEX IF NOT EXISTS idx_habit_tracking_user_id ON habit_tracking (user_id);
CREATE INDEX IF NOT EXISTS idx_financial_calculations_user_id ON financial_calculations (user_id);

CREATE INDEX IF NOT EXISTS idx_troubleshooting_guides_category ON troubleshooting_guides (category);
CREATE INDEX IF NOT EXISTS idx_troubleshooting_guides_sub_category ON troubleshooting_guides (sub_category);
CREATE INDEX IF NOT EXISTS idx_troubleshooting_guides_relevance ON troubleshooting_guides (relevance_score);

CREATE INDEX IF NOT EXISTS idx_agent_memory_agent_id ON agent_memory (agent_id);
CREATE INDEX IF NOT EXISTS idx_agent_memory_user_id ON agent_memory (user_id);
CREATE INDEX IF NOT EXISTS idx_agent_sessions_agent_id ON agent_sessions (agent_id);
CREATE INDEX IF NOT EXISTS idx_agent_sessions_user_id ON agent_sessions (user_id);
CREATE INDEX IF NOT EXISTS idx_agent_sessions_status ON agent_sessions (status);
CREATE INDEX IF NOT EXISTS idx_agent_interactions_agent_id ON agent_interactions (agent_id);
CREATE INDEX IF NOT EXISTS idx_agent_interactions_user_id ON agent_interactions (user_id);

-- Insert some sample AI tools data
INSERT INTO ai_tools (name, description, category, url, pricing, features, rating, popularity, tags) VALUES 
('ChatGPT', 'Advanced AI language model for conversations and content creation', 'conversational-ai', 'https://chat.openai.com', 'Free tier + paid plans', '["conversation", "content-creation", "coding", "analysis"]', 4.8, 100, '["ai", "gpt", "openai", "chatbot"]'),
('Claude', 'AI assistant for analysis, writing, math, coding, creative tasks', 'conversational-ai', 'https://claude.ai', 'Free tier + paid plans', '["analysis", "writing", "coding", "creative"]', 4.7, 95, '["ai", "anthropic", "assistant", "analysis"]'),
('GitHub Copilot', 'AI-powered code completion and assistance', 'coding', 'https://github.com/features/copilot', '$10/month', '["code-completion", "code-generation", "ide-integration"]', 4.5, 85, '["coding", "github", "ai", "development"]'),
('Midjourney', 'AI image generation from text prompts', 'image-generation', 'https://midjourney.com', 'Subscription plans', '["image-generation", "art", "creativity"]', 4.6, 90, '["ai", "art", "image", "creative"]'),
('Notion AI', 'AI writing assistant integrated into Notion workspace', 'productivity', 'https://notion.so', 'Add-on to Notion plans', '["writing", "productivity", "workspace"]', 4.3, 70, '["productivity", "notion", "writing", "workspace"]')
ON CONFLICT DO NOTHING;

-- Insert some sample troubleshooting guides
INSERT INTO troubleshooting_guides (title, category, sub_category, symptoms, diagnostic_steps, solutions, common_causes, prevention_tips, difficulty, estimated_time, relevance_score) VALUES 
('Discord Bot Not Responding', 'discord', 'bot-issues', '["Bot offline", "Commands not working", "No response"]', '["Check bot status", "Verify token", "Check permissions", "Review logs"]', '["Restart bot", "Update token", "Fix permissions", "Check code errors"]', '["Invalid token", "Missing permissions", "Code errors", "Server issues"]', '["Regular monitoring", "Token security", "Permission audits"]', 'medium', '15-30 minutes', 10),
('Database Connection Failed', 'database', 'connection', '["Connection timeout", "Authentication failed", "Cannot connect"]', '["Check credentials", "Verify network", "Test connection", "Review configuration"]', '["Update credentials", "Fix network issues", "Restart database", "Update configuration"]', '["Wrong credentials", "Network issues", "Database down", "Configuration errors"]', '["Regular backups", "Monitor connections", "Secure credentials"]', 'medium', '10-20 minutes', 9),
('High Memory Usage', 'performance', 'memory', '["Out of memory errors", "Slow performance", "System crashes"]', '["Check memory usage", "Identify memory leaks", "Review processes", "Analyze logs"]', '["Optimize code", "Fix memory leaks", "Increase memory", "Restart services"]', '["Memory leaks", "Inefficient code", "Large datasets", "Poor optimization"]', '["Code reviews", "Memory profiling", "Regular monitoring"]', 'hard', '30-60 minutes', 8)
ON CONFLICT DO NOTHING;

-- Create updated_at triggers for tables that need them
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply updated_at triggers
DO $$
DECLARE
    table_name TEXT;
    table_names TEXT[] := ARRAY[
        'panel_states', 'user_panel_states', 'user_subscriptions', 
        'ai_tools', 'troubleshooting_guides', 'agent_sessions'
    ];
BEGIN
    FOREACH table_name IN ARRAY table_names
    LOOP
        -- Drop trigger if exists, then create
        EXECUTE format('DROP TRIGGER IF EXISTS %I_updated_at_trigger ON %I', table_name, table_name);
        EXECUTE format('CREATE TRIGGER %I_updated_at_trigger 
                       BEFORE UPDATE ON %I 
                       FOR EACH ROW 
                       EXECUTE FUNCTION update_updated_at_column()', table_name, table_name);
    END LOOP;
END $$;

-- Create a function to clean up old data periodically
CREATE OR REPLACE FUNCTION cleanup_old_panel_data()
RETURNS void AS $$
BEGIN
    -- Clean up old panel analytics (older than 90 days)
    DELETE FROM panel_analytics WHERE created_at < NOW() - INTERVAL '90 days';
    
    -- Clean up old panel errors (older than 30 days) 
    DELETE FROM panel_errors WHERE created_at < NOW() - INTERVAL '30 days';
    
    -- Clean up old agent interactions (older than 60 days)
    DELETE FROM agent_interactions WHERE created_at < NOW() - INTERVAL '60 days';
    
    -- Clean up old feature usage data (older than 1 year)
    DELETE FROM feature_usage WHERE created_at < NOW() - INTERVAL '1 year';
END;
$$ LANGUAGE plpgsql;

-- Grant permissions (uncomment and adjust for your setup)
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO your_app_user;
-- GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO your_app_user;