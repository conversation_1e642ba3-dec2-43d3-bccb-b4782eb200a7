-- Create panel deployment tracking tables
-- Migration: 002_create_panel_deployment_tables
-- Description: Add persistent state management for panel deployments

-- Create panel_deployments table
CREATE TABLE IF NOT EXISTS panel_deployments (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Core deployment info
    channel_id VARCHAR(50) NOT NULL,
    guild_id VARCHAR(50) NOT NULL,
    panel_id VARCHAR(100) NOT NULL,
    message_id VARCHAR(50),
    
    -- Deployment state
    deployment_status VARCHAR(20) NOT NULL DEFAULT 'active',
    last_update_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    last_successful_update TIMESTAMP WITH TIME ZONE,
    panel_version VARCHAR(50) DEFAULT '1.0.0',
    content_hash VARCHAR(64), -- SHA-256 hash for change detection
    
    -- Configuration
    deployment_config J<PERSON><PERSON><PERSON>,
    discord_state JSONB,
    deployment_metrics JSONB,
    error_state JSONB,
    
    -- Control flags
    is_active BOOLEAN DEFAULT TRUE NOT NULL,
    scheduled_update_at TIMESTAMP WITH TIME ZONE,
    retry_count INTEGER DEFAULT 0 NOT NULL,
    max_retries INTEGER DEFAULT 3 NOT NULL,
    
    -- Metadata
    metadata JSONB
);

-- Create deployment_logs table
CREATE TABLE IF NOT EXISTS deployment_logs (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    deployment_id INTEGER NOT NULL,
    log_level VARCHAR(10) NOT NULL,
    message TEXT NOT NULL,
    context JSONB,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create indexes for panel_deployments
CREATE INDEX IF NOT EXISTS panel_deployments_channel_id_idx ON panel_deployments (channel_id);
CREATE INDEX IF NOT EXISTS panel_deployments_guild_id_idx ON panel_deployments (guild_id);
CREATE INDEX IF NOT EXISTS panel_deployments_panel_id_idx ON panel_deployments (panel_id);
CREATE INDEX IF NOT EXISTS panel_deployments_message_id_idx ON panel_deployments (message_id);
CREATE INDEX IF NOT EXISTS panel_deployments_status_idx ON panel_deployments (deployment_status);
CREATE INDEX IF NOT EXISTS panel_deployments_last_update_idx ON panel_deployments (last_update_at);
CREATE INDEX IF NOT EXISTS panel_deployments_active_idx ON panel_deployments (is_active);
CREATE INDEX IF NOT EXISTS panel_deployments_scheduled_idx ON panel_deployments (scheduled_update_at);
CREATE INDEX IF NOT EXISTS panel_deployments_content_hash_idx ON panel_deployments (content_hash);

-- Unique constraint to prevent duplicate deployments per channel-panel combination
CREATE UNIQUE INDEX IF NOT EXISTS panel_deployments_channel_panel_unique 
ON panel_deployments (channel_id, panel_id) 
WHERE deleted_at IS NULL;

-- Create indexes for deployment_logs
CREATE INDEX IF NOT EXISTS deployment_logs_deployment_id_idx ON deployment_logs (deployment_id);
CREATE INDEX IF NOT EXISTS deployment_logs_log_level_idx ON deployment_logs (log_level);
CREATE INDEX IF NOT EXISTS deployment_logs_timestamp_idx ON deployment_logs (timestamp);

-- Add foreign key constraint
ALTER TABLE deployment_logs 
ADD CONSTRAINT fk_deployment_logs_deployment_id 
FOREIGN KEY (deployment_id) REFERENCES panel_deployments (id) 
ON DELETE CASCADE;

-- Create updated_at trigger for panel_deployments
CREATE OR REPLACE FUNCTION update_panel_deployments_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER panel_deployments_updated_at_trigger
    BEFORE UPDATE ON panel_deployments
    FOR EACH ROW
    EXECUTE FUNCTION update_panel_deployments_updated_at();

-- Create updated_at trigger for deployment_logs
CREATE OR REPLACE FUNCTION update_deployment_logs_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER deployment_logs_updated_at_trigger
    BEFORE UPDATE ON deployment_logs
    FOR EACH ROW
    EXECUTE FUNCTION update_deployment_logs_updated_at();

-- Insert default deployment configurations
INSERT INTO panel_deployments (
    channel_id, 
    guild_id, 
    panel_id, 
    deployment_config, 
    deployment_status,
    metadata
) VALUES 
('default', 'default', 'system_default', 
 '{"autoUpdate": true, "updateInterval": 21600000, "updateStrategy": "immediate", "maxRetries": 3}',
 'inactive',
 '{"deploymentSource": "system", "notes": "Default configuration template"}'
)
ON CONFLICT DO NOTHING;

-- Create view for active deployments with enhanced information
CREATE OR REPLACE VIEW active_panel_deployments AS
SELECT 
    pd.*,
    (pd.discord_state->>'channelName') as channel_name,
    (pd.discord_state->>'categoryName') as category_name,
    (pd.discord_state->>'guildName') as guild_name,
    (pd.deployment_metrics->>'totalDeployments')::INTEGER as total_deployments,
    (pd.deployment_metrics->>'successfulDeployments')::INTEGER as successful_deployments,
    (pd.deployment_metrics->>'failedDeployments')::INTEGER as failed_deployments,
    (pd.error_state->>'hasErrors')::BOOLEAN as has_errors,
    (pd.error_state->>'errorCount')::INTEGER as error_count,
    CASE 
        WHEN pd.scheduled_update_at IS NOT NULL AND pd.scheduled_update_at <= NOW() 
        THEN true 
        ELSE false 
    END as needs_update,
    EXTRACT(EPOCH FROM (NOW() - pd.last_update_at)) / 3600 as hours_since_update
FROM panel_deployments pd
WHERE pd.is_active = true 
  AND pd.deleted_at IS NULL
  AND pd.deployment_status IN ('active', 'updating', 'scheduled');

-- Grant necessary permissions (adjust as needed for your setup)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON panel_deployments TO your_app_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON deployment_logs TO your_app_user;
-- GRANT SELECT ON active_panel_deployments TO your_app_user;
-- GRANT USAGE, SELECT ON SEQUENCE panel_deployments_id_seq TO your_app_user;
-- GRANT USAGE, SELECT ON SEQUENCE deployment_logs_id_seq TO your_app_user;