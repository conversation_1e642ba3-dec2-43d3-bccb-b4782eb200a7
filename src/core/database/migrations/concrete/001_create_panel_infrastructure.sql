-- Migration: Create Panel Infrastructure Tables
-- Purpose: Add user state persistence, dynamic content, and analytics infrastructure
-- Author: <PERSON> Assistant
-- Date: 2025-01-29

BEGIN;

-- User Panel States Table
CREATE TABLE IF NOT EXISTS user_panel_states (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    panel_id VARCHAR(100) NOT NULL,
    channel_id VARCHAR(50) NOT NULL,
    guild_id VARCHAR(50) NOT NULL,
    preferences JSONB DEFAULT '{}',
    progress JSONB DEFAULT '{}',
    interaction_history JSONB DEFAULT '[]',
    favorites JSONB DEFAULT '{"panels": [], "features": [], "content": [], "shortcuts": []}',
    last_interaction_at TIMESTAMP WITH TIME ZONE,
    session_count JSONB DEFAULT '{"total": 0, "thisWeek": 0, "thisMonth": 0, "lastReset": ""}',
    is_active BOOLEAN DEFAULT TRUE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Panel Analytics Table
CREATE TABLE IF NOT EXISTS panel_analytics (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(50),
    panel_id VARCHAR(100) NOT NULL,
    channel_id VARCHAR(50) NOT NULL,
    guild_id VARCHAR(50) NOT NULL,
    event_type VARCHAR(50) NOT NULL,
    event_data JSONB DEFAULT '{}',
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    session_id VARCHAR(100),
    ip VARCHAR(45),
    user_agent TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Panel Performance Metrics Table
CREATE TABLE IF NOT EXISTS panel_performance_metrics (
    id SERIAL PRIMARY KEY,
    panel_id VARCHAR(100) NOT NULL,
    channel_id VARCHAR(50) NOT NULL,
    guild_id VARCHAR(50) NOT NULL,
    date TIMESTAMP WITH TIME ZONE NOT NULL,
    metrics JSONB DEFAULT '{}',
    is_aggregated BOOLEAN DEFAULT FALSE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- A/B Test Results Table
CREATE TABLE IF NOT EXISTS ab_test_results (
    id SERIAL PRIMARY KEY,
    test_id VARCHAR(100) NOT NULL,
    panel_id VARCHAR(100) NOT NULL,
    variant VARCHAR(50) NOT NULL,
    user_id VARCHAR(50),
    channel_id VARCHAR(50) NOT NULL,
    guild_id VARCHAR(50) NOT NULL,
    outcome VARCHAR(50) NOT NULL,
    value INTEGER NOT NULL,
    test_started TIMESTAMP WITH TIME ZONE NOT NULL,
    test_ended TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Dynamic Content Sources Table
CREATE TABLE IF NOT EXISTS dynamic_content_sources (
    id SERIAL PRIMARY KEY,
    source_id VARCHAR(100) NOT NULL UNIQUE,
    name VARCHAR(200) NOT NULL,
    type VARCHAR(50) NOT NULL,
    config JSONB DEFAULT '{}',
    refresh_interval VARCHAR(20) DEFAULT '3600' NOT NULL,
    last_fetched TIMESTAMP WITH TIME ZONE,
    last_successful TIMESTAMP WITH TIME ZONE,
    error_count JSONB DEFAULT '{"consecutive": 0, "total": 0}',
    is_active BOOLEAN DEFAULT TRUE NOT NULL,
    priority VARCHAR(20) DEFAULT 'medium' NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Dynamic Content Cache Table
CREATE TABLE IF NOT EXISTS dynamic_content_cache (
    id SERIAL PRIMARY KEY,
    cache_key VARCHAR(200) NOT NULL UNIQUE,
    source_id VARCHAR(100) NOT NULL,
    data JSONB,
    metadata JSONB DEFAULT '{}',
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    access_count JSONB DEFAULT '{"total": 0, "thisHour": 0, "thisDay": 0, "lastAccessed": ""}',
    tags JSONB DEFAULT '[]',
    is_stale BOOLEAN DEFAULT FALSE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Panel Content Mappings Table
CREATE TABLE IF NOT EXISTS panel_content_mappings (
    id SERIAL PRIMARY KEY,
    panel_id VARCHAR(100) NOT NULL,
    source_id VARCHAR(100) NOT NULL,
    mapping JSONB DEFAULT '{}',
    priority VARCHAR(20) DEFAULT 'medium' NOT NULL,
    is_active BOOLEAN DEFAULT TRUE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Content Freshness Tracking Table
CREATE TABLE IF NOT EXISTS content_freshness_tracking (
    id SERIAL PRIMARY KEY,
    content_id VARCHAR(200) NOT NULL,
    source_id VARCHAR(100) NOT NULL,
    last_checked TIMESTAMP WITH TIME ZONE NOT NULL,
    last_changed TIMESTAMP WITH TIME ZONE,
    change_frequency JSONB DEFAULT '{}',
    staleness_score JSONB DEFAULT '{}',
    refresh_strategy VARCHAR(50) DEFAULT 'interval' NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create Indexes for Performance

-- User Panel States Indexes
CREATE INDEX IF NOT EXISTS user_panel_states_user_id_idx ON user_panel_states(user_id);
CREATE INDEX IF NOT EXISTS user_panel_states_panel_id_idx ON user_panel_states(panel_id);
CREATE INDEX IF NOT EXISTS user_panel_states_channel_id_idx ON user_panel_states(channel_id);
CREATE INDEX IF NOT EXISTS user_panel_states_guild_id_idx ON user_panel_states(guild_id);
CREATE INDEX IF NOT EXISTS user_panel_states_last_interaction_idx ON user_panel_states(last_interaction_at);
CREATE UNIQUE INDEX IF NOT EXISTS user_panel_states_user_panel_unique ON user_panel_states(user_id, panel_id, channel_id);

-- Panel Analytics Indexes
CREATE INDEX IF NOT EXISTS panel_analytics_user_id_idx ON panel_analytics(user_id);
CREATE INDEX IF NOT EXISTS panel_analytics_panel_id_idx ON panel_analytics(panel_id);
CREATE INDEX IF NOT EXISTS panel_analytics_channel_id_idx ON panel_analytics(channel_id);
CREATE INDEX IF NOT EXISTS panel_analytics_guild_id_idx ON panel_analytics(guild_id);
CREATE INDEX IF NOT EXISTS panel_analytics_event_type_idx ON panel_analytics(event_type);
CREATE INDEX IF NOT EXISTS panel_analytics_timestamp_idx ON panel_analytics(timestamp);
CREATE INDEX IF NOT EXISTS panel_analytics_session_id_idx ON panel_analytics(session_id);

-- Panel Performance Metrics Indexes
CREATE INDEX IF NOT EXISTS panel_performance_metrics_panel_id_idx ON panel_performance_metrics(panel_id);
CREATE INDEX IF NOT EXISTS panel_performance_metrics_channel_id_idx ON panel_performance_metrics(channel_id);
CREATE INDEX IF NOT EXISTS panel_performance_metrics_guild_id_idx ON panel_performance_metrics(guild_id);
CREATE INDEX IF NOT EXISTS panel_performance_metrics_date_idx ON panel_performance_metrics(date);
CREATE INDEX IF NOT EXISTS panel_performance_metrics_aggregated_idx ON panel_performance_metrics(is_aggregated);

-- A/B Test Results Indexes
CREATE INDEX IF NOT EXISTS ab_test_results_test_id_idx ON ab_test_results(test_id);
CREATE INDEX IF NOT EXISTS ab_test_results_panel_id_idx ON ab_test_results(panel_id);
CREATE INDEX IF NOT EXISTS ab_test_results_variant_idx ON ab_test_results(variant);
CREATE INDEX IF NOT EXISTS ab_test_results_user_id_idx ON ab_test_results(user_id);
CREATE INDEX IF NOT EXISTS ab_test_results_outcome_idx ON ab_test_results(outcome);
CREATE INDEX IF NOT EXISTS ab_test_results_test_started_idx ON ab_test_results(test_started);

-- Dynamic Content Sources Indexes
CREATE INDEX IF NOT EXISTS dynamic_content_sources_source_id_idx ON dynamic_content_sources(source_id);
CREATE INDEX IF NOT EXISTS dynamic_content_sources_type_idx ON dynamic_content_sources(type);
CREATE INDEX IF NOT EXISTS dynamic_content_sources_last_fetched_idx ON dynamic_content_sources(last_fetched);
CREATE INDEX IF NOT EXISTS dynamic_content_sources_priority_idx ON dynamic_content_sources(priority);
CREATE INDEX IF NOT EXISTS dynamic_content_sources_active_idx ON dynamic_content_sources(is_active);

-- Dynamic Content Cache Indexes
CREATE INDEX IF NOT EXISTS dynamic_content_cache_cache_key_idx ON dynamic_content_cache(cache_key);
CREATE INDEX IF NOT EXISTS dynamic_content_cache_source_id_idx ON dynamic_content_cache(source_id);
CREATE INDEX IF NOT EXISTS dynamic_content_cache_expires_at_idx ON dynamic_content_cache(expires_at);
CREATE INDEX IF NOT EXISTS dynamic_content_cache_is_stale_idx ON dynamic_content_cache(is_stale);

-- Panel Content Mappings Indexes
CREATE INDEX IF NOT EXISTS panel_content_mappings_panel_id_idx ON panel_content_mappings(panel_id);
CREATE INDEX IF NOT EXISTS panel_content_mappings_source_id_idx ON panel_content_mappings(source_id);
CREATE INDEX IF NOT EXISTS panel_content_mappings_priority_idx ON panel_content_mappings(priority);
CREATE INDEX IF NOT EXISTS panel_content_mappings_active_idx ON panel_content_mappings(is_active);
CREATE UNIQUE INDEX IF NOT EXISTS panel_content_mappings_panel_source_unique ON panel_content_mappings(panel_id, source_id);

-- Content Freshness Tracking Indexes
CREATE INDEX IF NOT EXISTS content_freshness_tracking_content_id_idx ON content_freshness_tracking(content_id);
CREATE INDEX IF NOT EXISTS content_freshness_tracking_source_id_idx ON content_freshness_tracking(source_id);
CREATE INDEX IF NOT EXISTS content_freshness_tracking_last_checked_idx ON content_freshness_tracking(last_checked);
CREATE INDEX IF NOT EXISTS content_freshness_tracking_refresh_strategy_idx ON content_freshness_tracking(refresh_strategy);

-- Add Foreign Key Constraints (if referencing existing tables)
-- Note: Uncomment these if the referenced tables exist and you want referential integrity

-- ALTER TABLE user_panel_states 
--     ADD CONSTRAINT fk_user_panel_states_user_id 
--     FOREIGN KEY (user_id) REFERENCES users(discord_id) ON DELETE CASCADE;

-- ALTER TABLE panel_analytics 
--     ADD CONSTRAINT fk_panel_analytics_user_id 
--     FOREIGN KEY (user_id) REFERENCES users(discord_id) ON DELETE SET NULL;

-- ALTER TABLE ab_test_results 
--     ADD CONSTRAINT fk_ab_test_results_user_id 
--     FOREIGN KEY (user_id) REFERENCES users(discord_id) ON DELETE SET NULL;

-- ALTER TABLE dynamic_content_cache 
--     ADD CONSTRAINT fk_dynamic_content_cache_source_id 
--     FOREIGN KEY (source_id) REFERENCES dynamic_content_sources(source_id) ON DELETE CASCADE;

-- ALTER TABLE panel_content_mappings 
--     ADD CONSTRAINT fk_panel_content_mappings_source_id 
--     FOREIGN KEY (source_id) REFERENCES dynamic_content_sources(source_id) ON DELETE CASCADE;

-- ALTER TABLE content_freshness_tracking 
--     ADD CONSTRAINT fk_content_freshness_tracking_source_id 
--     FOREIGN KEY (source_id) REFERENCES dynamic_content_sources(source_id) ON DELETE CASCADE;

-- Create Functions for Automatic Timestamp Updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create Triggers for Automatic Timestamp Updates
DROP TRIGGER IF EXISTS update_user_panel_states_updated_at ON user_panel_states;
CREATE TRIGGER update_user_panel_states_updated_at 
    BEFORE UPDATE ON user_panel_states 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_panel_analytics_updated_at ON panel_analytics;
CREATE TRIGGER update_panel_analytics_updated_at 
    BEFORE UPDATE ON panel_analytics 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_panel_performance_metrics_updated_at ON panel_performance_metrics;
CREATE TRIGGER update_panel_performance_metrics_updated_at 
    BEFORE UPDATE ON panel_performance_metrics 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_ab_test_results_updated_at ON ab_test_results;
CREATE TRIGGER update_ab_test_results_updated_at 
    BEFORE UPDATE ON ab_test_results 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_dynamic_content_sources_updated_at ON dynamic_content_sources;
CREATE TRIGGER update_dynamic_content_sources_updated_at 
    BEFORE UPDATE ON dynamic_content_sources 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_dynamic_content_cache_updated_at ON dynamic_content_cache;
CREATE TRIGGER update_dynamic_content_cache_updated_at 
    BEFORE UPDATE ON dynamic_content_cache 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_panel_content_mappings_updated_at ON panel_content_mappings;
CREATE TRIGGER update_panel_content_mappings_updated_at 
    BEFORE UPDATE ON panel_content_mappings 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_content_freshness_tracking_updated_at ON content_freshness_tracking;
CREATE TRIGGER update_content_freshness_tracking_updated_at 
    BEFORE UPDATE ON content_freshness_tracking 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Grant Permissions (adjust as needed for your user setup)
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO your_app_user;
-- GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO your_app_user;

COMMIT;

-- Verification Queries (run after migration to verify)
-- SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name LIKE '%panel%' OR table_name LIKE '%content%';
-- SELECT indexname FROM pg_indexes WHERE tablename LIKE '%panel%' OR tablename LIKE '%content%';