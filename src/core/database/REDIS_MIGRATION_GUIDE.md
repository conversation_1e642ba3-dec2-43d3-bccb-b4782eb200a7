# Redis Data Access Layer Migration Guide

## Overview

This project has been migrated from Drizzle ORM with PostgreSQL to a comprehensive Redis-based data access layer. This guide provides information on using the new Redis DAL.

## Architecture

### Core Components

1. **RedisService** - Core Redis operations and connection management
2. **BaseRepository** - Generic repository pattern for all entities
3. **RedisQueryBuilder** - SQL-like query building for Redis operations
4. **RedisDatabaseService** - Main service providing access to all repositories
5. **Utilities** - Migration tools, data management, and health monitoring

### Key Features

- ✅ Generic repository pattern with TypeScript support
- ✅ SQL-like query builder for complex operations
- ✅ Automatic connection management with retry logic
- ✅ Transaction support using Redis pipelines
- ✅ Built-in caching and performance optimization
- ✅ Data integrity validation
- ✅ Backup and restore utilities
- ✅ Health monitoring and metrics
- ✅ Migration system for schema changes

## Usage Examples

### Basic Usage

```typescript
import { RedisDatabaseService } from '@core/database';

@Injectable()
export class UserService {
  constructor(private readonly db: RedisDatabaseService) {}

  async createUser(userData: CreateUserDto) {
    return this.db.users.create({
      discordId: userData.discordId,
      username: userData.username,
      email: userData.email,
      isActive: true
    });
  }

  async findUserByDiscordId(discordId: string) {
    return this.db.users.findByDiscordId(discordId);
  }

  async updateUserExperience(userId: string, amount: number) {
    return this.db.users.addExperience(userId, amount);
  }
}
```

### Advanced Queries

```typescript
// Using the query builder
const activeUsersWithHighExperience = await this.db.users.find({
  where: { isActive: true },
  orderBy: { field: 'experience', direction: 'DESC' },
  limit: 10
});

// Complex filtering
const recentlyActiveUsers = await this.db.users.findRecentlyActive(24);

// Search functionality
const searchResults = await this.db.users.searchUsers('john', 20);

// Aggregation
const userStats = await this.db.users.getUserStats();
```

### Repository Pattern

```typescript
// Each entity has its own repository with specific methods
await this.db.sessions.createSession({
  sessionId: 'unique-session-id',
  userId: 'discord-user-id',
  expiryHours: 24
});

await this.db.guilds.findByDiscordId(guildId);
await this.db.guilds.updateSettings(guildId, { welcomeEnabled: true });
```

### Transactions

```typescript
const transaction = await this.db.redis.transaction();

try {
  // Multiple operations in transaction
  transaction.pipeline.hset('user:1', 'balance', '1000');
  transaction.pipeline.hset('user:2', 'balance', '500');
  
  await transaction.exec();
} catch (error) {
  // Transaction automatically rolled back on error
  console.error('Transaction failed:', error);
}
```

### Health Monitoring

```typescript
// Check database health
const health = await this.db.healthCheck();
console.log('Database status:', health.status);

// Get performance metrics
const metrics = await this.db.getMetrics();
console.log('Total keys:', metrics.storage.totalKeys);

// Validate data integrity
const integrity = await this.db.validateIntegrity();
if (!integrity.valid) {
  console.warn('Data integrity issues found:', integrity.issues);
}
```

## Configuration

### Environment Variables

```bash
# Required
REDIS_URL=redis://localhost:6379
# OR individual settings:
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your-password  # optional
REDIS_DB=0                    # optional, defaults to 0
```

### Module Setup

```typescript
import { RedisDatabaseModule } from '@core/database';

@Module({
  imports: [
    RedisDatabaseModule,
    // other imports
  ],
})
export class AppModule {}
```

## Key Differences from Drizzle/PostgreSQL

### Data Storage
- **Before**: Relational tables with foreign keys
- **After**: Redis hashes with referential integrity maintained in application code

### Queries
- **Before**: SQL queries with JOIN operations
- **After**: Redis operations with application-level joins for complex relationships

### Transactions
- **Before**: Database transactions with ACID properties
- **After**: Redis pipelines for atomic operations

### Schema Evolution
- **Before**: Database migrations
- **After**: Redis migration utilities with versioning

## Best Practices

### 1. Use Repository Methods
Always use the provided repository methods instead of direct Redis calls:

```typescript
// Good
await this.db.users.findByDiscordId(discordId);

// Avoid direct Redis calls unless necessary
await this.db.redis.getClient().get('users:123');
```

### 2. Handle Relationships
Since Redis doesn't have foreign keys, maintain referential integrity in your application:

```typescript
// When creating a session, ensure the user exists
const user = await this.db.users.findById(userId);
if (!user) {
  throw new Error('User not found');
}

const session = await this.db.sessions.create({
  userId: user.id,
  // other session data
});
```

### 3. Use Transactions for Related Operations

```typescript
// When updating multiple related entities
const transaction = await this.db.redis.transaction();
// Perform multiple operations
await transaction.exec();
```

### 4. Monitor Performance

```typescript
// Regular health checks
const health = await this.db.healthCheck();
if (health.status !== 'healthy') {
  // Alert or take corrective action
}
```

## Migration from Existing Code

### 1. Update Imports
```typescript
// Old
import { DatabaseService } from '@core/database/database.service';

// New  
import { RedisDatabaseService } from '@core/database';
```

### 2. Update Queries
```typescript
// Old Drizzle query
const users = await this.db.select().from(usersTable).where(eq(usersTable.isActive, true));

// New Redis query
const users = await this.db.users.find({ where: { isActive: true } });
```

### 3. Handle Relationships
```typescript
// Old with JOIN
const userWithSessions = await this.db.select().from(usersTable)
  .leftJoin(sessionsTable, eq(usersTable.id, sessionsTable.userId));

// New with separate queries
const user = await this.db.users.findById(userId);
const sessions = await this.db.sessions.findByUserId(userId);
```

## Utilities and Tools

### Backup and Restore
```typescript
// Create backup
const backup = await this.db.backup();
// Save backup to file or external storage

// Restore from backup
await this.db.restore(backup, true); // true = clear existing data
```

### Data Cleanup
```typescript
// Clean expired sessions and temporary data
const cleanupResult = await this.db.data.cleanup();
console.log(`Cleaned up ${cleanupResult.deletedKeys} expired keys`);
```

### Migration System
```typescript
const migrations = [
  {
    version: '001',
    description: 'Initial setup',
    up: async (redis) => {
      // Migration logic
    },
    down: async (redis) => {
      // Rollback logic
    }
  }
];

await this.db.migration.migrate(migrations);
```

## Performance Considerations

1. **Indexing**: Use Redis sorted sets for fields that need sorting/range queries
2. **Memory Usage**: Monitor Redis memory usage, consider data expiration policies
3. **Connection Pooling**: Built-in connection management handles pooling
4. **Batch Operations**: Use transactions for multiple related operations
5. **Caching**: Repository methods include intelligent caching where appropriate

## Troubleshooting

### Common Issues

1. **Connection Failed**: Check REDIS_URL configuration
2. **Memory Issues**: Monitor Redis memory usage with `db.getMetrics()`
3. **Data Integrity**: Run `db.validateIntegrity()` to check for orphaned references
4. **Performance**: Use `db.healthCheck()` to monitor latency

### Debug Mode

Enable Redis command logging by setting NODE_ENV to 'development'.

## Support

This Redis DAL provides:
- Type-safe operations
- Comprehensive error handling
- Performance monitoring
- Data integrity validation
- Easy migration from relational databases

For additional custom repositories or specific use cases, extend the BaseRepository class or use the RepositoryFactory.