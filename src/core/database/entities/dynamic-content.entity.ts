
import { boolean, index, jsonb, pgTable, text, timestamp, varchar } from 'drizzle-orm/pg-core';
import { baseColumns } from './base.entity';

export const dynamicContentSources = pgTable('dynamic_content_sources', {
  ...baseColumns,
  sourceId: varchar('source_id', { length: 100 }).notNull().unique(),
  name: varchar('name', { length: 200 }).notNull(),
  type: varchar('type', { length: 50 }).notNull(), // 'api', 'rss', 'websocket', 'database', 'custom'
  config: jsonb('config').$type<{
    url?: string;
    method?: 'GET' | 'POST' | 'PUT' | 'PATCH';
    headers?: Record<string, string>;
    params?: Record<string, any>;
    authentication?: {
      type: 'bearer' | 'api-key' | 'basic' | 'oauth2';
      credentials: Record<string, string>;
    };
    rateLimit?: {
      requests: number;
      window: number; // seconds
      retryAfter?: number;
    };
    caching?: {
      ttl: number; // seconds
      strategy: 'memory' | 'redis' | 'database';
      key?: string;
    };
    transformation?: {
      jsonPath?: string;
      mapping?: Record<string, string>;
      filters?: Array<{
        field: string;
        operator: 'equals' | 'contains' | 'greater' | 'less' | 'regex';
        value: any;
      }>;
    };
    pagination?: {
      type: 'offset' | 'cursor' | 'page';
      limit: number;
      offsetParam?: string;
      cursorParam?: string;
      pageParam?: string;
    };
  }>(),
  refreshInterval: varchar('refresh_interval', { length: 20 }).notNull().default('3600'), // seconds
  lastFetched: timestamp('last_fetched', { withTimezone: true }),
  lastSuccessful: timestamp('last_successful', { withTimezone: true }),
  errorCount: jsonb('error_count').$type<{
    consecutive: number;
    total: number;
    lastError?: {
      message: string;
      code?: string;
      timestamp: string;
    };
  }>(),
  isActive: boolean('is_active').default(true).notNull(),
  priority: varchar('priority', { length: 20 }).default('medium').notNull(), // 'low', 'medium', 'high', 'critical'
}, (table) => ({
  sourceIdIdx: index('dynamic_content_sources_source_id_idx').on(table.sourceId),
  typeIdx: index('dynamic_content_sources_type_idx').on(table.type),
  lastFetchedIdx: index('dynamic_content_sources_last_fetched_idx').on(table.lastFetched),
  priorityIdx: index('dynamic_content_sources_priority_idx').on(table.priority),
  activeIdx: index('dynamic_content_sources_active_idx').on(table.isActive),
}));

export const dynamicContentCache = pgTable('dynamic_content_cache', {
  ...baseColumns,
  cacheKey: varchar('cache_key', { length: 200 }).notNull().unique(),
  sourceId: varchar('source_id', { length: 100 }).notNull(),
  data: jsonb('data').$type<any>(),
  metadata: jsonb('metadata').$type<{
    size: number;
    checksum: string;
    contentType?: string;
    encoding?: string;
    lastModified?: string;
    etag?: string;
    headers?: Record<string, string>;
  }>(),
  expiresAt: timestamp('expires_at', { withTimezone: true }).notNull(),
  accessCount: jsonb('access_count').$type<{
    total: number;
    thisHour: number;
    thisDay: number;
    lastAccessed: string;
  }>(),
  tags: jsonb('tags').$type<string[]>(),
  isStale: boolean('is_stale').default(false).notNull(),
}, (table) => ({
  cacheKeyIdx: index('dynamic_content_cache_cache_key_idx').on(table.cacheKey),
  sourceIdIdx: index('dynamic_content_cache_source_id_idx').on(table.sourceId),
  expiresAtIdx: index('dynamic_content_cache_expires_at_idx').on(table.expiresAt),
  isStaleIdx: index('dynamic_content_cache_is_stale_idx').on(table.isStale),
}));

export const panelContentMappings = pgTable('panel_content_mappings', {
  ...baseColumns,
  panelId: varchar('panel_id', { length: 100 }).notNull(),
  sourceId: varchar('source_id', { length: 100 }).notNull(),
  mapping: jsonb('mapping').$type<{
    contentType: 'embed' | 'button' | 'select' | 'text' | 'image' | 'link';
    fieldMappings: Array<{
      sourceField: string;
      targetField: string;
      transformer?: 'date' | 'currency' | 'percentage' | 'truncate' | 'uppercase' | 'lowercase' | 'markdown';
      params?: Record<string, any>;
    }>;
    conditions?: Array<{
      field: string;
      operator: 'equals' | 'contains' | 'greater' | 'less' | 'exists';
      value: any;
    }>;
    template?: string; // Template for rendering content
    maxItems?: number;
    sortBy?: {
      field: string;
      order: 'asc' | 'desc';
    };
  }>(),
  priority: varchar('priority', { length: 20 }).default('medium').notNull(),
  isActive: boolean('is_active').default(true).notNull(),
}, (table) => ({
  panelIdIdx: index('panel_content_mappings_panel_id_idx').on(table.panelId),
  sourceIdIdx: index('panel_content_mappings_source_id_idx').on(table.sourceId),
  priorityIdx: index('panel_content_mappings_priority_idx').on(table.priority),
  activeIdx: index('panel_content_mappings_active_idx').on(table.isActive),
  panelSourceUnique: index('panel_content_mappings_panel_source_unique').on(table.panelId, table.sourceId),
}));

export const contentFreshnessTracking = pgTable('content_freshness_tracking', {
  ...baseColumns,
  contentId: varchar('content_id', { length: 200 }).notNull(),
  sourceId: varchar('source_id', { length: 100 }).notNull(),
  lastChecked: timestamp('last_checked', { withTimezone: true }).notNull(),
  lastChanged: timestamp('last_changed', { withTimezone: true }),
  changeFrequency: jsonb('change_frequency').$type<{
    average: number; // seconds between changes
    pattern: 'constant' | 'periodic' | 'random' | 'decreasing' | 'increasing';
    predictions: Array<{
      nextChange: string;
      confidence: number;
    }>;
    historicalChanges: Array<{
      timestamp: string;
      changeType: 'minor' | 'major' | 'structural';
      diff?: any;
    }>;
  }>(),
  stalenessScore: jsonb('staleness_score').$type<{
    current: number; // 0-100, higher = more stale
    threshold: number;
    factors: Array<{
      name: string;
      weight: number;
      value: number;
    }>;
  }>(),
  refreshStrategy: varchar('refresh_strategy', { length: 50 }).default('interval').notNull(), // 'interval', 'adaptive', 'on-demand', 'hybrid'
}, (table) => ({
  contentIdIdx: index('content_freshness_tracking_content_id_idx').on(table.contentId),
  sourceIdIdx: index('content_freshness_tracking_source_id_idx').on(table.sourceId),
  lastCheckedIdx: index('content_freshness_tracking_last_checked_idx').on(table.lastChecked),
  refreshStrategyIdx: index('content_freshness_tracking_refresh_strategy_idx').on(table.refreshStrategy),
}));

