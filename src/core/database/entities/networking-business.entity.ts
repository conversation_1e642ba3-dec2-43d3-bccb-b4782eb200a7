
import { boolean, integer, jsonb, pgTable, text, timestamp, varchar, decimal } from 'drizzle-orm/pg-core';
import { baseColumns } from './base.entity';
import { users } from './user.entity';

export const professionalProfiles = pgTable('professional_profiles', {
  ...baseColumns,
  userId: varchar('user_id', { length: 50 }).notNull().unique(),
  displayName: varchar('display_name', { length: 255 }).notNull(),
  title: varchar('title', { length: 255 }).notNull(),
  company: varchar('company', { length: 255 }),
  industry: varchar('industry', { length: 100 }).notNull(),
  location: varchar('location', { length: 255 }),
  bio: text('bio'),
  skills: jsonb('skills').$type<string[]>(),
  experience: varchar('experience', { length: 50 }), // 'entry' | 'junior' | 'mid' | 'senior' | 'executive'
  education: jsonb('education').$type<Array<{
    degree: string;
    institution: string;
    year: number;
    field?: string;
  }>>(),
  certifications: jsonb('certifications').$type<string[]>(),
  portfolioUrl: text('portfolio_url'),
  linkedinUrl: text('linkedin_url'),
  githubUrl: text('github_url'),
  websiteUrl: text('website_url'),
  isAvailableForHire: boolean('is_available_for_hire').default(false).notNull(),
  isOpenToCollaboration: boolean('is_open_to_collaboration').default(true).notNull(),
  hourlyRate: decimal('hourly_rate', { precision: 10, scale: 2 }),
  currency: varchar('currency', { length: 3 }).default('USD'),
  guildId: varchar('guild_id', { length: 50 }).notNull(),
  connectionCount: integer('connection_count').default(0).notNull(),
  endorsementCount: integer('endorsement_count').default(0).notNull(),
  isVerified: boolean('is_verified').default(false).notNull(),
  verifiedAt: timestamp('verified_at', { withTimezone: true }),
});

export const jobPostings = pgTable('job_postings', {
  ...baseColumns,
  employerId: varchar('employer_id', { length: 50 }).notNull(),
  title: varchar('title', { length: 255 }).notNull(),
  description: text('description').notNull(),
  company: varchar('company', { length: 255 }).notNull(),
  location: varchar('location', { length: 255 }),
  jobType: varchar('job_type', { length: 50 }).notNull(), // 'full-time' | 'part-time' | 'contract' | 'internship' | 'freelance'
  workMode: varchar('work_mode', { length: 50 }).notNull(), // 'remote' | 'hybrid' | 'on-site'
  industry: varchar('industry', { length: 100 }).notNull(),
  experienceLevel: varchar('experience_level', { length: 50 }).notNull(),
  requiredSkills: jsonb('required_skills').$type<string[]>(),
  preferredSkills: jsonb('preferred_skills').$type<string[]>(),
  salaryMin: decimal('salary_min', { precision: 12, scale: 2 }),
  salaryMax: decimal('salary_max', { precision: 12, scale: 2 }),
  currency: varchar('currency', { length: 3 }).default('USD'),
  benefits: jsonb('benefits').$type<string[]>(),
  applicationUrl: text('application_url'),
  applicationEmail: varchar('application_email', { length: 255 }),
  expiresAt: timestamp('expires_at', { withTimezone: true }).notNull(),
  isActive: boolean('is_active').default(true).notNull(),
  applicationCount: integer('application_count').default(0).notNull(),
  guildId: varchar('guild_id', { length: 50 }).notNull(),
  tags: jsonb('tags').$type<string[]>(),
});

export const jobApplications = pgTable('job_applications', {
  ...baseColumns,
  jobId: integer('job_id').notNull(),
  applicantId: varchar('applicant_id', { length: 50 }).notNull(),
  coverLetter: text('cover_letter'),
  resumeUrl: text('resume_url'),
  portfolioUrl: text('portfolio_url'),
  status: varchar('status', { length: 50 }).default('applied').notNull(), // 'applied' | 'reviewed' | 'interview' | 'rejected' | 'accepted'
  notes: text('notes'),
  reviewedAt: timestamp('reviewed_at', { withTimezone: true }),
  reviewerId: varchar('reviewer_id', { length: 50 }),
});

export const professionalConnections = pgTable('professional_connections', {
  ...baseColumns,
  requesterId: varchar('requester_id', { length: 50 }).notNull(),
  receiverId: varchar('receiver_id', { length: 50 }).notNull(),
  status: varchar('status', { length: 50 }).default('pending').notNull(), // 'pending' | 'accepted' | 'rejected' | 'blocked'
  message: text('message'),
  acceptedAt: timestamp('accepted_at', { withTimezone: true }),
});

export const skillEndorsements = pgTable('skill_endorsements', {
  ...baseColumns,
  profileId: varchar('profile_id', { length: 50 }).notNull(),
  endorserId: varchar('endorser_id', { length: 50 }).notNull(),
  skill: varchar('skill', { length: 100 }).notNull(),
  message: text('message'),
});

export const businessOpportunities = pgTable('business_opportunities', {
  ...baseColumns,
  creatorId: varchar('creator_id', { length: 50 }).notNull(),
  title: varchar('title', { length: 255 }).notNull(),
  description: text('description').notNull(),
  type: varchar('type', { length: 50 }).notNull(), // 'partnership' | 'investment' | 'collaboration' | 'mentorship' | 'service'
  industry: varchar('industry', { length: 100 }).notNull(),
  budget: decimal('budget', { precision: 15, scale: 2 }),
  currency: varchar('currency', { length: 3 }).default('USD'),
  timeline: varchar('timeline', { length: 255 }),
  requiredSkills: jsonb('required_skills').$type<string[]>(),
  location: varchar('location', { length: 255 }),
  isRemote: boolean('is_remote').default(true).notNull(),
  contactMethod: varchar('contact_method', { length: 50 }).notNull(), // 'dm' | 'email' | 'form'
  contactInfo: text('contact_info'),
  expiresAt: timestamp('expires_at', { withTimezone: true }).notNull(),
  isActive: boolean('is_active').default(true).notNull(),
  interestCount: integer('interest_count').default(0).notNull(),
  guildId: varchar('guild_id', { length: 50 }).notNull(),
  tags: jsonb('tags').$type<string[]>(),
});

export const opportunityInterests = pgTable('opportunity_interests', {
  ...baseColumns,
  opportunityId: integer('opportunity_id').notNull(),
  userId: varchar('user_id', { length: 50 }).notNull(),
  message: text('message'),
  status: varchar('status', { length: 50 }).default('interested').notNull(), // 'interested' | 'contacted' | 'selected' | 'declined'
});

// Relations
