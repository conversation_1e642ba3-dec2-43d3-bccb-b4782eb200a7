
import { boolean, integer, jsonb, pgTable, text, timestamp, varchar } from 'drizzle-orm/pg-core';
import { baseColumns } from './base.entity';
import { users } from './user.entity';

export const aiTools = pgTable('ai_tools', {
  ...baseColumns,
  name: varchar('name', { length: 255 }).notNull(),
  description: text('description').notNull(),
  category: varchar('category', { length: 100 }).notNull(), // 'coding' | 'creative' | 'business' | 'research' | 'automation'
  rating: integer('rating').default(0).notNull(), // 1-5 scale
  pricing: varchar('pricing', { length: 50 }).notNull(), // 'free' | 'freemium' | 'paid' | 'subscription'
  features: jsonb('features').$type<string[]>(),
  url: text('url'),
  tags: jsonb('tags').$type<string[]>(),
  isActive: boolean('is_active').default(true).notNull(),
  addedBy: varchar('added_by', { length: 50 }).notNull(),
  votes: integer('votes').default(0).notNull(),
  viewCount: integer('view_count').default(0).notNull(),
  lastUpdated: timestamp('last_updated', { withTimezone: true }).defaultNow().notNull(),
});

export const aiTutorials = pgTable('ai_tutorials', {
  ...baseColumns,
  title: varchar('title', { length: 255 }).notNull(),
  description: text('description').notNull(),
  difficulty: varchar('difficulty', { length: 20 }).notNull(), // 'beginner' | 'intermediate' | 'advanced'
  duration: varchar('duration', { length: 50 }).notNull(), // e.g., "2 hours", "30 minutes"
  topics: jsonb('topics').$type<string[]>(),
  content: text('content').notNull(),
  prerequisites: jsonb('prerequisites').$type<string[]>(),
  learningOutcomes: jsonb('learning_outcomes').$type<string[]>(),
  resources: jsonb('resources').$type<Array<{
    title: string;
    url: string;
    type: 'video' | 'article' | 'code' | 'tool';
  }>>(),
  authorId: varchar('author_id', { length: 50 }).notNull(),
  isPublished: boolean('is_published').default(false).notNull(),
  viewCount: integer('view_count').default(0).notNull(),
  completionCount: integer('completion_count').default(0).notNull(),
  rating: integer('rating').default(0).notNull(),
  ratingCount: integer('rating_count').default(0).notNull(),
});

export const aiNews = pgTable('ai_news', {
  ...baseColumns,
  title: varchar('title', { length: 255 }).notNull(),
  summary: text('summary').notNull(),
  content: text('content'),
  category: varchar('category', { length: 100 }).notNull(), // 'breakthrough' | 'product-update' | 'industry-news' | 'research'
  importance: varchar('importance', { length: 20 }).notNull(), // 'low' | 'medium' | 'high' | 'critical'
  source: varchar('source', { length: 255 }).notNull(),
  sourceUrl: text('source_url'),
  publishedAt: timestamp('published_at', { withTimezone: true }).notNull(),
  tags: jsonb('tags').$type<string[]>(),
  isBreaking: boolean('is_breaking').default(false).notNull(),
  viewCount: integer('view_count').default(0).notNull(),
  addedBy: varchar('added_by', { length: 50 }).notNull(),
});

export const aiToolBookmarks = pgTable('ai_tool_bookmarks', {
  ...baseColumns,
  userId: varchar('user_id', { length: 50 }).notNull(),
  toolId: integer('tool_id').notNull(),
  notes: text('notes'),
  tags: jsonb('tags').$type<string[]>(),
});

export const tutorialProgress = pgTable('tutorial_progress', {
  ...baseColumns,
  userId: varchar('user_id', { length: 50 }).notNull(),
  tutorialId: integer('tutorial_id').notNull(),
  progress: integer('progress').default(0).notNull(), // 0-100 percentage
  currentSection: integer('current_section').default(0).notNull(),
  completedSections: jsonb('completed_sections').$type<number[]>(),
  timeSpent: integer('time_spent').default(0).notNull(), // minutes
  isCompleted: boolean('is_completed').default(false).notNull(),
  completedAt: timestamp('completed_at', { withTimezone: true }),
  rating: integer('rating'), // 1-5 scale
  feedback: text('feedback'),
  lastAccessedAt: timestamp('last_accessed_at', { withTimezone: true }).defaultNow().notNull(),
});

export const aiUserPreferences = pgTable('ai_user_preferences', {
  ...baseColumns,
  userId: varchar('user_id', { length: 50 }).notNull().unique(),
  preferredCategories: jsonb('preferred_categories').$type<string[]>(),
  skillLevel: varchar('skill_level', { length: 20 }).default('beginner').notNull(),
  learningGoals: jsonb('learning_goals').$type<string[]>(),
  notificationPreferences: jsonb('notification_preferences').$type<{
    newTools: boolean;
    tutorialUpdates: boolean;
    breakingNews: boolean;
    weeklyDigest: boolean;
  }>(),
  favoriteTools: jsonb('favorite_tools').$type<number[]>(),
  completedTutorials: jsonb('completed_tutorials').$type<number[]>(),
  bookmarkedNews: jsonb('bookmarked_news').$type<number[]>(),
});

// Relations
