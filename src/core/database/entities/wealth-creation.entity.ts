
import { boolean, integer, jsonb, pgTable, text, timestamp, varchar, decimal } from 'drizzle-orm/pg-core';
import { baseColumns } from './base.entity';
import { users } from './user.entity';

export const financialGoals = pgTable('financial_goals', {
  ...baseColumns,
  userId: varchar('user_id', { length: 50 }).notNull(),
  title: varchar('title', { length: 255 }).notNull(),
  description: text('description').notNull(),
  targetAmount: decimal('target_amount', { precision: 15, scale: 2 }).notNull(),
  currentAmount: decimal('current_amount', { precision: 15, scale: 2 }).default('0').notNull(),
  targetDate: timestamp('target_date', { withTimezone: true }).notNull(),
  category: varchar('category', { length: 100 }).notNull(), // 'retirement' | 'emergency-fund' | 'house' | 'education' | 'vacation' | 'investment' | 'debt-payoff'
  priority: varchar('priority', { length: 20 }).notNull(), // 'low' | 'medium' | 'high' | 'critical'
  strategies: jsonb('strategies').$type<string[]>(),
  isCompleted: boolean('is_completed').default(false).notNull(),
  completedAt: timestamp('completed_at', { withTimezone: true }),
  guildId: varchar('guild_id', { length: 50 }).notNull(),
});

export const financialGoalMilestones = pgTable('financial_goal_milestones', {
  ...baseColumns,
  goalId: integer('goal_id').notNull(),
  title: varchar('title', { length: 255 }).notNull(),
  targetAmount: decimal('target_amount', { precision: 15, scale: 2 }).notNull(),
  targetDate: timestamp('target_date', { withTimezone: true }).notNull(),
  isCompleted: boolean('is_completed').default(false).notNull(),
  completedAt: timestamp('completed_at', { withTimezone: true }),
  notes: text('notes'),
});

export const investments = pgTable('investments', {
  ...baseColumns,
  userId: varchar('user_id', { length: 50 }).notNull(),
  symbol: varchar('symbol', { length: 20 }).notNull(),
  name: varchar('name', { length: 255 }).notNull(),
  type: varchar('type', { length: 50 }).notNull(), // 'stock' | 'etf' | 'crypto' | 'bond' | 'real-estate' | 'commodity'
  purchasePrice: decimal('purchase_price', { precision: 15, scale: 4 }).notNull(),
  currentPrice: decimal('current_price', { precision: 15, scale: 4 }).notNull(),
  quantity: decimal('quantity', { precision: 15, scale: 8 }).notNull(),
  purchaseDate: timestamp('purchase_date', { withTimezone: true }).notNull(),
  platform: varchar('platform', { length: 100 }).notNull(),
  notes: text('notes'),
  isActive: boolean('is_active').default(true).notNull(),
  guildId: varchar('guild_id', { length: 50 }).notNull(),
  totalValue: decimal('total_value', { precision: 15, scale: 2 }).notNull(),
  gainLoss: decimal('gain_loss', { precision: 15, scale: 2 }).notNull(),
  gainLossPercentage: decimal('gain_loss_percentage', { precision: 5, scale: 2 }).notNull(),
});

export const investmentTransactions = pgTable('investment_transactions', {
  ...baseColumns,
  investmentId: integer('investment_id').notNull(),
  type: varchar('type', { length: 20 }).notNull(), // 'buy' | 'sell' | 'dividend' | 'split'
  quantity: decimal('quantity', { precision: 15, scale: 8 }).notNull(),
  price: decimal('price', { precision: 15, scale: 4 }).notNull(),
  fees: decimal('fees', { precision: 15, scale: 2 }).default('0').notNull(),
  transactionDate: timestamp('transaction_date', { withTimezone: true }).notNull(),
  notes: text('notes'),
});

export const marketAlerts = pgTable('market_alerts', {
  ...baseColumns,
  userId: varchar('user_id', { length: 50 }).notNull(),
  symbol: varchar('symbol', { length: 20 }).notNull(),
  type: varchar('type', { length: 50 }).notNull(), // 'price_above' | 'price_below' | 'volume_spike' | 'news_mention'
  condition: varchar('condition', { length: 255 }).notNull(),
  value: decimal('value', { precision: 15, scale: 4 }).notNull(),
  isActive: boolean('is_active').default(true).notNull(),
  triggeredAt: timestamp('triggered_at', { withTimezone: true }),
  guildId: varchar('guild_id', { length: 50 }).notNull(),
  notificationSent: boolean('notification_sent').default(false).notNull(),
});

export const calculatorHistory = pgTable('calculator_history', {
  ...baseColumns,
  userId: varchar('user_id', { length: 50 }).notNull(),
  calculatorType: varchar('calculator_type', { length: 100 }).notNull(), // 'compound_interest' | 'loan_payment' | 'investment_return' | 'retirement'
  inputs: jsonb('inputs').$type<Record<string, any>>(),
  results: jsonb('results').$type<Record<string, any>>(),
  guildId: varchar('guild_id', { length: 50 }).notNull(),
  isSaved: boolean('is_saved').default(false).notNull(),
  title: varchar('title', { length: 255 }),
});

export const portfolios = pgTable('portfolios', {
  ...baseColumns,
  userId: varchar('user_id', { length: 50 }).notNull(),
  name: varchar('name', { length: 255 }).notNull(),
  description: text('description'),
  totalValue: decimal('total_value', { precision: 15, scale: 2 }).default('0').notNull(),
  totalGainLoss: decimal('total_gain_loss', { precision: 15, scale: 2 }).default('0').notNull(),
  totalGainLossPercentage: decimal('total_gain_loss_percentage', { precision: 5, scale: 2 }).default('0').notNull(),
  isDefault: boolean('is_default').default(false).notNull(),
  isPublic: boolean('is_public').default(false).notNull(),
  guildId: varchar('guild_id', { length: 50 }).notNull(),
  lastUpdated: timestamp('last_updated', { withTimezone: true }).defaultNow().notNull(),
});

export const portfolioInvestments = pgTable('portfolio_investments', {
  ...baseColumns,
  portfolioId: integer('portfolio_id').notNull(),
  investmentId: integer('investment_id').notNull(),
  allocation: decimal('allocation', { precision: 5, scale: 2 }).notNull(), // percentage
});

export const wealthAnalytics = pgTable('wealth_analytics', {
  ...baseColumns,
  userId: varchar('user_id', { length: 50 }).notNull(),
  date: timestamp('date', { withTimezone: true }).notNull(),
  totalNetWorth: decimal('total_net_worth', { precision: 15, scale: 2 }).notNull(),
  totalInvestments: decimal('total_investments', { precision: 15, scale: 2 }).notNull(),
  totalDebt: decimal('total_debt', { precision: 15, scale: 2 }).default('0').notNull(),
  monthlyIncome: decimal('monthly_income', { precision: 15, scale: 2 }).default('0').notNull(),
  monthlyExpenses: decimal('monthly_expenses', { precision: 15, scale: 2 }).default('0').notNull(),
  savingsRate: decimal('savings_rate', { precision: 5, scale: 2 }).default('0').notNull(),
  guildId: varchar('guild_id', { length: 50 }).notNull(),
});

// Relations
