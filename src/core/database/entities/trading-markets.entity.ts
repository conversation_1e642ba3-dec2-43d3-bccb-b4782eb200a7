
import { bigint, boolean, decimal, integer, jsonb, pgTable, text, timestamp, varchar } from 'drizzle-orm/pg-core';
import { baseColumns } from './base.entity';
import { users } from './user.entity';

export const marketData = pgTable('market_data', {
  ...baseColumns,
  symbol: varchar('symbol', { length: 20 }).notNull(),
  name: varchar('name', { length: 255 }).notNull(),
  market: varchar('market', { length: 50 }).notNull(), // 'stocks' | 'crypto' | 'forex' | 'commodities'
  currentPrice: decimal('current_price', { precision: 15, scale: 4 }).notNull(),
  previousClose: decimal('previous_close', { precision: 15, scale: 4 }),
  change: decimal('change', { precision: 15, scale: 4 }),
  changePercent: decimal('change_percent', { precision: 5, scale: 2 }),
  volume: bigint('volume', { mode: 'number' }),
  marketCap: decimal('market_cap', { precision: 20, scale: 2 }),
  high24h: decimal('high_24h', { precision: 15, scale: 4 }),
  low24h: decimal('low_24h', { precision: 15, scale: 4 }),
  lastUpdated: timestamp('last_updated', { withTimezone: true }).defaultNow().notNull(),
  isActive: boolean('is_active').default(true).notNull(),
  metadata: jsonb('metadata').$type<Record<string, any>>(),
});

export const tradingPortfolios = pgTable('trading_portfolios', {
  ...baseColumns,
  userId: varchar('user_id', { length: 50 }).notNull(),
  name: varchar('name', { length: 255 }).notNull(),
  description: text('description'),
  totalValue: decimal('total_value', { precision: 15, scale: 2 }).default('0').notNull(),
  totalCost: decimal('total_cost', { precision: 15, scale: 2 }).default('0').notNull(),
  totalGainLoss: decimal('total_gain_loss', { precision: 15, scale: 2 }).default('0').notNull(),
  totalGainLossPercent: decimal('total_gain_loss_percent', { precision: 5, scale: 2 }).default('0').notNull(),
  isDefault: boolean('is_default').default(false).notNull(),
  isPublic: boolean('is_public').default(false).notNull(),
  riskLevel: varchar('risk_level', { length: 20 }).default('medium').notNull(), // 'low' | 'medium' | 'high' | 'aggressive'
  strategy: varchar('strategy', { length: 100 }),
  guildId: varchar('guild_id', { length: 50 }).notNull(),
  lastUpdated: timestamp('last_updated', { withTimezone: true }).defaultNow().notNull(),
});

export const portfolioHoldings = pgTable('portfolio_holdings', {
  ...baseColumns,
  portfolioId: integer('portfolio_id').notNull(),
  symbol: varchar('symbol', { length: 20 }).notNull(),
  quantity: decimal('quantity', { precision: 15, scale: 8 }).notNull(),
  averageCost: decimal('average_cost', { precision: 15, scale: 4 }).notNull(),
  currentPrice: decimal('current_price', { precision: 15, scale: 4 }).notNull(),
  totalCost: decimal('total_cost', { precision: 15, scale: 2 }).notNull(),
  currentValue: decimal('current_value', { precision: 15, scale: 2 }).notNull(),
  gainLoss: decimal('gain_loss', { precision: 15, scale: 2 }).notNull(),
  gainLossPercent: decimal('gain_loss_percent', { precision: 5, scale: 2 }).notNull(),
  allocation: decimal('allocation', { precision: 5, scale: 2 }).notNull(), // percentage of portfolio
  firstPurchaseDate: timestamp('first_purchase_date', { withTimezone: true }).notNull(),
  lastUpdated: timestamp('last_updated', { withTimezone: true }).defaultNow().notNull(),
});

export const tradingTransactions = pgTable('trading_transactions', {
  ...baseColumns,
  portfolioId: integer('portfolio_id').notNull(),
  symbol: varchar('symbol', { length: 20 }).notNull(),
  type: varchar('type', { length: 20 }).notNull(), // 'buy' | 'sell' | 'dividend' | 'split' | 'transfer'
  quantity: decimal('quantity', { precision: 15, scale: 8 }).notNull(),
  price: decimal('price', { precision: 15, scale: 4 }).notNull(),
  totalAmount: decimal('total_amount', { precision: 15, scale: 2 }).notNull(),
  fees: decimal('fees', { precision: 15, scale: 2 }).default('0').notNull(),
  transactionDate: timestamp('transaction_date', { withTimezone: true }).notNull(),
  platform: varchar('platform', { length: 100 }),
  notes: text('notes'),
  isExecuted: boolean('is_executed').default(true).notNull(),
});

export const tradingAlerts = pgTable('trading_alerts', {
  ...baseColumns,
  userId: varchar('user_id', { length: 50 }).notNull(),
  symbol: varchar('symbol', { length: 20 }).notNull(),
  alertType: varchar('alert_type', { length: 50 }).notNull(), // 'price_above' | 'price_below' | 'volume_spike' | 'rsi_overbought' | 'rsi_oversold' | 'moving_average_cross'
  condition: varchar('condition', { length: 255 }).notNull(),
  targetValue: decimal('target_value', { precision: 15, scale: 4 }).notNull(),
  currentValue: decimal('current_value', { precision: 15, scale: 4 }),
  isActive: boolean('is_active').default(true).notNull(),
  triggeredAt: timestamp('triggered_at', { withTimezone: true }),
  notificationSent: boolean('notification_sent').default(false).notNull(),
  guildId: varchar('guild_id', { length: 50 }).notNull(),
  metadata: jsonb('metadata').$type<Record<string, any>>(),
});

export const tradingStrategies = pgTable('trading_strategies', {
  ...baseColumns,
  userId: varchar('user_id', { length: 50 }).notNull(),
  name: varchar('name', { length: 255 }).notNull(),
  description: text('description').notNull(),
  type: varchar('type', { length: 50 }).notNull(), // 'swing' | 'day' | 'scalping' | 'position' | 'arbitrage'
  rules: jsonb('rules').$type<Array<{
    condition: string;
    action: string;
    parameters: Record<string, any>;
  }>>(),
  backtestResults: jsonb('backtest_results').$type<{
    totalReturn: number;
    sharpeRatio: number;
    maxDrawdown: number;
    winRate: number;
    avgWin: number;
    avgLoss: number;
  }>(),
  isActive: boolean('is_active').default(false).notNull(),
  isPublic: boolean('is_public').default(false).notNull(),
  performanceMetrics: jsonb('performance_metrics').$type<Record<string, any>>(),
  guildId: varchar('guild_id', { length: 50 }).notNull(),
});

export const marketAnalysis = pgTable('market_analysis', {
  ...baseColumns,
  symbol: varchar('symbol', { length: 20 }).notNull(),
  analysisType: varchar('analysis_type', { length: 50 }).notNull(), // 'technical' | 'fundamental' | 'sentiment'
  timeframe: varchar('timeframe', { length: 20 }).notNull(), // '1m' | '5m' | '1h' | '1d' | '1w' | '1M'
  indicators: jsonb('indicators').$type<Record<string, any>>(),
  signals: jsonb('signals').$type<Array<{
    type: 'buy' | 'sell' | 'hold';
    strength: number; // 1-10
    reason: string;
  }>>(),
  priceTargets: jsonb('price_targets').$type<{
    support: number[];
    resistance: number[];
    target: number;
    stopLoss: number;
  }>(),
  confidence: integer('confidence').notNull(), // 1-100
  createdBy: varchar('created_by', { length: 50 }).notNull(),
  guildId: varchar('guild_id', { length: 50 }).notNull(),
});

export const tradingJournal = pgTable('trading_journal', {
  ...baseColumns,
  userId: varchar('user_id', { length: 50 }).notNull(),
  transactionId: integer('transaction_id'),
  symbol: varchar('symbol', { length: 20 }).notNull(),
  entryDate: timestamp('entry_date', { withTimezone: true }).notNull(),
  exitDate: timestamp('exit_date', { withTimezone: true }),
  entryPrice: decimal('entry_price', { precision: 15, scale: 4 }).notNull(),
  exitPrice: decimal('exit_price', { precision: 15, scale: 4 }),
  quantity: decimal('quantity', { precision: 15, scale: 8 }).notNull(),
  tradeType: varchar('trade_type', { length: 20 }).notNull(), // 'long' | 'short'
  strategy: varchar('strategy', { length: 100 }),
  setup: text('setup'),
  reasoning: text('reasoning'),
  outcome: text('outcome'),
  lessons: text('lessons'),
  pnl: decimal('pnl', { precision: 15, scale: 2 }),
  pnlPercent: decimal('pnl_percent', { precision: 5, scale: 2 }),
  emotions: varchar('emotions', { length: 255 }),
  tags: jsonb('tags').$type<string[]>(),
  screenshots: jsonb('screenshots').$type<string[]>(),
  guildId: varchar('guild_id', { length: 50 }).notNull(),
});

export const watchlists = pgTable('watchlists', {
  ...baseColumns,
  userId: varchar('user_id', { length: 50 }).notNull(),
  name: varchar('name', { length: 255 }).notNull(),
  description: text('description'),
  symbols: jsonb('symbols').$type<string[]>(),
  isPublic: boolean('is_public').default(false).notNull(),
  guildId: varchar('guild_id', { length: 50 }).notNull(),
});

// Relations
