/**
 * Base entity interface for Redis storage
 * Replaces Drizzle ORM base columns
 */

/**
 * Base columns for compatibility with existing Drizzle schemas
 * These represent the common fields that all entities should have
 */
import { text, timestamp } from 'drizzle-orm/pg-core';

export const baseColumns = {
  id: text('id').primaryKey(),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
  deletedAt: timestamp('deleted_at', { withTimezone: true })
};

export interface BaseEntity {
  id: string; // Changed from number to string for Redis compatibility
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date | null;
}

/**
 * Type for creating new entities (excluding auto-generated fields)
 */
export type CreateBaseEntity<T extends BaseEntity> = Omit<T, 'id' | 'createdAt' | 'updatedAt'>;

/**
 * Type for updating entities (partial, excluding id and createdAt)
 */
export type UpdateBaseEntity<T extends BaseEntity> = Partial<Omit<T, 'id' | 'createdAt'>>;

/**
 * Utility to generate entity with base fields
 */
export const createEntityWithBase = <T extends Record<string, any>>(
  data: T
): T & BaseEntity => {
  const now = new Date();
  return {
    ...data,
    id: generateId(),
    createdAt: now,
    updatedAt: now,
  };
};

/**
 * Utility to update entity with new updatedAt timestamp
 */
export const updateEntityWithBase = <T extends BaseEntity>(
  entity: T,
  updates: Partial<Omit<T, 'id' | 'createdAt'>>
): T => {
  return {
    ...entity,
    ...updates,
    updatedAt: new Date(),
  };
};

/**
 * Simple ID generator for Redis entities
 * In production, you might want to use a more robust solution like nanoid or uuid
 */
export const generateId = (): string => {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 8);
  return `${timestamp}_${random}`;
};

/**
 * Utility to check if entity is soft deleted
 */
export const isDeleted = (entity: BaseEntity): boolean => {
  return entity.deletedAt !== null && entity.deletedAt !== undefined;
};

/**
 * Utility to soft delete an entity
 */
export const softDelete = <T extends BaseEntity>(entity: T): T => {
  return {
    ...entity,
    deletedAt: new Date(),
    updatedAt: new Date(),
  };
};

/**
 * Utility to restore a soft deleted entity
 */
export const restore = <T extends BaseEntity>(entity: T): T => {
  return {
    ...entity,
    deletedAt: null,
    updatedAt: new Date(),
  };
};