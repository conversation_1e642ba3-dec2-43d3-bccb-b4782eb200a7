
import { boolean, integer, jsonb, pgTable, text, timestamp, varchar } from 'drizzle-orm/pg-core';
import { baseColumns } from './base.entity';
import { users } from './user.entity';

export const tournaments = pgTable('tournaments', {
  ...baseColumns,
  title: varchar('title', { length: 255 }).notNull(),
  description: text('description').notNull(),
  game: varchar('game', { length: 100 }).notNull(),
  platform: jsonb('platform').$type<string[]>(),
  format: varchar('format', { length: 50 }).notNull(), // 'single-elimination' | 'double-elimination' | 'round-robin' | 'swiss'
  maxParticipants: integer('max_participants').notNull(),
  currentParticipants: integer('current_participants').default(0).notNull(),
  prizePool: varchar('prize_pool', { length: 255 }),
  registrationStart: timestamp('registration_start', { withTimezone: true }).notNull(),
  registrationEnd: timestamp('registration_end', { withTimezone: true }).notNull(),
  tournamentStart: timestamp('tournament_start', { withTimezone: true }).notNull(),
  tournamentEnd: timestamp('tournament_end', { withTimezone: true }).notNull(),
  status: varchar('status', { length: 50 }).default('upcoming').notNull(), // 'upcoming' | 'registration-open' | 'in-progress' | 'completed'
  organizerId: varchar('organizer_id', { length: 50 }).notNull(),
  guildId: varchar('guild_id', { length: 50 }).notNull(),
  rules: jsonb('rules').$type<string[]>(),
  requirements: jsonb('requirements').$type<string[]>(),
  bracket: text('bracket'), // JSON string for bracket data
  streamLink: text('stream_link'),
  isActive: boolean('is_active').default(true).notNull(),
  tags: jsonb('tags').$type<string[]>(),
});

export const tournamentParticipants = pgTable('tournament_participants', {
  ...baseColumns,
  tournamentId: integer('tournament_id').notNull(),
  userId: varchar('user_id', { length: 50 }).notNull(),
  teamName: varchar('team_name', { length: 255 }),
  registeredAt: timestamp('registered_at', { withTimezone: true }).defaultNow().notNull(),
  status: varchar('status', { length: 50 }).default('registered').notNull(), // 'registered' | 'checked-in' | 'competing' | 'eliminated' | 'winner'
  placement: integer('placement'),
  teamMembers: jsonb('team_members').$type<string[]>(), // Array of Discord IDs for team tournaments
  notes: text('notes'),
});

export const gameSessions = pgTable('game_sessions', {
  ...baseColumns,
  hostId: varchar('host_id', { length: 50 }).notNull(),
  title: varchar('title', { length: 255 }).notNull(),
  game: varchar('game', { length: 100 }).notNull(),
  platform: varchar('platform', { length: 50 }).notNull(),
  maxPlayers: integer('max_players').notNull(),
  currentPlayers: integer('current_players').default(1).notNull(), // Host counts as 1
  scheduledTime: timestamp('scheduled_time', { withTimezone: true }).notNull(),
  duration: integer('duration').notNull(), // minutes
  difficulty: varchar('difficulty', { length: 20 }).notNull(), // 'casual' | 'competitive' | 'hardcore'
  description: text('description').notNull(),
  requirements: jsonb('requirements').$type<string[]>(),
  voiceChannelId: varchar('voice_channel_id', { length: 50 }),
  status: varchar('status', { length: 50 }).default('scheduled').notNull(), // 'scheduled' | 'active' | 'completed' | 'cancelled'
  gameMode: varchar('game_mode', { length: 100 }).notNull(),
  isPrivate: boolean('is_private').default(false).notNull(),
  inviteCode: varchar('invite_code', { length: 20 }),
  guildId: varchar('guild_id', { length: 50 }).notNull(),
  tags: jsonb('tags').$type<string[]>(),
});

export const gameSessionParticipants = pgTable('game_session_participants', {
  ...baseColumns,
  sessionId: integer('session_id').notNull(),
  userId: varchar('user_id', { length: 50 }).notNull(),
  joinedAt: timestamp('joined_at', { withTimezone: true }).defaultNow().notNull(),
  status: varchar('status', { length: 50 }).default('joined').notNull(), // 'joined' | 'ready' | 'playing' | 'left'
  score: integer('score').default(0),
  achievements: jsonb('achievements').$type<string[]>(),
});

export const playerProfiles = pgTable('player_profiles', {
  ...baseColumns,
  userId: varchar('user_id', { length: 50 }).notNull().unique(),
  gamertag: varchar('gamertag', { length: 100 }).notNull(),
  platforms: jsonb('platforms').$type<string[]>(),
  favoriteGames: jsonb('favorite_games').$type<string[]>(),
  skillLevel: varchar('skill_level', { length: 20 }).default('beginner').notNull(), // 'beginner' | 'intermediate' | 'advanced' | 'professional'
  playtimeHours: integer('playtime_hours').default(0).notNull(),
  totalSessions: integer('total_sessions').default(0).notNull(),
  totalTournaments: integer('total_tournaments').default(0).notNull(),
  tournamentsWon: integer('tournaments_won').default(0).notNull(),
  winRate: integer('win_rate').default(0).notNull(), // percentage
  bio: text('bio'),
  achievements: jsonb('achievements').$type<Array<{
    id: string;
    name: string;
    description: string;
    unlockedAt: string;
    rarity: 'common' | 'rare' | 'epic' | 'legendary';
  }>>(),
  statistics: jsonb('statistics').$type<Record<string, any>>(),
  badges: jsonb('badges').$type<string[]>(),
  isPublic: boolean('is_public').default(true).notNull(),
  guildId: varchar('guild_id', { length: 50 }).notNull(),
});

export const gameLeaderboards = pgTable('game_leaderboards', {
  ...baseColumns,
  guildId: varchar('guild_id', { length: 50 }).notNull(),
  game: varchar('game', { length: 100 }).notNull(),
  category: varchar('category', { length: 100 }).notNull(), // 'wins' | 'score' | 'time' | 'tournaments'
  period: varchar('period', { length: 20 }).notNull(), // 'daily' | 'weekly' | 'monthly' | 'all-time'
  userId: varchar('user_id', { length: 50 }).notNull(),
  score: integer('score').notNull(),
  rank: integer('rank').notNull(),
  lastUpdated: timestamp('last_updated', { withTimezone: true }).defaultNow().notNull(),
});

export const gameStreams = pgTable('game_streams', {
  ...baseColumns,
  streamerId: varchar('streamer_id', { length: 50 }).notNull(),
  title: varchar('title', { length: 255 }).notNull(),
  game: varchar('game', { length: 100 }).notNull(),
  platform: varchar('platform', { length: 50 }).notNull(), // 'twitch' | 'youtube' | 'discord'
  streamUrl: text('stream_url').notNull(),
  isLive: boolean('is_live').default(false).notNull(),
  startedAt: timestamp('started_at', { withTimezone: true }),
  endedAt: timestamp('ended_at', { withTimezone: true }),
  viewerCount: integer('viewer_count').default(0).notNull(),
  maxViewers: integer('max_viewers').default(0).notNull(),
  guildId: varchar('guild_id', { length: 50 }).notNull(),
  tags: jsonb('tags').$type<string[]>(),
  description: text('description'),
});

export const gameAchievements = pgTable('game_achievements', {
  ...baseColumns,
  name: varchar('name', { length: 255 }).notNull(),
  description: text('description').notNull(),
  category: varchar('category', { length: 100 }).notNull(),
  rarity: varchar('rarity', { length: 20 }).notNull(), // 'common' | 'rare' | 'epic' | 'legendary'
  icon: text('icon'),
  requirements: jsonb('requirements').$type<Record<string, any>>(),
  points: integer('points').default(0).notNull(),
  guildId: varchar('guild_id', { length: 50 }).notNull(),
  isActive: boolean('is_active').default(true).notNull(),
});

export const userAchievements = pgTable('user_achievements', {
  ...baseColumns,
  userId: varchar('user_id', { length: 50 }).notNull(),
  achievementId: integer('achievement_id').notNull(),
  unlockedAt: timestamp('unlocked_at', { withTimezone: true }).defaultNow().notNull(),
  progress: jsonb('progress').$type<Record<string, any>>(),
});

// Relations
