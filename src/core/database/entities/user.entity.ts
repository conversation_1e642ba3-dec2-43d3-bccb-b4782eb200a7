import { BaseEntity, CreateEntity, UpdateEntity } from '../types/base.interface';

/**
 * User preferences type definition
 */
export interface UserPreferences {
  notifications?: {
    enabled: boolean;
    types: string[];
    frequency: 'immediate' | 'daily' | 'weekly';
  };
  privacy?: {
    shareProgress: boolean;
    allowDataCollection: boolean;
    showOnLeaderboard: boolean;
  };
  ai?: {
    preferredAgent: string;
    responseStyle: 'formal' | 'casual' | 'friendly';
    maxResponseLength: number;
  };
  timezone?: string;
  language?: string;
  // Economy-related preferences
  balance?: number;
  dailyStreak?: number;
  lastDaily?: string;
  devRequests?: DevRequest[];
  devProfile?: DevProfile;
  warnings?: UserWarning[];
}

/**
 * User profile type definition
 */
export interface UserProfile {
  displayName?: string;
  bio?: string;
  avatar?: string;
  badges?: string[];
  achievements?: Achievement[];
  stats?: UserStats;
  goals?: UserGoal[];
}

/**
 * Development request type
 */
export interface DevRequest {
  id: string;
  clientId: string;
  clientTag: string;
  description: string;
  budget?: string;
  timeline?: string;
  skills: string[];
  status: 'open' | 'assigned' | 'in_progress' | 'completed' | 'cancelled' | 'payment_pending' | 'payment_held';
  createdAt: string;
  developerId?: string;
  developerTag?: string;
  assignedAt?: string;
  channelId?: string;
  paymentAmount?: number;
  escrowId?: string;
  milestones?: Milestone[];
  paymentStatus?: 'pending' | 'held' | 'released' | 'refunded';
  completedAt?: string;
  cancelledAt?: string;
}

/**
 * Development milestone type
 */
export interface Milestone {
  id: string;
  description: string;
  amount: number;
  status: 'pending' | 'completed' | 'released';
  completedAt?: string;
  releasedAt?: string;
}

/**
 * Developer profile type
 */
export interface DevProfile {
  isClient?: boolean;
  isDeveloper: boolean;
  skills: string[];
  portfolio?: string;
  rating: number;
  completedProjects: number;
}

/**
 * User warning type
 */
export interface UserWarning {
  id: string;
  guildId: string;
  reason: string;
  moderatorId: string;
  timestamp: string;
  severity: 'low' | 'medium' | 'high';
}

/**
 * User achievement type
 */
export interface Achievement {
  id: string;
  name: string;
  description: string;
  unlockedAt: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
}

/**
 * User statistics type
 */
export interface UserStats {
  messagesCount: number;
  commandsUsed: number;
  timeSpent: number;
  level: number;
  experience: number;
}

/**
 * User goal type
 */
export interface UserGoal {
  id: string;
  title: string;
  category: string;
  status: 'active' | 'completed' | 'paused';
  progress: number;
  createdAt: string;
  targetDate?: string;
}

/**
 * User entity interface - Redis compatible
 */
export interface User extends BaseEntity {
  discordId: string;
  username: string;
  email?: string;
  isActive: boolean;
  lastActivityAt?: Date;
  preferences?: UserPreferences;
  profile?: UserProfile;
  accessToken?: string;
  refreshToken?: string;
  tokenExpiresAt?: Date;
  experience: number;
  balance: number;
  userId?: string;
  discriminator?: string;
  avatarUrl?: string;
}

/**
 * Type for creating a new user (strict typing)
 */
export type CreateUser = CreateEntity<User>;

/**
 * Type for updating a user (strict typing)
 */
export type UpdateUser = UpdateEntity<User>;

/**
 * User with relations type for expanded queries
 */
export interface UserWithRelations extends User {
  sessions?: any[]; // Will be properly typed when session entity is updated
  relationships?: any[]; // Will be properly typed when user-relationship entity is updated
  agentInteractions?: any[]; // Will be properly typed when agent-interaction entity is updated
  growthPlans?: any[]; // Will be properly typed when personal-growth-plan entity is updated
}

/**
 * Redis key patterns for user entity with strict typing
 */
export const UserKeys = {
  primary: (id: string) => `user:${id}`,
  byDiscordId: (discordId: string) => `user:discord:${discordId}`,
  byUsername: (username: string) => `user:username:${username}`,
  byEmail: (email: string) => `user:email:${email}`,
  active: () => 'users:active',
  inactive: () => 'users:inactive',
  pattern: 'user:*',
  index: {
    discordId: 'idx:user:discord_id',
    username: 'idx:user:username',
    email: 'idx:user:email',
    createdAt: 'idx:user:created_at',
    isActive: 'idx:user:is_active',
  }
} as const;

/**
 * Compatibility export for Drizzle-style references
 * This allows other entities to reference 'users' from this module
 */
export const users = {
  $inferSelect: {} as User,
  $inferInsert: {} as CreateUser
} as const;

export type UsersTable = typeof users;

