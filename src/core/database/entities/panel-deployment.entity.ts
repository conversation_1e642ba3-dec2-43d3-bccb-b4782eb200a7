
import { boolean, index, integer, jsonb, pgTable, text, timestamp, varchar } from 'drizzle-orm/pg-core';
import { baseColumns } from './base.entity';

export const panelDeployments = pgTable('panel_deployments', {
  ...baseColumns,
  channelId: varchar('channel_id', { length: 50 }).notNull(),
  guildId: varchar('guild_id', { length: 50 }).notNull(),
  panelId: varchar('panel_id', { length: 100 }).notNull(),
  messageId: varchar('message_id', { length: 50 }),
  deploymentStatus: varchar('deployment_status', { length: 20 })
    .notNull()
    .default('active'), // 'active', 'inactive', 'failed', 'updating', 'scheduled'
  lastUpdateAt: timestamp('last_update_at', { withTimezone: true }).defaultNow().notNull(),
  lastSuccessfulUpdate: timestamp('last_successful_update', { withTimezone: true }),
  panelVersion: varchar('panel_version', { length: 50 }).default('1.0.0'),
  contentHash: varchar('content_hash', { length: 64 }), // SHA-256 hash of panel content for change detection
  deploymentConfig: jsonb('deployment_config').$type<{
    autoUpdate?: boolean;
    updateInterval?: number; // milliseconds
    updateStrategy?: 'immediate' | 'scheduled' | 'manual';
    maxRetries?: number;
    retryDelay?: number;
    fallbackAction?: 'keep_existing' | 'remove' | 'show_error';
    priority?: number;
    conditions?: Array<{
      type: 'time-window' | 'user-activity' | 'data-change' | 'manual-trigger';
      config?: Record<string, any>;
    }>;
  }>(),
  discordState: jsonb('discord_state').$type<{
    channelName?: string;
    categoryId?: string;
    categoryName?: string;
    guildName?: string;
    messageExists?: boolean;
    lastVerified?: string;
    permissions?: {
      canSend: boolean;
      canEdit: boolean;
      canDelete: boolean;
      canEmbed: boolean;
      canAttach: boolean;
    };
  }>(),
  deploymentMetrics: jsonb('deployment_metrics').$type<{
    totalDeployments: number;
    successfulDeployments: number;
    failedDeployments: number;
    lastDeploymentTime?: number; // milliseconds
    averageDeploymentTime?: number;
    lastErrorCode?: string;
    lastErrorMessage?: string;
    lastErrorTime?: string;
    updateHistory: Array<{
      timestamp: string;
      action: 'deployed' | 'updated' | 'failed' | 'removed';
      fromVersion?: string;
      toVersion?: string;
      duration?: number;
      error?: string;
    }>;
  }>(),
  errorState: jsonb('error_state').$type<{
    hasErrors: boolean;
    errorCount: number;
    lastError?: {
      code: string;
      message: string;
      timestamp: string;
      stackTrace?: string;
      context?: Record<string, any>;
    };
    errors: Array<{
      code: string;
      message: string;
      timestamp: string;
      severity: 'low' | 'medium' | 'high' | 'critical';
      resolved: boolean;
      resolvedAt?: string;
    }>;
    recovery?: {
      attempts: number;
      lastAttempt?: string;
      nextAttempt?: string;
      strategy: 'retry' | 'fallback' | 'manual' | 'abandon';
    };
  }>(),
  isActive: boolean('is_active').default(true).notNull(),
  needsCleanup: boolean('needs_cleanup').default(false).notNull(), // Flag for cleanup candidates
  scheduledUpdateAt: timestamp('scheduled_update_at', { withTimezone: true }),
  retryCount: integer('retry_count').default(0).notNull(),
  maxRetries: integer('max_retries').default(3).notNull(),
  
  // Interaction tracking for cleanup logic
  interactionCount: integer('interaction_count').default(0).notNull(),
  lastInteractionAt: timestamp('last_interaction_at', { withTimezone: true }),
  metadata: jsonb('metadata').$type<{
    deployedBy?: string; // user ID who triggered deployment
    deploymentSource?: 'auto' | 'manual' | 'scheduled' | 'recovery';
    tags?: string[];
    notes?: string;
    experiment?: {
      testId: string;
      variant: string;
      allocation: number;
    };
    customConfig?: Record<string, any>;
  }>(),
}, (table) => ({
  channelIdIdx: index('panel_deployments_channel_id_idx').on(table.channelId),
  guildIdIdx: index('panel_deployments_guild_id_idx').on(table.guildId),
  panelIdIdx: index('panel_deployments_panel_id_idx').on(table.panelId),
  messageIdIdx: index('panel_deployments_message_id_idx').on(table.messageId),
  statusIdx: index('panel_deployments_status_idx').on(table.deploymentStatus),
  lastUpdateIdx: index('panel_deployments_last_update_idx').on(table.lastUpdateAt),
  activeIdx: index('panel_deployments_active_idx').on(table.isActive),
  cleanupIdx: index('panel_deployments_cleanup_idx').on(table.needsCleanup),
  scheduledIdx: index('panel_deployments_scheduled_idx').on(table.scheduledUpdateAt),
  channelPanelUnique: index('panel_deployments_channel_panel_unique').on(table.channelId, table.panelId),
  contentHashIdx: index('panel_deployments_content_hash_idx').on(table.contentHash),
  interactionIdx: index('panel_deployments_interaction_idx').on(table.lastInteractionAt),
}));

export const deploymentLogs = pgTable('deployment_logs', {
  ...baseColumns,
  deploymentId: integer('deployment_id').notNull(),
  logLevel: varchar('log_level', { length: 10 }).notNull(), // 'DEBUG', 'INFO', 'WARN', 'ERROR'
  message: text('message').notNull(),
  context: jsonb('context').$type<{
    action?: string;
    phase?: 'preparation' | 'deployment' | 'verification' | 'cleanup';
    duration?: number;
    error?: {
      code: string;
      message: string;
      stackTrace?: string;
    };
    metrics?: {
      memoryUsage: number;
      cpuUsage: number;
      networkLatency: number;
    };
    discord?: {
      rateLimit?: boolean;
      retryAfter?: number;
      requestId?: string;
    };
    additional?: Record<string, any>;
  }>(),
  timestamp: timestamp('timestamp', { withTimezone: true }).defaultNow().notNull(),
}, (table) => ({
  deploymentIdIdx: index('deployment_logs_deployment_id_idx').on(table.deploymentId),
  logLevelIdx: index('deployment_logs_log_level_idx').on(table.logLevel),
  timestampIdx: index('deployment_logs_timestamp_idx').on(table.timestamp),
}));

