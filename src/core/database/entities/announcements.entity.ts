
import { boolean, integer, jsonb, pgTable, text, timestamp, varchar } from 'drizzle-orm/pg-core';
import { baseColumns } from './base.entity';
import { users } from './user.entity';

export const announcements = pgTable('announcements', {
  ...baseColumns,
  title: varchar('title', { length: 255 }).notNull(),
  content: text('content').notNull(),
  authorId: varchar('author_id', { length: 50 }).notNull(),
  type: varchar('type', { length: 50 }).default('general').notNull(), // 'general' | 'maintenance' | 'feature' | 'event' | 'urgent' | 'celebration'
  priority: varchar('priority', { length: 20 }).default('normal').notNull(), // 'low' | 'normal' | 'high' | 'urgent'
  guildId: varchar('guild_id', { length: 50 }).notNull(),
  targetChannels: jsonb('target_channels').$type<string[]>(), // specific channel IDs
  targetRoles: jsonb('target_roles').$type<string[]>(), // specific role IDs
  targetUsers: jsonb('target_users').$type<string[]>(), // specific user IDs
  scheduledFor: timestamp('scheduled_for', { withTimezone: true }),
  publishedAt: timestamp('published_at', { withTimezone: true }),
  expiresAt: timestamp('expires_at', { withTimezone: true }),
  status: varchar('status', { length: 50 }).default('draft').notNull(), // 'draft' | 'scheduled' | 'published' | 'expired' | 'cancelled'
  isPinned: boolean('is_pinned').default(false).notNull(),
  allowComments: boolean('allow_comments').default(true).notNull(),
  requiresAcknowledgment: boolean('requires_acknowledgment').default(false).notNull(),
  embedData: jsonb('embed_data').$type<{
    color?: string;
    thumbnail?: string;
    image?: string;
    footer?: string;
    fields?: Array<{
      name: string;
      value: string;
      inline?: boolean;
    }>;
  }>(),
  attachments: jsonb('attachments').$type<string[]>(),
  tags: jsonb('tags').$type<string[]>(),
  viewCount: integer('view_count').default(0).notNull(),
  reactionCount: integer('reaction_count').default(0).notNull(),
  commentCount: integer('comment_count').default(0).notNull(),
  acknowledgmentCount: integer('acknowledgment_count').default(0).notNull(),
  totalRecipients: integer('total_recipients').default(0).notNull(),
});

export const announcementDeliveries = pgTable('announcement_deliveries', {
  ...baseColumns,
  announcementId: integer('announcement_id').notNull(),
  channelId: varchar('channel_id', { length: 50 }).notNull(),
  messageId: varchar('message_id', { length: 50 }).notNull(),
  deliveredAt: timestamp('delivered_at', { withTimezone: true }).defaultNow().notNull(),
  isSuccessful: boolean('is_successful').default(true).notNull(),
  errorMessage: text('error_message'),
  retryCount: integer('retry_count').default(0).notNull(),
});

export const announcementSubscriptions = pgTable('announcement_subscriptions', {
  ...baseColumns,
  userId: varchar('user_id', { length: 50 }).notNull(),
  guildId: varchar('guild_id', { length: 50 }).notNull(),
  subscribedTypes: jsonb('subscribed_types').$type<string[]>(),
  notificationChannels: jsonb('notification_channels').$type<{
    discord: boolean;
    email: boolean;
    dm: boolean;
  }>(),
  frequency: varchar('frequency', { length: 20 }).default('immediate').notNull(), // 'immediate' | 'daily' | 'weekly' | 'never'
  isActive: boolean('is_active').default(true).notNull(),
  lastNotificationAt: timestamp('last_notification_at', { withTimezone: true }),
});

export const announcementReactions = pgTable('announcement_reactions', {
  ...baseColumns,
  announcementId: integer('announcement_id').notNull(),
  userId: varchar('user_id', { length: 50 }).notNull(),
  emoji: varchar('emoji', { length: 100 }).notNull(),
  messageId: varchar('message_id', { length: 50 }), // Discord message ID where reaction was added
});

export const announcementComments = pgTable('announcement_comments', {
  ...baseColumns,
  announcementId: integer('announcement_id').notNull(),
  userId: varchar('user_id', { length: 50 }).notNull(),
  content: text('content').notNull(),
  parentCommentId: integer('parent_comment_id'), // For replies
  isEdited: boolean('is_edited').default(false).notNull(),
  editedAt: timestamp('edited_at', { withTimezone: true }),
  isDeleted: boolean('is_deleted').default(false).notNull(),
  deletedAt: timestamp('deleted_at', { withTimezone: true }),
  moderatorId: varchar('moderator_id', { length: 50 }), // If deleted by moderator
});

export const announcementAcknowledgments = pgTable('announcement_acknowledgments', {
  ...baseColumns,
  announcementId: integer('announcement_id').notNull(),
  userId: varchar('user_id', { length: 50 }).notNull(),
  acknowledgedAt: timestamp('acknowledged_at', { withTimezone: true }).defaultNow().notNull(),
  channelId: varchar('channel_id', { length: 50 }), // Where they acknowledged it
  messageId: varchar('message_id', { length: 50 }), // Discord message ID
});

export const announcementTemplates = pgTable('announcement_templates', {
  ...baseColumns,
  name: varchar('name', { length: 255 }).notNull(),
  description: text('description'),
  creatorId: varchar('creator_id', { length: 50 }).notNull(),
  guildId: varchar('guild_id', { length: 50 }).notNull(),
  type: varchar('type', { length: 50 }).notNull(),
  titleTemplate: varchar('title_template', { length: 255 }).notNull(),
  contentTemplate: text('content_template').notNull(),
  embedTemplate: jsonb('embed_template').$type<{
    color?: string;
    thumbnail?: string;
    image?: string;
    footer?: string;
    fields?: Array<{
      name: string;
      value: string;
      inline?: boolean;
    }>;
  }>(),
  defaultSettings: jsonb('default_settings').$type<{
    priority: string;
    allowComments: boolean;
    requiresAcknowledgment: boolean;
    isPinned: boolean;
    targetChannels?: string[];
    targetRoles?: string[];
  }>(),
  usageCount: integer('usage_count').default(0).notNull(),
  isPublic: boolean('is_public').default(false).notNull(),
  isActive: boolean('is_active').default(true).notNull(),
});

export const announcementAnalytics = pgTable('announcement_analytics', {
  ...baseColumns,
  announcementId: integer('announcement_id').notNull(),
  date: timestamp('date', { withTimezone: true }).notNull(),
  views: integer('views').default(0).notNull(),
  uniqueViews: integer('unique_views').default(0).notNull(),
  reactions: integer('reactions').default(0).notNull(),
  comments: integer('comments').default(0).notNull(),
  acknowledgments: integer('acknowledgments').default(0).notNull(),
  deliveries: integer('deliveries').default(0).notNull(),
  failedDeliveries: integer('failed_deliveries').default(0).notNull(),
  engagementRate: integer('engagement_rate').default(0).notNull(), // percentage
});

export const announcementSchedule = pgTable('announcement_schedule', {
  ...baseColumns,
  name: varchar('name', { length: 255 }).notNull(),
  description: text('description'),
  creatorId: varchar('creator_id', { length: 50 }).notNull(),
  guildId: varchar('guild_id', { length: 50 }).notNull(),
  templateId: integer('template_id'),
  schedule: jsonb('schedule').$type<{
    type: 'once' | 'daily' | 'weekly' | 'monthly';
    dayOfWeek?: number; // 0-6 for weekly
    dayOfMonth?: number; // 1-31 for monthly
    time: string; // HH:MM format
    timezone: string;
  }>(),
  nextRun: timestamp('next_run', { withTimezone: true }),
  lastRun: timestamp('last_run', { withTimezone: true }),
  isActive: boolean('is_active').default(true).notNull(),
  runCount: integer('run_count').default(0).notNull(),
  maxRuns: integer('max_runs'), // null for unlimited
});

// Relations
