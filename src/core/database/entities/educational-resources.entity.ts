
import { boolean, integer, jsonb, pgTable, text, timestamp, varchar, decimal } from 'drizzle-orm/pg-core';
import { baseColumns } from './base.entity';
import { users } from './user.entity';

export const courses = pgTable('courses', {
  ...baseColumns,
  title: varchar('title', { length: 255 }).notNull(),
  description: text('description').notNull(),
  instructorId: varchar('instructor_id', { length: 50 }).notNull(),
  difficulty: varchar('difficulty', { length: 20 }).notNull(), // 'beginner' | 'intermediate' | 'advanced'
  category: varchar('category', { length: 100 }).notNull(),
  subcategory: varchar('subcategory', { length: 100 }),
  duration: integer('duration').notNull(), // in hours
  price: decimal('price', { precision: 10, scale: 2 }).default('0').notNull(),
  currency: varchar('currency', { length: 3 }).default('USD').notNull(),
  prerequisites: jsonb('prerequisites').$type<string[]>(),
  learningOutcomes: jsonb('learning_outcomes').$type<string[]>(),
  syllabus: jsonb('syllabus').$type<Array<{
    module: string;
    lessons: string[];
    duration: number;
  }>>(),
  tags: jsonb('tags').$type<string[]>(),
  thumbnailUrl: text('thumbnail_url'),
  isPublished: boolean('is_published').default(false).notNull(),
  maxEnrollments: integer('max_enrollments'),
  currentEnrollments: integer('current_enrollments').default(0).notNull(),
  rating: decimal('rating', { precision: 3, scale: 2 }).default('0').notNull(),
  reviewCount: integer('review_count').default(0).notNull(),
  guildId: varchar('guild_id', { length: 50 }).notNull(),
});

export const courseEnrollments = pgTable('course_enrollments', {
  ...baseColumns,
  courseId: integer('course_id').notNull(),
  userId: varchar('user_id', { length: 50 }).notNull(),
  enrolledAt: timestamp('enrolled_at', { withTimezone: true }).defaultNow().notNull(),
  progress: integer('progress').default(0).notNull(), // 0-100 percentage
  isCompleted: boolean('is_completed').default(false).notNull(),
  completedAt: timestamp('completed_at', { withTimezone: true }),
  certificateId: integer('certificate_id'),
  lastAccessedAt: timestamp('last_accessed_at', { withTimezone: true }).defaultNow().notNull(),
});

export const courseLessons = pgTable('course_lessons', {
  ...baseColumns,
  courseId: integer('course_id').notNull(),
  title: varchar('title', { length: 255 }).notNull(),
  description: text('description'),
  content: text('content').notNull(),
  lessonType: varchar('lesson_type', { length: 50 }).notNull(), // 'video' | 'text' | 'quiz' | 'assignment'
  duration: integer('duration'), // in minutes
  order: integer('order').notNull(),
  moduleId: integer('module_id'),
  videoUrl: text('video_url'),
  resources: jsonb('resources').$type<Array<{
    title: string;
    url: string;
    type: string;
  }>>(),
  isPreview: boolean('is_preview').default(false).notNull(),
});

export const learningProgress = pgTable('learning_progress', {
  ...baseColumns,
  userId: varchar('user_id', { length: 50 }).notNull(),
  courseId: integer('course_id').notNull(),
  lessonId: integer('lesson_id').notNull(),
  isCompleted: boolean('is_completed').default(false).notNull(),
  completedAt: timestamp('completed_at', { withTimezone: true }),
  timeSpent: integer('time_spent').default(0).notNull(), // in minutes
  score: integer('score'), // for quizzes/assignments
  attempts: integer('attempts').default(0).notNull(),
});

export const certificates = pgTable('certificates', {
  ...baseColumns,
  userId: varchar('user_id', { length: 50 }).notNull(),
  courseId: integer('course_id').notNull(),
  certificateNumber: varchar('certificate_number', { length: 100 }).notNull().unique(),
  issuedAt: timestamp('issued_at', { withTimezone: true }).defaultNow().notNull(),
  expiresAt: timestamp('expires_at', { withTimezone: true }),
  isVerified: boolean('is_verified').default(true).notNull(),
  certificateUrl: text('certificate_url'),
  metadata: jsonb('metadata').$type<Record<string, any>>(),
});

export const courseReviews = pgTable('course_reviews', {
  ...baseColumns,
  courseId: integer('course_id').notNull(),
  userId: varchar('user_id', { length: 50 }).notNull(),
  rating: integer('rating').notNull(), // 1-5
  title: varchar('title', { length: 255 }),
  content: text('content'),
  isVerified: boolean('is_verified').default(false).notNull(), // completed course
  helpful: integer('helpful').default(0).notNull(),
});

// Relations
