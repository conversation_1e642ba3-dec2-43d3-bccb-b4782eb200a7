
import { boolean, integer, jsonb, pgTable, text, timestamp, varchar } from 'drizzle-orm/pg-core';
import { baseColumns } from './base.entity';
import { users } from './user.entity';

export const supportTickets = pgTable('support_tickets', {
  ...baseColumns,
  ticketNumber: varchar('ticket_number', { length: 20 }).notNull().unique(), // e.g., TK-2024-001
  userId: varchar('user_id', { length: 50 }).notNull(),
  title: varchar('title', { length: 255 }).notNull(),
  description: text('description').notNull(),
  category: varchar('category', { length: 50 }).notNull(), // 'bug' | 'feature' | 'account' | 'billing' | 'general' | 'security' | 'integration'
  priority: varchar('priority', { length: 20 }).notNull(), // 'low' | 'medium' | 'high' | 'critical'
  status: varchar('status', { length: 20 }).default('open').notNull(), // 'open' | 'in-progress' | 'waiting-response' | 'resolved' | 'closed'
  assignedTo: varchar('assigned_to', { length: 50 }),
  guildId: varchar('guild_id', { length: 50 }).notNull(),
  estimatedResolution: timestamp('estimated_resolution', { withTimezone: true }),
  resolvedAt: timestamp('resolved_at', { withTimezone: true }),
  attachments: jsonb('attachments').$type<string[]>(),
  tags: jsonb('tags').$type<string[]>(),
});

export const ticketResponses = pgTable('ticket_responses', {
  ...baseColumns,
  ticketId: varchar('ticket_id', { length: 50 }).notNull(),
  userId: varchar('user_id', { length: 50 }).notNull(),
  username: varchar('username', { length: 100 }).notNull(),
  message: text('message').notNull(),
  isStaff: boolean('is_staff').default(false).notNull(),
  isInternal: boolean('is_internal').default(false).notNull(),
  attachments: jsonb('attachments').$type<string[]>(),
});

export const knowledgeBaseArticles = pgTable('knowledge_base_articles', {
  ...baseColumns,
  title: varchar('title', { length: 255 }).notNull(),
  content: text('content').notNull(),
  category: varchar('category', { length: 100 }).notNull(),
  subcategory: varchar('subcategory', { length: 100 }).notNull(),
  tags: jsonb('tags').$type<string[]>(),
  difficulty: varchar('difficulty', { length: 20 }).notNull(), // 'beginner' | 'intermediate' | 'advanced'
  views: integer('views').default(0).notNull(),
  helpful: integer('helpful').default(0).notNull(),
  notHelpful: integer('not_helpful').default(0).notNull(),
  author: varchar('author', { length: 100 }).notNull(),
  relatedArticles: jsonb('related_articles').$type<string[]>(),
  searchTerms: jsonb('search_terms').$type<string[]>(),
  isPublished: boolean('is_published').default(true).notNull(),
});

export const troubleshootingGuides = pgTable('troubleshooting_guides', {
  ...baseColumns,
  title: varchar('title', { length: 255 }).notNull(),
  description: text('description').notNull(),
  category: varchar('category', { length: 100 }).notNull(),
  symptoms: jsonb('symptoms').$type<string[]>(),
  solutions: jsonb('solutions').$type<Array<{
    id: string;
    title: string;
    description: string;
    commands?: string[];
    screenshots?: string[];
    expectedResult: string;
    alternativeSolutions?: string[];
    commonIssues?: string[];
  }>>(),
  prerequisites: jsonb('prerequisites').$type<string[]>(),
  estimatedTime: integer('estimated_time').notNull(), // minutes
  difficulty: varchar('difficulty', { length: 20 }).notNull(), // 'easy' | 'medium' | 'hard'
  successRate: integer('success_rate').default(0).notNull(), // percentage
  relatedGuides: jsonb('related_guides').$type<string[]>(),
  isActive: boolean('is_active').default(true).notNull(),
});

export const systemStatus = pgTable('system_status', {
  ...baseColumns,
  service: varchar('service', { length: 100 }).notNull().unique(),
  status: varchar('status', { length: 20 }).notNull(), // 'operational' | 'degraded' | 'partial-outage' | 'major-outage'
  lastChecked: timestamp('last_checked', { withTimezone: true }).defaultNow().notNull(),
  uptime: integer('uptime').default(100).notNull(), // percentage
  responseTime: integer('response_time').default(0).notNull(), // milliseconds
  incidents: integer('incidents').default(0).notNull(),
  description: text('description'),
});

// Relations
