import { boolean, integer, jsonb, pgTable, timestamp, varchar } from 'drizzle-orm/pg-core';

export type AIChatSessionStatus = 'active' | 'archived' | 'expired' | 'closed';
export type AIAccessLevel = 'free' | 'premium' | 'enterprise';

export const aiChatSessions = pgTable('ai_chat_sessions', {
  id: varchar('id', { length: 255 }).primaryKey(),
  userId: varchar('user_id', { length: 255 }).notNull(),
  guildId: varchar('guild_id', { length: 255 }).notNull(),
  agentType: varchar('agent_type', { length: 100 }).notNull(),
  threadId: varchar('thread_id', { length: 255 }).notNull(),
  channelId: varchar('channel_id', { length: 255 }).notNull(),
  status: varchar('status', { length: 50 }).notNull().$type<AIChatSessionStatus>().default('active'),
  accessLevel: varchar('access_level', { length: 50 }).notNull().$type<AIAccessLevel>().default('free'),
  sessionData: jsonb('session_data'),
  messageCount: integer('message_count').default(0),
  lastMessageAt: timestamp('last_message_at'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  archivedAt: timestamp('archived_at'),
  expiresAt: timestamp('expires_at'),
});

export type AIChatSession = typeof aiChatSessions.$inferSelect;
export type NewAIChatSession = typeof aiChatSessions.$inferInsert;