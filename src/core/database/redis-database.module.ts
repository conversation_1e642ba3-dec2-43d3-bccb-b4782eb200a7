import { Global, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { RedisDatabaseService } from './redis-database.service';
import { RedisEntityManagerService } from './redis-entity-manager.service';
import { RedisService } from './redis.service';
import { RepositoryFactory } from './repositories/repository.factory';
import { UserRepository } from './repositories/user.repository';
import { SessionRepository } from './repositories/session.repository';
import { GuildRepository } from './repositories/guild.repository';
import { RedisMigrationUtil, RedisDataUtil } from './utils/redis-migration.util';

@Global()
@Module({
  imports: [
    ConfigModule
  ],
  providers: [
    RedisService,
    RedisDatabaseService,
    RedisEntityManagerService,
    RepositoryFactory,
    UserRepository,
    SessionRepository,
    GuildRepository,
    RedisMigrationUtil,
    RedisDataUtil
  ],
  exports: [
    RedisService,
    RedisDatabaseService,
    RedisEntityManagerService,
    RepositoryFactory,
    UserRepository,
    SessionRepository,
    GuildRepository
  ]
})
export class RedisDatabaseModule {}