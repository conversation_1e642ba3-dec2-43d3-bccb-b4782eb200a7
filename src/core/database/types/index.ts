/**
 * Export all Redis types for the application
 */

// Base interfaces
export * from './base.interface';

// Redis types
export * from './redis.types';

// Repository types
export * from './repository.types';

// Re-export commonly used types with aliases
export type {
  BaseEntity,
  BaseEntity as RedisEntity,
  CreateEntity,
  UpdateEntity,
  UserOwned,
  GuildSpecific,
  ChannelSpecific,
  ActivityTracked,
  Versioned,
  Auditable,
  Taggable,
  Searchable,
  PreferenceStorage,
  ProfileStorage,
  MetadataStorage,
  ConfigurationStorage,
  StatisticsStorage,
} from './base.interface';

export type {
  RedisConfig,
  BaseRedisEntity,
  RedisKeyPattern,
  RedisOperationResult,
  RedisRepository,
  RedisSearchOptions,
  RedisTransactionContext,
  RedisTransaction,
  RedisCacheOptions,
  RedisPubSubMessage,
  RedisEventListener,
  RedisValue,
  RedisHash,
  RedisList,
  RedisSet,
  RedisSortedSet,
  RedisMigration,
  MigrationStatus,
} from './redis.types';

export {
  RedisError,
  RedisConnectionError,
  RedisTimeoutError,
} from './redis.types';

export type {
  BaseRedisRepository,
  ExtendedRedisRepository,
  RepositoryFactory,
  EntityKeyGenerator,
  RepositoryOptions,
  BulkOperation,
  EntityEvent,
  ExportOptions,
  ImportOptions,
  QueryBuilder,
  ComparisonOperator,
  QueryableRepository,
  MigrationRepository,
  MigrationRecord,
  HealthCheckRepository,
  RedisStats,
  MemoryUsage,
  RepositoryResult,
  RepositoryListResult,
  RepositoryBooleanResult,
  RepositoryNumberResult,
  RepositoryManager,
} from './repository.types';

/**
 * Common type guards for Redis entities
 */
export const isBaseEntity = (obj: unknown): obj is BaseEntity => {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'id' in obj &&
    'createdAt' in obj &&
    'updatedAt' in obj &&
    typeof (obj as any).id === 'string' &&
    (obj as any).createdAt instanceof Date &&
    (obj as any).updatedAt instanceof Date
  );
};

export const isDeleted = (entity: BaseEntity): boolean => {
  return entity.deletedAt !== null && entity.deletedAt !== undefined;
};

/**
 * Utility function to create Redis key patterns
 */
export const createKeyPattern = (entityName: string) => {
  const entityKey = entityName.toLowerCase();
  return {
    primary: (id: string) => `${entityKey}:${id}`,
    pattern: `${entityKey}:*`,
    index: (field: string) => `idx:${entityKey}:${field}`,
    search: `search:${entityKey}`,
    byField: (field: string, value: any) => `${entityKey}:${field}:${value}`,
  };
};

/**
 * Utility function to generate unique IDs for Redis entities
 */
export const generateRedisId = (): string => {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 8);
  return `${timestamp}_${random}`;
};

/**
 * Utility function to serialize/deserialize Redis values
 */
export const RedisSerializer = {
  serialize: (value: unknown): string => {
    if (typeof value === 'string') return value;
    return JSON.stringify(value);
  },

  deserialize: <T = unknown>(value: string): T => {
    try {
      return JSON.parse(value);
    } catch {
      return value as T;
    }
  },

  serializeHash: (obj: Record<string, unknown>): Record<string, string> => {
    const hash: Record<string, string> = {};
    for (const [key, value] of Object.entries(obj)) {
      hash[key] = RedisSerializer.serialize(value);
    }
    return hash;
  },

  deserializeHash: <T extends BaseEntity = BaseEntity>(hash: Record<string, string>): T => {
    const obj: Record<string, unknown> = {};
    for (const [key, value] of Object.entries(hash)) {
      obj[key] = RedisSerializer.deserialize(value);
    }
    return obj as T;
  },
} as const;

/**
 * Common Redis error codes
 */
export const RedisErrorCodes = {
  CONNECTION_ERROR: 'CONNECTION_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  SERIALIZATION_ERROR: 'SERIALIZATION_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  NOT_FOUND: 'NOT_FOUND',
  DUPLICATE_KEY: 'DUPLICATE_KEY',
  TRANSACTION_FAILED: 'TRANSACTION_FAILED',
  MIGRATION_FAILED: 'MIGRATION_FAILED',
} as const;

export type RedisErrorCode = typeof RedisErrorCodes[keyof typeof RedisErrorCodes];