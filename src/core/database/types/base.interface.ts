/**
 * Base interfaces for Redis entities - replacement for Drizzle ORM entities
 */

/**
 * Base interface for all Redis entities with common fields
 * Generic constraint ensures proper typing for ID field
 */
export interface BaseEntity {
  readonly id: string;
  readonly createdAt: Date;
  readonly updatedAt: Date;
  deletedAt?: Date | null;
}

/**
 * Entity creation type - excludes auto-generated fields
 * Strict constraint ensures T has all required BaseEntity properties
 */
export type CreateEntity<T extends BaseEntity> = Omit<T, 'id' | 'createdAt' | 'updatedAt'> & {
  readonly __create?: never; // Brand type for creation
};

/**
 * Entity update type - excludes id and createdAt, makes other fields optional
 * Ensures type safety for update operations
 */
export type UpdateEntity<T extends BaseEntity> = Partial<Omit<T, 'id' | 'createdAt'>> & {
  readonly __update?: never; // Brand type for updates
  readonly updatedAt?: Date; // Allow explicit updatedAt override
};

/**
 * Soft delete interface for entities that support soft deletion
 */
export interface SoftDeletable {
  deletedAt?: Date | null;
}

/**
 * Timestamped interface for entities with timestamp tracking
 */
export interface Timestamped {
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Identifiable interface for entities with ID
 */
export interface Identifiable {
  id: string;
}

/**
 * User-owned interface for entities that belong to a user
 */
export interface UserOwned {
  userId: string;
}

/**
 * Guild-specific interface for Discord guild-related entities
 */
export interface GuildSpecific {
  guildId: string;
}

/**
 * Channel-specific interface for Discord channel-related entities
 */
export interface ChannelSpecific {
  channelId: string;
}

/**
 * Activity tracking interface
 */
export interface ActivityTracked {
  lastActivityAt?: Date;
  isActive: boolean;
}

/**
 * Versioned interface for entities that support versioning
 */
export interface Versioned {
  version: number;
}

/**
 * Auditable interface for entities that need audit trails
 */
export interface Auditable {
  createdBy?: string;
  updatedBy?: string;
  auditLog?: AuditEntry[];
}

export interface AuditEntry {
  action: 'CREATE' | 'UPDATE' | 'DELETE' | 'RESTORE';
  performedBy: string;
  performedAt: Date;
  changes?: Record<string, { from: any; to: any }>;
  reason?: string;
}

/**
 * Taggable interface for entities that support tagging
 */
export interface Taggable {
  tags?: string[];
}

/**
 * Searchable interface for entities that support full-text search
 */
export interface Searchable {
  searchableContent?: string;
  searchVector?: string;
}

/**
 * Preference storage interface for flexible JSON preferences
 */
export interface PreferenceStorage<T = Record<string, any>> {
  preferences?: T;
}

/**
 * Profile storage interface for flexible JSON profiles
 */
export interface ProfileStorage<T = Record<string, any>> {
  profile?: T;
}

/**
 * Metadata storage interface for flexible JSON metadata
 */
export interface MetadataStorage<T = Record<string, any>> {
  metadata?: T;
}

/**
 * Configuration storage interface
 */
export interface ConfigurationStorage<T = Record<string, any>> {
  config?: T;
}

/**
 * Statistics storage interface
 */
export interface StatisticsStorage<T = Record<string, any>> {
  stats?: T;
}

/**
 * Utility type to make all properties of T required
 */
export type RequiredEntity<T> = Required<T>;

/**
 * Utility type to make all properties of T optional except K
 */
export type PartialExcept<T, K extends keyof T> = Partial<T> & Pick<T, K>;

/**
 * Utility type for entity with relations
 */
export type EntityWithRelations<T, R = {}> = T & R;

/**
 * Redis key generation utilities with strict typing
 */
export type EntityKeyGenerator<T extends BaseEntity> = {
  readonly primary: (id: string) => string;
  readonly byUser?: (userId: string) => string;
  readonly byGuild?: (guildId: string) => string;
  readonly byChannel?: (channelId: string) => string;
  readonly index: Partial<Record<keyof T, string>>;
  readonly search?: string;
  readonly pattern: string;
  readonly byField?: <K extends keyof T>(field: K, value: T[K]) => string;
};

/**
 * Entity validation interface with proper constraints
 */
export interface EntityValidator<T extends BaseEntity> {
  validate(entity: Partial<T>): Promise<ValidationResult>;
  validateCreate(data: CreateEntity<T>): Promise<ValidationResult>;
  validateUpdate(data: UpdateEntity<T>): Promise<ValidationResult>;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
  value?: any;
}

/**
 * Entity serialization interface
 */
export interface EntitySerializer<T> {
  serialize(entity: T): string;
  deserialize(data: string): T;
  toRedisHash(entity: T): Record<string, string>;
  fromRedisHash(hash: Record<string, string>): T;
}