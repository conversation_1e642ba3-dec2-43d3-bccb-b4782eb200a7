/**
 * Redis-specific types for type-safe operations
 */

/**
 * Redis configuration interface that matches ioredis RedisOptions
 * Based on ioredis v5.6.1 RedisOptions interface
 */
export interface RedisConfig {
  host?: string;
  port?: number;
  password?: string;
  username?: string;
  db?: number;
  url?: string;
  
  // Connection options
  connectTimeout?: number;
  commandTimeout?: number;
  socketTimeout?: number;
  lazyConnect?: boolean;
  keepAlive?: number;
  noDelay?: boolean;
  connectionName?: string;
  family?: number;
  
  // Retry and resilience options
  retryStrategy?: (times: number) => number | void | null;
  maxRetriesPerRequest?: number | null;
  reconnectOnError?: ((err: Error) => boolean | 1 | 2) | null;
  
  // Queue and command options
  enableOfflineQueue?: boolean;
  enableReadyCheck?: boolean;
  maxLoadingRetryTime?: number;
  
  // Pub/Sub options
  autoResubscribe?: boolean;
  autoResendUnfulfilledCommands?: boolean;
  
  // Pipeline options
  enableAutoPipelining?: boolean;
  autoPipeliningIgnoredCommands?: string[];
  
  // Other options
  readOnly?: boolean;
  stringNumbers?: boolean;
  monitor?: boolean;
  offlineQueue?: boolean;
  commandQueue?: boolean;
}

/**
 * Base Redis entity with timestamps - strict typing
 */
export interface BaseRedisEntity {
  readonly id: string;
  readonly createdAt: Date;
  readonly updatedAt: Date;
  deletedAt?: Date | null;
}

/**
 * Redis key patterns for consistent naming
 */
export type RedisKeyPattern = 
  | `user:${string}`
  | `session:${string}`
  | `guild:${string}`
  | `agent:${string}`
  | `chat:${string}`
  | `panel:${string}`
  | `support:${string}`
  | `community:${string}`
  | `config:${string}`;

/**
 * Redis operation result types with strict constraints
 */
export interface RedisOperationResult<T = unknown> {
  readonly success: boolean;
  readonly data?: T;
  readonly error?: string;
  readonly timestamp?: Date;
  readonly operation?: string;
}

/**
 * Generic Redis repository operations with strict typing
 */
export interface RedisRepository<T extends BaseRedisEntity> {
  findById(id: string): Promise<T | null>;
  findByPattern(pattern: string): Promise<ReadonlyArray<T>>;
  findAll(): Promise<ReadonlyArray<T>>;
  create(data: Omit<T, 'id' | 'createdAt' | 'updatedAt'>): Promise<T>;
  update(id: string, data: Partial<Omit<T, 'id' | 'createdAt'>>): Promise<T | null>;
  delete(id: string): Promise<boolean>;
  softDelete(id: string): Promise<boolean>;
  exists(id: string): Promise<boolean>;
  count(): Promise<number>;
}

/**
 * Redis search options with proper typing
 */
export interface RedisSearchOptions {
  readonly limit?: number;
  readonly offset?: number;
  readonly sortBy?: string;
  readonly sortOrder?: 'ASC' | 'DESC';
  readonly filters?: Readonly<Record<string, unknown>>;
}

/**
 * Redis transaction context
 */
export interface RedisTransactionContext {
  multi(): RedisTransaction;
}

/**
 * Redis transaction operations
 */
export interface RedisTransaction {
  pipeline?: any; // Pipeline object for transaction operations
  set?(key: string, value: string | object): RedisTransaction;
  get?(key: string): RedisTransaction;
  del?(key: string): RedisTransaction;
  exists?(key: string): RedisTransaction;
  expire?(key: string, seconds: number): RedisTransaction;
  exec(): Promise<ReadonlyArray<unknown>>;
}

/**
 * Redis cache options
 */
export interface RedisCacheOptions {
  ttl?: number; // Time to live in seconds
  compress?: boolean;
  serialize?: boolean;
}

/**
 * Redis pub/sub types
 */
export interface RedisPubSubMessage<T = any> {
  channel: string;
  pattern?: string;
  data: T;
  timestamp: Date;
}

export type RedisEventListener<T = any> = (message: RedisPubSubMessage<T>) => void;

/**
 * Redis collection types for different data structures with strict typing
 */
export type RedisValue = string | number | boolean | Record<string, unknown> | null | undefined;
export type RedisHash = Readonly<Record<string, RedisValue>>;
export type RedisList = ReadonlyArray<RedisValue>;
export type RedisSet = ReadonlySet<RedisValue>;
export type RedisSortedSet = ReadonlyMap<RedisValue, number>;

/**
 * Redis error types
 */
export class RedisError extends Error {
  constructor(
    message: string,
    public readonly code?: string,
    public readonly operation?: string
  ) {
    super(message);
    this.name = 'RedisError';
  }
}

export class RedisConnectionError extends RedisError {
  constructor(message: string) {
    super(message, 'CONNECTION_ERROR');
    this.name = 'RedisConnectionError';
  }
}

export class RedisTimeoutError extends RedisError {
  constructor(operation: string) {
    super(`Operation ${operation} timed out`, 'TIMEOUT_ERROR', operation);
    this.name = 'RedisTimeoutError';
  }
}

/**
 * Redis migration types
 */
export interface RedisMigration {
  version: string;
  description: string;
  up: (redis: any) => Promise<void>;
  down: (redis: any) => Promise<void>;
}

export interface MigrationStatus {
  version: string;
  appliedAt: Date;
  success: boolean;
  error?: string;
}