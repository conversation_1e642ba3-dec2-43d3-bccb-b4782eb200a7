import { ConflictException, Inject, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { DatabaseService } from '@/core/database';
import { ConfigService } from '@nestjs/config';




import {
    BillingInfo,
    Organization,
    OrganizationStatus,
    OrganizationTier,
    ResourceLimits
} from '../../common/interfaces/enterprise.interfaces';

import { CacheService } from '../cache/cache.service';
import { organizations } from '@/core/database';
import { LoggingService } from '../monitoring/logging.service';
import { MetricsService } from '../monitoring/metrics.service';
import { eq, and, or, desc, asc, count, sql } from 'drizzle-orm';

export interface CreateOrganizationDto {
  name: string;
  slug?: string;
  tier?: OrganizationTier;
  ownerId: string;
  settings?: Partial<Organization['settings']>;
}

export interface UpdateOrganizationDto {
  name?: string;
  tier?: OrganizationTier;
  status?: OrganizationStatus;
  settings?: Partial<Organization['settings']>;
}

@Injectable()
export class OrganizationService {
  private readonly logger = new Logger(OrganizationService.name);

  constructor(
    private readonly databaseService: DatabaseService,
    private readonly cacheService: CacheService,
    private readonly metricsService: MetricsService,
    private readonly loggingService: LoggingService,
    private readonly configService: ConfigService,
  ) {}

  private convertDbToInterface(dbOrg: any): Organization {
    return {
      ...dbOrg,
      id: dbOrg.id.toString(),
    };
  }

  async create(dto: CreateOrganizationDto): Promise<Organization> {
    const slug = dto.slug || this.generateSlug(dto.name);
    
    // Check if slug is already taken
    const existing = await this.findBySlug(slug);
    if (existing) {
      throw new ConflictException(`Organization with slug '${slug}' already exists`);
    }

    const tier = dto.tier || OrganizationTier.FREE;
    const limits = this.getDefaultLimits(tier);
    
    const organizationData = {
      name: dto.name,
      slug,
      tier,
      status: OrganizationStatus.ACTIVE,
      ownerId: dto.ownerId,
      settings: {
        allowedDomains: [],
        ssoEnabled: false,
        auditLogRetention: 90,
        dataRetention: 365,
        customBranding: tier !== OrganizationTier.FREE,
        apiAccess: tier !== OrganizationTier.FREE,
        webhookEndpoints: [],
        ...dto.settings,
      },
      limits,
      billing: this.getDefaultBilling(tier),
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    try {
      const [organization] = await this.db
        .insert(organizations)
        .values(organizationData)
        .returning();

      // Cache the organization
      await this.cacheService.set(`org:${organization.id}`, organization, 3600);
      await this.cacheService.set(`org:slug:${slug}`, organization, 3600);

      // Update metrics
      this.metricsService.organizationsTotal.inc({ tier, status: organization.status });

      // Log the creation
      this.loggingService.logAuditEvent(
        'organization_created',
        'organization',
        organization.id.toString(),
        dto.ownerId,
        organization.id.toString(),
      );

      this.logger.log(`Organization created: ${organization.name} (${organization.id})`);
      return this.convertDbToInterface(organization);
    } catch (error) {
      this.logger.error('Failed to create organization', error);
      throw error;
    }
  }

  async findById(id: string): Promise<Organization | null> {
    // Try cache first
    const cached = await this.cacheService.get<Organization>(`org:${id}`);
    if (cached) {
      return cached;
    }

    try {
      const [organization] = await this.db
        .select()
        .from(organizations)
        .where(eq(organizations.id, parseInt(id)))
        .limit(1);

      if (organization) {
        const converted = this.convertDbToInterface(organization);
        // Cache for 1 hour
        await this.cacheService.set(`org:${id}`, converted, 3600);
        return converted;
      }

      return null;
    } catch (error) {
      this.logger.error(`Failed to find organization by ID: ${id}`, error);
      return null;
    }
  }

  async findBySlug(slug: string): Promise<Organization | null> {
    // Try cache first
    const cached = await this.cacheService.get<Organization>(`org:slug:${slug}`);
    if (cached) {
      return cached;
    }

    try {
      const [organization] = await this.db
        .select()
        .from(organizations)
        .where(eq(organizations.slug, slug))
        .limit(1);

      if (organization) {
        const converted = this.convertDbToInterface(organization);
        // Cache for 1 hour
        await this.cacheService.set(`org:slug:${slug}`, converted, 3600);
        await this.cacheService.set(`org:${converted.id}`, converted, 3600);
        return converted;
      }

      return null;
    } catch (error) {
      this.logger.error(`Failed to find organization by slug: ${slug}`, error);
      return null;
    }
  }

  async update(id: string, dto: UpdateOrganizationDto, updatedBy: string): Promise<Organization> {
    const existing = await this.findById(id);
    if (!existing) {
      throw new NotFoundException(`Organization with ID '${id}' not found`);
    }

    const updateData: any = {
      ...dto,
      updatedAt: new Date(),
    };

    // If tier is being changed, update limits
    if (dto.tier && dto.tier !== existing.tier) {
      updateData.limits = this.getDefaultLimits(dto.tier);
      updateData.billing = this.getDefaultBilling(dto.tier);
    }

    try {
      const [updated] = await this.db
        .update(organizations)
        .set(updateData)
        .where(eq(organizations.id, parseInt(id)))
        .returning();

      // Invalidate cache
      await this.cacheService.del(`org:${id}`);
      await this.cacheService.del(`org:slug:${existing.slug}`);

      // Update metrics if tier or status changed
      if (dto.tier || dto.status) {
        this.metricsService.organizationsTotal.inc({ 
          tier: updated.tier, 
          status: updated.status 
        });
        
        if (existing.tier !== updated.tier || existing.status !== updated.status) {
          this.metricsService.organizationsTotal.dec({ 
            tier: existing.tier, 
            status: existing.status 
          });
        }
      }

      // Log the update
      this.loggingService.logAuditEvent(
        'organization_updated',
        'organization',
        id,
        updatedBy,
        id,
        JSON.stringify({ changes: dto })
      );

      this.logger.log(`Organization updated: ${updated.name} (${id})`);
      return this.convertDbToInterface(updated);
    } catch (error) {
      this.logger.error(`Failed to update organization: ${id}`, error);
      throw error;
    }
  }

  async delete(id: string, deletedBy: string): Promise<void> {
    const existing = await this.findById(id);
    if (!existing) {
      throw new NotFoundException(`Organization with ID '${id}' not found`);
    }

    try {
      await this.db
        .delete(organizations)
        .where(eq(organizations.id, parseInt(id)));

      // Invalidate cache
      await this.cacheService.del(`org:${id}`);
      await this.cacheService.del(`org:slug:${existing.slug}`);

      // Update metrics
      this.metricsService.organizationsTotal.dec({ 
        tier: existing.tier, 
        status: existing.status 
      });

      // Log the deletion
      this.loggingService.logAuditEvent(
        'organization_deleted',
        'organization',
        id,
        deletedBy,
        id,
      );

      this.logger.log(`Organization deleted: ${existing.name} (${id})`);
    } catch (error) {
      this.logger.error(`Failed to delete organization: ${id}`, error);
      throw error;
    }
  }

  async list(options: any = {}): Promise<{ organizations: Organization[]; total: number }> {
    const page = options.page || 1;
    const limit = Math.min(options.limit || 50, 100);
    const offset = (page - 1) * limit;

    try {
      let query = this.db.select().from(organizations);
      let countQuery = this.db.select({ count: count() }).from(organizations);

      const conditions = [];
      if (options.tier) conditions.push(eq(organizations.tier, options.tier));
      if (options.status) conditions.push(eq(organizations.status, options.status));

      if (conditions.length > 0) {
        const whereClause = and(...conditions);
        query = query.where(whereClause) as any;
        countQuery = countQuery.where(whereClause) as any;
      }

      const [orgs, totalResult] = await Promise.all([
        query.orderBy(desc(organizations.createdAt)).limit(limit).offset(offset),
        countQuery
      ]);

      return {
        organizations: orgs.map((org: any) => this.convertDbToInterface(org)),
        total: totalResult[0].count,
      };
    } catch (error) {
      this.logger.error('Failed to list organizations', error);
      throw error;
    }
  }

  async getStats(): Promise<any> {
    try {
      const stats = await this.db
        .select({ tier: organizations.tier, status: organizations.status, count: count() })
        .from(organizations)
        .groupBy(organizations.tier, organizations.status);

      const result = { total: 0, byTier: {}, byStatus: {} };
      stats.forEach(stat => {
        result.total += stat.count;
        ((result.byTier as any)[stat.tier]) = (((result.byTier as any)[stat.tier]) || 0) + stat.count;
        ((result.byStatus as any)[stat.status]) = (((result.byStatus as any)[stat.status]) || 0) + stat.count;
      });
      return result;
    } catch (error) {
      this.logger.error('Failed to get organization stats', error);
      throw error;
    }
  }

  private generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '')
      .substring(0, 50);
  }

  private getDefaultLimits(tier: OrganizationTier): ResourceLimits {
    const defaults = { maxGuilds: 1, maxUsers: 100, maxApiCalls: 1000, maxStorage: 100, maxBandwidth: 1000, maxConcurrentConnections: 5, features: ['basic'] };
    if (tier === OrganizationTier.ENTERPRISE) return { ...defaults, maxGuilds: -1, maxUsers: -1, maxApiCalls: -1, features: ['all'] };
    return defaults;
  }

  private getDefaultBilling(tier: OrganizationTier): BillingInfo {
    return {
      planId: tier,
      status: tier === OrganizationTier.FREE ? 'active' : 'trialing',
      currentPeriodStart: new Date(),
      currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
      trialEnd: tier !== OrganizationTier.FREE ? new Date(Date.now() + 14 * 24 * 60 * 60 * 1000) : undefined,
    };
  }
}
