import { Module, Global } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

import { OrganizationService } from './organization.service';
import { TenantService } from './tenant.service';
import { ResourceQuotaService } from './resource-quota.service';
import { BillingService } from './billing.service';
import { FeatureFlagService } from './feature-flag.service';

import { OrganizationController } from './controllers/organization.controller';
import { TenantController } from './controllers/tenant.controller';
import { BillingController } from './controllers/billing.controller';

import { TenantGuard } from './guards/tenant.guard';
import { ResourceQuotaGuard } from './guards/resource-quota.guard';
import { FeatureFlagGuard } from './guards/feature-flag.guard';

import { TenantInterceptor } from './interceptors/tenant.interceptor';
import { ResourceQuotaInterceptor } from './interceptors/resource-quota.interceptor';

import { DatabaseModule } from '@/core/database';
import { CacheModule } from '../cache/cache.module';
import { MonitoringModule } from '../monitoring/monitoring.module';

@Global()
@Module({
  imports: [
    ConfigModule,
    DatabaseModule,
    CacheModule,
    MonitoringModule,
  ],
  providers: [
    OrganizationService,
    TenantService,
    ResourceQuotaService,
    BillingService,
    FeatureFlagService,
    TenantGuard,
    ResourceQuotaGuard,
    FeatureFlagGuard,
    TenantInterceptor,
    ResourceQuotaInterceptor,
  ],
  controllers: [
    OrganizationController,
    TenantController,
    BillingController,
  ],
  exports: [
    OrganizationService,
    TenantService,
    ResourceQuotaService,
    BillingService,
    FeatureFlagService,
    TenantGuard,
    ResourceQuotaGuard,
    FeatureFlagGuard,
    TenantInterceptor,
    ResourceQuotaInterceptor,
  ],
})
export class MultiTenancyModule {}
