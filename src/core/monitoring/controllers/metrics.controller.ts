import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { MetricsService } from '../metrics.service';
import { PerformanceService } from '../performance.service';

@ApiTags('Monitoring')
@Controller('metrics')
export class MetricsController {
  constructor(
    private readonly metricsService: MetricsService,
    private readonly performanceService: PerformanceService,
  ) {}

  @Get()
  @ApiOperation({ summary: 'Get Prometheus metrics' })
  @ApiResponse({ status: 200, description: 'Prometheus metrics in text format' })
  async getMetrics(): Promise<string> {
    return this.metricsService.getMetrics();
  }

  @Get('performance')
  @ApiOperation({ summary: 'Get performance metrics' })
  @ApiQuery({ name: 'metric', required: false, description: 'Specific metric name' })
  @ApiQuery({ name: 'since', required: false, description: 'ISO date string for filtering' })
  async getPerformanceMetrics(
    @Query('metric') metric?: string,
    @Query('since') since?: string,
  ) {
    const sinceDate = since ? new Date(since) : undefined;

    if (metric) {
      return {
        metric,
        data: this.performanceService.getMetrics(metric, sinceDate),
        average: this.performanceService.getAverageMetric(metric, sinceDate),
        p95: this.performanceService.getPercentile(metric, 95, sinceDate),
        p99: this.performanceService.getPercentile(metric, 99, sinceDate),
      };
    }

    // Return summary of all metrics
    const thresholds = this.performanceService.getThresholds();
    const summary = thresholds.map((threshold: any) => ({
      metric: threshold.metric,
      average: this.performanceService.getAverageMetric(threshold.metric, sinceDate),
      p95: this.performanceService.getPercentile(threshold.metric, 95, sinceDate),
      p99: this.performanceService.getPercentile(threshold.metric, 99, sinceDate),
      threshold,
    }));

    return { summary };
  }

  @Get('system')
  @ApiOperation({ summary: 'Get system metrics' })
  async getSystemMetrics() {
    // Record current system metrics
    this.performanceService.recordMemoryUsage();
    this.performanceService.recordCpuUsage();

    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();

    return {
      memory: {
        heapUsed: memoryUsage.heapUsed,
        heapTotal: memoryUsage.heapTotal,
        rss: memoryUsage.rss,
        external: memoryUsage.external,
        heapUsedMB: Math.round(memoryUsage.heapUsed / 1024 / 1024),
        heapTotalMB: Math.round(memoryUsage.heapTotal / 1024 / 1024),
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system,
      },
      uptime: process.uptime(),
      version: process.version,
      platform: process.platform,
      arch: process.arch,
    };
  }

  @Get('thresholds')
  @ApiOperation({ summary: 'Get performance thresholds' })
  async getThresholds() {
    return {
      thresholds: this.performanceService.getThresholds(),
    };
  }
}
