import { Module, Global } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TerminusModule } from '@nestjs/terminus';
import { PrometheusModule } from '@willsoto/nestjs-prometheus';

import { MetricsService } from './metrics.service';
import { HealthService } from './health.service';
import { LoggingService } from './logging.service';
import { TracingService } from './tracing.service';
import { AlertingService } from './alerting.service';
import { PerformanceService } from './performance.service';

import { MetricsController } from './controllers/metrics.controller';
import { HealthController } from './controllers/health.controller';
import { AlertsController } from './controllers/alerts.controller';

import { MetricsInterceptor } from './interceptors/metrics.interceptor';
import { PerformanceInterceptor } from './interceptors/performance.interceptor';
import { AuditInterceptor } from './interceptors/audit.interceptor';

import { DatabaseModule } from '@/core/database';

@Global()
@Module({
  imports: [
    ConfigModule,
    TerminusModule,
    PrometheusModule.register({
      defaultMetrics: {
        enabled: true,
        config: {
          prefix: 'discord_bot_',
        },
      },
      defaultLabels: {
        app: 'discord-bot-energex',
        version: process.env.npm_package_version || '1.0.0',
        environment: process.env.NODE_ENV || 'development',
      },
    }),
    DatabaseModule,
  ],
  providers: [
    MetricsService,
    HealthService,
    LoggingService,
    TracingService,
    AlertingService,
    PerformanceService,
    MetricsInterceptor,
    PerformanceInterceptor,
    AuditInterceptor,
  ],
  controllers: [
    MetricsController,
    HealthController,
    AlertsController,
  ],
  exports: [
    MetricsService,
    HealthService,
    LoggingService,
    TracingService,
    AlertingService,
    PerformanceService,
    MetricsInterceptor,
    PerformanceInterceptor,
    AuditInterceptor,
  ],
})
export class MonitoringModule {}
