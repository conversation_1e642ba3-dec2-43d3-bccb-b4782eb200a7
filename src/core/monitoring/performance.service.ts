import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  timestamp: Date;
  tags?: Record<string, string>;
}

export interface PerformanceThreshold {
  metric: string;
  warning: number;
  critical: number;
  unit: string;
}

@Injectable()
export class PerformanceService {
  private readonly logger = new Logger(PerformanceService.name);
  private readonly metrics = new Map<string, PerformanceMetric[]>();
  private readonly thresholds = new Map<string, PerformanceThreshold>();

  constructor(private readonly configService: ConfigService) {
    this.initializeDefaultThresholds();
  }

  recordMetric(
    name: string,
    value: number,
    unit: string,
    tags?: Record<string, string>,
  ): void {
    const metric: PerformanceMetric = {
      name,
      value,
      unit,
      timestamp: new Date(),
      tags,
    };

    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }

    const metricHistory = this.metrics.get(name)!;
    metricHistory.push(metric);

    // Keep only last 1000 metrics per type
    if (metricHistory.length > 1000) {
      metricHistory.shift();
    }

    // Check thresholds
    this.checkThresholds(metric);

    this.logger.debug(`Performance metric recorded: ${name} = ${value} ${unit}`, {
      metric: name,
      value,
      unit,
      tags,
    });
  }

  recordResponseTime(endpoint: string, duration: number, statusCode?: number): void {
    this.recordMetric('response_time', duration, 'ms', {
      endpoint,
      status_code: statusCode?.toString() || 'unknown',
    });
  }

  recordDatabaseQuery(query: string, duration: number, success: boolean): void {
    this.recordMetric('database_query_time', duration, 'ms', {
      query_type: this.extractQueryType(query),
      success: success.toString(),
    });
  }

  recordMemoryUsage(): void {
    const memUsage = process.memoryUsage();
    
    this.recordMetric('memory_heap_used', memUsage.heapUsed, 'bytes');
    this.recordMetric('memory_heap_total', memUsage.heapTotal, 'bytes');
    this.recordMetric('memory_rss', memUsage.rss, 'bytes');
    this.recordMetric('memory_external', memUsage.external, 'bytes');
  }

  recordCpuUsage(): void {
    const cpuUsage = process.cpuUsage();
    
    this.recordMetric('cpu_user', cpuUsage.user, 'microseconds');
    this.recordMetric('cpu_system', cpuUsage.system, 'microseconds');
  }

  getMetrics(name: string, since?: Date): PerformanceMetric[] {
    const metrics = this.metrics.get(name) || [];
    
    if (since) {
      return metrics.filter((metric: any) => metric.timestamp >= since);
    }
    
    return [...metrics];
  }

  getAverageMetric(name: string, since?: Date): number | null {
    const metrics = this.getMetrics(name, since);
    
    if (metrics.length === 0) {
      return null;
    }
    
    const sum = metrics.reduce((acc, metric) => acc + metric.value, 0);
    return sum / metrics.length;
  }

  getPercentile(name: string, percentile: number, since?: Date): number | null {
    const metrics = this.getMetrics(name, since);
    
    if (metrics.length === 0) {
      return null;
    }
    
    const values = metrics.map((m: any) => m.value).sort((a, b) => a - b);
    const index = Math.ceil((percentile / 100) * values.length) - 1;
    
    return values[Math.max(0, index)];
  }

  setThreshold(metric: string, warning: number, critical: number, unit: string): void {
    this.thresholds.set(metric, { metric, warning, critical, unit });
    this.logger.log(`Performance threshold set for ${metric}: warning=${warning}, critical=${critical} ${unit}`);
  }

  getThresholds(): PerformanceThreshold[] {
    return Array.from(this.thresholds.values());
  }

  clearMetrics(name?: string, olderThan?: Date): void {
    if (name) {
      if (olderThan) {
        const metrics = this.metrics.get(name);
        if (metrics) {
          const filtered = metrics.filter((m: any) => m.timestamp >= olderThan);
          this.metrics.set(name, filtered);
        }
      } else {
        this.metrics.delete(name);
      }
    } else {
      if (olderThan) {
        for (const [metricName, metrics] of this.metrics.entries()) {
          const filtered = metrics.filter((m: any) => m.timestamp >= olderThan);
          this.metrics.set(metricName, filtered);
        }
      } else {
        this.metrics.clear();
      }
    }
  }

  private initializeDefaultThresholds(): void {
    this.setThreshold('response_time', 1000, 5000, 'ms');
    this.setThreshold('database_query_time', 500, 2000, 'ms');
    this.setThreshold('memory_heap_used', 500 * 1024 * 1024, 1024 * 1024 * 1024, 'bytes'); // 500MB warning, 1GB critical
  }

  private checkThresholds(metric: PerformanceMetric): void {
    const threshold = this.thresholds.get(metric.name);
    
    if (!threshold) {
      return;
    }

    if (metric.value >= threshold.critical) {
      this.logger.error(`Performance threshold CRITICAL exceeded: ${metric.name} = ${metric.value} ${metric.unit} (threshold: ${threshold.critical})`, {
        metric: metric.name,
        value: metric.value,
        threshold: threshold.critical,
        unit: metric.unit,
        tags: metric.tags,
      });
    } else if (metric.value >= threshold.warning) {
      this.logger.warn(`Performance threshold WARNING exceeded: ${metric.name} = ${metric.value} ${metric.unit} (threshold: ${threshold.warning})`, {
        metric: metric.name,
        value: metric.value,
        threshold: threshold.warning,
        unit: metric.unit,
        tags: metric.tags,
      });
    }
  }

  private extractQueryType(query: string): string {
    const trimmed = query.trim().toLowerCase();
    
    if (trimmed.startsWith('select')) return 'select';
    if (trimmed.startsWith('insert')) return 'insert';
    if (trimmed.startsWith('update')) return 'update';
    if (trimmed.startsWith('delete')) return 'delete';
    if (trimmed.startsWith('create')) return 'create';
    if (trimmed.startsWith('drop')) return 'drop';
    if (trimmed.startsWith('alter')) return 'alter';
    
    return 'other';
  }
}
