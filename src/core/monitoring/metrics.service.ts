import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
    Counter,
    Gauge,
    Histogram,
    collectDefaultMetrics,
    register
} from 'prom-client';

@Injectable()
export class MetricsService {
  private readonly logger = new Logger(MetricsService.name);
  
  // HTTP Metrics
  public readonly httpRequestsTotal = new Counter({
    name: 'discord_bot_http_requests_total',
    help: 'Total number of HTTP requests',
    labelNames: ['method', 'route', 'status_code', 'organization_id'],
  });

  public readonly httpRequestDuration = new Histogram({
    name: 'discord_bot_http_request_duration_seconds',
    help: 'HTTP request duration in seconds',
    labelNames: ['method', 'route', 'status_code', 'organization_id'],
    buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 5, 10],
  });

  // Discord Bot Metrics
  public readonly discordEventsTotal = new Counter({
    name: 'discord_bot_events_total',
    help: 'Total number of Discord events processed',
    labelNames: ['event_type', 'guild_id', 'organization_id'],
  });

  public readonly discordCommandsTotal = new Counter({
    name: 'discord_bot_commands_total',
    help: 'Total number of Discord commands executed',
    labelNames: ['command', 'guild_id', 'user_id', 'organization_id', 'status'],
  });

  public readonly discordGuildsConnected = new Gauge({
    name: 'discord_bot_guilds_connected',
    help: 'Number of Discord guilds connected',
    labelNames: ['organization_id'],
  });

  public readonly discordUsersActive = new Gauge({
    name: 'discord_bot_users_active',
    help: 'Number of active Discord users',
    labelNames: ['guild_id', 'organization_id'],
  });

  // Database Metrics
  public readonly databaseConnectionsActive = new Gauge({
    name: 'discord_bot_database_connections_active',
    help: 'Number of active database connections',
  });

  public readonly databaseQueriesTotal = new Counter({
    name: 'discord_bot_database_queries_total',
    help: 'Total number of database queries',
    labelNames: ['operation', 'table', 'status'],
  });

  public readonly databaseQueryDuration = new Histogram({
    name: 'discord_bot_database_query_duration_seconds',
    help: 'Database query duration in seconds',
    labelNames: ['operation', 'table'],
    buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 5],
  });

  // Cache Metrics
  public readonly cacheOperationsTotal = new Counter({
    name: 'discord_bot_cache_operations_total',
    help: 'Total number of cache operations',
    labelNames: ['operation', 'cache_name', 'status'],
  });

  public readonly cacheHitRatio = new Gauge({
    name: 'discord_bot_cache_hit_ratio',
    help: 'Cache hit ratio',
    labelNames: ['cache_name'],
  });

  // Business Metrics
  public readonly organizationsTotal = new Gauge({
    name: 'discord_bot_organizations_total',
    help: 'Total number of organizations',
    labelNames: ['tier', 'status'],
  });

  public readonly sessionsActive = new Gauge({
    name: 'discord_bot_sessions_active',
    help: 'Number of active user sessions',
    labelNames: ['organization_id'],
  });

  public readonly apiKeysActive = new Gauge({
    name: 'discord_bot_api_keys_active',
    help: 'Number of active API keys',
    labelNames: ['organization_id'],
  });

  // Error Metrics
  public readonly errorsTotal = new Counter({
    name: 'discord_bot_errors_total',
    help: 'Total number of errors',
    labelNames: ['service', 'error_type', 'severity', 'organization_id'],
  });

  // Performance Metrics
  public readonly memoryUsage = new Gauge({
    name: 'discord_bot_memory_usage_bytes',
    help: 'Memory usage in bytes',
    labelNames: ['type'],
  });

  public readonly cpuUsage = new Gauge({
    name: 'discord_bot_cpu_usage_percent',
    help: 'CPU usage percentage',
  });

  // Queue Metrics
  public readonly queueJobsTotal = new Counter({
    name: 'discord_bot_queue_jobs_total',
    help: 'Total number of queue jobs',
    labelNames: ['queue_name', 'job_type', 'status'],
  });

  public readonly queueJobDuration = new Histogram({
    name: 'discord_bot_queue_job_duration_seconds',
    help: 'Queue job processing duration in seconds',
    labelNames: ['queue_name', 'job_type'],
    buckets: [0.1, 0.5, 1, 5, 10, 30, 60, 300],
  });

  public readonly queueDepth = new Gauge({
    name: 'discord_bot_queue_depth',
    help: 'Number of jobs waiting in queue',
    labelNames: ['queue_name'],
  });

  // Rate Limiting Metrics
  public readonly rateLimitHits = new Counter({
    name: 'discord_bot_rate_limit_hits_total',
    help: 'Total number of rate limit hits',
    labelNames: ['endpoint', 'organization_id', 'user_id'],
  });

  // Security Metrics
  public readonly securityEventsTotal = new Counter({
    name: 'discord_bot_security_events_total',
    help: 'Total number of security events',
    labelNames: ['event_type', 'severity', 'organization_id'],
  });

  public readonly authenticationAttemptsTotal = new Counter({
    name: 'discord_bot_authentication_attempts_total',
    help: 'Total number of authentication attempts',
    labelNames: ['method', 'status', 'organization_id'],
  });

  constructor(private configService: ConfigService) {
    this.initializeMetrics();
  }

  private initializeMetrics() {
    // Collect default Node.js metrics
    collectDefaultMetrics({
      prefix: 'discord_bot_nodejs_',
      register,
    });

    // Start collecting system metrics
    this.collectSystemMetrics();
    
    this.logger.log('Metrics service initialized with Prometheus');
  }

  private collectSystemMetrics() {
    setInterval(() => {
      const memUsage = process.memoryUsage();
      this.memoryUsage.set({ type: 'rss' }, memUsage.rss);
      this.memoryUsage.set({ type: 'heapTotal' }, memUsage.heapTotal);
      this.memoryUsage.set({ type: 'heapUsed' }, memUsage.heapUsed);
      this.memoryUsage.set({ type: 'external' }, memUsage.external);

      const cpuUsage = process.cpuUsage();
      const cpuPercent = (cpuUsage.user + cpuUsage.system) / 1000000; // Convert to seconds
      this.cpuUsage.set(cpuPercent);
    }, 5000); // Collect every 5 seconds
  }

  incrementCounter(name: string, labels: Record<string, string>) {
    switch (name) {
      case 'http_requests_total':
      case 'http_responses_total':
        this.httpRequestsTotal.inc(labels);
        break;
      case 'http_errors_total':
        this.errorsTotal.inc({ service: 'http', error_type: 'http_error', severity: 'medium', organization_id: labels.organization_id || 'unknown' });
        break;
    }
  }

  recordHistogram(name: string, value: number, labels: Record<string, string>) {
    if (name === 'http_request_duration_ms') {
      this.httpRequestDuration.observe(labels, value / 1000);
    }
  }

  incrementHttpRequest(method: string, route: string, statusCode: number, organizationId?: string) {
    this.httpRequestsTotal.inc({ method, route, status_code: statusCode.toString(), organization_id: organizationId || 'unknown' });
  }

  recordHttpDuration(method: string, route: string, statusCode: number, duration: number, organizationId?: string) {
    this.httpRequestDuration.observe({ method, route, status_code: statusCode.toString(), organization_id: organizationId || 'unknown' }, duration);
  }

  incrementDiscordEvent(eventType: string, guildId?: string, organizationId?: string) {
    this.discordEventsTotal.inc({
      event_type: eventType,
      guild_id: guildId || 'unknown',
      organization_id: organizationId || 'unknown',
    });
  }

  incrementDiscordCommand(command: string, guildId: string, userId: string, organizationId: string, status: 'success' | 'error') {
    this.discordCommandsTotal.inc({
      command,
      guild_id: guildId,
      user_id: userId,
      organization_id: organizationId,
      status,
    });
  }

  recordDatabaseQuery(operation: string, table: string, duration: number, status: 'success' | 'error') {
    this.databaseQueriesTotal.inc({ operation, table, status });
    this.databaseQueryDuration.observe({ operation, table }, duration);
  }

  incrementCacheOperation(operation: 'get' | 'set' | 'del', cacheName: string, status: 'hit' | 'miss' | 'success' | 'error') {
    this.cacheOperationsTotal.inc({ operation, cache_name: cacheName, status });
  }

  updateCacheHitRatio(cacheName: string, ratio: number) {
    this.cacheHitRatio.set({ cache_name: cacheName }, ratio);
  }

  incrementError(service: string, errorType: string, severity: 'low' | 'medium' | 'high' | 'critical', organizationId?: string) {
    this.errorsTotal.inc({
      service,
      error_type: errorType,
      severity,
      organization_id: organizationId || 'unknown',
    });
  }

  incrementSecurityEvent(eventType: string, severity: string, organizationId?: string) {
    this.securityEventsTotal.inc({
      event_type: eventType,
      severity,
      organization_id: organizationId || 'unknown',
    });
  }

  incrementRateLimitHit(endpoint: string, organizationId?: string, userId?: string) {
    this.rateLimitHits.inc({
      endpoint,
      organization_id: organizationId || 'unknown',
      user_id: userId || 'unknown',
    });
  }

  // Get metrics for external consumption
  async getMetrics(): Promise<string> {
    return register.metrics();
  }

  // Reset all metrics (useful for testing)
  resetMetrics() {
    register.resetMetrics();
  }
}
