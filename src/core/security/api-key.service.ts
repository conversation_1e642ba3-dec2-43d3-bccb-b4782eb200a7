import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EncryptionService } from './encryption.service';
import { RateLimitingService, RateLimitConfig } from './rate-limiting.service';

export interface ApiKey {
  id: string;
  organizationId: string;
  name: string;
  keyHash: string; // Hashed version of the key
  permissions: string[];
  rateLimit: RateLimitConfig;
  lastUsed?: Date;
  expiresAt?: Date;
  isActive: boolean;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateApiKeyRequest {
  organizationId: string;
  name: string;
  permissions: string[];
  rateLimit?: Partial<RateLimitConfig>;
  expiresAt?: Date;
  createdBy: string;
}

export interface ApiKeyValidationResult {
  isValid: boolean;
  apiKey?: ApiKey;
  error?: string;
}

export interface ApiKeyUsage {
  apiKeyId: string;
  timestamp: Date;
  endpoint: string;
  method: string;
  ipAddress?: string;
  userAgent?: string;
  success: boolean;
  errorMessage?: string;
}

@Injectable()
export class ApiKeyService {
  private readonly logger = new Logger(ApiKeyService.name);
  private readonly apiKeys = new Map<string, ApiKey>(); // In-memory storage for demo
  private readonly apiKeyUsage: ApiKeyUsage[] = []; // Usage tracking

  constructor(
    private readonly configService: ConfigService,
    private readonly encryptionService: EncryptionService,
    private readonly rateLimitingService: RateLimitingService,
  ) {}

  /**
   * Create a new API key
   */
  async createApiKey(request: CreateApiKeyRequest): Promise<{ apiKey: ApiKey; rawKey: string }> {
    try {
      // Generate raw API key
      const rawKey = this.generateRawApiKey();
      const keyHash = await this.encryptionService.hashPassword(rawKey);

      // Create API key object
      const apiKey: ApiKey = {
        id: this.generateApiKeyId(),
        organizationId: request.organizationId,
        name: request.name,
        keyHash,
        permissions: request.permissions,
        rateLimit: {
          windowMs: 60 * 1000, // 1 minute
          maxRequests: 100,
          ...request.rateLimit,
        },
        expiresAt: request.expiresAt,
        isActive: true,
        createdBy: request.createdBy,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Store API key
      this.apiKeys.set(apiKey.id, apiKey);

      this.logger.log(`Created API key ${apiKey.id} for organization ${apiKey.organizationId}`);

      return { apiKey, rawKey };
    } catch (error) {
      this.logger.error('Failed to create API key:', error);
      throw error;
    }
  }

  /**
   * Validate API key
   */
  async validateApiKey(rawKey: string): Promise<ApiKeyValidationResult> {
    try {
      // Extract key ID from raw key
      const keyId = this.extractKeyIdFromRawKey(rawKey);
      if (!keyId) {
        return { isValid: false, error: 'Invalid API key format' };
      }

      // Get API key from storage
      const apiKey = this.apiKeys.get(keyId);
      if (!apiKey) {
        return { isValid: false, error: 'API key not found' };
      }

      // Check if key is active
      if (!apiKey.isActive) {
        return { isValid: false, error: 'API key is inactive' };
      }

      // Check if key has expired
      if (apiKey.expiresAt && new Date() > apiKey.expiresAt) {
        return { isValid: false, error: 'API key has expired' };
      }

      // Verify key hash
      const isValidHash = await this.encryptionService.verifyPassword(rawKey, apiKey.keyHash);
      if (!isValidHash) {
        return { isValid: false, error: 'Invalid API key' };
      }

      // Update last used timestamp
      apiKey.lastUsed = new Date();
      apiKey.updatedAt = new Date();

      return { isValid: true, apiKey };
    } catch (error) {
      this.logger.error('Failed to validate API key:', error);
      return { isValid: false, error: 'Validation error' };
    }
  }

  /**
   * Check API key permissions
   */
  async hasPermission(apiKey: ApiKey, permission: string): Promise<boolean> {
    return apiKey.permissions.includes(permission) || apiKey.permissions.includes('*');
  }

  /**
   * Check rate limit for API key
   */
  async checkRateLimit(apiKey: ApiKey, context: {
    endpoint?: string;
    method?: string;
    ipAddress?: string;
  } = {}): Promise<{ allowed: boolean; remaining: number; resetTime: Date }> {
    const rateLimitResult = await this.rateLimitingService.checkRateLimit(
      {
        userId: apiKey.id,
        organizationId: apiKey.organizationId,
        ...context,
      },
      apiKey.rateLimit,
    );

    return {
      allowed: rateLimitResult.allowed,
      remaining: rateLimitResult.remaining,
      resetTime: rateLimitResult.resetTime,
    };
  }

  /**
   * Log API key usage
   */
  async logUsage(
    apiKeyId: string,
    endpoint: string,
    method: string,
    success: boolean,
    context: {
      ipAddress?: string;
      userAgent?: string;
      errorMessage?: string;
    } = {},
  ): Promise<void> {
    const usage: ApiKeyUsage = {
      apiKeyId,
      timestamp: new Date(),
      endpoint,
      method,
      success,
      ...context,
    };

    this.apiKeyUsage.push(usage);

    // Keep only last 10000 usage records
    if (this.apiKeyUsage.length > 10000) {
      this.apiKeyUsage.shift();
    }
  }

  /**
   * Get API key by ID
   */
  async getApiKey(id: string): Promise<ApiKey | null> {
    return this.apiKeys.get(id) || null;
  }

  /**
   * Get API keys for organization
   */
  async getApiKeysForOrganization(organizationId: string): Promise<ApiKey[]> {
    return Array.from(this.apiKeys.values()).filter(
      apiKey => apiKey.organizationId === organizationId,
    );
  }

  /**
   * Update API key
   */
  async updateApiKey(
    id: string,
    updates: Partial<Pick<ApiKey, 'name' | 'permissions' | 'rateLimit' | 'expiresAt' | 'isActive'>>,
  ): Promise<ApiKey | null> {
    const apiKey = this.apiKeys.get(id);
    if (!apiKey) {
      return null;
    }

    Object.assign(apiKey, updates, { updatedAt: new Date() });
    this.apiKeys.set(id, apiKey);

    this.logger.log(`Updated API key ${id}`);
    return apiKey;
  }

  /**
   * Deactivate API key
   */
  async deactivateApiKey(id: string): Promise<boolean> {
    const apiKey = this.apiKeys.get(id);
    if (!apiKey) {
      return false;
    }

    apiKey.isActive = false;
    apiKey.updatedAt = new Date();
    this.apiKeys.set(id, apiKey);

    this.logger.log(`Deactivated API key ${id}`);
    return true;
  }

  /**
   * Delete API key
   */
  async deleteApiKey(id: string): Promise<boolean> {
    const deleted = this.apiKeys.delete(id);
    if (deleted) {
      this.logger.log(`Deleted API key ${id}`);
    }
    return deleted;
  }

  /**
   * Get API key usage statistics
   */
  async getUsageStatistics(apiKeyId: string, days: number = 30): Promise<{
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    topEndpoints: Array<{ endpoint: string; count: number }>;
    dailyUsage: Array<{ date: string; requests: number }>;
  }> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);

    const relevantUsage = this.apiKeyUsage.filter(
      usage => usage.apiKeyId === apiKeyId && usage.timestamp >= cutoffDate,
    );

    const totalRequests = relevantUsage.length;
    const successfulRequests = relevantUsage.filter((usage: any) => usage.success).length;
    const failedRequests = totalRequests - successfulRequests;

    // Top endpoints
    const endpointCounts = new Map<string, number>();
    relevantUsage.forEach(usage => {
      const count = endpointCounts.get(usage.endpoint) || 0;
      endpointCounts.set(usage.endpoint, count + 1);
    });
    const topEndpoints = Array.from(endpointCounts.entries())
      .map(([endpoint, count]) => ({ endpoint, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Daily usage
    const dailyCounts = new Map<string, number>();
    relevantUsage.forEach(usage => {
      const date = usage.timestamp.toISOString().split('T')[0];
      const count = dailyCounts.get(date) || 0;
      dailyCounts.set(date, count + 1);
    });
    const dailyUsage = Array.from(dailyCounts.entries())
      .map(([date, requests]) => ({ date, requests }))
      .sort((a, b) => a.date.localeCompare(b.date));

    return {
      totalRequests,
      successfulRequests,
      failedRequests,
      topEndpoints,
      dailyUsage,
    };
  }

  /**
   * Generate raw API key
   */
  private generateRawApiKey(): string {
    const keyId = this.generateApiKeyId();
    const secret = this.encryptionService.generateSecureToken(32);
    return `ak_${keyId}_${secret}`;
  }

  /**
   * Generate API key ID
   */
  private generateApiKeyId(): string {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Extract key ID from raw key
   */
  private extractKeyIdFromRawKey(rawKey: string): string | null {
    const parts = rawKey.split('_');
    if (parts.length >= 3 && parts[0] === 'ak') {
      return `${parts[1]}_${parts[2]}`;
    }
    return null;
  }

  /**
   * Create default permissions for different API key types
   */
  static createReadOnlyPermissions(): string[] {
    return [
      'read:organizations',
      'read:users',
      'read:guilds',
      'read:metrics',
    ];
  }

  static createFullAccessPermissions(): string[] {
    return ['*'];
  }

  static createLimitedPermissions(): string[] {
    return [
      'read:organizations',
      'write:organizations',
      'read:users',
      'read:guilds',
      'write:guilds',
    ];
  }
}
