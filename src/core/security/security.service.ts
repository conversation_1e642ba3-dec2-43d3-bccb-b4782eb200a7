import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EncryptionService } from './encryption.service';
import { RbacService } from './rbac.service';
import { SessionService } from './session.service';
import { UserService } from './user.service';

export interface SecurityContext {
  userId?: string;
  organizationId?: string;
  roles: string[];
  permissions: string[];
  sessionId?: string;
  ipAddress?: string;
  userAgent?: string;
  timestamp: Date;
}

export interface SecurityEvent {
  id: string;
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  userId?: string;
  organizationId?: string;
  ipAddress?: string;
  userAgent?: string;
  details: Record<string, any>;
  timestamp: Date;
}

export interface SecurityPolicy {
  id: string;
  name: string;
  description: string;
  rules: SecurityRule[];
  isActive: boolean;
  organizationId?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface SecurityRule {
  id: string;
  type: 'access' | 'rate_limit' | 'validation' | 'encryption';
  condition: string;
  action: 'allow' | 'deny' | 'log' | 'alert';
  parameters: Record<string, any>;
}

@Injectable()
export class SecurityService {
  private readonly logger = new Logger(SecurityService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly encryptionService: EncryptionService,
    private readonly sessionService: SessionService,
    private readonly rbacService: RbacService,
    private readonly userService: UserService,
  ) {}

  /**
   * Create a security context for a user
   */
  async createSecurityContext(
    userId: string,
    organizationId?: string,
    sessionId?: string,
    ipAddress?: string,
    userAgent?: string,
  ): Promise<SecurityContext> {
    try {
      // Get user roles and permissions
      const roles = await this.rbacService.getUserRoles(userId, organizationId);
      const permissions = await this.rbacService.getUserPermissionObjects(userId, organizationId);

      return {
        userId,
        ...(organizationId && { organizationId }),
        roles: roles.map((role: any) => role.name),
        permissions: permissions.map((permission: any) => permission.name),
        ...(sessionId && { sessionId }),
        ...(ipAddress && { ipAddress }),
        ...(userAgent && { userAgent }),
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error(`Failed to create security context for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Validate security context
   */
  async validateSecurityContext(context: SecurityContext): Promise<boolean> {
    try {
      // Check if session is valid
      if (context.sessionId) {
        const isValidSession = await this.userService.validateSession(context.sessionId);
        if (!isValidSession) {
          return false;
        }
      }

      // Check if user still has required roles/permissions
      if (context.userId && context.organizationId) {
        const currentRoles = await this.rbacService.getUserRoles(context.userId, context.organizationId);
        const currentRoleNames = currentRoles.map((role: any) => role.name);
        
        // Check if user still has at least one of the original roles
        const hasValidRole = context.roles.some(role => currentRoleNames.includes(role));
        if (!hasValidRole) {
          return false;
        }
      }

      return true;
    } catch (error) {
      this.logger.error('Failed to validate security context:', error);
      return false;
    }
  }

  /**
   * Check if user has required permission
   */
  async hasPermission(
    userId: string,
    permission: string,
    organizationId?: string,
    resourceId?: string,
  ): Promise<boolean> {
    try {
      return await this.rbacService.hasPermission(userId, permission, organizationId, resourceId);
    } catch (error) {
      this.logger.error(`Failed to check permission ${permission} for user ${userId}:`, error);
      return false;
    }
  }

  /**
   * Check if user has required role
   */
  async hasRole(
    userId: string,
    role: string,
    organizationId?: string,
  ): Promise<boolean> {
    try {
      return await this.rbacService.hasRole(userId, role, organizationId);
    } catch (error) {
      this.logger.error(`Failed to check role ${role} for user ${userId}:`, error);
      return false;
    }
  }

  /**
   * Encrypt sensitive data
   */
  async encryptSensitiveData(data: string, context?: string): Promise<string> {
    try {
      return await this.encryptionService.encrypt(data, context);
    } catch (error) {
      this.logger.error('Failed to encrypt sensitive data:', error);
      throw error;
    }
  }

  /**
   * Decrypt sensitive data
   */
  async decryptSensitiveData(encryptedData: string, context?: string): Promise<string> {
    try {
      return await this.encryptionService.decrypt(encryptedData, context);
    } catch (error) {
      this.logger.error('Failed to decrypt sensitive data:', error);
      throw error;
    }
  }

  /**
   * Generate secure token
   */
  generateSecureToken(length: number = 32): string {
    try {
      return this.encryptionService.generateSecureToken(length);
    } catch (error) {
      this.logger.error('Failed to generate secure token:', error);
      throw error;
    }
  }

  /**
   * Hash password
   */
  async hashPassword(password: string): Promise<string> {
    try {
      return await this.encryptionService.hashPassword(password);
    } catch (error) {
      this.logger.error('Failed to hash password:', error);
      throw error;
    }
  }

  /**
   * Verify password
   */
  async verifyPassword(password: string, hash: string): Promise<boolean> {
    try {
      return await this.encryptionService.verifyPassword(password, hash);
    } catch (error) {
      this.logger.error('Failed to verify password:', error);
      return false;
    }
  }

  /**
   * Sanitize input data
   */
  sanitizeInput(input: any): any {
    if (typeof input === 'string') {
      // Remove potentially dangerous characters
      return input
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+\s*=/gi, '')
        .trim();
    }

    if (Array.isArray(input)) {
      return input.map((item: any) => this.sanitizeInput(item));
    }

    if (typeof input === 'object' && input !== null) {
      const sanitized: any = {};
      for (const [key, value] of Object.entries(input)) {
        sanitized[this.sanitizeInput(key)] = this.sanitizeInput(value);
      }
      return sanitized;
    }

    return input;
  }

  /**
   * Validate input against security rules
   */
  validateInput(input: any, rules: SecurityRule[]): boolean {
    try {
      for (const rule of rules) {
        if (rule.type === 'validation') {
          // Apply validation rule
          const isValid = this.applyValidationRule(input, rule);
          if (!isValid && rule.action === 'deny') {
            return false;
          }
        }
      }
      return true;
    } catch (error) {
      this.logger.error('Failed to validate input:', error);
      return false;
    }
  }

  /**
   * Apply validation rule to input
   */
  private applyValidationRule(input: any, rule: SecurityRule): boolean {
    const { condition, parameters } = rule;

    switch (condition) {
      case 'max_length':
        return typeof input === 'string' && input.length <= (parameters.maxLength || 1000);
      case 'min_length':
        return typeof input === 'string' && input.length >= (parameters.minLength || 0);
      case 'pattern':
        return typeof input === 'string' && new RegExp(parameters.pattern).test(input);
      case 'no_script_tags':
        return typeof input === 'string' && !/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi.test(input);
      case 'no_sql_injection':
        return typeof input === 'string' && !/(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/gi.test(input);
      default:
        return true;
    }
  }

  /**
   * Get security configuration
   */
  getSecurityConfig(): Record<string, any> {
    return {
      jwtSecret: this.configService.get<string>('JWT_SECRET'),
      jwtExpiresIn: this.configService.get<string>('JWT_EXPIRES_IN', '24h'),
      sessionTimeout: this.configService.get<number>('SESSION_TIMEOUT', 86400000),
      rateLimitTtl: this.configService.get<number>('RATE_LIMIT_TTL', 60),
      rateLimitMax: this.configService.get<number>('RATE_LIMIT_MAX', 100),
      encryptionEnabled: this.configService.get<boolean>('ENCRYPTION_ENABLED', true),
      auditLoggingEnabled: this.configService.get<boolean>('AUDIT_LOGGING_ENABLED', true),
    };
  }
}
