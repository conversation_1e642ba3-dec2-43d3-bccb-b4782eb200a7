import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { Request } from 'express';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { AuditService } from '../audit.service';
import { RbacService } from '../rbac.service';
import { SecurityEventService } from '../security-event.service';
import { UserService } from '../user.service';

export interface JwtPayload {
  sub: string; // User ID
  email?: string;
  username?: string;
  organizationId?: string;
  roles?: string[];
  permissions?: string[];
  iat?: number;
  exp?: number;
  iss?: string;
  aud?: string;
  sessionId?: string;
  tokenType?: 'access' | 'refresh';
}

export interface ValidatedUser {
  id: string;
  email?: string;
  username?: string;
  organizationId?: string;
  roles: string[];
  permissions: string[];
  isActive: boolean;
  lastLoginAt?: Date;
  sessionId?: string;
}

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy, 'jwt') {
  private readonly logger = new Logger(JwtStrategy.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly userService: UserService,
    private readonly rbacService: RbacService,
    private readonly securityEventService: SecurityEventService,
    private readonly auditService: AuditService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromExtractors([
        JwtStrategy.extractJwtFromCookie,
        JwtStrategy.extractJwtFromAuthHeaderAsBearerToken,
        JwtStrategy.extractJwtFromQuery,
      ]),
      ignoreExpiration: true, // Allow Discord tokens (non-JWT)
      secretOrKey: configService.get<string>('JWT_SECRET') || 'fallback',
      passReqToCallback: true,
    });
  }

  /**
   * Validate JWT payload or Discord token and return user
   */
  async validate(request: Request, payload?: JwtPayload): Promise<ValidatedUser> {
    try {
      // If no payload, try Discord token validation
      if (!payload) {
        return await this.validateDiscordToken(request);
      }

      this.logger.debug(`Validating JWT for user: ${payload.sub}`);
      if (!payload.sub) throw new UnauthorizedException('Invalid token payload');

      const user = await this.userService.findById(payload.sub);
      if (!user || !user.isActive) throw new UnauthorizedException('User not found or inactive');

      const roles = await this.rbacService.getUserRoles(payload.sub, payload.organizationId);
      const permissions = await this.rbacService.getUserPermissionObjects(payload.sub, payload.organizationId);

      await this.userService.updateLastActivity(payload.sub);

      return {
        id: payload.sub,
        email: user.email || payload.email,
        username: user.username || payload.username,
        organizationId: payload.organizationId,
        roles: roles.map((role: any) => role.name),
        permissions: permissions.map((permission: any) => permission.name),
        isActive: user.isActive,
        lastLoginAt: user.lastActivityAt,
        sessionId: payload.sessionId,
      };
    } catch (error) {
      if (error instanceof UnauthorizedException) throw error;
      this.logger.error('Authentication error:', error);
      throw new UnauthorizedException('Authentication failed');
    }
  }

  /**
   * Validate Discord token by calling Discord API
   */
  private async validateDiscordToken(request: Request): Promise<ValidatedUser> {
    const token = request.get('Authorization')?.replace('Bearer ', '');
    if (!token) throw new UnauthorizedException('No token provided');

    const response = await fetch('https://discord.com/api/v10/users/@me', {
      headers: { 'Authorization': `Bearer ${token}` },
    });

    if (!response.ok) throw new UnauthorizedException('Invalid Discord token');
    const discordUser = await response.json();

    let user = await this.userService.findByDiscordId(discordUser.id);
    if (!user) {
      user = await this.userService.upsertUser({
        discordId: discordUser.id,
        username: discordUser.username,
        email: discordUser.email,
      });
    }

    if (!user.isActive) throw new UnauthorizedException('Account is inactive');

    const roles = await this.rbacService.getUserRoles(user.id.toString());
    const permissions = await this.rbacService.getUserPermissionObjects(user.id.toString());

    await this.userService.updateLastActivity(user.id.toString());

    return {
      id: user.id.toString(),
      username: discordUser.username,
      email: discordUser.email,
      roles: roles.map((r: any) => r.name),
      permissions: permissions.map((p: any) => p.name),
      isActive: user.isActive,
    };
  }

  /**
   * Extract JWT from cookie
   */
  private static extractJwtFromCookie(request: Request): string | null {
    if (request.cookies && request.cookies['access_token']) {
      return request.cookies['access_token'];
    }
    return null;
  }

  /**
   * Extract JWT from Authorization header as Bearer token
   */
  private static extractJwtFromAuthHeaderAsBearerToken(request: Request): string | null {
    return ExtractJwt.fromAuthHeaderAsBearerToken()(request);
  }

  /**
   * Extract JWT from query parameter
   */
  private static extractJwtFromQuery(request: Request): string | null {
    if (request.query && request.query.token && typeof request.query.token === 'string') {
      return request.query.token;
    }
    return null;
  }

  /**
   * Get client IP address
   */
  private getClientIp(request: Request): string {
    return (
      request.get('X-Forwarded-For')?.split(',')[0]?.trim() ||
      request.get('X-Real-IP') ||
      request.connection.remoteAddress ||
      request.socket.remoteAddress ||
      'unknown'
    );
  }

  /**
   * Log authentication failure
   */
  private async logAuthenticationFailure(
    request: Request,
    reason: string,
    userId?: string,
  ): Promise<void> {
    try {
      const ipAddress = this.getClientIp(request);
      const userAgent = request.get('User-Agent');

      // Log security event
      await this.securityEventService.recordFailedLogin(
        userId || 'unknown',
        ipAddress,
        userAgent,
        {
          reason,
          endpoint: request.url,
          method: request.method,
          authMethod: 'jwt',
        },
      );

      // Log audit event
      await this.auditService.logEvent(
        'authentication',
        'jwt_validation_failed',
        'user',
        {
          reason,
          endpoint: request.url,
          method: request.method,
          authMethod: 'jwt',
        },
        {
          userId: userId || 'unknown',
          ipAddress,
          userAgent,
        },
        false,
        reason,
      );
    } catch (error) {
      this.logger.error('Failed to log authentication failure:', error);
    }
  }
}

