import {
    CanActivate,
    ExecutionContext,
    Injectable,
    Logger,
    SetMetadata,
    UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Reflector } from '@nestjs/core';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';
import { AuditService } from '../audit.service';
import { SecurityEventService } from '../security-event.service';

export interface JwtPayload {
  sub: string; // User ID
  email?: string;
  username?: string;
  organizationId?: string;
  roles?: string[];
  permissions?: string[];
  iat?: number;
  exp?: number;
  iss?: string;
  aud?: string;
}

export interface AuthenticatedRequest extends Request {
  user: JwtPayload;
}

@Injectable()
export class JwtAuthGuard implements CanActivate {
  private readonly logger = new Logger(JwtAuthGuard.name);

  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly reflector: Reflector,
    private readonly securityEventService: SecurityEventService,
    private readonly auditService: AuditService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<AuthenticatedRequest>();
    const response = context.switchToHttp().getResponse();

    try {
      // Check if route is marked as public
      const isPublic = this.reflector.getAllAndOverride<boolean>('isPublic', [
        context.getHandler(),
        context.getClass(),
      ]);

      if (isPublic) {
        return true;
      }

      // Extract JWT token from request
      const token = this.extractTokenFromRequest(request);
      if (!token) {
        await this.logUnauthorizedAccess(request, 'No JWT token provided');
        throw new UnauthorizedException('Access token is required');
      }

      // Verify and decode JWT token
      const payload = await this.verifyToken(token);
      if (!payload) {
        await this.logUnauthorizedAccess(request, 'Invalid JWT token', payload?.sub);
        throw new UnauthorizedException('Invalid access token');
      }

      // Check if token has expired
      if (payload.exp && Date.now() >= payload.exp * 1000) {
        await this.logUnauthorizedAccess(request, 'Expired JWT token', payload.sub);
        throw new UnauthorizedException('Access token has expired');
      }

      // Attach user to request
      request.user = payload;

      // Log successful authentication
      await this.auditService.logEvent(
        'authentication',
        'token_validation',
        'user',
        {
          endpoint: request.url,
          method: request.method,
          userAgent: request.get('User-Agent'),
        },
        {
          userId: payload.sub,
          ipAddress: this.getClientIp(request),
          userAgent: request.get('User-Agent'),
        },
        true,
      );

      return true;
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }

      this.logger.error('JWT authentication error:', error);
      await this.logUnauthorizedAccess(request, 'JWT authentication error');
      throw new UnauthorizedException('Authentication failed');
    }
  }

  /**
   * Extract JWT token from request
   */
  private extractTokenFromRequest(request: Request): string | null {
    // Check Authorization header (Bearer token)
    const authHeader = request.get('Authorization');
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    // Check query parameter
    const queryToken = request.query.token as string;
    if (queryToken) {
      return queryToken;
    }

    // Check cookies
    const cookieToken = request.cookies?.['access_token'];
    if (cookieToken) {
      return cookieToken;
    }

    return null;
  }

  /**
   * Verify JWT token
   */
  private async verifyToken(token: string): Promise<JwtPayload | null> {
    try {
      const secret = this.configService.get<string>('JWT_SECRET');
      if (!secret) {
        this.logger.error('JWT_SECRET not configured');
        return null;
      }

      const payload = this.jwtService.verify<JwtPayload>(token, {
        secret,
        issuer: this.configService.get<string>('JWT_ISSUER'),
        audience: this.configService.get<string>('JWT_AUDIENCE'),
      });

      return payload;
    } catch (error) {
      this.logger.debug('JWT verification failed:', error.message);
      return null;
    }
  }

  /**
   * Get client IP address
   */
  private getClientIp(request: Request): string {
    return (
      request.get('X-Forwarded-For')?.split(',')[0]?.trim() ||
      request.get('X-Real-IP') ||
      request.connection.remoteAddress ||
      request.socket.remoteAddress ||
      'unknown'
    );
  }

  /**
   * Log unauthorized access attempt
   */
  private async logUnauthorizedAccess(
    request: Request,
    reason: string,
    userId?: string,
  ): Promise<void> {
    try {
      const ipAddress = this.getClientIp(request);
      const userAgent = request.get('User-Agent');
      const endpoint = request.url;
      const method = request.method;

      // Log security event
      await this.securityEventService.recordUnauthorizedAccess(
        endpoint,
        {
          reason,
          method,
          userAgent,
          headers: this.sanitizeHeaders(request.headers),
        },
        {
          userId,
          ipAddress,
          userAgent,
          endpoint,
        },
      );

      // Log audit event
      await this.auditService.logEvent(
        'authentication',
        'token_validation_failed',
        'user',
        {
          reason,
          endpoint,
          method,
          userAgent,
        },
        {
          userId: userId || 'unknown',
          ipAddress,
          userAgent,
        },
        false,
        reason,
      );
    } catch (error) {
      this.logger.error('Failed to log unauthorized access:', error);
    }
  }

  /**
   * Sanitize request headers for logging
   */
  private sanitizeHeaders(headers: Record<string, any>): Record<string, any> {
    const sanitized = { ...headers };
    
    // Remove sensitive headers
    delete sanitized.authorization;
    delete sanitized.cookie;
    delete sanitized['x-api-key'];
    
    return sanitized;
  }
}

/**
 * Decorator to mark routes as public (skip JWT authentication)
 */
export const Public = () => SetMetadata('isPublic', true);
