import {
    CanActivate,
    ExecutionContext,
    ForbiddenException,
    Injectable,
    Logger,
    SetMetadata,
    UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Request } from 'express';
import { ApiKey, ApiKeyService } from '../api-key.service';
import { AuditService } from '../audit.service';
import { SecurityEventService } from '../security-event.service';

export interface ApiKeyRequest extends Request {
  apiKey: ApiKey;
}

export interface ApiKeyRequirement {
  permissions?: string[];
  required?: boolean;
}

@Injectable()
export class ApiKeyGuard implements CanActivate {
  private readonly logger = new Logger(ApiKeyGuard.name);

  constructor(
    private readonly reflector: Reflector,
    private readonly apiKeyService: ApiKeyService,
    private readonly securityEventService: SecurityEventService,
    private readonly auditService: AuditService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<ApiKeyRequest>();

    try {
      // Check if route is marked as public
      const isPublic = this.reflector.getAllAndOverride<boolean>('isPublic', [
        context.getHandler(),
        context.getClass(),
      ]);

      if (isPublic) {
        return true;
      }

      // Get API key requirements from decorators
      const apiKeyRequirement = this.reflector.getAllAndOverride<ApiKeyRequirement>('api_key_required', [
        context.getHandler(),
        context.getClass(),
      ]);

      // If no API key requirement is specified, skip this guard
      if (!apiKeyRequirement) {
        return true;
      }

      // Extract API key from request
      const rawApiKey = this.extractApiKeyFromRequest(request);
      if (!rawApiKey) {
        if (apiKeyRequirement.required !== false) {
          await this.logUnauthorizedAccess(request, 'No API key provided');
          throw new UnauthorizedException('API key is required');
        }
        return true;
      }

      // Validate API key
      const validationResult = await this.apiKeyService.validateApiKey(rawApiKey);
      if (!validationResult.isValid || !validationResult.apiKey) {
        await this.logUnauthorizedAccess(request, validationResult.error || 'Invalid API key');
        throw new UnauthorizedException('Invalid API key');
      }

      const apiKey = validationResult.apiKey;

      // Check rate limits
      const rateLimitResult = await this.apiKeyService.checkRateLimit(apiKey, {
        endpoint: request.url,
        method: request.method,
        ipAddress: this.getClientIp(request),
      });

      if (!rateLimitResult.allowed) {
        await this.logRateLimitExceeded(request, apiKey);
        throw new UnauthorizedException('API key rate limit exceeded');
      }

      // Check required permissions
      if (apiKeyRequirement.permissions && apiKeyRequirement.permissions.length > 0) {
        const hasPermissions = await this.checkPermissions(apiKey, apiKeyRequirement.permissions);
        if (!hasPermissions) {
          await this.logInsufficientPermissions(request, apiKey, apiKeyRequirement.permissions);
          throw new ForbiddenException('API key has insufficient permissions');
        }
      }

      // Attach API key to request
      request.apiKey = apiKey;

      // Log successful API key usage
      await this.apiKeyService.logUsage(
        apiKey.id,
        request.url,
        request.method,
        true,
        {
          ipAddress: this.getClientIp(request),
          userAgent: request.get('User-Agent'),
        },
      );

      // Log audit event
      await this.auditService.logEvent(
        'authentication',
        'api_key_validation',
        'api_key',
        {
          apiKeyName: apiKey.name,
          organizationId: apiKey.organizationId,
          endpoint: request.url,
          method: request.method,
          permissions: apiKey.permissions,
        },
        {
          userId: apiKey.id,
          organizationId: apiKey.organizationId,
          ipAddress: this.getClientIp(request),
          userAgent: request.get('User-Agent'),
        },
        true,
      );

      return true;
    } catch (error) {
      if (error instanceof UnauthorizedException || error instanceof ForbiddenException) {
        throw error;
      }

      this.logger.error('API key authentication error:', error);
      await this.logUnauthorizedAccess(request, 'API key authentication error');
      throw new UnauthorizedException('API key authentication failed');
    }
  }

  /**
   * Extract API key from request
   */
  private extractApiKeyFromRequest(request: Request): string | null {
    // Check X-API-Key header
    const apiKeyHeader = request.get('X-API-Key');
    if (apiKeyHeader) {
      return apiKeyHeader;
    }

    // Check Authorization header (API key format)
    const authHeader = request.get('Authorization');
    if (authHeader && authHeader.startsWith('ApiKey ')) {
      return authHeader.substring(7);
    }

    // Check query parameter
    const queryApiKey = request.query.api_key as string;
    if (queryApiKey) {
      return queryApiKey;
    }

    return null;
  }

  /**
   * Check if API key has required permissions
   */
  private async checkPermissions(apiKey: ApiKey, requiredPermissions: string[]): Promise<boolean> {
    try {
      for (const permission of requiredPermissions) {
        const hasPermission = await this.apiKeyService.hasPermission(apiKey, permission);
        if (!hasPermission) {
          return false;
        }
      }
      return true;
    } catch (error) {
      this.logger.error('Error checking API key permissions:', error);
      return false;
    }
  }

  /**
   * Get client IP address
   */
  private getClientIp(request: Request): string {
    return (
      request.get('X-Forwarded-For')?.split(',')[0]?.trim() ||
      request.get('X-Real-IP') ||
      request.connection.remoteAddress ||
      request.socket.remoteAddress ||
      'unknown'
    );
  }

  /**
   * Log unauthorized access attempt
   */
  private async logUnauthorizedAccess(
    request: Request,
    reason: string,
  ): Promise<void> {
    try {
      const ipAddress = this.getClientIp(request);
      const userAgent = request.get('User-Agent');
      const endpoint = request.url;
      const method = request.method;

      // Log security event
      await this.securityEventService.recordUnauthorizedAccess(
        endpoint,
        {
          reason,
          method,
          userAgent,
          authType: 'api_key',
        },
        {
          ipAddress,
          userAgent,
          endpoint,
        },
      );

      // Log audit event
      await this.auditService.logEvent(
        'authentication',
        'api_key_validation_failed',
        'api_key',
        {
          reason,
          endpoint,
          method,
          userAgent,
        },
        {
          userId: 'unknown',
          ipAddress,
          userAgent,
        },
        false,
        reason,
      );
    } catch (error) {
      this.logger.error('Failed to log unauthorized access:', error);
    }
  }

  /**
   * Log rate limit exceeded
   */
  private async logRateLimitExceeded(
    request: Request,
    apiKey: ApiKey,
  ): Promise<void> {
    try {
      const ipAddress = this.getClientIp(request);
      const userAgent = request.get('User-Agent');

      // Log security event
      await this.securityEventService.recordRateLimitExceeded(
        ipAddress,
        request.url,
        {
          apiKeyId: apiKey.id,
          apiKeyName: apiKey.name,
          organizationId: apiKey.organizationId,
          method: request.method,
          rateLimit: apiKey.rateLimit,
        },
        {
          userAgent,
        },
      );

      // Log API key usage (failed)
      await this.apiKeyService.logUsage(
        apiKey.id,
        request.url,
        request.method,
        false,
        {
          ipAddress,
          userAgent,
          errorMessage: 'Rate limit exceeded',
        },
      );
    } catch (error) {
      this.logger.error('Failed to log rate limit exceeded:', error);
    }
  }

  /**
   * Log insufficient permissions
   */
  private async logInsufficientPermissions(
    request: Request,
    apiKey: ApiKey,
    requiredPermissions: string[],
  ): Promise<void> {
    try {
      const ipAddress = this.getClientIp(request);
      const userAgent = request.get('User-Agent');

      // Log security event
      await this.securityEventService.recordUnauthorizedAccess(
        request.url,
        {
          reason: 'Insufficient API key permissions',
          method: request.method,
          apiKeyId: apiKey.id,
          apiKeyName: apiKey.name,
          apiKeyPermissions: apiKey.permissions,
          requiredPermissions,
          userAgent,
        },
        {
          organizationId: apiKey.organizationId,
          ipAddress,
          userAgent,
          endpoint: request.url,
        },
      );

      // Log API key usage (failed)
      await this.apiKeyService.logUsage(
        apiKey.id,
        request.url,
        request.method,
        false,
        {
          ipAddress,
          userAgent,
          errorMessage: 'Insufficient permissions',
        },
      );

      // Log audit event
      await this.auditService.logEvent(
        'authorization',
        'api_key_permission_check',
        'api_key',
        {
          apiKeyName: apiKey.name,
          apiKeyPermissions: apiKey.permissions,
          requiredPermissions,
          organizationId: apiKey.organizationId,
        },
        {
          userId: apiKey.id,
          organizationId: apiKey.organizationId,
          ipAddress,
          userAgent,
        },
        false,
        'Insufficient permissions',
      );
    } catch (error) {
      this.logger.error('Failed to log insufficient permissions:', error);
    }
  }
}

/**
 * Decorator to require API key with optional permissions
 */
export const RequireApiKey = (permissions?: string[], required: boolean = true) =>
  SetMetadata('api_key_required', { permissions, required });
