import {
    CanActivate,
    ExecutionContext,
    ForbiddenException,
    Injectable,
    Logger,
    SetMetadata,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Request } from 'express';
import { AuditService } from '../audit.service';
import { RbacService } from '../rbac.service';
import { SecurityEventService } from '../security-event.service';
import { AuthenticatedRequest, JwtPayload } from './jwt-auth.guard';

export interface RbacContext {
  user: JwtPayload;
  requiredRoles?: string[];
  requiredPermissions?: string[];
  organizationRequired?: boolean;
  resourceId?: string;
  endpoint: string;
  method: string;
}

@Injectable()
export class RbacGuard implements CanActivate {
  private readonly logger = new Logger(RbacGuard.name);

  constructor(
    private readonly reflector: Reflector,
    private readonly rbacService: RbacService,
    private readonly securityEventService: SecurityEventService,
    private readonly auditService: AuditService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<AuthenticatedRequest>();

    try {
      // Skip RBAC check if route is public
      const isPublic = this.reflector.getAllAndOverride<boolean>('isPublic', [
        context.getHandler(),
        context.getClass(),
      ]);

      if (isPublic) {
        return true;
      }

      // Check if user is authenticated
      if (!request.user) {
        this.logger.warn('RBAC check attempted without authenticated user');
        return false;
      }

      // Get RBAC requirements from decorators
      const requiredRoles = this.reflector.getAllAndOverride<string[]>('roles', [
        context.getHandler(),
        context.getClass(),
      ]);

      const requiredPermissions = this.reflector.getAllAndOverride<string[]>('permissions', [
        context.getHandler(),
        context.getClass(),
      ]);

      const organizationRequired = this.reflector.getAllAndOverride<boolean>('organization_required', [
        context.getHandler(),
        context.getClass(),
      ]);

      // If no RBAC requirements are specified, allow access
      if (!requiredRoles && !requiredPermissions && !organizationRequired) {
        return true;
      }

      const rbacContext: RbacContext = {
        user: request.user,
        requiredRoles,
        requiredPermissions,
        organizationRequired,
        resourceId: this.extractResourceId(request),
        endpoint: request.url,
        method: request.method,
      };

      // Perform RBAC checks
      const accessGranted = await this.checkAccess(rbacContext);

      if (!accessGranted) {
        await this.logAccessDenied(request, rbacContext);
        throw new ForbiddenException('Insufficient permissions');
      }

      // Log successful authorization
      await this.auditService.logEvent(
        'authorization',
        'permission_check',
        rbacContext.endpoint,
        {
          requiredRoles,
          requiredPermissions,
          organizationRequired,
          resourceId: rbacContext.resourceId,
        },
        {
          userId: request.user.sub,
          organizationId: request.user.organizationId,
          resourceId: rbacContext.resourceId,
          ipAddress: this.getClientIp(request),
          userAgent: request.get('User-Agent'),
        },
        true,
      );

      return true;
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }

      this.logger.error('RBAC check error:', error);
      await this.logAccessDenied(request, {
        user: request.user,
        endpoint: request.url,
        method: request.method,
      });
      throw new ForbiddenException('Authorization failed');
    }
  }

  /**
   * Check if user has required access
   */
  private async checkAccess(context: RbacContext): Promise<boolean> {
    const { user, requiredRoles, requiredPermissions, organizationRequired, resourceId } = context;

    try {
      // Check organization requirement
      if (organizationRequired && !user.organizationId) {
        this.logger.debug(`Organization required but user ${user.sub} has no organization`);
        return false;
      }

      // Check required roles
      if (requiredRoles && requiredRoles.length > 0) {
        const hasRequiredRole = await this.checkRoles(user, requiredRoles, user.organizationId);
        if (!hasRequiredRole) {
          this.logger.debug(`User ${user.sub} missing required roles: ${requiredRoles.join(', ')}`);
          return false;
        }
      }

      // Check required permissions
      if (requiredPermissions && requiredPermissions.length > 0) {
        const hasRequiredPermissions = await this.checkPermissions(
          user,
          requiredPermissions,
          user.organizationId,
          resourceId,
        );
        if (!hasRequiredPermissions) {
          this.logger.debug(`User ${user.sub} missing required permissions: ${requiredPermissions.join(', ')}`);
          return false;
        }
      }

      return true;
    } catch (error) {
      this.logger.error('Error checking access:', error);
      return false;
    }
  }

  /**
   * Check if user has required roles
   */
  private async checkRoles(
    user: JwtPayload,
    requiredRoles: string[],
    organizationId?: string,
  ): Promise<boolean> {
    try {
      // Check roles from JWT payload first (for performance)
      if (user.roles && user.roles.length > 0) {
        const hasRole = requiredRoles.some(role => user.roles!.includes(role));
        if (hasRole) {
          return true;
        }
      }

      // Fallback to database check
      for (const role of requiredRoles) {
        const hasRole = await this.rbacService.hasRole(user.sub, role, organizationId);
        if (hasRole) {
          return true;
        }
      }

      return false;
    } catch (error) {
      this.logger.error('Error checking roles:', error);
      return false;
    }
  }

  /**
   * Check if user has required permissions
   */
  private async checkPermissions(
    user: JwtPayload,
    requiredPermissions: string[],
    organizationId?: string,
    resourceId?: string,
  ): Promise<boolean> {
    try {
      // Check permissions from JWT payload first (for performance)
      if (user.permissions && user.permissions.length > 0) {
        const hasPermission = requiredPermissions.some(permission => 
          user.permissions!.includes(permission) || user.permissions!.includes('*')
        );
        if (hasPermission) {
          return true;
        }
      }

      // Fallback to database check
      for (const permission of requiredPermissions) {
        const hasPermission = await this.rbacService.hasPermission(
          user.sub,
          permission,
          organizationId,
          resourceId,
        );
        if (hasPermission) {
          return true;
        }
      }

      return false;
    } catch (error) {
      this.logger.error('Error checking permissions:', error);
      return false;
    }
  }

  /**
   * Extract resource ID from request
   */
  private extractResourceId(request: Request): string | undefined {
    // Try to extract resource ID from URL parameters
    const params = request.params;
    
    // Common parameter names for resource IDs
    const resourceIdParams = ['id', 'resourceId', 'organizationId', 'userId', 'guildId'];
    
    for (const param of resourceIdParams) {
      if (params[param]) {
        return params[param];
      }
    }

    // Try to extract from query parameters
    const query = request.query;
    for (const param of resourceIdParams) {
      if (query[param] && typeof query[param] === 'string') {
        return query[param] as string;
      }
    }

    return undefined;
  }

  /**
   * Get client IP address
   */
  private getClientIp(request: Request): string {
    return (
      request.get('X-Forwarded-For')?.split(',')[0]?.trim() ||
      request.get('X-Real-IP') ||
      request.connection.remoteAddress ||
      request.socket.remoteAddress ||
      'unknown'
    );
  }

  /**
   * Log access denied event
   */
  private async logAccessDenied(
    request: Request,
    context: Partial<RbacContext>,
  ): Promise<void> {
    try {
      const ipAddress = this.getClientIp(request);
      const userAgent = request.get('User-Agent');

      // Log security event
      await this.securityEventService.recordUnauthorizedAccess(
        context.endpoint || request.url,
        {
          reason: 'Insufficient permissions',
          method: context.method || request.method,
          requiredRoles: context.requiredRoles,
          requiredPermissions: context.requiredPermissions,
          userAgent,
        },
        {
          userId: context.user?.sub,
          organizationId: context.user?.organizationId,
          ipAddress,
          userAgent,
          endpoint: context.endpoint || request.url,
        },
      );

      // Log audit event
      await this.auditService.logEvent(
        'authorization',
        'access_denied',
        context.endpoint || request.url,
        {
          reason: 'Insufficient permissions',
          requiredRoles: context.requiredRoles,
          requiredPermissions: context.requiredPermissions,
          resourceId: context.resourceId,
        },
        {
          userId: context.user?.sub || 'unknown',
          organizationId: context.user?.organizationId,
          resourceId: context.resourceId,
          ipAddress,
          userAgent,
        },
        false,
        'Insufficient permissions',
      );
    } catch (error) {
      this.logger.error('Failed to log access denied event:', error);
    }
  }
}

/**
 * Decorator to require specific roles
 */
export const RequireRoles = (...roles: string[]) => SetMetadata('roles', roles);

/**
 * Decorator to require specific permissions
 */
export const RequirePermissions = (...permissions: string[]) => SetMetadata('permissions', permissions);

/**
 * Decorator to require organization context
 */
export const RequireOrganization = () => SetMetadata('organization_required', true);
