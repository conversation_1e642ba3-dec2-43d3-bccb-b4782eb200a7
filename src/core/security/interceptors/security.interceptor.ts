import {
    BadRequestException,
    CallHandler,
    ExecutionContext,
    Injectable,
    Logger,
    NestInterceptor,
    SetMetadata,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Request, Response } from 'express';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { AuthenticatedRequest } from '../guards/jwt-auth.guard';
import { SecurityEventService } from '../security-event.service';
import { SecurityService } from '../security.service';
import { ValidationService } from '../validation.service';

export interface SecurityInterceptorConfig {
  sanitizeInput?: boolean;
  validateInput?: boolean;
  detectSqlInjection?: boolean;
  detectXss?: boolean;
  detectPathTraversal?: boolean;
  logSuspiciousActivity?: boolean;
  blockSuspiciousRequests?: boolean;
}

@Injectable()
export class SecurityInterceptor implements NestInterceptor {
  private readonly logger = new Logger(SecurityInterceptor.name);

  constructor(
    private readonly reflector: Reflector,
    private readonly securityService: SecurityService,
    private readonly validationService: ValidationService,
    private readonly securityEventService: SecurityEventService,
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<AuthenticatedRequest>();
    const response = context.switchToHttp().getResponse<Response>();

    // Get security configuration from decorators
    const config = this.getSecurityConfig(context);

    // Skip security checks if disabled
    const securityDisabled = this.reflector.getAllAndOverride<boolean>('security_disabled', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (securityDisabled) {
      return next.handle();
    }

    try {
      // Perform pre-request security checks
      this.performPreRequestChecks(request, config);

      // Continue with request processing
      return next.handle().pipe(
        tap((data) => {
          // Perform post-request security checks
          this.performPostRequestChecks(request, response, data, config);
        }),
        catchError((error) => {
          // Log security-related errors
          this.handleSecurityError(request, error, config);
          return throwError(() => error);
        }),
      );
    } catch (error) {
      this.handleSecurityError(request, error, config);
      return throwError(() => error);
    }
  }

  /**
   * Get security configuration from decorators
   */
  private getSecurityConfig(context: ExecutionContext): SecurityInterceptorConfig {
    const customConfig = this.reflector.getAllAndOverride<SecurityInterceptorConfig>('security_config', [
      context.getHandler(),
      context.getClass(),
    ]);

    return {
      sanitizeInput: true,
      validateInput: true,
      detectSqlInjection: true,
      detectXss: true,
      detectPathTraversal: true,
      logSuspiciousActivity: true,
      blockSuspiciousRequests: true,
      ...customConfig,
    };
  }

  /**
   * Perform pre-request security checks
   */
  private performPreRequestChecks(request: AuthenticatedRequest, config: SecurityInterceptorConfig): void {
    // Sanitize input data
    if (config.sanitizeInput) {
      this.sanitizeRequestData(request);
    }

    // Validate input data
    if (config.validateInput) {
      this.validateRequestData(request);
    }

    // Detect security threats
    if (config.detectSqlInjection || config.detectXss || config.detectPathTraversal) {
      this.detectSecurityThreats(request, config);
    }
  }

  /**
   * Perform post-request security checks
   */
  private performPostRequestChecks(
    request: AuthenticatedRequest,
    response: Response,
    data: any,
    config: SecurityInterceptorConfig,
  ): void {
    // Sanitize response data if needed
    if (config.sanitizeInput && data) {
      this.sanitizeResponseData(data);
    }

    // Add security headers if not already set
    this.addSecurityHeaders(response);

    // Log successful request for audit
    this.logSuccessfulRequest(request, response);
  }

  /**
   * Sanitize request data
   */
  private sanitizeRequestData(request: AuthenticatedRequest): void {
    try {
      if (request.body) {
        request.body = this.securityService.sanitizeInput(request.body);
      }

      if (request.query) {
        request.query = this.securityService.sanitizeInput(request.query);
      }

      if (request.params) {
        request.params = this.securityService.sanitizeInput(request.params);
      }
    } catch (error) {
      this.logger.error('Error sanitizing request data:', error);
    }
  }

  /**
   * Validate request data
   */
  private validateRequestData(request: AuthenticatedRequest): void {
    try {
      // Basic validation rules
      const validationRules = [
        {
          field: 'body',
          type: 'object' as const,
          required: false,
          customValidator: (value: any) => {
            if (value && typeof value === 'object') {
              const jsonString = JSON.stringify(value);
              if (jsonString.length > 1000000) { // 1MB limit
                return 'Request body too large';
              }
            }
            return true;
          },
        },
      ];

      const requestData = {
        body: request.body,
        query: request.query,
        params: request.params,
      };

      this.validationService.validateOrThrow(requestData, validationRules);
    } catch (error) {
      this.logger.warn('Request validation failed:', error);
      throw new BadRequestException('Invalid request data');
    }
  }

  /**
   * Detect security threats in request
   */
  private detectSecurityThreats(request: AuthenticatedRequest, config: SecurityInterceptorConfig): void {
    const requestData = {
      ...request.body,
      ...request.query,
      ...request.params,
      url: request.url,
      userAgent: request.get('User-Agent'),
    };

    const threats: string[] = [];

    // SQL Injection detection
    if (config.detectSqlInjection && this.detectSqlInjection(requestData)) {
      threats.push('SQL Injection');
    }

    // XSS detection
    if (config.detectXss && this.detectXss(requestData)) {
      threats.push('Cross-Site Scripting (XSS)');
    }

    // Path traversal detection
    if (config.detectPathTraversal && this.detectPathTraversal(requestData)) {
      threats.push('Path Traversal');
    }

    // Handle detected threats
    if (threats.length > 0) {
      this.handleDetectedThreats(request, threats, config);
    }
  }

  /**
   * Detect SQL injection attempts
   */
  private detectSqlInjection(data: any): boolean {
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/gi,
      /(\b(OR|AND)\s+\d+\s*=\s*\d+)/gi,
      /(UNION\s+SELECT)/gi,
      /(\'\s*OR\s*\'\d+\'\s*=\s*\'\d+)/gi,
      /(\'\s*;\s*DROP\s+TABLE)/gi,
    ];

    return this.checkPatternsInData(data, sqlPatterns);
  }

  /**
   * Detect XSS attempts
   */
  private detectXss(data: any): boolean {
    const xssPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /<iframe\b[^>]*>/gi,
      /<object\b[^>]*>/gi,
      /<embed\b[^>]*>/gi,
      /expression\s*\(/gi,
    ];

    return this.checkPatternsInData(data, xssPatterns);
  }

  /**
   * Detect path traversal attempts
   */
  private detectPathTraversal(data: any): boolean {
    const pathTraversalPatterns = [
      /\.\.\//g,
      /\.\.\\/g,
      /%2e%2e%2f/gi,
      /%2e%2e%5c/gi,
      /\.\.%2f/gi,
      /\.\.%5c/gi,
    ];

    return this.checkPatternsInData(data, pathTraversalPatterns);
  }

  /**
   * Check patterns in data recursively
   */
  private checkPatternsInData(data: any, patterns: RegExp[]): boolean {
    if (typeof data === 'string') {
      return patterns.some(pattern => pattern.test(data));
    }

    if (Array.isArray(data)) {
      return data.some(item => this.checkPatternsInData(item, patterns));
    }

    if (typeof data === 'object' && data !== null) {
      return Object.values(data).some(value => this.checkPatternsInData(value, patterns));
    }

    return false;
  }

  /**
   * Handle detected security threats
   */
  private handleDetectedThreats(
    request: AuthenticatedRequest,
    threats: string[],
    config: SecurityInterceptorConfig,
  ): void {
    const threatDescription = `Detected security threats: ${threats.join(', ')}`;

    // Log suspicious activity
    if (config.logSuspiciousActivity) {
      this.securityEventService.recordSuspiciousActivity(
        threatDescription,
        {
          threats,
          url: request.url,
          method: request.method,
          body: request.body,
          query: request.query,
          params: request.params,
          userAgent: request.get('User-Agent'),
        },
        {
          userId: request.user?.sub,
          ipAddress: this.getClientIp(request),
          userAgent: request.get('User-Agent'),
          endpoint: request.url,
        },
      );
    }

    // Block suspicious requests
    if (config.blockSuspiciousRequests) {
      throw new BadRequestException('Suspicious request detected');
    }
  }

  /**
   * Sanitize response data
   */
  private sanitizeResponseData(data: any): any {
    // Remove sensitive fields from response
    if (typeof data === 'object' && data !== null) {
      const sensitiveFields = ['password', 'secret', 'token', 'key', 'hash'];
      
      if (Array.isArray(data)) {
        return data.map((item: any) => this.sanitizeResponseData(item));
      }

      const sanitized = { ...data };
      sensitiveFields.forEach(field => {
        if (field in sanitized) {
          delete sanitized[field];
        }
      });

      return sanitized;
    }

    return data;
  }

  /**
   * Add security headers to response
   */
  private addSecurityHeaders(response: Response): void {
    // Only add headers if they haven't been set already
    if (!response.getHeader('X-Content-Type-Options')) {
      response.setHeader('X-Content-Type-Options', 'nosniff');
    }

    if (!response.getHeader('X-Frame-Options')) {
      response.setHeader('X-Frame-Options', 'DENY');
    }

    if (!response.getHeader('X-XSS-Protection')) {
      response.setHeader('X-XSS-Protection', '1; mode=block');
    }
  }

  /**
   * Log successful request
   */
  private logSuccessfulRequest(request: AuthenticatedRequest, response: Response): void {
    // Only log if it's not a health check or similar
    if (!request.url.includes('/health') && !request.url.includes('/ping')) {
      this.logger.debug(`Successful request: ${request.method} ${request.url}`, {
        userId: request.user?.sub,
        statusCode: response.statusCode,
        userAgent: request.get('User-Agent'),
      });
    }
  }

  /**
   * Handle security errors
   */
  private handleSecurityError(request: AuthenticatedRequest, error: any, config: SecurityInterceptorConfig): void {
    if (config.logSuspiciousActivity) {
      this.logger.error('Security error occurred:', {
        error: error.message,
        url: request.url,
        method: request.method,
        userId: request.user?.sub,
        ipAddress: this.getClientIp(request),
      });
    }
  }

  /**
   * Get client IP address
   */
  private getClientIp(request: Request): string {
    return (
      request.get('X-Forwarded-For')?.split(',')[0]?.trim() ||
      request.get('X-Real-IP') ||
      request.connection.remoteAddress ||
      request.socket.remoteAddress ||
      'unknown'
    );
  }
}

/**
 * Decorator to configure security interceptor
 */
export const SecurityConfig = (config: SecurityInterceptorConfig) => SetMetadata('security_config', config);

/**
 * Decorator to disable security interceptor
 */
export const DisableSecurity = () => SetMetadata('security_disabled', true);
