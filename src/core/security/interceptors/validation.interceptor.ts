import {
    BadRequestException,
    CallHandler,
    ExecutionContext,
    Injectable,
    Logger,
    NestInterceptor,
    SetMetadata,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Request } from 'express';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { AuthenticatedRequest } from '../guards/jwt-auth.guard';
import { SecurityEventService } from '../security-event.service';
import { ValidationRule, ValidationService } from '../validation.service';

export interface ValidationConfig {
  validateRequest?: boolean;
  validateResponse?: boolean;
  sanitizeRequest?: boolean;
  sanitizeResponse?: boolean;
  requestRules?: ValidationRule[];
  responseRules?: ValidationRule[];
  strictMode?: boolean;
  logValidationErrors?: boolean;
}

@Injectable()
export class ValidationInterceptor implements NestInterceptor {
  private readonly logger = new Logger(ValidationInterceptor.name);

  constructor(
    private readonly reflector: Reflector,
    private readonly validationService: ValidationService,
    private readonly securityEventService: SecurityEventService,
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<AuthenticatedRequest>();

    // Get validation configuration from decorators
    const validationConfig = this.getValidationConfig(context);

    // Skip validation if disabled
    const validationDisabled = this.reflector.getAllAndOverride<boolean>('validation_disabled', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (validationDisabled) {
      return next.handle();
    }

    try {
      // Validate and sanitize request
      this.processRequest(request, validationConfig);

      return next.handle().pipe(
        map((data) => {
          // Validate and sanitize response
          return this.processResponse(data, validationConfig, request);
        }),
      );
    } catch (error) {
      this.handleValidationError(request, error, validationConfig);
      throw error;
    }
  }

  /**
   * Get validation configuration from decorators
   */
  private getValidationConfig(context: ExecutionContext): ValidationConfig {
    const customConfig = this.reflector.getAllAndOverride<ValidationConfig>('validation_config', [
      context.getHandler(),
      context.getClass(),
    ]);

    const defaultConfig: ValidationConfig = {
      validateRequest: true,
      validateResponse: false,
      sanitizeRequest: true,
      sanitizeResponse: true,
      requestRules: [],
      responseRules: [],
      strictMode: false,
      logValidationErrors: true,
    };

    return { ...defaultConfig, ...customConfig };
  }

  /**
   * Process and validate request
   */
  private processRequest(request: AuthenticatedRequest, config: ValidationConfig): void {
    // Sanitize request data
    if (config.sanitizeRequest) {
      this.sanitizeRequest(request);
    }

    // Validate request data
    if (config.validateRequest) {
      this.validateRequest(request, config);
    }
  }

  /**
   * Process and validate response
   */
  private processResponse(data: any, config: ValidationConfig, request: AuthenticatedRequest): any {
    let processedData = data;

    // Sanitize response data
    if (config.sanitizeResponse) {
      processedData = this.sanitizeResponse(processedData);
    }

    // Validate response data
    if (config.validateResponse && config.responseRules && config.responseRules.length > 0) {
      this.validateResponse(processedData, config, request);
    }

    return processedData;
  }

  /**
   * Sanitize request data
   */
  private sanitizeRequest(request: AuthenticatedRequest): void {
    try {
      const sanitizationOptions = {
        trimStrings: true,
        removeHtml: true,
        removeScripts: true,
        normalizeEmail: true,
        maxStringLength: 10000,
      };

      if (request.body) {
        request.body = this.validationService.sanitize(request.body, sanitizationOptions);
      }

      if (request.query) {
        request.query = this.validationService.sanitize(request.query, sanitizationOptions);
      }

      if (request.params) {
        request.params = this.validationService.sanitize(request.params, sanitizationOptions);
      }
    } catch (error) {
      this.logger.error('Error sanitizing request:', error);
      throw new BadRequestException('Request sanitization failed');
    }
  }

  /**
   * Sanitize response data
   */
  private sanitizeResponse(data: any): any {
    try {
      if (!data || typeof data !== 'object') {
        return data;
      }

      // Remove sensitive fields from response
      const sensitiveFields = [
        'password',
        'secret',
        'token',
        'key',
        'hash',
        'privateKey',
        'apiKey',
        'accessToken',
        'refreshToken',
      ];

      return this.removeSensitiveFields(data, sensitiveFields);
    } catch (error) {
      this.logger.error('Error sanitizing response:', error);
      return data;
    }
  }

  /**
   * Validate request data
   */
  private validateRequest(request: AuthenticatedRequest, config: ValidationConfig): void {
    try {
      // Get validation rules
      const rules = this.getRequestValidationRules(request, config);

      if (rules.length === 0) {
        return;
      }

      // Prepare data for validation
      const requestData = {
        ...request.body,
        ...request.query,
        ...request.params,
      };

      // Validate data
      const validationResult = this.validationService.validate(requestData, rules);

      if (!validationResult.isValid) {
        const errorMessage = validationResult.errors
          .map((error: any) => `${error.field}: ${error.message}`)
          .join(', ');

        if (config.strictMode) {
          throw new BadRequestException(`Validation failed: ${errorMessage}`);
        } else {
          this.logger.warn(`Validation warnings: ${errorMessage}`, {
            url: request.url,
            method: request.method,
            userId: request.user?.sub,
          });
        }
      }
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      
      this.logger.error('Request validation error:', error);
      if (config.strictMode) {
        throw new BadRequestException('Request validation failed');
      }
    }
  }

  /**
   * Validate response data
   */
  private validateResponse(data: any, config: ValidationConfig, request: AuthenticatedRequest): void {
    try {
      if (!config.responseRules || config.responseRules.length === 0) {
        return;
      }

      const validationResult = this.validationService.validate(data, config.responseRules);

      if (!validationResult.isValid) {
        const errorMessage = validationResult.errors
          .map((error: any) => `${error.field}: ${error.message}`)
          .join(', ');

        this.logger.error(`Response validation failed: ${errorMessage}`, {
          url: request.url,
          method: request.method,
          userId: request.user?.sub,
        });

        // Don't throw error for response validation, just log it
        if (config.logValidationErrors) {
          this.securityEventService.recordSuspiciousActivity(
            'Response validation failed',
            {
              validationErrors: validationResult.errors,
              url: request.url,
              method: request.method,
            },
            {
              userId: request.user?.sub,
              ipAddress: this.getClientIp(request),
              endpoint: request.url,
            },
          );
        }
      }
    } catch (error) {
      this.logger.error('Response validation error:', error);
    }
  }

  /**
   * Get request validation rules based on request type
   */
  private getRequestValidationRules(request: AuthenticatedRequest, config: ValidationConfig): ValidationRule[] {
    // Use custom rules if provided
    if (config.requestRules && config.requestRules.length > 0) {
      return config.requestRules;
    }

    // Generate default rules based on request method and path
    const rules: ValidationRule[] = [];

    // Common validation rules
    if (request.body) {
      // Limit request body size
      rules.push({
        field: 'body',
        type: 'object',
        required: false,
        customValidator: (value: any) => {
          if (value && typeof value === 'object') {
            const jsonString = JSON.stringify(value);
            if (jsonString.length > 1000000) { // 1MB limit
              return 'Request body too large';
            }
          }
          return true;
        },
      });
    }

    // Add specific rules based on endpoint
    if (request.url.includes('/auth/')) {
      rules.push(...this.getAuthValidationRules());
    } else if (request.url.includes('/users/')) {
      rules.push(...this.getUserValidationRules());
    } else if (request.url.includes('/organizations/')) {
      rules.push(...this.getOrganizationValidationRules());
    }

    return rules;
  }

  /**
   * Get authentication validation rules
   */
  private getAuthValidationRules(): ValidationRule[] {
    return [
      {
        field: 'email',
        type: 'email',
        required: false,
        maxLength: 255,
      },
      {
        field: 'password',
        type: 'string',
        required: false,
        minLength: 8,
        maxLength: 128,
      },
      {
        field: 'username',
        type: 'string',
        required: false,
        minLength: 3,
        maxLength: 50,
        pattern: '^[a-zA-Z0-9_-]+$',
      },
    ];
  }

  /**
   * Get user validation rules
   */
  private getUserValidationRules(): ValidationRule[] {
    return ValidationService.createUserValidationRules();
  }

  /**
   * Get organization validation rules
   */
  private getOrganizationValidationRules(): ValidationRule[] {
    return ValidationService.createOrganizationValidationRules();
  }

  /**
   * Remove sensitive fields from data
   */
  private removeSensitiveFields(data: any, sensitiveFields: string[]): any {
    if (!data || typeof data !== 'object') {
      return data;
    }

    if (Array.isArray(data)) {
      return data.map((item: any) => this.removeSensitiveFields(item, sensitiveFields));
    }

    const sanitized = { ...data };

    sensitiveFields.forEach(field => {
      if (field in sanitized) {
        delete sanitized[field];
      }
    });

    // Recursively process nested objects
    Object.keys(sanitized).forEach(key => {
      if (typeof sanitized[key] === 'object' && sanitized[key] !== null) {
        sanitized[key] = this.removeSensitiveFields(sanitized[key], sensitiveFields);
      }
    });

    return sanitized;
  }

  /**
   * Handle validation errors
   */
  private handleValidationError(
    request: AuthenticatedRequest,
    error: any,
    config: ValidationConfig,
  ): void {
    if (config.logValidationErrors) {
      this.logger.error('Validation error:', {
        error: error.message,
        url: request.url,
        method: request.method,
        userId: request.user?.sub,
        ipAddress: this.getClientIp(request),
      });

      // Log security event for validation failures
      this.securityEventService.recordSuspiciousActivity(
        'Request validation failed',
        {
          error: error.message,
          url: request.url,
          method: request.method,
        },
        {
          userId: request.user?.sub,
          ipAddress: this.getClientIp(request),
          endpoint: request.url,
        },
      );
    }
  }

  /**
   * Get client IP address
   */
  private getClientIp(request: Request): string {
    return (
      request.get('X-Forwarded-For')?.split(',')[0]?.trim() ||
      request.get('X-Real-IP') ||
      request.connection.remoteAddress ||
      request.socket.remoteAddress ||
      'unknown'
    );
  }
}

/**
 * Decorator to configure validation
 */
export const ValidateRequest = (config: ValidationConfig) => SetMetadata('validation_config', config);

/**
 * Decorator to disable validation
 */
export const DisableValidation = () => SetMetadata('validation_disabled', true);

/**
 * Predefined validation configurations
 */
export const StrictValidation = () => ValidateRequest({
  validateRequest: true,
  validateResponse: true,
  sanitizeRequest: true,
  sanitizeResponse: true,
  strictMode: true,
  logValidationErrors: true,
});

export const LenientValidation = () => ValidateRequest({
  validateRequest: true,
  validateResponse: false,
  sanitizeRequest: true,
  sanitizeResponse: true,
  strictMode: false,
  logValidationErrors: false,
});
