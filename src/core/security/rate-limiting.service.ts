import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  keyGenerator?: (context: RateLimitContext) => string;
  onLimitReached?: (context: RateLimitContext) => void;
}

export interface RateLimitContext {
  userId?: string;
  organizationId?: string;
  ipAddress?: string;
  userAgent?: string;
  endpoint?: string;
  method?: string;
}

export interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  resetTime: Date;
  totalHits: number;
}

export interface RateLimitEntry {
  key: string;
  requests: number;
  windowStart: Date;
  config: RateLimitConfig;
}

@Injectable()
export class RateLimitingService {
  private readonly logger = new Logger(RateLimitingService.name);
  private readonly rateLimitStore = new Map<string, RateLimitEntry>();
  private readonly defaultConfig: RateLimitConfig;

  constructor(private readonly configService: ConfigService) {
    this.defaultConfig = {
      windowMs: this.configService.get<number>('RATE_LIMIT_TTL', 60) * 1000,
      maxRequests: this.configService.get<number>('RATE_LIMIT_MAX', 100),
      skipSuccessfulRequests: false,
      skipFailedRequests: false,
    };

    // Clean up expired entries every minute
    setInterval(() => this.cleanupExpiredEntries(), 60000);
  }

  /**
   * Check if request is allowed under rate limit
   */
  async checkRateLimit(
    context: RateLimitContext,
    config: Partial<RateLimitConfig> = {},
  ): Promise<RateLimitResult> {
    const finalConfig = { ...this.defaultConfig, ...config };
    const key = this.generateKey(context, finalConfig);

    const now = new Date();
    let entry = this.rateLimitStore.get(key);

    // Create new entry if doesn't exist or window has expired
    if (!entry || this.isWindowExpired(entry, now)) {
      entry = {
        key,
        requests: 0,
        windowStart: now,
        config: finalConfig,
      };
      this.rateLimitStore.set(key, entry);
    }

    // Increment request count
    entry.requests++;

    const allowed = entry.requests <= finalConfig.maxRequests;
    const remaining = Math.max(0, finalConfig.maxRequests - entry.requests);
    const resetTime = new Date(entry.windowStart.getTime() + finalConfig.windowMs);

    if (!allowed && finalConfig.onLimitReached) {
      finalConfig.onLimitReached(context);
    }

    // Log rate limit violations
    if (!allowed) {
      this.logger.warn(`Rate limit exceeded for key: ${key}`, {
        context,
        requests: entry.requests,
        maxRequests: finalConfig.maxRequests,
        windowMs: finalConfig.windowMs,
      });
    }

    return {
      allowed,
      remaining,
      resetTime,
      totalHits: entry.requests,
    };
  }

  /**
   * Check rate limit for user
   */
  async checkUserRateLimit(
    userId: string,
    organizationId?: string,
    config?: Partial<RateLimitConfig>,
  ): Promise<RateLimitResult> {
    return this.checkRateLimit({ userId, organizationId }, config);
  }

  /**
   * Check rate limit for IP address
   */
  async checkIpRateLimit(
    ipAddress: string,
    config?: Partial<RateLimitConfig>,
  ): Promise<RateLimitResult> {
    return this.checkRateLimit({ ipAddress }, config);
  }

  /**
   * Check rate limit for API endpoint
   */
  async checkEndpointRateLimit(
    endpoint: string,
    method: string,
    context: Partial<RateLimitContext> = {},
    config?: Partial<RateLimitConfig>,
  ): Promise<RateLimitResult> {
    return this.checkRateLimit({ ...context, endpoint, method }, config);
  }

  /**
   * Reset rate limit for a key
   */
  async resetRateLimit(context: RateLimitContext, config: Partial<RateLimitConfig> = {}): Promise<void> {
    const finalConfig = { ...this.defaultConfig, ...config };
    const key = this.generateKey(context, finalConfig);
    this.rateLimitStore.delete(key);
  }

  /**
   * Get current rate limit status
   */
  async getRateLimitStatus(
    context: RateLimitContext,
    config: Partial<RateLimitConfig> = {},
  ): Promise<RateLimitResult | null> {
    const finalConfig = { ...this.defaultConfig, ...config };
    const key = this.generateKey(context, finalConfig);
    const entry = this.rateLimitStore.get(key);

    if (!entry || this.isWindowExpired(entry, new Date())) {
      return null;
    }

    const remaining = Math.max(0, finalConfig.maxRequests - entry.requests);
    const resetTime = new Date(entry.windowStart.getTime() + finalConfig.windowMs);

    return {
      allowed: entry.requests <= finalConfig.maxRequests,
      remaining,
      resetTime,
      totalHits: entry.requests,
    };
  }

  /**
   * Get all active rate limits
   */
  async getActiveRateLimits(): Promise<Array<{ key: string; entry: RateLimitEntry }>> {
    const now = new Date();
    const activeEntries: Array<{ key: string; entry: RateLimitEntry }> = [];

    for (const [key, entry] of this.rateLimitStore.entries()) {
      if (!this.isWindowExpired(entry, now)) {
        activeEntries.push({ key, entry });
      }
    }

    return activeEntries;
  }

  /**
   * Get rate limit statistics
   */
  async getStatistics(): Promise<{
    totalActiveKeys: number;
    totalRequests: number;
    violatedKeys: number;
    topViolators: Array<{ key: string; requests: number; maxRequests: number }>;
  }> {
    const activeEntries = await this.getActiveRateLimits();
    const totalActiveKeys = activeEntries.length;
    const totalRequests = activeEntries.reduce((sum, { entry }) => sum + entry.requests, 0);
    const violatedKeys = activeEntries.filter(({ entry }) => entry.requests > entry.config.maxRequests).length;

    const topViolators = activeEntries
      .filter(({ entry }) => entry.requests > entry.config.maxRequests)
      .map(({ key, entry }) => ({
        key,
        requests: entry.requests,
        maxRequests: entry.config.maxRequests,
      }))
      .sort((a, b) => (b.requests - b.maxRequests) - (a.requests - a.maxRequests))
      .slice(0, 10);

    return {
      totalActiveKeys,
      totalRequests,
      violatedKeys,
      topViolators,
    };
  }

  /**
   * Generate rate limit key
   */
  private generateKey(context: RateLimitContext, config: RateLimitConfig): string {
    if (config.keyGenerator) {
      return config.keyGenerator(context);
    }

    const parts: string[] = [];

    if (context.userId) {
      parts.push(`user:${context.userId}`);
    }

    if (context.organizationId) {
      parts.push(`org:${context.organizationId}`);
    }

    if (context.ipAddress) {
      parts.push(`ip:${context.ipAddress}`);
    }

    if (context.endpoint) {
      parts.push(`endpoint:${context.endpoint}`);
    }

    if (context.method) {
      parts.push(`method:${context.method}`);
    }

    return parts.join('|') || 'global';
  }

  /**
   * Check if rate limit window has expired
   */
  private isWindowExpired(entry: RateLimitEntry, now: Date): boolean {
    const windowEnd = new Date(entry.windowStart.getTime() + entry.config.windowMs);
    return now > windowEnd;
  }

  /**
   * Clean up expired entries
   */
  private cleanupExpiredEntries(): void {
    const now = new Date();
    const expiredKeys: string[] = [];

    for (const [key, entry] of this.rateLimitStore.entries()) {
      if (this.isWindowExpired(entry, now)) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => this.rateLimitStore.delete(key));

    if (expiredKeys.length > 0) {
      this.logger.debug(`Cleaned up ${expiredKeys.length} expired rate limit entries`);
    }
  }

  /**
   * Create rate limit configurations for different scenarios
   */
  static createAuthRateLimitConfig(): RateLimitConfig {
    return {
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 5, // 5 login attempts per 15 minutes
      skipSuccessfulRequests: false,
      skipFailedRequests: false,
    };
  }

  static createApiRateLimitConfig(): RateLimitConfig {
    return {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 100, // 100 requests per minute
      skipSuccessfulRequests: false,
      skipFailedRequests: false,
    };
  }

  static createStrictRateLimitConfig(): RateLimitConfig {
    return {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 10, // 10 requests per minute
      skipSuccessfulRequests: false,
      skipFailedRequests: false,
    };
  }

  static createLenientRateLimitConfig(): RateLimitConfig {
    return {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 1000, // 1000 requests per minute
      skipSuccessfulRequests: true,
      skipFailedRequests: false,
    };
  }
}
