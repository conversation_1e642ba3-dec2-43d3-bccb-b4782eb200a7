import { Injectable, Logger } from '@nestjs/common';
import { DatabaseService } from '@/core/database';
import { User, User<PERSON>eys, CreateUser } from '@/core/database';
import { Organization, OrganizationMember, OrganizationKeys, OrganizationMemberKeys } from '@/core/database';
import { EncryptionService } from './encryption.service';
import { generateId } from '@/core/database';

@Injectable()
export class UserService {
  private readonly logger = new Logger(UserService.name);

  constructor(
    private readonly databaseService: DatabaseService,
    private readonly encryptionService: EncryptionService,
  ) {}

  async findByDiscordId(discordId: string): Promise<User | null> {
    try {
      const key = UserKeys.byDiscordId(discordId);
      const result = await this.databaseService.get(key);
      
      if (!result.success || !result.data) {
        return null;
      }
      
      return JSON.parse(result.data) as User;
    } catch (error) {
      this.logger.error(`Failed to find user by Discord ID ${discordId}:`, error);
      return null;
    }
  }

  async createUser(userData: {
    discordId: string;
    username: string;
    email?: string;
    accessToken?: string;
    refreshToken?: string;
  }): Promise<User> {
    try {
      const now = new Date();
      const newUser: User = {
        id: generateId(),
        createdAt: now,
        updatedAt: now,
        discordId: userData.discordId,
        username: userData.username,
        email: userData.email,
        accessToken: userData.accessToken ?
          this.encryptionService.encryptUserData(userData.accessToken) : undefined,
        refreshToken: userData.refreshToken ?
          this.encryptionService.encryptUserData(userData.refreshToken) : undefined,
        isActive: true,
        lastActivityAt: new Date(),
        experience: 0,
        balance: 0,
      };

      // Store user with multiple key patterns for efficient lookups
      const primaryKey = UserKeys.primary(newUser.id);
      const discordIdKey = UserKeys.byDiscordId(newUser.discordId);
      const usernameKey = UserKeys.byUsername(newUser.username);
      
      const userData_serialized = JSON.stringify(newUser);
      
      // Store with primary key
      const result = await this.databaseService.set(primaryKey, userData_serialized);
      if (!result.success) {
        throw new Error(`Failed to store user: ${result.error}`);
      }
      
      // Store lookup keys
      await this.databaseService.set(discordIdKey, newUser.id);
      if (newUser.username) {
        await this.databaseService.set(usernameKey, newUser.id);
      }
      if (newUser.email) {
        await this.databaseService.set(UserKeys.byEmail(newUser.email), newUser.id);
      }
      
      return newUser;
    } catch (error) {
      this.logger.error('Failed to create user:', error);
      throw new Error('User creation failed');
    }
  }

  async updateUser(discordId: string, updates: Partial<User>): Promise<User | null> {
    try {
      const existingUser = await this.findByDiscordId(discordId);
      if (!existingUser) {
        return null;
      }
      
      const updatedUser: User = {
        ...existingUser,
        ...updates,
        updatedAt: new Date(),
      };
      
      const primaryKey = UserKeys.primary(existingUser.id);
      const result = await this.databaseService.set(primaryKey, JSON.stringify(updatedUser));
      
      if (!result.success) {
        throw new Error(`Failed to update user: ${result.error}`);
      }
      
      return updatedUser;
    } catch (error) {
      this.logger.error(`Failed to update user ${discordId}:`, error);
      return null;
    }
  }

  async upsertUser(userData: {
    discordId: string;
    username: string;
    email?: string;
    accessToken?: string;
    refreshToken?: string;
  }): Promise<User> {
    const existingUser = await this.findByDiscordId(userData.discordId);
    
    if (existingUser) {
      const updated = await this.updateUser(userData.discordId, {
        username: userData.username,
        email: userData.email,
        lastActivityAt: new Date(),
      });
      return updated!;
    } else {
      return await this.createUser(userData);
    }
  }

  async getUserCount(): Promise<number> {
    try {
      // In Redis, we'll need to maintain a count or scan pattern
      // For now, let's implement a simple pattern-based count
      const result = await this.databaseService.executeCommand('EVAL', 
        `return #redis.call('KEYS', ARGV[1])`, 
        '0', 
        UserKeys.pattern
      );
      
      if (result.success && typeof result.data === 'number') {
        return result.data;
      }
      
      return 0;
    } catch (error) {
      this.logger.error('Failed to get user count:', error);
      return 0;
    }
  }

  async findById(userId: string): Promise<User | null> {
    try {
      const key = UserKeys.primary(userId);
      const result = await this.databaseService.get(key);
      
      if (!result.success || !result.data) {
        return null;
      }
      
      return JSON.parse(result.data) as User;
    } catch (error) {
      this.logger.error(`Failed to find user by ID ${userId}:`, error);
      return null;
    }
  }

  async validateSession(sessionId: string): Promise<boolean> {
    try {
      // In a real implementation, this would check against a sessions table
      // For now, we'll just return true if sessionId is provided
      return !!sessionId;
    } catch (error) {
      this.logger.error(`Failed to validate session ${sessionId}:`, error);
      return false;
    }
  }

  async updateLastActivity(userId: string): Promise<void> {
    try {
      const user = await this.findById(userId);
      if (!user) {
        this.logger.error(`User not found: ${userId}`);
        return;
      }

      const updatedUser: User = {
        ...user,
        lastActivityAt: new Date(),
        updatedAt: new Date()
      };

      const key = UserKeys.primary(userId);
      await this.databaseService.set(key, JSON.stringify(updatedUser));
    } catch (error) {
      this.logger.error(`Failed to update last activity for user ${userId}:`, error);
    }
  }

  async getUserOrganization(discordId: string): Promise<{ organization: any; role: string } | null> {
    try {
      const user = await this.findByDiscordId(discordId);
      if (!user) {
        return null;
      }

      // Get organization memberships for user
      const membershipKey = OrganizationMemberKeys.byUser(user.id);
      const membershipResult = await this.databaseService.get(membershipKey);
      
      if (!membershipResult.success || !membershipResult.data) {
        return null;
      }
      
      const membership = JSON.parse(membershipResult.data) as OrganizationMember;
      
      if (!membership.isActive) {
        return null;
      }
      
      // Get organization details
      const orgKey = OrganizationKeys.primary(membership.organizationId);
      const orgResult = await this.databaseService.get(orgKey);
      
      if (!orgResult.success || !orgResult.data) {
        return null;
      }
      
      const organization = JSON.parse(orgResult.data) as Organization;
      
      return {
        organization: {
          id: organization.id,
          name: organization.name,
          slug: organization.slug,
          tier: organization.tier,
          status: organization.status,
        },
        role: membership.role,
      };
    } catch (error) {
      this.logger.error(`Failed to get user organization for ${discordId}:`, error);
      return null;
    }
  }
}