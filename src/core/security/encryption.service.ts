import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { createCipheriv, createDecipheriv, createHash, createHmac, randomBytes } from 'crypto';

@Injectable()
export class EncryptionService {
  private readonly logger = new Logger(EncryptionService.name);
  private readonly userEncryptionKey: string;
  private readonly sessionEncryptionKey: string;
  private readonly csrfEncryptionKey: string;

  constructor(private configService: ConfigService) {
    this.userEncryptionKey = this.configService.get<string>('USER_ENCRYPTION_KEY')!;
    this.sessionEncryptionKey = this.configService.get<string>('SESSION_ENCRYPTION_KEY')!;
    this.csrfEncryptionKey = this.configService.get<string>('CSRF_ENCRYPTION_KEY')!;

    if (!this.userEncryptionKey || !this.sessionEncryptionKey || !this.csrfEncryptionKey) {
      throw new Error('Encryption keys not configured properly');
    }
  }

  /**
   * Encrypts user-specific data using AES-256-GCM
   */
  encryptUserData(data: string): string {
    try {
      const iv = randomBytes(16);
      const key = createHash('sha256').update(this.userEncryptionKey).digest();
      const cipher = createCipheriv('aes-256-gcm', key, iv);
      cipher.setAAD(Buffer.from('user-data'));
      
      let encrypted = cipher.update(data, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      const authTag = cipher.getAuthTag();
      
      return iv.toString('hex') + ':' + authTag.toString('hex') + ':' + encrypted;
    } catch (error) {
      this.logger.error('Failed to encrypt user data', error);
      throw new Error('Encryption failed');
    }
  }

  /**
   * Decrypts user-specific data
   */
  decryptUserData(encryptedData: string): string {
    try {
      const parts = encryptedData.split(':');
      if (parts.length !== 3) {
        throw new Error('Invalid encrypted data format');
      }
      
      const iv = Buffer.from(parts[0], 'hex');
      const authTag = Buffer.from(parts[1], 'hex');
      const encrypted = parts[2];
      
      const key = createHash('sha256').update(this.userEncryptionKey).digest();
      const decipher = createDecipheriv('aes-256-gcm', key, iv);
      decipher.setAAD(Buffer.from('user-data'));
      decipher.setAuthTag(authTag);
      
      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      return decrypted;
    } catch (error) {
      this.logger.error('Failed to decrypt user data', error);
      throw new Error('Decryption failed');
    }
  }

  /**
   * Encrypts session data
   */
  encryptSessionData(data: string): string {
    try {
      const iv = randomBytes(16);
      const key = createHash('sha256').update(this.sessionEncryptionKey).digest();
      const cipher = createCipheriv('aes-256-gcm', key, iv);
      cipher.setAAD(Buffer.from('session-data'));
      
      let encrypted = cipher.update(data, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      const authTag = cipher.getAuthTag();
      
      return iv.toString('hex') + ':' + authTag.toString('hex') + ':' + encrypted;
    } catch (error) {
      this.logger.error('Failed to encrypt session data', error);
      throw new Error('Session encryption failed');
    }
  }

  /**
   * Decrypts session data
   */
  decryptSessionData(encryptedData: string): string {
    try {
      const parts = encryptedData.split(':');
      if (parts.length !== 3) {
        throw new Error('Invalid encrypted session data format');
      }
      
      const iv = Buffer.from(parts[0], 'hex');
      const authTag = Buffer.from(parts[1], 'hex');
      const encrypted = parts[2];
      
      const key = createHash('sha256').update(this.sessionEncryptionKey).digest();
      const decipher = createDecipheriv('aes-256-gcm', key, iv);
      decipher.setAAD(Buffer.from('session-data'));
      decipher.setAuthTag(authTag);
      
      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      return decrypted;
    } catch (error) {
      this.logger.error('Failed to decrypt session data', error);
      throw new Error('Session decryption failed');
    }
  }

  /**
   * Generates a secure device fingerprint
   */
  generateDeviceFingerprint(userAgent: string, ipAddress: string, additionalData?: string): string {
    const data = `${userAgent}|${ipAddress}|${additionalData || ''}`;
    return createHash('sha256').update(data).digest('hex');
  }

  /**
   * Generates CSRF token
   */
  generateCSRFToken(): string {
    const randomData = randomBytes(32).toString('hex');
    const timestamp = Date.now().toString();
    const data = `${randomData}|${timestamp}`;
    
    const hash = createHmac('sha256', this.csrfEncryptionKey)
      .update(data)
      .digest('hex');
    
    return `${randomData}.${timestamp}.${hash}`;
  }

  /**
   * Validates CSRF token
   */
  validateCSRFToken(token: string, maxAge: number = 3600000): boolean { // 1 hour default
    try {
      const parts = token.split('.');
      if (parts.length !== 3) {
        return false;
      }
      
      const [randomData, timestamp, hash] = parts;
      const tokenTime = parseInt(timestamp);
      
      // Check if token is expired
      if (Date.now() - tokenTime > maxAge) {
        return false;
      }
      
      // Verify hash
      const data = `${randomData}|${timestamp}`;
      const expectedHash = createHmac('sha256', this.csrfEncryptionKey)
        .update(data)
        .digest('hex');
      
      return hash === expectedHash;
    } catch (error) {
      this.logger.error('CSRF token validation failed', error);
      return false;
    }
  }

  /**
   * Generates a secure random session ID
   */
  generateSessionId(): string {
    return randomBytes(64).toString('hex');
  }

  /**
   * Hash sensitive data for storage
   */
  hashData(data: string, salt?: string): string {
    const actualSalt = salt || randomBytes(16).toString('hex');
    const hash = createHash('sha256').update(data + actualSalt).digest('hex');
    return `${actualSalt}:${hash}`;
  }

  /**
   * Verify hashed data
   */
  verifyHashedData(data: string, hashedData: string): boolean {
    try {
      const [salt, hash] = hashedData.split(':');
      const expectedHash = createHash('sha256').update(data + salt).digest('hex');
      return hash === expectedHash;
    } catch (error) {
      this.logger.error('Hash verification failed', error);
      return false;
    }
  }

  /**
   * Hash password for storage (alias for hashData)
   */
  async hashPassword(password: string): Promise<string> {
    return this.hashData(password);
  }

  /**
   * Verify password against hash (alias for verifyHashedData)
   */
  async verifyPassword(password: string, hash: string): Promise<boolean> {
    return this.verifyHashedData(password, hash);
  }

  /**
   * Generate secure token
   */
  generateSecureToken(length: number = 32): string {
    return randomBytes(length).toString('hex');
  }

  /**
   * Encrypt data with context
   */
  async encrypt(data: string, context?: string): Promise<string> {
    if (context === 'user') {
      return this.encryptUserData(data);
    } else if (context === 'session') {
      return this.encryptSessionData(data);
    } else {
      return this.encryptUserData(data); // Default to user encryption
    }
  }

  /**
   * Decrypt data with context
   */
  async decrypt(encryptedData: string, context?: string): Promise<string> {
    if (context === 'user') {
      return this.decryptUserData(encryptedData);
    } else if (context === 'session') {
      return this.decryptSessionData(encryptedData);
    } else {
      return this.decryptUserData(encryptedData); // Default to user decryption
    }
  }
}