import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export interface ValidationRule {
  field: string;
  type: 'string' | 'number' | 'email' | 'url' | 'uuid' | 'date' | 'boolean' | 'array' | 'object';
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  pattern?: string;
  allowedValues?: any[];
  customValidator?: (value: any) => boolean | string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

export interface SanitizationOptions {
  trimStrings?: boolean;
  removeHtml?: boolean;
  removeScripts?: boolean;
  normalizeEmail?: boolean;
  maxStringLength?: number;
}

@Injectable()
export class ValidationService {
  private readonly logger = new Logger(ValidationService.name);

  constructor(private readonly configService: ConfigService) {}

  /**
   * Validate data against rules
   */
  validate(data: Record<string, any>, rules: ValidationRule[]): ValidationResult {
    const errors: ValidationError[] = [];

    for (const rule of rules) {
      const value = data[rule.field];
      const fieldErrors = this.validateField(rule.field, value, rule);
      errors.push(...fieldErrors);
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Validate and throw if invalid
   */
  validateOrThrow(data: Record<string, any>, rules: ValidationRule[]): void {
    const result = this.validate(data, rules);
    if (!result.isValid) {
      const errorMessages = result.errors.map((error: any) => `${error.field}: ${error.message}`);
      throw new BadRequestException(`Validation failed: ${errorMessages.join(', ')}`);
    }
  }

  /**
   * Validate a single field
   */
  private validateField(fieldName: string, value: any, rule: ValidationRule): ValidationError[] {
    const errors: ValidationError[] = [];

    // Check required
    if (rule.required && (value === undefined || value === null || value === '')) {
      errors.push({
        field: fieldName,
        message: 'Field is required',
        value,
      });
      return errors; // Don't continue validation if required field is missing
    }

    // Skip validation if value is not provided and not required
    if (value === undefined || value === null) {
      return errors;
    }

    // Type validation
    const typeError = this.validateType(fieldName, value, rule.type);
    if (typeError) {
      errors.push(typeError);
      return errors; // Don't continue if type is wrong
    }

    // String validations
    if (rule.type === 'string' && typeof value === 'string') {
      if (rule.minLength !== undefined && value.length < rule.minLength) {
        errors.push({
          field: fieldName,
          message: `Minimum length is ${rule.minLength}`,
          value,
        });
      }

      if (rule.maxLength !== undefined && value.length > rule.maxLength) {
        errors.push({
          field: fieldName,
          message: `Maximum length is ${rule.maxLength}`,
          value,
        });
      }

      if (rule.pattern) {
        const regex = new RegExp(rule.pattern);
        if (!regex.test(value)) {
          errors.push({
            field: fieldName,
            message: `Value does not match required pattern`,
            value,
          });
        }
      }
    }

    // Number validations
    if (rule.type === 'number' && typeof value === 'number') {
      if (rule.min !== undefined && value < rule.min) {
        errors.push({
          field: fieldName,
          message: `Minimum value is ${rule.min}`,
          value,
        });
      }

      if (rule.max !== undefined && value > rule.max) {
        errors.push({
          field: fieldName,
          message: `Maximum value is ${rule.max}`,
          value,
        });
      }
    }

    // Array validations
    if (rule.type === 'array' && Array.isArray(value)) {
      if (rule.minLength !== undefined && value.length < rule.minLength) {
        errors.push({
          field: fieldName,
          message: `Array must have at least ${rule.minLength} items`,
          value,
        });
      }

      if (rule.maxLength !== undefined && value.length > rule.maxLength) {
        errors.push({
          field: fieldName,
          message: `Array must have at most ${rule.maxLength} items`,
          value,
        });
      }
    }

    // Allowed values validation
    if (rule.allowedValues && !rule.allowedValues.includes(value)) {
      errors.push({
        field: fieldName,
        message: `Value must be one of: ${rule.allowedValues.join(', ')}`,
        value,
      });
    }

    // Custom validator
    if (rule.customValidator) {
      const customResult = rule.customValidator(value);
      if (customResult !== true) {
        errors.push({
          field: fieldName,
          message: typeof customResult === 'string' ? customResult : 'Custom validation failed',
          value,
        });
      }
    }

    return errors;
  }

  /**
   * Validate field type
   */
  private validateType(fieldName: string, value: any, expectedType: string): ValidationError | null {
    switch (expectedType) {
      case 'string':
        if (typeof value !== 'string') {
          return { field: fieldName, message: 'Must be a string', value };
        }
        break;

      case 'number':
        if (typeof value !== 'number' || isNaN(value)) {
          return { field: fieldName, message: 'Must be a valid number', value };
        }
        break;

      case 'boolean':
        if (typeof value !== 'boolean') {
          return { field: fieldName, message: 'Must be a boolean', value };
        }
        break;

      case 'array':
        if (!Array.isArray(value)) {
          return { field: fieldName, message: 'Must be an array', value };
        }
        break;

      case 'object':
        if (typeof value !== 'object' || Array.isArray(value) || value === null) {
          return { field: fieldName, message: 'Must be an object', value };
        }
        break;

      case 'email':
        if (typeof value !== 'string' || !this.isValidEmail(value)) {
          return { field: fieldName, message: 'Must be a valid email address', value };
        }
        break;

      case 'url':
        if (typeof value !== 'string' || !this.isValidUrl(value)) {
          return { field: fieldName, message: 'Must be a valid URL', value };
        }
        break;

      case 'uuid':
        if (typeof value !== 'string' || !this.isValidUuid(value)) {
          return { field: fieldName, message: 'Must be a valid UUID', value };
        }
        break;

      case 'date':
        if (!(value instanceof Date) && !this.isValidDateString(value)) {
          return { field: fieldName, message: 'Must be a valid date', value };
        }
        break;
    }

    return null;
  }

  /**
   * Sanitize input data
   */
  sanitize(data: any, options: SanitizationOptions = {}): any {
    const defaultOptions: SanitizationOptions = {
      trimStrings: true,
      removeHtml: true,
      removeScripts: true,
      normalizeEmail: true,
      maxStringLength: 10000,
      ...options,
    };

    return this.sanitizeValue(data, defaultOptions);
  }

  /**
   * Sanitize a single value
   */
  private sanitizeValue(value: any, options: SanitizationOptions): any {
    if (typeof value === 'string') {
      let sanitized = value;

      // Trim whitespace
      if (options.trimStrings) {
        sanitized = sanitized.trim();
      }

      // Remove HTML tags
      if (options.removeHtml) {
        sanitized = sanitized.replace(/<[^>]*>/g, '');
      }

      // Remove script tags and javascript
      if (options.removeScripts) {
        sanitized = sanitized
          .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
          .replace(/javascript:/gi, '')
          .replace(/on\w+\s*=/gi, '');
      }

      // Normalize email
      if (options.normalizeEmail && this.isValidEmail(sanitized)) {
        sanitized = sanitized.toLowerCase();
      }

      // Limit string length
      if (options.maxStringLength && sanitized.length > options.maxStringLength) {
        sanitized = sanitized.substring(0, options.maxStringLength);
      }

      return sanitized;
    }

    if (Array.isArray(value)) {
      return value.map((item: any) => this.sanitizeValue(item, options));
    }

    if (typeof value === 'object' && value !== null) {
      const sanitized: any = {};
      for (const [key, val] of Object.entries(value)) {
        sanitized[key] = this.sanitizeValue(val, options);
      }
      return sanitized;
    }

    return value;
  }

  /**
   * Check if string is valid email
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Check if string is valid URL
   */
  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Check if string is valid UUID
   */
  private isValidUuid(uuid: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }

  /**
   * Check if string is valid date
   */
  private isValidDateString(dateString: any): boolean {
    if (typeof dateString !== 'string') return false;
    const date = new Date(dateString);
    return !isNaN(date.getTime());
  }

  /**
   * Create common validation rules
   */
  static createUserValidationRules(): ValidationRule[] {
    return [
      {
        field: 'email',
        type: 'email',
        required: true,
        maxLength: 255,
      },
      {
        field: 'username',
        type: 'string',
        required: true,
        minLength: 3,
        maxLength: 50,
        pattern: '^[a-zA-Z0-9_-]+$',
      },
      {
        field: 'password',
        type: 'string',
        required: true,
        minLength: 8,
        maxLength: 128,
        customValidator: (value: string) => {
          if (!/(?=.*[a-z])/.test(value)) return 'Password must contain at least one lowercase letter';
          if (!/(?=.*[A-Z])/.test(value)) return 'Password must contain at least one uppercase letter';
          if (!/(?=.*\d)/.test(value)) return 'Password must contain at least one number';
          if (!/(?=.*[@$!%*?&])/.test(value)) return 'Password must contain at least one special character';
          return true;
        },
      },
    ];
  }

  static createOrganizationValidationRules(): ValidationRule[] {
    return [
      {
        field: 'name',
        type: 'string',
        required: true,
        minLength: 2,
        maxLength: 100,
      },
      {
        field: 'description',
        type: 'string',
        required: false,
        maxLength: 500,
      },
      {
        field: 'website',
        type: 'url',
        required: false,
      },
      {
        field: 'contactEmail',
        type: 'email',
        required: true,
      },
    ];
  }
}
