import { Modu<PERSON> } from '@nestjs/common';
import { DatabaseModule } from '@/core/database';
import { BYOKService } from './services/byok.service';
import { ExaSearchService } from './services/exa-search.service';
import { MemberIsolationService } from './services/member-isolation.service';
import { PersonalAgentCoordinatorFactory } from './services/personal-agent-coordinator-factory';
import { PrivateMemoryService } from './services/private-memory.service';

@Module({
  imports: [DatabaseModule],
  providers: [
    BYOKService,
    ExaSearchService,
    MemberIsolationService,
    PersonalAgentCoordinatorFactory,
    PrivateMemoryService,
  ],
  exports: [
    BYOKService,
    ExaSearchService,
    MemberIsolationService,
    PersonalAgentCoordinatorFactory,
    PrivateMemoryService,
  ],
})
export class AgentsModule {}