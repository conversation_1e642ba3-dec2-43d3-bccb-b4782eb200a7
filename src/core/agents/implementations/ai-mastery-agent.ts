import { Injectable, Logger } from '@nestjs/common';
import { 
  PersonalAIAgent,
  AgentEvolutionStage,
  EvolutionMilestone
} from '../interfaces/member-isolation.interface';
import { 
  MemberContext, 
  PersonalizedResponse, 
  MemberFeedback, 
  CustomContent,
  MemoryUpdate,
  PersonalActionItem,
  MemberPreferences,
  ProgressData,
  LearningProfile,
  ConversationMemory,
  AgentType
} from '../interfaces/personal-agent.interface';
import { PrivateMemoryStore } from '../interfaces/private-memory.interface';

@Injectable()
export class AIMasteryAgent implements PersonalAIAgent {
  private readonly logger = new Logger(AIMasteryAgent.name);
  
  public memberId: string;
  public agentType = 'ai-mastery' as const;
  public personalMemory: PrivateMemoryStore;
  public learningProfile: LearningProfile;
  public conversationHistory: ConversationMemory;
  public createdAt: Date;
  public lastInteraction: Date;
  
  private evolutionStage: AgentEvolutionStage = {
    stage: 'learning',
    startDate: new Date(),
    milestones: [],
    nextStageRequirements: []
  };
  private userApiKey?: string;

  constructor(
    memberId: string,
    personalMemory: PrivateMemoryStore,
    userApiKey?: string
  ) {
    this.memberId = memberId;
    this.personalMemory = personalMemory;
    if (userApiKey !== undefined) {
      this.userApiKey = userApiKey;
    }
    this.createdAt = new Date();
    this.lastInteraction = new Date();
    
    // Initialize with proper types
    this.learningProfile = {
      preferredContentTypes: ['tutorial', 'example'],
      learningSpeed: 50,
      retentionRate: 50,
      engagementPatterns: [],
      adaptationHistory: []
    };
    
    this.conversationHistory = {
      recentTopics: [],
      ongoingProjects: [],
      pendingQuestions: [],
      memberMoods: [],
      contextSummary: ''
    };
    
    this.initializeEvolutionStage();
    this.initializeLearningProfile();
  }

  async processMessage(message: string, context: MemberContext): Promise<PersonalizedResponse> {
    try {
      this.logger.log(`Processing message for member ${this.memberId} (AI Mastery)`);
      
      // Update last interaction
      this.lastInteraction = new Date();
      
      // Retrieve personal context from memory
      const personalContext = await this.getPersonalContext();
      
      // Analyze member's intent and skill level
      const intent = await this.analyzeIntent(message, personalContext);
      const skillLevel = await this.assessCurrentSkillLevel(message, personalContext);
      
      // Generate personalized response based on member's history and preferences
      const response = await this.generatePersonalizedResponse(
        message,
        intent,
        skillLevel,
        context,
        personalContext
      );
      
      // Update memory with new interaction
      const memoryUpdates = await this.updateMemoryFromInteraction(
        message,
        response,
        intent,
        skillLevel
      );
      
      // Track learning progress
      await this.trackLearningProgress(intent, skillLevel);
      
      // Check for evolution milestones
      await this.checkEvolutionMilestones();
      
      return {
        content: response.content,
        adaptedTone: response.tone,
        followUpSuggestions: response.suggestions,
        actionItems: response.actionItems,
        memoryUpdates
      };
    } catch (error) {
      this.logger.error(`Failed to process message for member ${this.memberId}:`, error);
      
      return {
        content: "I'm having trouble processing your request right now. Let me learn from this and get better!",
        adaptedTone: 'apologetic',
        followUpSuggestions: ['Try rephrasing your question', 'Check back in a moment'],
        memoryUpdates: [{
          category: 'context',
          key: 'processing_errors',
          value: { error: error instanceof Error ? error.message : 'Unknown error', timestamp: new Date() },
          confidence: 100
        }]
      };
    }
  }

  async updatePersonalMemory(interaction: any): Promise<void> {
    try {
      // Store interaction in personal memory
      await this.personalMemory.store('recent_interactions', interaction);
      
      // Update conversation context
      const context = await this.personalMemory.retrieve('conversation_context') || {};
      context.recentTopics = context.recentTopics || [];
      context.recentTopics.push(interaction.topic);
      
      // Keep only last 20 topics
      if (context.recentTopics.length > 20) {
        context.recentTopics = context.recentTopics.slice(-20);
      }
      
      await this.personalMemory.store('conversation_context', context);
      
      this.logger.log(`Updated personal memory for member ${this.memberId}`);
    } catch (error) {
      this.logger.error(`Failed to update memory for member ${this.memberId}:`, error);
    }
  }

  async adaptToMemberStyle(feedback: MemberFeedback): Promise<void> {
    try {
      this.logger.log(`Adapting to member ${this.memberId} style based on feedback`);
      
      // Retrieve current preferences
      const preferences = await this.personalMemory.retrieve('preferences') || {};
      
      // Adapt based on feedback
      if (feedback.category === 'tone') {
        if (feedback.rating >= 4) {
          // Member likes current tone, reinforce it
          preferences.preferredTone = preferences.preferredTone || 'current';
          preferences.toneConfidence = (preferences.toneConfidence || 50) + 10;
        } else {
          // Member dislikes tone, adjust
          preferences.needsToneAdjustment = true;
          preferences.toneConfidence = (preferences.toneConfidence || 50) - 10;
        }
      }
      
      if (feedback.category === 'helpful') {
        preferences.helpfulnessScore = feedback.rating;
        if (feedback.rating >= 4) {
          preferences.currentApproachEffective = true;
        } else {
          preferences.needsApproachChange = true;
        }
      }
      
      // Update learning profile
      const learningProfile = await this.personalMemory.retrieve('learning_profile') || {};
      learningProfile.adaptationHistory = learningProfile.adaptationHistory || [];
      learningProfile.adaptationHistory.push({
        date: new Date(),
        change: `Adapted to feedback: ${feedback.category}`,
        reason: feedback.feedback,
        effectiveness: feedback.rating * 20 // Convert 1-5 to 0-100
      });
      
      // Store updated preferences and learning profile
      await this.personalMemory.store('preferences', preferences);
      await this.personalMemory.store('learning_profile', learningProfile);
      
      this.logger.log(`Successfully adapted to member ${this.memberId} feedback`);
    } catch (error) {
      this.logger.error(`Failed to adapt to feedback for member ${this.memberId}:`, error);
    }
  }

  async generatePersonalizedContent(): Promise<CustomContent> {
    try {
      const preferences = await this.personalMemory.retrieve('preferences') || {};
      const progress = await this.personalMemory.retrieve('progress') || {};
      const learningProfile = await this.personalMemory.retrieve('learning_profile') || {};
      
      // Determine content type based on member's learning style
      const contentType = this.determineOptimalContentType(learningProfile);
      
      // Generate content based on current skill level and goals
      const content = await this.createPersonalizedAIContent(
        progress.aiMasteryLevel || 0,
        preferences.goals || [],
        learningProfile.preferredContentTypes || []
      );
      
      return {
        type: contentType,
        title: content.title,
        content: content.body,
        difficulty: this.mapSkillLevelToDifficulty(progress.aiMasteryLevel || 0),
        estimatedTime: content.estimatedTime,
        personalizedFor: [
          'skill level',
          'learning style',
          'current goals',
          'interaction history'
        ]
      };
    } catch (error) {
      this.logger.error(`Failed to generate personalized content for member ${this.memberId}:`, error);
      throw error;
    }
  }

  async getPersonalizationLevel(): Promise<number> {
    try {
      const interactionCount = await this.getInteractionCount();
      const adaptationCount = await this.getAdaptationCount();
      const feedbackCount = await this.getFeedbackCount();
      
      // Calculate personalization level based on data richness
      let level = 0;
      
      // Base level from interactions (0-40 points)
      level += Math.min(40, interactionCount * 2);
      
      // Adaptation level (0-30 points)
      level += Math.min(30, adaptationCount * 5);
      
      // Feedback level (0-30 points)
      level += Math.min(30, feedbackCount * 10);
      
      return Math.min(100, level);
    } catch (error) {
      this.logger.error(`Failed to calculate personalization level for member ${this.memberId}:`, error);
      return 0;
    }
  }

  async exportPersonalData(): Promise<any> {
    try {
      return {
        memberId: this.memberId,
        agentType: this.agentType,
        evolutionStage: this.evolutionStage,
        personalMemory: {
          preferences: await this.personalMemory.retrieve('preferences'),
          progress: await this.personalMemory.retrieve('progress'),
          learningProfile: await this.personalMemory.retrieve('learning_profile'),
          conversationContext: await this.personalMemory.retrieve('conversation_context')
        },
        metadata: {
          createdAt: this.createdAt,
          lastInteraction: this.lastInteraction,
          personalizationLevel: await this.getPersonalizationLevel()
        }
      };
    } catch (error) {
      this.logger.error(`Failed to export data for member ${this.memberId}:`, error);
      throw error;
    }
  }

  private initializeEvolutionStage(): void {
    this.evolutionStage = {
      stage: 'basic',
      startDate: new Date(),
      milestones: [
        { milestone: 'First interaction', achieved: false, impact: 20 },
        { milestone: '10 successful responses', achieved: false, impact: 25 },
        { milestone: 'Positive feedback received', achieved: false, impact: 30 },
        { milestone: 'Personal preferences learned', achieved: false, impact: 25 }
      ],
      nextStageRequirements: [
        'Complete all basic milestones',
        'Achieve 70+ personalization level',
        'Receive 5+ pieces of feedback'
      ]
    };
  }

  private async initializeLearningProfile(): Promise<void> {
    this.learningProfile = {
      preferredContentTypes: ['tutorial', 'example'],
      learningSpeed: 50,
      retentionRate: 50,
      engagementPatterns: [],
      adaptationHistory: []
    };
    
    await this.personalMemory.store('learning_profile', this.learningProfile);
  }

  private async getPersonalContext(): Promise<{
    preferences: MemberPreferences;
    progress: ProgressData;
    learningProfile: LearningProfile;
    context: ConversationMemory;
  }> {
    return {
      preferences: await this.personalMemory.retrieve('preferences') || {} as MemberPreferences,
      progress: await this.personalMemory.retrieve('progress') || {} as ProgressData,
      learningProfile: await this.personalMemory.retrieve('learning_profile') || {} as LearningProfile,
      context: await this.personalMemory.retrieve('conversation_context') || {} as ConversationMemory
    };
  }

  private async analyzeIntent(message: string, personalContext: {
    preferences: MemberPreferences;
    progress: ProgressData;
    learningProfile: LearningProfile;
    context: ConversationMemory;
  }): Promise<string> {
    const lowerMessage = message.toLowerCase();
    
    // Analyze based on personal context and message content
    if (lowerMessage.includes('learn') || lowerMessage.includes('tutorial')) {
      return 'learning';
    }
    if (lowerMessage.includes('help') || lowerMessage.includes('how')) {
      return 'assistance';
    }
    if (lowerMessage.includes('automat') || lowerMessage.includes('workflow')) {
      return 'automation';
    }
    if (lowerMessage.includes('code') || lowerMessage.includes('script')) {
      return 'coding';
    }
    
    return 'general';
  }

  private async assessCurrentSkillLevel(message: string, personalContext: {
    preferences: MemberPreferences;
    progress: ProgressData;
    learningProfile: LearningProfile;
    context: ConversationMemory;
  }): Promise<number> {
    const currentLevel = personalContext.progress?.aiMasteryLevel || 0;
    
    // Assess based on message complexity and personal history
    let adjustment = 0;
    
    if (message.includes('advanced') || message.includes('complex')) {
      adjustment += 10;
    }
    if (message.includes('beginner') || message.includes('basic')) {
      adjustment -= 10;
    }
    
    return Math.max(0, Math.min(100, currentLevel + adjustment));
  }

  private async generatePersonalizedResponse(
    message: string,
    intent: string,
    skillLevel: number,
    context: MemberContext,
    personalContext: {
      preferences: MemberPreferences;
      progress: ProgressData;
      learningProfile: LearningProfile;
      context: ConversationMemory;
    }
  ): Promise<{
    content: string;
    tone: string;
    suggestions: string[];
    actionItems: PersonalActionItem[];
  }> {
    // Use member's API key for AI generation if available
    if (this.userApiKey) {
      return await this.generateWithUserAPI(message, intent, skillLevel, personalContext);
    }
    
    // Fallback to template-based personalized response
    return this.generateTemplateResponse(message, intent, skillLevel, personalContext);
  }

  private async generateWithUserAPI(
    message: string,
    intent: string,
    skillLevel: number,
    personalContext: {
      preferences: MemberPreferences;
      progress: ProgressData;
      learningProfile: LearningProfile;
      context: ConversationMemory;
    }
  ): Promise<{
    content: string;
    tone: string;
    suggestions: string[];
    actionItems: PersonalActionItem[];
  }> {
    // This would use the member's API key to generate personalized responses
    // Implementation would call OpenAI/Anthropic with member's key
    
    const personalizedPrompt = this.buildPersonalizedPrompt(
      message,
      intent,
      skillLevel,
      personalContext
    );
    
    // Mock response for now - real implementation would call AI API
    return {
      content: `Based on your AI mastery level (${skillLevel}/100) and our previous conversations, here's my personalized response to "${message}"...`,
      tone: personalContext.preferences?.communicationStyle || 'professional',
      suggestions: [
        'Continue with advanced topics',
        'Practice with real examples',
        'Build a small project'
      ],
      actionItems: [{
        type: 'skill_practice',
        description: 'Practice the discussed concept',
        priority: 'medium'
      }]
    };
  }

  private generateTemplateResponse(
    message: string,
    intent: string,
    skillLevel: number,
    personalContext: {
      preferences: MemberPreferences;
      progress: ProgressData;
      learningProfile: LearningProfile;
      context: ConversationMemory;
    }
  ): {
    content: string;
    tone: string;
    suggestions: string[];
    actionItems: PersonalActionItem[];
  } {
    const responses: Record<string, string> = {
      learning: `I remember you're at skill level ${skillLevel}. Let me give you a ${skillLevel < 30 ? 'beginner-friendly' : skillLevel < 70 ? 'intermediate' : 'advanced'} explanation...`,
      assistance: `Based on our previous conversations, I think you'll find this approach helpful...`,
      automation: `Given your interest in automation (which I remember from our chats), here's what I recommend...`,
      coding: `Since you've been working on coding projects, let's build on what you already know...`,
      general: `Hi again! I'm excited to continue helping you with your AI journey...`
    };
    
    return {
      content: (intent in responses) ? responses[intent]! : responses.general!,
      tone: personalContext.preferences?.communicationStyle || 'casual',
      suggestions: this.generatePersonalizedSuggestions(intent, skillLevel),
      actionItems: this.generatePersonalizedActionItems(intent, skillLevel)
    };
  }

  private buildPersonalizedPrompt(
    message: string,
    intent: string,
    skillLevel: number,
    personalContext: {
      preferences: MemberPreferences;
      progress: ProgressData;
      learningProfile: LearningProfile;
      context: ConversationMemory;
    }
  ): string {
    return `You are a personal AI Mastery assistant for a member with skill level ${skillLevel}/100.
    
Member's preferences: ${JSON.stringify(personalContext.preferences)}
Recent topics: ${personalContext.context?.recentTopics?.join(', ') || 'None'}
Learning style: ${personalContext.learningProfile?.preferredContentTypes?.join(', ') || 'General'}

Member asks: "${message}"

Provide a highly personalized response that builds on their previous learning and matches their communication style.`;
  }

  private generatePersonalizedSuggestions(intent: string, skillLevel: number): string[] {
    const suggestions: Record<string, string[]> = {
      learning: skillLevel < 50 ? 
        ['Start with basics', 'Try hands-on exercises', 'Join beginner community'] :
        ['Explore advanced concepts', 'Build real projects', 'Teach others'],
      assistance: ['Break it into steps', 'Try it yourself first', 'Ask follow-up questions'],
      automation: ['Start small', 'Document your process', 'Test thoroughly'],
      coding: ['Write clean code', 'Add comments', 'Test your code'],
      general: ['Explore your interests', 'Set learning goals', 'Track progress']
    };
    
    return (intent in suggestions) ? suggestions[intent]! : suggestions.general!;
  }

  private generatePersonalizedActionItems(intent: string, skillLevel: number): PersonalActionItem[] {
    return [{
      type: 'skill_practice',
      description: `Practice ${intent} concepts at your ${skillLevel < 50 ? 'beginner' : 'advanced'} level`,
      priority: 'medium'
    }];
  }

  private async updateMemoryFromInteraction(
    message: string,
    response: any,
    intent: string,
    skillLevel: number
  ): Promise<MemoryUpdate[]> {
    const updates: MemoryUpdate[] = [];
    
    // Update skill level if learning occurred
    if (intent === 'learning') {
      updates.push({
        category: 'progress',
        key: 'aiMasteryLevel',
        value: Math.min(100, skillLevel + 1),
        confidence: 80
      });
    }
    
    // Update context
    updates.push({
      category: 'context',
      key: 'lastInteraction',
      value: {
        message,
        response: response.content,
        intent,
        timestamp: new Date()
      },
      confidence: 100
    });
    
    return updates;
  }

  private async trackLearningProgress(intent: string, skillLevel: number): Promise<void> {
    const progress = await this.personalMemory.retrieve('progress') || {};
    
    if (intent === 'learning') {
      progress.learningEvents = (progress.learningEvents || 0) + 1;
      progress.aiMasteryLevel = Math.min(100, (progress.aiMasteryLevel || 0) + 0.5);
    }
    
    await this.personalMemory.store('progress', progress);
  }

  private async checkEvolutionMilestones(): Promise<void> {
    const interactionCount = await this.getInteractionCount();
    
    // Check milestone achievements
    const firstMilestone = this.evolutionStage.milestones[0];
    if (interactionCount >= 1 && firstMilestone && !firstMilestone.achieved) {
      firstMilestone.achieved = true;
      firstMilestone.achievedAt = new Date();
    }
    
    const secondMilestone = this.evolutionStage.milestones[1];
    if (interactionCount >= 10 && secondMilestone && !secondMilestone.achieved) {
      secondMilestone.achieved = true;
      secondMilestone.achievedAt = new Date();
    }
    
    // Check for stage progression
    const achievedMilestones = this.evolutionStage.milestones.filter((m: any) => m.achieved).length;
    const personalizationLevel = await this.getPersonalizationLevel();
    
    if (achievedMilestones >= 3 && personalizationLevel >= 70 && this.evolutionStage.stage === 'basic') {
      this.evolutionStage.stage = 'learning';
      this.logger.log(`Member ${this.memberId} AI Mastery agent evolved to 'learning' stage`);
    }
  }

  private determineOptimalContentType(learningProfile: any): 'tutorial' | 'strategy' | 'exercise' | 'assessment' {
    const preferences = learningProfile.preferredContentTypes || [];
    
    if (preferences.includes('tutorial')) return 'tutorial';
    if (preferences.includes('exercise')) return 'exercise';
    if (preferences.includes('assessment')) return 'assessment';
    
    return 'tutorial'; // Default
  }

  private async createPersonalizedAIContent(
    skillLevel: number,
    goals: string[],
    preferredTypes: string[]
  ): Promise<{ title: string; body: string; estimatedTime: number }> {
    const difficulty = this.mapSkillLevelToDifficulty(skillLevel);
    
    return {
      title: `Personalized AI ${difficulty} Guide`,
      body: `Based on your skill level (${skillLevel}/100) and goals (${goals.join(', ')}), here's your personalized content...`,
      estimatedTime: skillLevel < 30 ? 15 : skillLevel < 70 ? 25 : 45
    };
  }

  private mapSkillLevelToDifficulty(skillLevel: number): 'beginner' | 'intermediate' | 'advanced' {
    if (skillLevel < 30) return 'beginner';
    if (skillLevel < 70) return 'intermediate';
    return 'advanced';
  }

  private async getInteractionCount(): Promise<number> {
    const context = await this.personalMemory.retrieve('conversation_context') || {};
    return context.recentTopics?.length || 0;
  }

  private async getAdaptationCount(): Promise<number> {
    const learningProfile = await this.personalMemory.retrieve('learning_profile') || {};
    return learningProfile.adaptationHistory?.length || 0;
  }

  private async getFeedbackCount(): Promise<number> {
    const progress = await this.personalMemory.retrieve('progress') || {};
    return progress.feedbackCount || 0;
  }
}