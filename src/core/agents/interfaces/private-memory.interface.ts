import { 
  MemberGoal, 
  MemberPreferences, 
  ProgressData, 
  ConversationMemory, 
  LearningProfile,
  AgentType 
} from './personal-agent.interface';

export interface PrivateMemoryStore {
  memberId: string;
  agentType: AgentType;
  encrypted: boolean;
  isolated: boolean;
  
  // Core member data
  goals: MemberGoal[];
  preferences: MemberPreferences;
  progress: ProgressData;
  conversationContext: ConversationMemory;
  learningProfile: LearningProfile;
  
  // Memory operations (all isolated to this member)
  store(key: string, data: any): Promise<void>;
  retrieve(key: string): Promise<any>;
  update(key: string, data: any): Promise<void>;
  delete(key: string): Promise<void>;
  
  // Additional operations
  encrypt(data: any): Promise<string>;
  storeInPartition(key: string, data: any): Promise<void>;
  retrieveFromPartition(key: string): Promise<any>;
  decrypt(encryptedData: string): Promise<any>;
  deleteFromPartition(key: string): Promise<void>;
  enforceAccessControls(): Promise<void>;
  
  // Isolation guarantees
  verifyIsolation(): Promise<boolean>;
  getAccessibleMembers(): Promise<string[]>; // Should always return [memberId]
  preventCrossAccess(): Promise<void>;
}

export interface MemoryPartition {
  partitionId: string;
  memberId: string;
  encryptionKey: string;
  isolationLevel: 'complete' | 'partial';
  accessLog: MemoryAccessLog[];
}

export interface MemoryAccessLog {
  timestamp: Date;
  operation: 'read' | 'write' | 'update' | 'delete';
  key: string;
  agentType: AgentType;
  success: boolean;
  isolationVerified: boolean;
}

export interface IsolatedConversation {
  conversationId: string;
  memberId: string;
  agentType: AgentType;
  messages: IsolatedMessage[];
  context: ConversationContext;
  privacy: PrivacySettings;
}

export interface IsolatedMessage {
  messageId: string;
  timestamp: Date;
  content: string;
  role: 'member' | 'agent';
  agentType: AgentType;
  processingContext: ProcessingContext;
}

export interface ConversationContext {
  topic: string;
  mood: string;
  intent: string;
  previousContext: string;
  relatedGoals: string[];
}

export interface ProcessingContext {
  apiKeyUsed: string; // Hashed reference to member's API key
  modelUsed: string;
  tokenCount: number;
  processingTime: number;
  cost: number;
}

export interface PrivacySettings {
  dataRetention: number; // days
  shareWithOtherAgents: boolean; // within same member only
  encryptionLevel: 'standard' | 'enhanced';
  accessRestrictions: AccessRestriction[];
}

export interface AccessRestriction {
  type: 'time_based' | 'context_based' | 'tier_based';
  rule: string;
  active: boolean;
}

export interface MemberMemorySnapshot {
  memberId: string;
  snapshotDate: Date;
  totalMemorySize: number;
  agentMemories: AgentMemorySnapshot[];
  isolationStatus: IsolationStatus;
}

export interface AgentMemorySnapshot {
  agentType: AgentType;
  memorySize: number;
  lastAccessed: Date;
  keyMetrics: MemoryMetrics;
}

export interface MemoryMetrics {
  conversationCount: number;
  goalCount: number;
  learningEvents: number;
  adaptationCount: number;
  averageResponseRelevance: number;
}

export interface IsolationStatus {
  fullyIsolated: boolean;
  lastVerification: Date;
  isolationScore: number; // 0-100
  potentialLeaks: SecurityLeak[];
  recommendations: string[];
}

export interface SecurityLeak {
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  affectedData: string[];
  detectedAt: Date;
  resolved: boolean;
}

export interface MemoryFirewall {
  memberId: string;
  rules: FirewallRule[];
  status: 'active' | 'inactive';
  lastUpdated: Date;
}

export interface FirewallRule {
  id: string;
  type: 'block_cross_member' | 'encrypt_sensitive' | 'audit_access';
  condition: string;
  action: string;
  priority: number;
}

export interface IPrivateMemoryService {
  createMemberMemory(memberId: string): Promise<PrivateMemoryStore>;
  getMemberMemory(memberId: string, agentType: AgentType): Promise<PrivateMemoryStore>;
  isolateMemory(memberId: string): Promise<void>;
  verifyIsolation(memberId: string): Promise<IsolationStatus>;
  destroyMemberMemory(memberId: string): Promise<void>;
  auditMemoryAccess(memberId: string): Promise<MemoryAccessLog[]>;
  enforcePrivacy(memberId: string, settings: PrivacySettings): Promise<void>;
}