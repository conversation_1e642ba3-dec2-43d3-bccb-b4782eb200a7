import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  Call<PERSON><PERSON><PERSON>,
  Logger,
} from '@nestjs/common';
import { Observable, of } from 'rxjs';
import { tap } from 'rxjs/operators';
import { Reflector } from '@nestjs/core';
import { CacheService } from '../cache.service';

export const CACHE_KEY_METADATA = 'cache_key';
export const CACHE_TTL_METADATA = 'cache_ttl';

export const CacheKey = (key: string) => 
  Reflect.metadata(CACHE_KEY_METADATA, key);

export const CacheTTL = (ttl: number) => 
  Reflect.metadata(CACHE_TTL_METADATA, ttl);

@Injectable()
export class CacheInterceptor implements NestInterceptor {
  private readonly logger = new Logger(CacheInterceptor.name);

  constructor(
    private readonly cacheService: CacheService,
    private readonly reflector: Reflector,
  ) {}

  async intercept(
    context: ExecutionContext,
    next: <PERSON><PERSON><PERSON><PERSON>,
  ): Promise<Observable<any>> {
    const cacheKey = this.reflector.get<string>(
      CACHE_KEY_METADATA,
      context.getHandler(),
    );

    if (!cacheKey) {
      return next.handle();
    }

    const ttl = this.reflector.get<number>(
      CACHE_TTL_METADATA,
      context.getHandler(),
    ) || 300; // 5 minutes default

    // Generate dynamic cache key based on request parameters
    const dynamicKey = this.generateCacheKey(context, cacheKey);

    try {
      // Try to get from cache
      const cachedResult = await this.cacheService.get(dynamicKey);
      
      if (cachedResult !== null) {
        this.logger.debug(`Cache hit for key: ${dynamicKey}`);
        return of(cachedResult);
      }

      this.logger.debug(`Cache miss for key: ${dynamicKey}`);

      // Execute the method and cache the result
      return next.handle().pipe(
        tap(async (result) => {
          if (result !== null && result !== undefined) {
            await this.cacheService.set(dynamicKey, result, ttl);
            this.logger.debug(`Cached result for key: ${dynamicKey}`);
          }
        }),
      );
    } catch (error) {
      this.logger.error(`Cache error for key ${dynamicKey}:`, error);
      return next.handle();
    }
  }

  private generateCacheKey(context: ExecutionContext, baseKey: string): string {
    const request = context.switchToHttp().getRequest();
    const { params, query, user } = request;

    // Build cache key with parameters
    const keyParts = [baseKey];

    if (params && Object.keys(params).length > 0) {
      keyParts.push(`params:${JSON.stringify(params)}`);
    }

    if (query && Object.keys(query).length > 0) {
      keyParts.push(`query:${JSON.stringify(query)}`);
    }

    if (user?.id) {
      keyParts.push(`user:${user.id}`);
    }

    return keyParts.join(':');
  }
}
