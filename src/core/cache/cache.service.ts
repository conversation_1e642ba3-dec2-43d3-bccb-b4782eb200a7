import { Injectable, Logger, Inject } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { ConfigService } from '@nestjs/config';
import { Cache } from 'cache-manager';
import { MetricsService } from '../monitoring/metrics.service';
import { CacheConfig } from '../../common/interfaces/enterprise.interfaces';

@Injectable()
export class CacheService {
  private readonly logger = new Logger(CacheService.name);
  private readonly defaultTtl: number;
  private readonly cacheStats = new Map<string, { hits: number; misses: number; sets: number }>();

  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    private configService: ConfigService,
    private metricsService: MetricsService,
  ) {
    this.defaultTtl = this.configService.get<number>('CACHE_TTL', 300);
  }

  // Basic cache operations
  async get<T>(key: string, namespace?: string): Promise<T | null> {
    const fullKey = this.buildKey(key, namespace);
    const startTime = Date.now();
    
    try {
      const value = await this.cacheManager.get<T>(fullKey);
      const duration = Date.now() - startTime;
      
      if (value !== null && value !== undefined) {
        this.recordCacheHit(namespace || 'default');
        this.metricsService.incrementCacheOperation('get', namespace || 'default', 'hit');
        this.logger.debug(`Cache hit for key: ${fullKey} (${duration}ms)`);
        return value;
      } else {
        this.recordCacheMiss(namespace || 'default');
        this.metricsService.incrementCacheOperation('get', namespace || 'default', 'miss');
        this.logger.debug(`Cache miss for key: ${fullKey} (${duration}ms)`);
        return null;
      }
    } catch (error) {
      this.logger.error(`Cache get error for key: ${fullKey}`, error);
      this.metricsService.incrementCacheOperation('get', namespace || 'default', 'error');
      return null;
    }
  }

  async set<T>(key: string, value: T, ttl?: number, namespace?: string): Promise<void> {
    const fullKey = this.buildKey(key, namespace);
    const cacheTtl = ttl || this.defaultTtl;
    const startTime = Date.now();
    
    try {
      await this.cacheManager.set(fullKey, value, cacheTtl * 1000); // Convert to milliseconds
      const duration = Date.now() - startTime;
      
      this.recordCacheSet(namespace || 'default');
      this.metricsService.incrementCacheOperation('set', namespace || 'default', 'success');
      this.logger.debug(`Cache set for key: ${fullKey}, TTL: ${cacheTtl}s (${duration}ms)`);
    } catch (error) {
      this.logger.error(`Cache set error for key: ${fullKey}`, error);
      this.metricsService.incrementCacheOperation('set', namespace || 'default', 'error');
      throw error;
    }
  }

  async del(key: string, namespace?: string): Promise<void> {
    const fullKey = this.buildKey(key, namespace);
    const startTime = Date.now();
    
    try {
      await this.cacheManager.del(fullKey);
      const duration = Date.now() - startTime;
      
      this.metricsService.incrementCacheOperation('del', namespace || 'default', 'success');
      this.logger.debug(`Cache delete for key: ${fullKey} (${duration}ms)`);
    } catch (error) {
      this.logger.error(`Cache delete error for key: ${fullKey}`, error);
      this.metricsService.incrementCacheOperation('del', namespace || 'default', 'error');
      throw error;
    }
  }

  // Advanced cache operations
  async getOrSet<T>(
    key: string,
    factory: () => Promise<T>,
    ttl?: number,
    namespace?: string
  ): Promise<T> {
    const cached = await this.get<T>(key, namespace);
    
    if (cached !== null) {
      return cached;
    }

    const value = await factory();
    await this.set(key, value, ttl, namespace);
    return value;
  }

  async mget<T>(keys: string[], namespace?: string): Promise<(T | null)[]> {
    const promises = keys.map((key: any) => this.get<T>(key, namespace));
    return Promise.all(promises);
  }

  async mset<T>(entries: Array<{ key: string; value: T; ttl?: number }>, namespace?: string): Promise<void> {
    const promises = entries.map((entry: any) => 
      this.set(entry.key, entry.value, entry.ttl, namespace)
    );
    await Promise.all(promises);
  }

  async mdel(keys: string[], namespace?: string): Promise<void> {
    const promises = keys.map((key: any) => this.del(key, namespace));
    await Promise.all(promises);
  }

  // Pattern-based operations
  async delPattern(pattern: string, namespace?: string): Promise<number> {
    const fullPattern = this.buildKey(pattern, namespace);
    
    try {
      // Note: This is a simplified implementation
      // In a real Redis implementation, you'd use SCAN with pattern matching
      this.logger.warn(`Pattern deletion not fully implemented for pattern: ${fullPattern}`);
      return 0;
    } catch (error) {
      this.logger.error(`Cache pattern delete error for pattern: ${fullPattern}`, error);
      throw error;
    }
  }

  // Cache warming
  async warmCache(entries: Array<{ key: string; factory: () => Promise<any>; ttl?: number }>, namespace?: string): Promise<void> {
    this.logger.log(`Warming cache with ${entries.length} entries`);
    
    const promises = entries.map(async entry => {
      try {
        const value = await entry.factory();
        await this.set(entry.key, value, entry.ttl, namespace);
      } catch (error) {
        this.logger.error(`Cache warming failed for key: ${entry.key}`, error);
      }
    });

    await Promise.allSettled(promises);
    this.logger.log('Cache warming completed');
  }

  // Organization-specific caching
  async getForOrganization<T>(key: string, organizationId: string, ttl?: number): Promise<T | null> {
    return this.get<T>(key, `org:${organizationId}`);
  }

  async setForOrganization<T>(key: string, value: T, organizationId: string, ttl?: number): Promise<void> {
    return this.set(key, value, ttl, `org:${organizationId}`);
  }

  async delForOrganization(key: string, organizationId: string): Promise<void> {
    return this.del(key, `org:${organizationId}`);
  }

  async clearOrganizationCache(organizationId: string): Promise<void> {
    await this.delPattern('*', `org:${organizationId}`);
    this.logger.log(`Cleared cache for organization: ${organizationId}`);
  }

  // User-specific caching
  async getForUser<T>(key: string, userId: string, ttl?: number): Promise<T | null> {
    return this.get<T>(key, `user:${userId}`);
  }

  async setForUser<T>(key: string, value: T, userId: string, ttl?: number): Promise<void> {
    return this.set(key, value, ttl, `user:${userId}`);
  }

  async delForUser(key: string, userId: string): Promise<void> {
    return this.del(key, `user:${userId}`);
  }

  async clearUserCache(userId: string): Promise<void> {
    await this.delPattern('*', `user:${userId}`);
    this.logger.log(`Cleared cache for user: ${userId}`);
  }

  // Guild-specific caching
  async getForGuild<T>(key: string, guildId: string, ttl?: number): Promise<T | null> {
    return this.get<T>(key, `guild:${guildId}`);
  }

  async setForGuild<T>(key: string, value: T, guildId: string, ttl?: number): Promise<void> {
    return this.set(key, value, ttl, `guild:${guildId}`);
  }

  async delForGuild(key: string, guildId: string): Promise<void> {
    return this.del(key, `guild:${guildId}`);
  }

  async clearGuildCache(guildId: string): Promise<void> {
    await this.delPattern('*', `guild:${guildId}`);
    this.logger.log(`Cleared cache for guild: ${guildId}`);
  }

  // Cache statistics and monitoring
  getCacheStats(namespace?: string): { hits: number; misses: number; sets: number; hitRatio: number } {
    const key = namespace || 'default';
    const stats = this.cacheStats.get(key) || { hits: 0, misses: 0, sets: 0 };
    const total = stats.hits + stats.misses;
    const hitRatio = total > 0 ? stats.hits / total : 0;
    
    return { ...stats, hitRatio };
  }

  getAllCacheStats(): Record<string, { hits: number; misses: number; sets: number; hitRatio: number }> {
    const result: Record<string, any> = {};
    
    for (const [namespace, stats] of this.cacheStats.entries()) {
      const total = stats.hits + stats.misses;
      const hitRatio = total > 0 ? stats.hits / total : 0;
      result[namespace] = { ...stats, hitRatio };
    }
    
    return result;
  }

  resetStats(namespace?: string): void {
    if (namespace) {
      this.cacheStats.delete(namespace);
    } else {
      this.cacheStats.clear();
    }
  }

  // Health check
  async isHealthy(): Promise<boolean> {
    try {
      const testKey = 'health_check';
      const testValue = Date.now().toString();
      
      await this.set(testKey, testValue, 10);
      const retrieved = await this.get<string>(testKey);
      await this.del(testKey);
      
      return retrieved === testValue;
    } catch (error) {
      this.logger.error('Cache health check failed', error);
      return false;
    }
  }

  // Private helper methods
  private buildKey(key: string, namespace?: string): string {
    const prefix = this.configService.get<string>('CACHE_PREFIX', 'discord_bot');
    return namespace ? `${prefix}:${namespace}:${key}` : `${prefix}:${key}`;
  }

  private recordCacheHit(namespace: string): void {
    const stats = this.cacheStats.get(namespace) || { hits: 0, misses: 0, sets: 0 };
    stats.hits++;
    this.cacheStats.set(namespace, stats);
    this.updateCacheMetrics(namespace, stats);
  }

  private recordCacheMiss(namespace: string): void {
    const stats = this.cacheStats.get(namespace) || { hits: 0, misses: 0, sets: 0 };
    stats.misses++;
    this.cacheStats.set(namespace, stats);
    this.updateCacheMetrics(namespace, stats);
  }

  private recordCacheSet(namespace: string): void {
    const stats = this.cacheStats.get(namespace) || { hits: 0, misses: 0, sets: 0 };
    stats.sets++;
    this.cacheStats.set(namespace, stats);
    this.updateCacheMetrics(namespace, stats);
  }

  private updateCacheMetrics(namespace: string, stats: { hits: number; misses: number; sets: number }): void {
    const total = stats.hits + stats.misses;
    const hitRatio = total > 0 ? stats.hits / total : 0;
    this.metricsService.updateCacheHitRatio(namespace, hitRatio);
  }
}
