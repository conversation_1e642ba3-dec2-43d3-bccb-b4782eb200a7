import { CacheModule as NestCacheModule } from '@nestjs/cache-manager';
import { Global, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import * as redisStore from 'cache-manager-redis-store';

import { CacheInvalidationService } from './cache-invalidation.service';
import { CacheService } from './cache.service';
import { DistributedCacheService } from './distributed-cache.service';
import { CacheInterceptor } from './interceptors/cache.interceptor';

@Global()
@Module({
  imports: [
    ConfigModule,
    NestCacheModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        const redisUrl = configService.get<string>('REDIS_URL');
        
        if (redisUrl) {
          // Use Redis for distributed caching
          return {
            store: redisStore as any,
            url: redisUrl,
            ttl: configService.get<number>('CACHE_TTL', 300),
            max: configService.get<number>('CACHE_MAX_ITEMS', 1000),
            isGlobal: true,
          } as any;
        } else {
          // Fallback to in-memory cache
          return {
            ttl: configService.get<number>('CACHE_TTL', 300),
            max: configService.get<number>('CACHE_MAX_ITEMS', 1000),
            isGlobal: true,
          };
        }
      },
      inject: [ConfigService],
    }),
  ],
  providers: [
    CacheService,
    DistributedCacheService,
    CacheInterceptor,
    CacheInvalidationService,
  ],
  exports: [
    CacheService,
    DistributedCacheService,
    CacheInterceptor,
    CacheInvalidationService,
  ],
})
export class CacheModule {}
