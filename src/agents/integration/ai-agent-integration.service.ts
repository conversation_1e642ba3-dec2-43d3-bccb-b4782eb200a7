import { Injectable, Logger, OnModuleInit, Optional, Inject } from '@nestjs/common';
import { Client, Message, Interaction, ButtonInteraction } from 'discord.js';
import { Context, On, SlashCommand, SlashCommandContext } from 'necord';
import { AgentsService } from '../agents.service';
import { SchedulerService } from './scheduler.service';
import { ChannelRoutingService } from './channel-routing.service';
import { PersonalGrowthCoach } from '../types/personal-growth-coach';
import { IntakeSpecialist } from '../types/intake-specialist';
import { ProgressTracker } from '../types/progress-tracker';
import { PrivateChatManagerService } from '../../features/ai-channel/private-chat-manager.service';
import { AIChannelPanelService } from '../../features/ai-channel/ai-channel-panel.service';
import { ChannelFilterService } from '../../core/services/channel-filter.service';

@Injectable()
export class AIAgentIntegrationService implements OnModuleInit {
  private readonly logger = new Logger(AIAgentIntegrationService.name);

  constructor(
    @Optional() @Inject(Client) private readonly client: Client | null,
    private readonly agentsService: AgentsService,
    private readonly schedulerService: SchedulerService,
    private readonly channelRoutingService: ChannelRoutingService,
    private readonly personalGrowthCoach: PersonalGrowthCoach,
    private readonly intakeSpecialist: IntakeSpecialist,
    private readonly progressTracker: ProgressTracker,
    private readonly channelFilterService: ChannelFilterService,
    @Optional() private readonly privateChatManager?: PrivateChatManagerService,
    @Optional() private readonly aiChannelPanel?: AIChannelPanelService,
  ) {}

  async onModuleInit() {
    this.logger.log('🤖 Initializing AI Agent Integration Service...');
    
    try {
      // Initialize scheduler for proactive messaging
      await this.schedulerService.initialize();
      
      // Initialize channel routing
      await this.channelRoutingService.initialize();
      
      this.logger.log('✅ AI Agent Integration Service initialized successfully');
      this.logger.log('🎯 Available AI agents: Personal Growth Coach, Intake Specialist, Progress Tracker');
    } catch (error) {
      this.logger.error('❌ Failed to initialize AI Agent Integration Service:', error);
      throw error;
    }
  }

  @On('messageCreate')
  async handleMessage(@Context() [message]: [Message]) {
    if (!this.client) return;

    // Use centralized channel filtering service
    if (!this.channelFilterService.shouldProcessMessage(message)) {
      return;
    }

    try {
      // Check if this is a private thread message (priority handling)
      if (message.channel.isThread() && message.channel.type === 12) { // PrivateThread
        await this.handlePrivateThreadMessage(message);
        return;
      }

      // Check if this is an AI agent channel or DM
      const shouldProcess = await this.shouldProcessMessage(message);
      if (!shouldProcess) return;

      // Route message to appropriate agent
      const agentType = await this.channelRoutingService.routeMessage(message);
      if (!agentType) return;

      // Process with the determined agent
      await this.processWithAgent(message, agentType);

      // Log the interaction
      await this.agentsService.logInteraction({
        userId: message.author.id,
        agentType: agentType as 'personal_growth_coach' | 'intake_specialist' | 'progress_tracker' | 'general',
        interactionType: 'message',
        content: message.content,
        ...(message.guild?.id && { guildId: message.guild.id }),
        channelId: message.channel.id,
      });
    } catch (error) {
      this.logger.error('Failed to handle message for AI agents:', error);
    }
  }

  @On('interactionCreate')
  async handleInteraction(@Context() [interaction]: [Interaction]) {
    try {
      // Handle button interactions
      if (interaction.isButton()) {
        // Handle AI panel button interactions (including API key management)
        if ((interaction.customId.startsWith('ai_agent_') || interaction.customId.startsWith('api_key_') || interaction.customId.startsWith('provider_')) && this.aiChannelPanel) {
          await this.aiChannelPanel.handlePanelInteraction(interaction);
        }
      }
      
      // Handle select menu interactions
      if (interaction.isStringSelectMenu()) {
        if (interaction.customId === 'provider_select' && this.aiChannelPanel) {
          await this.aiChannelPanel.handleSelectMenuInteraction(interaction);
        }
        if (interaction.customId === 'browse_models_provider' && this.aiChannelPanel) {
          await this.aiChannelPanel.handleModelBrowserInteraction(interaction);
        }
        if ((interaction.customId === 'select_api_key_for_model' || interaction.customId === 'select_model_for_api_key') && this.aiChannelPanel) {
          await this.aiChannelPanel.handleSelectMenuInteraction(interaction);
        }
      }
    } catch (error) {
      this.logger.error('Failed to handle interaction:', error);
    }
  }

  @SlashCommand({
    name: 'coach',
    description: 'Start a conversation with your personal growth coach',
  })
  async onCoachCommand(@Context() [interaction]: SlashCommandContext) {
    try {
      const response = await this.personalGrowthCoach.handleInteraction(
        interaction.user.id,
        'greeting',
        'Hello, I\'d like to start working with a personal growth coach.',
        { guildId: interaction.guild?.id, channelId: interaction.channel?.id }
      );

      await interaction.reply({ 
        content: response,
        ephemeral: true,
      });

      await this.agentsService.logInteraction({
        userId: interaction.user.id,
        agentType: 'personal_growth_coach',
        interactionType: 'command',
        content: '/coach command',
        response,
        ...(interaction.guild?.id && { guildId: interaction.guild.id }),
        ...(interaction.channel?.id && { channelId: interaction.channel.id }),
      });
    } catch (error) {
      this.logger.error('Coach command failed:', error);
      await interaction.reply({
        content: '❌ Sorry, I\'m having trouble connecting to your coach right now.',
        ephemeral: true,
      });
    }
  }

  @SlashCommand({
    name: 'intake',
    description: 'Complete your intake assessment with our specialist',
  })
  async onIntakeCommand(@Context() [interaction]: SlashCommandContext) {
    try {
      const response = await this.intakeSpecialist.handleInteraction(
        interaction.user.id,
        'assessment',
        'I\'d like to complete my intake assessment.',
        { guildId: interaction.guild?.id, channelId: interaction.channel?.id }
      );

      await interaction.reply({ 
        content: response,
        ephemeral: true,
      });

      await this.agentsService.logInteraction({
        userId: interaction.user.id,
        agentType: 'intake_specialist',
        interactionType: 'command',
        content: '/intake command',
        response,
        ...(interaction.guild?.id && { guildId: interaction.guild.id }),
        ...(interaction.channel?.id && { channelId: interaction.channel.id }),
      });
    } catch (error) {
      this.logger.error('Intake command failed:', error);
      await interaction.reply({
        content: '❌ Sorry, the intake specialist is unavailable right now.',
        ephemeral: true,
      });
    }
  }

  @SlashCommand({
    name: 'progress',
    description: 'Check your progress and goals with the progress tracker',
  })
  async onProgressCommand(@Context() [interaction]: SlashCommandContext) {
    try {
      const response = await this.progressTracker.handleInteraction(
        interaction.user.id,
        'check_progress',
        'I\'d like to review my progress and goals.',
        { guildId: interaction.guild?.id, channelId: interaction.channel?.id }
      );

      await interaction.reply({ 
        content: response,
        ephemeral: true,
      });

      await this.agentsService.logInteraction({
        userId: interaction.user.id,
        agentType: 'progress_tracker',
        interactionType: 'command',
        content: '/progress command',
        response,
        ...(interaction.guild?.id && { guildId: interaction.guild.id }),
        ...(interaction.channel?.id && { channelId: interaction.channel.id }),
      });
    } catch (error) {
      this.logger.error('Progress command failed:', error);
      await interaction.reply({
        content: '❌ Sorry, the progress tracker is not available right now.',
        ephemeral: true,
      });
    }
  }

  private async shouldProcessMessage(message: Message): Promise<boolean> {
    // Check if it's a DM
    if (!message.guild) return true;

    // Check if it's in an AI agent configured channel
    const config = await this.agentsService.getAgentConfig(message.guild.id, 'general');
    if (!config) return false;

    // Check if the channel is in the configured channels list
    const configuredChannels = config.configuration?.channels || [];
    return configuredChannels.includes(message.channel.id) || configuredChannels.length === 0;
  }

  private async processWithAgent(message: Message, agentType: string): Promise<void> {
    try {
      let response: string;

      switch (agentType) {
        case 'personal_growth_coach':
          response = await this.personalGrowthCoach.handleInteraction(
            message.author.id,
            'message',
            message.content,
            { guildId: message.guild?.id, channelId: message.channel.id }
          );
          break;
        case 'intake_specialist':
          response = await this.intakeSpecialist.handleInteraction(
            message.author.id,
            'message',
            message.content,
            { guildId: message.guild?.id, channelId: message.channel.id }
          );
          break;
        case 'progress_tracker':
          response = await this.progressTracker.handleInteraction(
            message.author.id,
            'message',
            message.content,
            { guildId: message.guild?.id, channelId: message.channel.id }
          );
          break;
        default:
          this.logger.warn(`Unknown agent type: ${agentType}`);
          return;
      }

      if (response && response.trim()) {
        await message.reply(response);
      }
    } catch (error) {
      this.logger.error(`Failed to process message with agent ${agentType}:`, error);
    }
  }

  async getAgentStats(): Promise<any> {
    try {
      // In a real implementation, this would gather actual statistics
      return {
        totalInteractions: await this.getTotalInteractions(),
        activeAgents: ['personal_growth_coach', 'intake_specialist', 'progress_tracker'],
        scheduledTasks: await this.schedulerService.getScheduledTaskCount(),
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Failed to get agent stats:', error);
      return {
        error: 'Failed to retrieve agent statistics',
        timestamp: new Date().toISOString(),
      };
    }
  }

  private async getTotalInteractions(): Promise<number> {
    // This would query the database for interaction counts
    // For now, return a placeholder
    return 0;
  }

  /**
   * Handle messages in private AI threads
   */
  private async handlePrivateThreadMessage(message: Message): Promise<void> {
    try {
      if (!this.privateChatManager) {
        this.logger.warn('Private chat manager not available');
        return;
      }

      // Get session for this thread
      const session = await this.privateChatManager.getSessionByThreadId(message.channel.id);
      if (!session || session.status !== 'active') {
        this.logger.debug(`No active session found for thread ${message.channel.id}`);
        return;
      }

      // Update session activity
      await this.privateChatManager.updateSessionActivity(session.id);

      // Process message with the session's agent
      await this.processWithAgent(message, session.agentType);

      // Log the interaction
      await this.agentsService.logInteraction({
        userId: message.author.id,
        agentType: session.agentType as 'personal_growth_coach' | 'intake_specialist' | 'progress_tracker' | 'general',
        interactionType: 'thread_message',
        content: message.content,
        ...(message.guild?.id && { guildId: message.guild.id }),
        channelId: message.channel.id,
        response: 'Processed in private thread',
      });

      this.logger.debug(`Processed private thread message for session ${session.id}`);

    } catch (error) {
      this.logger.error('Failed to handle private thread message:', error);
    }
  }
}