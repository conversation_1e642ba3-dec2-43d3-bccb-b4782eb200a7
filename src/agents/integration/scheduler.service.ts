import { Inject, Injectable, Logger, Optional } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { Client } from 'discord.js';
import { RedisDatabaseService } from '../../core/database/redis-database.service';
import { AgentsService } from '../agents.service';
import { PersonalGrowthCoach } from '../types/personal-growth-coach';
import { ProgressTracker } from '../types/progress-tracker';

@Injectable()
export class SchedulerService {
  private readonly logger = new Logger(SchedulerService.name);
  private isInitialized = false;
  private scheduledTaskCount = 0;

  constructor(
    @Optional() @Inject(Client) private readonly client: Client | null,
    private readonly redisDatabaseService: RedisDatabaseService,
    private readonly agentsService: AgentsService,
    private readonly personalGrowthCoach: PersonalGrowthCoach,
    private readonly progressTracker: ProgressTracker,
  ) {}

  async initialize() {
    if (this.isInitialized) return;
    
    this.logger.log('🕐 Initializing AI Agent Scheduler...');
    this.isInitialized = true;
    this.logger.log('✅ AI Agent Scheduler initialized');
  }

  @Cron(CronExpression.EVERY_DAY_AT_9AM)
  async sendDailyCheckins() {
    if (!this.isInitialized || !this.client || !this.client.isReady()) return;

    try {
      this.logger.log('🌅 Running daily check-ins...');
      
      // This would typically query the database for users who have
      // opted in for daily check-ins
      const usersForCheckIn = await this.getUsersForDailyCheckin();
      
      for (const user of usersForCheckIn) {
        await this.sendDailyCheckin(user.discordId);
        this.scheduledTaskCount++;
        
        // Add delay to avoid rate limits
        await this.delay(1000);
      }
      
      this.logger.log(`✅ Daily check-ins sent to ${usersForCheckIn.length} users`);
    } catch (error) {
      this.logger.error('❌ Failed to send daily check-ins:', error);
    }
  }

  @Cron(CronExpression.EVERY_WEEK)
  async sendWeeklyProgressReports() {
    if (!this.isInitialized || !this.client || !this.client.isReady()) return;

    try {
      this.logger.log('📊 Running weekly progress reports...');
      
      const usersForReport = await this.getUsersForWeeklyReport();
      
      for (const user of usersForReport) {
        await this.sendWeeklyProgressReport(user.discordId);
        this.scheduledTaskCount++;
        
        // Add delay to avoid rate limits
        await this.delay(2000);
      }
      
      this.logger.log(`✅ Weekly progress reports sent to ${usersForReport.length} users`);
    } catch (error) {
      this.logger.error('❌ Failed to send weekly progress reports:', error);
    }
  }

  @Cron('0 */6 * * *') // Every 6 hours
  async sendProactiveMessages() {
    if (!this.isInitialized || !this.client || !this.client.isReady()) return;

    try {
      this.logger.log('💬 Running proactive messaging...');
      
      const usersForProactiveMsg = await this.getUsersForProactiveMessaging();
      
      for (const user of usersForProactiveMsg) {
        await this.sendProactiveMessage(user.discordId);
        this.scheduledTaskCount++;
        
        // Add delay to avoid rate limits
        await this.delay(1500);
      }
      
      this.logger.log(`✅ Proactive messages sent to ${usersForProactiveMsg.length} users`);
    } catch (error) {
      this.logger.error('❌ Failed to send proactive messages:', error);
    }
  }

  private async sendDailyCheckin(userId: string) {
    try {
      if (!this.client) return;
      const user = await this.client.users.fetch(userId);
      if (!user) return;

      const message = await this.personalGrowthCoach.handleInteraction(
        userId,
        'check_in',
        'Daily check-in scheduled message',
        { isScheduled: true }
      );

      await user.send(message);
      
      await this.agentsService.logInteraction({
        userId,
        agentType: 'personal_growth_coach',
        interactionType: 'check_in',
        content: 'Daily scheduled check-in',
        response: message,
      });
    } catch (error) {
      this.logger.error(`Failed to send daily check-in to user ${userId}:`, error);
    }
  }

  private async sendWeeklyProgressReport(userId: string) {
    try {
      if (!this.client) return;
      const user = await this.client.users.fetch(userId);
      if (!user) return;

      const report = await this.progressTracker.handleInteraction(
        userId,
        'weekly_report',
        'Weekly progress report scheduled message',
        { isScheduled: true }
      );

      await user.send(report);
      
      await this.agentsService.logInteraction({
        userId,
        agentType: 'progress_tracker',
        interactionType: 'progress_update',
        content: 'Weekly scheduled progress report',
        response: report,
      });
    } catch (error) {
      this.logger.error(`Failed to send weekly progress report to user ${userId}:`, error);
    }
  }

  private async sendProactiveMessage(userId: string) {
    try {
      if (!this.client) return;
      const user = await this.client.users.fetch(userId);
      if (!user) return;

      // Determine which type of proactive message to send
      const messageType = this.getProactiveMessageType();
      
      let message: string;
      let agentType: string;
      
      if (messageType === 'motivation') {
        message = await this.personalGrowthCoach.handleInteraction(
          userId,
          'motivation',
          'Proactive motivational message',
          { isScheduled: true }
        );
        agentType = 'personal_growth_coach';
      } else {
        message = await this.progressTracker.handleInteraction(
          userId,
          'reminder',
          'Proactive progress reminder',
          { isScheduled: true }
        );
        agentType = 'progress_tracker';
      }

      await user.send(message);
      
      await this.agentsService.logInteraction({
        userId,
        agentType: agentType as any,
        interactionType: 'message',
        content: `Proactive ${messageType} message`,
        response: message,
      });
    } catch (error) {
      this.logger.error(`Failed to send proactive message to user ${userId}:`, error);
    }
  }

  private async getUsersForDailyCheckin(): Promise<Array<{ discordId: string }>> {
    try {
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      const sevenDaysAgoTime = sevenDaysAgo.getTime();
      
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const todayTime = today.getTime();
      
      // Get all users with interactions in the last 7 days
      const activeUsersKey = 'active_users_for_checkin';
      const redis = this.redisDatabaseService.redis.getClient();
      const activeUsersStr = await redis.get(activeUsersKey);
      let activeUsers = activeUsersStr ? JSON.parse(activeUsersStr) : [];
      
      if (activeUsers.length === 0) {
        // Fallback: get users who have recent agent interactions
        const interactionKeys = await redis.keys('agent_interaction:*');
        const recentUsers = new Set<string>();
        
        for (const key of interactionKeys) {
          const interactionStr = await redis.get(key);
          if (interactionStr) {
            const interaction = JSON.parse(interactionStr);
            if (interaction.timestamp) {
              const interactionTime = new Date(interaction.timestamp).getTime();
              if (interactionTime >= sevenDaysAgoTime) {
                recentUsers.add(interaction.userId);
              }
            }
          }
        }
        
        activeUsers = Array.from(recentUsers).map((userId: any) => ({ discordId: userId }));
        await redis.setex(activeUsersKey, 3600, JSON.stringify(activeUsers)); // Cache for 1 hour
      }
      
      // Filter out users who already received a check-in today
      const usersWithoutTodayCheckin = [];
      for (const user of activeUsers) {
        const userCheckinKeys = await redis.keys(`agent_interaction:${user.discordId}:*`);
        let hasCheckinToday = false;
        
        for (const key of userCheckinKeys) {
          const interactionStr = await redis.get(key);
          if (interactionStr) {
            const interaction = JSON.parse(interactionStr);
            if (interaction.interactionType === 'check_in' && 
                interaction.timestamp &&
                new Date(interaction.timestamp).getTime() >= todayTime) {
              hasCheckinToday = true;
              break;
            }
          }
        }
        
        if (!hasCheckinToday) {
          usersWithoutTodayCheckin.push(user);
        }
      }
      
      return usersWithoutTodayCheckin;
    } catch (error) {
      this.logger.error('Failed to get users for daily check-in:', error);
      return [];
    }
  }

  private async getUsersForWeeklyReport(): Promise<Array<{ discordId: string }>> {
    try {
      const oneWeekAgo = new Date();
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
      const oneWeekAgoTime = oneWeekAgo.getTime();
      
      // Get users who have progress_tracker interactions in the last week
      const redis = this.redisDatabaseService.redis.getClient();
      const interactionKeys = await redis.keys('agent_interaction:*');
      const usersWithProgressTracking = new Set<string>();
      
      for (const key of interactionKeys) {
        const interactionStr = await redis.get(key);
        if (interactionStr) {
          const interaction = JSON.parse(interactionStr);
          if (interaction.agentType === 'progress_tracker' &&
              interaction.timestamp &&
              new Date(interaction.timestamp).getTime() >= oneWeekAgoTime) {
            usersWithProgressTracking.add(interaction.userId);
          }
        }
      }
      
      return Array.from(usersWithProgressTracking).map((userId: any) => ({ discordId: userId }));
    } catch (error) {
      this.logger.error('Failed to get users for weekly report:', error);
      return [];
    }
  }

  private async getUsersForProactiveMessaging(): Promise<Array<{ discordId: string }>> {
    try {
      const twoDaysAgo = new Date();
      twoDaysAgo.setDate(twoDaysAgo.getDate() - 2);
      const twoDaysAgoTime = twoDaysAgo.getTime();
      
      const twoWeeksAgo = new Date();
      twoWeeksAgo.setDate(twoWeeksAgo.getDate() - 14);
      const twoWeeksAgoTime = twoWeeksAgo.getTime();
      
      // Get users with agent interactions in the last 2 weeks
      const redis = this.redisDatabaseService.redis.getClient();
      const interactionKeys = await redis.keys('agent_interaction:*');
      const candidateUsers = new Map<string, number>(); // userId -> lastActivityTime
      
      for (const key of interactionKeys) {
        const interactionStr = await redis.get(key);
        if (interactionStr) {
          const interaction = JSON.parse(interactionStr);
          if (interaction.timestamp) {
            const interactionTime = new Date(interaction.timestamp).getTime();
            if (interactionTime >= twoWeeksAgoTime) {
              const userId = interaction.userId;
              const currentLastActivity = candidateUsers.get(userId) || 0;
              candidateUsers.set(userId, Math.max(currentLastActivity, interactionTime));
            }
          }
        }
      }
      
      // Filter for users who haven't been active in the last 2 days
      const inactiveUsers = Array.from(candidateUsers.entries())
        .filter(([_, lastActivity]) => lastActivity < twoDaysAgoTime)
        .map(([userId, _]) => ({ discordId: userId }))
        .slice(0, 10); // Limit to avoid spam
      
      return inactiveUsers;
    } catch (error) {
      this.logger.error('Failed to get users for proactive messaging:', error);
      return [];
    }
  }

  private getProactiveMessageType(): 'motivation' | 'reminder' {
    // Simple random selection - in practice, this could be more sophisticated
    return Math.random() > 0.5 ? 'motivation' : 'reminder';
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async getScheduledTaskCount(): Promise<number> {
    return this.scheduledTaskCount;
  }

  async getSchedulerStatus() {
    return {
      initialized: this.isInitialized,
      tasksExecuted: this.scheduledTaskCount,
      botReady: this.client?.isReady() ?? false,
      nextDailyCheckin: '09:00 UTC',
      nextWeeklyReport: 'Sunday 00:00 UTC',
      nextProactiveMessage: 'Every 6 hours',
    };
  }
}