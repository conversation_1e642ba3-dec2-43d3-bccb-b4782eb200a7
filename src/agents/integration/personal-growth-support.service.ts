import { Injectable, Logger, Inject } from '@nestjs/common';
import { Client, TextChannel, GuildMember, Message, EmbedBuilder, BaseGuildTextChannel } from 'discord.js';
import { RedisDatabaseService } from '../../core/database/redis-database.service';
import { ChannelRoutingService } from './channel-routing.service';
import { AgentsService } from '../agents.service';
import { PersonalGrowthCoach } from '../types/personal-growth-coach';
import { IntakeSpecialist } from '../types/intake-specialist';
import { ProgressTracker } from '../types/progress-tracker';

@Injectable()
export class PersonalGrowthSupportService {
  private readonly logger = new Logger(PersonalGrowthSupportService.name);

  // Keywords that indicate personal growth needs
  private readonly personalGrowthKeywords = [
    'motivation', 'motivated', 'consistency', 'consistent', 'productive', 'productivity',
    'goals', 'goal', 'habits', 'habit', 'discipline', 'focus', 'focused', 'struggling',
    'struggle', 'stuck', 'procrastination', 'procrastinating', 'overwhelmed', 'stressed',
    'anxiety', 'anxious', 'depression', 'depressed', 'burnout', 'tired', 'exhausted',
    'improvement', 'improve', 'better', 'growth', 'develop', 'development', 'progress',
    'achievement', 'achieve', 'success', 'successful', 'mindset', 'confidence', 'self-esteem',
    'relationship', 'relationships', 'communication', 'social', 'lonely', 'isolation',
    'support', 'help', 'guidance', 'advice', 'mentor', 'coaching', 'therapy'
  ];

  // Supportive response templates
  private readonly supportiveResponses = [
    "I hear you, and what you're going through is completely valid. You're not alone in this journey. 💙",
    "Thank you for sharing that with us. It takes courage to open up about your struggles. We're here to support you. 🤗",
    "Your feelings are important, and seeking help shows incredible strength. Let's work through this together. ✨",
    "I appreciate you trusting us with this. Everyone faces challenges, and you've taken the first step by reaching out. 🌟",
    "What you're experiencing is more common than you might think. You're brave for sharing, and we're here to help. 💪"
  ];

  constructor(
    @Inject(Client) private readonly client: Client,
    private readonly redisDatabaseService: RedisDatabaseService,
    private readonly channelRoutingService: ChannelRoutingService,
    private readonly agentsService: AgentsService,
    private readonly personalGrowthCoach: PersonalGrowthCoach,
    private readonly intakeSpecialist: IntakeSpecialist,
    private readonly progressTracker: ProgressTracker,
  ) {}

  /**
   * Analyzes a message for personal growth indicators and provides appropriate support
   */
  async analyzeAndSupport(message: Message): Promise<boolean> {
    try {
      const content = message.content.toLowerCase();
      const hasPersonalGrowthKeywords = this.personalGrowthKeywords.some(keyword => 
        content.includes(keyword)
      );

      if (!hasPersonalGrowthKeywords) {
        return false;
      }

      // Log the interaction
      await this.logPersonalGrowthInteraction(message);

      // Get or create personal growth channels
      const channels = await this.getOrCreatePersonalGrowthChannels(message.guild!);
      
      if (!channels) {
        this.logger.warn(`Could not create personal growth channels for guild ${message.guild!.id}`);
        return false;
      }

      // Send supportive response in the original channel
      await this.sendSupportiveResponse(message);

      // Route to appropriate agent based on message content
      const routingResult = await this.channelRoutingService.routeMessage(message);
      
      if (routingResult) {
        // Send a follow-up message directing them to the personal growth channel
        await this.sendChannelRedirect(message, channels.mainChannel, routingResult);
        
        // Initiate conversation in the personal growth channel
        await this.initiatePersonalGrowthConversation(message, channels.mainChannel, routingResult);
      }

      return true;
    } catch (error) {
      this.logger.error('Error in analyzeAndSupport:', error);
      return false;
    }
  }

  /**
   * Handles new member welcome with personal growth introduction
   */
  async welcomeNewMember(member: GuildMember): Promise<void> {
    try {
      const channels = await this.getOrCreatePersonalGrowthChannels(member.guild);
      
      if (!channels) {
        this.logger.warn(`Could not create personal growth channels for guild ${member.guild.id}`);
        return;
      }

      // Send welcome message with personal growth introduction
      const welcomeEmbed = new EmbedBuilder()
        .setTitle('🌟 Welcome to Our Supportive Community!')
        .setDescription(
          `Hey ${member.displayName}! 👋\n\n` +
          `We're so glad you're here! Our community is all about supporting each other through life's challenges and celebrating growth together.\n\n` +
          `**Here's how we can help you:**\n` +
          `🎯 **Goal Setting & Achievement** - Set and track meaningful goals\n` +
          `💪 **Motivation & Consistency** - Stay motivated and build lasting habits\n` +
          `🧠 **Personal Development** - Grow your mindset and skills\n` +
          `🤝 **Relationship Support** - Improve communication and connections\n` +
          `📈 **Progress Tracking** - Monitor your journey and celebrate wins\n\n` +
          `**Getting Started:**\n` +
          `• Share what you're working on or struggling with\n` +
          `• Ask questions - no judgment here!\n` +
          `• Check out ${channels.mainChannel} for ongoing support\n` +
          `• Use ${channels.progressChannel} to track your wins\n\n` +
          `Remember: Every expert was once a beginner. You've got this! 💙`
        )
        .setColor(0x00AE86)
        .setThumbnail(member.user.displayAvatarURL())
        .setTimestamp();

      await channels.mainChannel.send({ embeds: [welcomeEmbed] });

      // Start intake process
      await this.initiateIntakeProcess(member);

    } catch (error) {
      this.logger.error('Error in welcomeNewMember:', error);
    }
  }

  /**
   * Gets or creates personal growth channels for a guild
   */
  private async getOrCreatePersonalGrowthChannels(guild: any) {
    try {
      // Look for existing channels
      let mainChannel = guild.channels.cache.find((ch: any) => 
        ch.name === 'personal-growth-support' && ch.type === 0
      );
      let progressChannel = guild.channels.cache.find((ch: any) => 
        ch.name === 'progress-wins' && ch.type === 0
      );
      let resourcesChannel = guild.channels.cache.find((ch: any) => 
        ch.name === 'growth-resources' && ch.type === 0
      );

      // Create category if it doesn't exist
      let category = guild.channels.cache.find((ch: any) => 
        ch.name === 'Personal Growth' && ch.type === 4
      );
      
      if (!category) {
        category = await guild.channels.create({
          name: 'Personal Growth',
          type: 4, // Category
          position: 1,
        });
      }

      // Create main support channel
      if (!mainChannel) {
        mainChannel = await guild.channels.create({
          name: 'personal-growth-support',
          type: 0, // Text channel
          parent: category.id,
          topic: '🌟 Share your challenges, get support, and grow together! This is a safe space for personal development discussions.',
        });
      }

      // Create progress tracking channel
      if (!progressChannel) {
        progressChannel = await guild.channels.create({
          name: 'progress-wins',
          type: 0, // Text channel
          parent: category.id,
          topic: '🎉 Celebrate your wins, big and small! Share your progress and achievements here.',
        });
      }

      // Create resources channel
      if (!resourcesChannel) {
        resourcesChannel = await guild.channels.create({
          name: 'growth-resources',
          type: 0, // Text channel
          parent: category.id,
          topic: '📚 Helpful resources, tips, and tools for personal growth and development.',
        });

        // Add some initial resources
        await this.addInitialResources(resourcesChannel);
      }

      return {
        category,
        mainChannel,
        progressChannel,
        resourcesChannel
      };
    } catch (error) {
      this.logger.error('Error creating personal growth channels:', error);
      return null;
    }
  }

  /**
   * Sends a supportive response to the original message
   */
  private async sendSupportiveResponse(message: Message): Promise<void> {
    const randomIndex = Math.floor(Math.random() * this.supportiveResponses.length);
    const response = this.supportiveResponses[randomIndex];
    
    if (!response) {
      this.logger.warn('No supportive response available');
      return;
    }
    
    try {
      await message.reply(response);
    } catch (error) {
      this.logger.error('Error sending supportive response:', error);
    }
  }

  /**
   * Sends a message directing the user to the personal growth channel
   */
  private async sendChannelRedirect(message: Message, targetChannel: TextChannel, agentType: string): Promise<void> {
    const agentNames = {
      'personal_growth_coach': 'Personal Growth Coach',
      'intake_specialist': 'Intake Specialist',
      'progress_tracker': 'Progress Tracker'
    };

    const agentName = agentNames[agentType as keyof typeof agentNames] || 'Personal Growth Coach';

    try {
      if (message.channel && 'send' in message.channel) {
        await (message.channel as BaseGuildTextChannel).send(
          `${message.author}, I've connected you with our **${agentName}** who can provide more personalized support. ` +
          `Head over to ${targetChannel} where they're waiting to help you! 🌟`
        );
      }
    } catch (error) {
      this.logger.error('Error sending channel redirect:', error);
    }
  }

  /**
   * Initiates a conversation in the personal growth channel
   */
  private async initiatePersonalGrowthConversation(originalMessage: Message, targetChannel: TextChannel, agentType: string): Promise<void> {
    try {
      const user = originalMessage.author;
      const context = {
        originalMessage: originalMessage.content,
        channel: originalMessage.channel.id,
        referredFrom: 'automatic_detection'
      };

      let agent;
      let greeting;

      switch (agentType) {
        case 'intake_specialist':
          agent = this.intakeSpecialist;
          greeting = `Hi ${user.displayName}! 👋 I'm here to help you get started on your personal growth journey. I saw your message and I'd love to learn more about what you're working on. Let's start with a few questions to understand how I can best support you.`;
          break;
        case 'progress_tracker':
          agent = this.progressTracker;
          greeting = `Hello ${user.displayName}! 📈 I'm your Progress Tracker, and I'm here to help you monitor and celebrate your growth journey. I noticed you mentioned some challenges - let's work together to track your progress and build momentum!`;
          break;
        default:
          agent = this.personalGrowthCoach;
          greeting = `Hi ${user.displayName}! 🌟 I'm your Personal Growth Coach, and I'm here to support you through whatever you're facing. I saw your message and I want you to know that seeking help is a sign of strength. Let's work together to find strategies that work for you.`;
      }

      // Send greeting in the target channel
      await targetChannel.send(`${greeting}\n\n*Original message: "${originalMessage.content}"*`);

      // Process with the agent
      const response = await agent.handleInteraction(
        user.id,
        'message',
        originalMessage.content,
        context
      );

      if (response) {
        await targetChannel.send(response);
      }

    } catch (error) {
      this.logger.error('Error initiating personal growth conversation:', error);
    }
  }

  /**
   * Initiates the intake process for new members
   */
  private async initiateIntakeProcess(member: GuildMember): Promise<void> {
    try {
      // Wait a bit to let the welcome message settle
      setTimeout(async () => {
        const channels = await this.getOrCreatePersonalGrowthChannels(member.guild);
        if (channels) {
          const introMessage = 
            `${member.user}, when you're ready, I'd love to learn more about you and your goals! ` +
            `This helps me provide better support tailored to your needs. ` +
            `Just say "start assessment" or "tell me about the intake process" when you're ready to begin. ` +
            `No pressure - take your time! 😊`;
          
          await channels.mainChannel.send(introMessage);
        }
      }, 30000); // Wait 30 seconds
    } catch (error) {
      this.logger.error('Error initiating intake process:', error);
    }
  }

  /**
   * Adds initial resources to the resources channel
   */
  private async addInitialResources(channel: TextChannel): Promise<void> {
    try {
      const resourcesEmbed = new EmbedBuilder()
        .setTitle('📚 Personal Growth Resources')
        .setDescription('Here are some helpful resources to support your growth journey:')
        .addFields(
          {
            name: '🎯 Goal Setting',
            value: '• SMART Goals framework\n• Goal tracking templates\n• Accountability strategies',
            inline: true
          },
          {
            name: '💪 Habit Building',
            value: '• 21-day habit tracker\n• Habit stacking techniques\n• Breaking bad habits',
            inline: true
          },
          {
            name: '🧠 Mindset',
            value: '• Growth vs fixed mindset\n• Positive self-talk\n• Overcoming limiting beliefs',
            inline: true
          },
          {
            name: '📈 Progress Tracking',
            value: '• Weekly reflection prompts\n• Progress measurement tools\n• Celebrating small wins',
            inline: true
          },
          {
            name: '🤝 Relationships',
            value: '• Communication skills\n• Boundary setting\n• Conflict resolution',
            inline: true
          },
          {
            name: '🌟 Self-Care',
            value: '• Stress management\n• Work-life balance\n• Mental health resources',
            inline: true
          }
        )
        .setColor(0x00AE86)
        .setTimestamp();

      await channel.send({ embeds: [resourcesEmbed] });

      // Add some motivational quotes
      const quotesEmbed = new EmbedBuilder()
        .setTitle('💭 Daily Inspiration')
        .setDescription(
          '"The only way to do great work is to love what you do." - Steve Jobs\n\n' +
          '"Success is not final, failure is not fatal: it is the courage to continue that counts." - Winston Churchill\n\n' +
          '"The only person you are destined to become is the person you decide to be." - Ralph Waldo Emerson'
        )
        .setColor(0xFFD700)
        .setTimestamp();

      await channel.send({ embeds: [quotesEmbed] });

    } catch (error) {
      this.logger.error('Error adding initial resources:', error);
    }
  }

  /**
   * Logs personal growth interactions for analytics
   */
  private async logPersonalGrowthInteraction(message: Message): Promise<void> {
    try {
      const interaction = {
        userId: message.author.id,
        agentType: 'personal_growth_support',
        interactionType: 'detection',
        content: message.content,
        timestamp: new Date().toISOString(),
        guildId: message.guild?.id,
        channelId: message.channel.id,
      };
      const interactionKey = `agent_interaction:${message.author.id}:${Date.now()}`;
      const redis = this.redisDatabaseService.redis.getClient();
      await redis.setex(interactionKey, 86400, JSON.stringify(interaction)); // 24 hours TTL
    } catch (error) {
      this.logger.error('Error logging personal growth interaction:', error);
    }
  }

  /**
   * Updates user activity timestamp
   */
  async updateUserActivity(userId: string): Promise<void> {
    try {
      // For now, just log the activity - the field update can be implemented later
      this.logger.debug(`User activity updated for ${userId}`);
    } catch (error) {
      this.logger.error('Error updating user activity:', error);
    }
  }

  /**
   * Gets user's recent personal growth interactions
   */
  async getUserRecentInteractions(userId: string, days: number = 7): Promise<any[]> {
    try {
      const daysAgo = new Date();
      daysAgo.setDate(daysAgo.getDate() - days);
      const cutoffTime = daysAgo.getTime();

      // Get all interactions for this user from Redis
      const pattern = `agent_interaction:${userId}:*`;
      const redis = this.redisDatabaseService.redis.getClient();
      const keys = await redis.keys(pattern);
      
      const interactions = [];
      for (const key of keys) {
        const interactionStr = await redis.get(key);
        if (interactionStr) {
          const interaction = JSON.parse(interactionStr);
          if (interaction.timestamp) {
            const interactionTime = new Date(interaction.timestamp).getTime();
            if (interactionTime >= cutoffTime) {
              interactions.push(interaction);
            }
          }
        }
      }

      // Sort by timestamp descending and limit to 20
      return interactions
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(0, 20);
    } catch (error) {
      this.logger.error('Error getting user recent interactions:', error);
      return [];
    }
  }
}