import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { AgentsService } from './agents.service';
import { AIAgentIntegrationService } from './integration/ai-agent-integration.service';
import { SchedulerService } from './integration/scheduler.service';
import { ChannelRoutingService } from './integration/channel-routing.service';
import { PersonalGrowthSupportService } from './integration/personal-growth-support.service';
import { PersonalGrowthCoach } from './types/personal-growth-coach';
import { IntakeSpecialist } from './types/intake-specialist';
import { ProgressTracker } from './types/progress-tracker';
import { ResearchAgent } from '../core/agents/implementations/research-agent';
import { ExaSearchService } from '../core/agents/services/exa-search.service';
import { DatabaseModule } from '@/core/database';
import { ServicesModule } from '../core/services/services.module';
import { DiscordModule } from '../discord/discord.module';
import { AIChannelModule } from '../features/ai-channel/ai-channel.module';

@Module({
  imports: [DatabaseModule, ServicesModule, DiscordModule, AIChannelModule, ScheduleModule.forRoot()],
  providers: [
    AgentsService,
    AIAgentIntegrationService,
    SchedulerService,
    ChannelRoutingService,
    PersonalGrowthSupportService,
    PersonalGrowthCoach,
    IntakeSpecialist,
    ProgressTracker,
    ResearchAgent,
    ExaSearchService,
  ],
  exports: [
    AgentsService,
    AIAgentIntegrationService,
    SchedulerService,
    ChannelRoutingService,
    PersonalGrowthSupportService,
    PersonalGrowthCoach,
    IntakeSpecialist,
    ProgressTracker,
    ResearchAgent,
    ExaSearchService,
  ],
})
export class AgentsModule {}