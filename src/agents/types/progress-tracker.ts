import { Injectable, Logger } from '@nestjs/common';
import { AgentsService } from '../agents.service';

@Injectable()
export class ProgressTracker {
  private readonly logger = new Logger(ProgressTracker.name);
  
  // Progress tracker personality and tracking templates
  private readonly trackerPersonality = {
    tone: 'analytical',
    style: 'data-driven',
    expertise: ['goal tracking', 'progress measurement', 'milestone celebration', 'trend analysis'],
  };

  private readonly responseTemplates = {
    progress_check: [
      'Let\'s review your progress! I\'ve been tracking your journey and I have some insights to share.',
      'Time for a progress check! I\'ve analyzed your recent activity and goal advancement.',
      'Progress update time! I\'ve compiled your latest achievements and areas for improvement.',
    ],
    milestone_celebration: [
      'Congratulations! 🎉 You\'ve reached an important milestone. Let\'s celebrate this achievement!',
      'Amazing work! 🌟 You\'ve hit a significant goal marker. This is worth celebrating!',
      'Fantastic progress! 🎯 You\'ve achieved another milestone on your journey!',
    ],
    goal_review: [
      'Let\'s examine your current goals and see how they align with your progress.',
      'Time to review your goals! I\'ll help you assess what\'s working and what needs adjustment.',
      'Goal review session! Let\'s analyze your targets and optimize your path forward.',
    ],
    trend_analysis: [
      'I\'ve noticed some interesting patterns in your progress. Let me share what the data shows.',
      'Based on your activity trends, I have some insights that might help you optimize your approach.',
      'The numbers tell a story! Here\'s what your progress data reveals about your journey.',
    ],
  };

  private readonly progressMetrics = {
    daily: ['habits_completed', 'time_spent', 'mood_rating', 'energy_level'],
    weekly: ['goals_advanced', 'challenges_overcome', 'new_skills_learned'],
    monthly: ['major_milestones', 'habit_consistency', 'goal_completion_rate'],
  };

  constructor(private readonly agentsService: AgentsService) {}

  async handleInteraction(
    userId: string,
    interactionType: string,
    content: string,
    context?: any
  ): Promise<string> {
    try {
      // Get user's progress data and history
      const progressData = await this.getProgressData(userId);
      
      // Generate response based on interaction type
      let response = await this.generateResponse(
        interactionType,
        content,
        progressData,
        context
      );

      // Update progress tracking
      await this.updateProgressTracking(userId, interactionType, content, response);

      return response;
    } catch (error) {
      this.logger.error(`Failed to handle progress interaction for user ${userId}:`, error);
      return 'I\'m having trouble accessing your progress data right now. Please try again in a moment.';
    }
  }

  private async generateResponse(
    interactionType: string,
    content: string,
    progressData: any,
    context?: any
  ): Promise<string> {
    switch (interactionType) {
      case 'check_progress':
        return this.generateProgressCheck(progressData);
      
      case 'milestone_celebration':
        return this.generateMilestoneCelebration(progressData, content);
      
      case 'goal_review':
        return this.generateGoalReview(progressData);
      
      case 'trend_analysis':
        return this.generateTrendAnalysis(progressData);
      
      case 'message':
        return await this.getContextualResponse(content, progressData);
      
      default:
        return this.getDefaultProgressResponse(progressData);
    }
  }

  private generateProgressCheck(progressData: any): string {
    const baseResponse = this.getRandomTemplate('progress_check');
    
    if (!progressData.hasData) {
      return `${baseResponse}\n\nI notice this is your first progress check! Let's start tracking your journey. What goals would you like me to monitor?\n\n**To get started, tell me:**\n• Your current main goals\n• How you'd like to measure progress\n• How often you'd like check-ins`;
    }

    const summary = this.generateProgressSummary(progressData);
    return `${baseResponse}\n\n${summary}`;
  }

  private generateProgressSummary(progressData: any): string {
    let summary = '📊 **Your Progress Summary:**\n\n';
    
    if (progressData.completedGoals > 0) {
      summary += `✅ Goals Completed: ${progressData.completedGoals}\n`;
    }
    
    if (progressData.activeGoals > 0) {
      summary += `🎯 Active Goals: ${progressData.activeGoals}\n`;
    }
    
    if (progressData.streakDays > 0) {
      summary += `🔥 Current Streak: ${progressData.streakDays} days\n`;
    }
    
    if (progressData.weeklyProgress) {
      summary += `📈 This Week: ${Math.round(progressData.weeklyProgress)}% progress\n`;
    }
    
    summary += '\n**Recommendations:**\n';
    
    if (progressData.weeklyProgress < 50) {
      summary += '• Consider breaking down your goals into smaller, more manageable tasks\n';
    }
    
    if (progressData.streakDays > 7) {
      summary += '• You\'re on fire! Keep this momentum going\n';
    }
    
    if (progressData.completedGoals > 0) {
      summary += '• Great job on completing goals! Time to set new challenges\n';
    }
    
    return summary;
  }

  private generateMilestoneCelebration(progressData: any, content: string): string {
    const celebration = this.getRandomTemplate('milestone_celebration');
    
    let details = '\n\n**Milestone Details:**\n';
    details += `• Achievement: ${this.extractAchievement(content)}\n`;
    details += `• Progress: ${progressData.overallProgress || 'Advancing steadily'}\n`;
    details += `• Impact: This brings you closer to your ultimate goals\n`;
    
    details += '\n**What\'s Next:**\n';
    details += '• Reflect on what strategies worked best\n';
    details += '• Set your next milestone target\n';
    details += '• Consider sharing your success with others\n';
    
    return celebration + details;
  }

  private generateGoalReview(progressData: any): string {
    const baseReview = this.getRandomTemplate('goal_review');
    
    let review = `${baseReview}\n\n📋 **Goal Analysis:**\n\n`;
    
    if (progressData.goals && progressData.goals.length > 0) {
      progressData.goals.forEach((goal: any, index: number) => {
        review += `${index + 1}. **${goal.title}**\n`;
        review += `   Status: ${goal.status || 'In Progress'}\n`;
        review += `   Progress: ${goal.progress || 0}%\n`;
        review += `   Last Updated: ${goal.lastUpdate || 'Not tracked'}\n\n`;
      });
    } else {
      review += 'No goals currently tracked. Let\'s set up some goals to monitor!\n\n';
    }
    
    review += '**Recommendations:**\n';
    review += '• Focus on 3-5 main goals at a time\n';
    review += '• Review and adjust goals monthly\n';
    review += '• Celebrate small wins along the way\n';
    
    return review;
  }

  private generateTrendAnalysis(progressData: any): string {
    const baseTrend = this.getRandomTemplate('trend_analysis');
    
    let analysis = `${baseTrend}\n\n📈 **Trend Analysis:**\n\n`;
    
    if (progressData.trends) {
      if (progressData.trends.improving) {
        analysis += '📈 **Positive Trends:**\n';
        analysis += '• Consistency is improving over time\n';
        analysis += '• Goal completion rate is increasing\n';
        analysis += '• Engagement levels are trending upward\n\n';
      }
      
      if (progressData.trends.challenges) {
        analysis += '⚠️ **Areas for Attention:**\n';
        analysis += '• Some goals may need reassessment\n';
        analysis += '• Consider adjusting your approach\n';
        analysis += '• Time to celebrate recent wins and refocus\n\n';
      }
    }
    
    analysis += '**Data-Driven Insights:**\n';
    analysis += '• Your most productive days are when you start early\n';
    analysis += '• Smaller, frequent actions lead to better outcomes\n';
    analysis += '• Regular check-ins improve goal achievement by 40%\n';
    
    return analysis;
  }

  private async getContextualResponse(content: string, progressData: any): Promise<string> {
    const contentLower = content.toLowerCase();
    
    // Detect progress-related keywords
    if (this.isProgressQuery(contentLower)) {
      return this.generateProgressCheck(progressData);
    }
    
    if (this.isGoalRelated(contentLower)) {
      return this.generateGoalReview(progressData);
    }
    
    if (this.isCelebrationRelated(contentLower)) {
      return this.generateMilestoneCelebration(progressData, content);
    }
    
    if (this.isTrendQuery(contentLower)) {
      return this.generateTrendAnalysis(progressData);
    }
    
    return this.getGeneralTrackingResponse(content, progressData);
  }

  private isProgressQuery(content: string): boolean {
    const progressWords = ['progress', 'how am i doing', 'status', 'update', 'check', 'review'];
    return progressWords.some(word => content.includes(word));
  }

  private isGoalRelated(content: string): boolean {
    const goalWords = ['goal', 'target', 'objective', 'aim', 'plan', 'achieve'];
    return goalWords.some(word => content.includes(word));
  }

  private isCelebrationRelated(content: string): boolean {
    const celebrationWords = ['completed', 'achieved', 'finished', 'done', 'success', 'milestone'];
    return celebrationWords.some(word => content.includes(word));
  }

  private isTrendQuery(content: string): boolean {
    const trendWords = ['trend', 'pattern', 'analysis', 'data', 'insight', 'stats'];
    return trendWords.some(word => content.includes(word));
  }

  private getGeneralTrackingResponse(content: string, progressData: any): string {
    return `I'm here to help you track and analyze your progress! I can provide:\n\n• Progress summaries and check-ins\n• Goal reviews and adjustments\n• Milestone celebrations\n• Trend analysis and insights\n\nWhat specific aspect of your progress would you like to explore?`;
  }

  private getDefaultProgressResponse(progressData: any): string {
    return `Welcome to your progress tracking dashboard! 📊\n\nI'm here to help you monitor your goals, celebrate milestones, and optimize your growth journey.\n\n**Available Commands:**\n• Check progress status\n• Review current goals\n• Analyze trends and patterns\n• Celebrate achievements\n\nWhat would you like to track today?`;
  }

  private getRandomTemplate(type: string): string {
    const templates = this.responseTemplates[type as keyof typeof this.responseTemplates] || [];
    if (templates.length === 0) {
      return 'Let me help you track your progress!';
    }
    const randomIndex = Math.floor(Math.random() * templates.length);
    const template = templates[randomIndex];
    return template || 'Let me help you track your progress!';
  }

  private extractAchievement(content: string): string {
    // Simple extraction - in a real implementation this would be more sophisticated
    const lines = content.split('\n');
    const firstLine = lines[0];
    return firstLine ? firstLine.substring(0, 100) : 'Goal achievement';
  }

  private async getProgressData(userId: string): Promise<any> {
    try {
      const goals = await this.agentsService.getMemory(userId, 'tracked_goals');
      const progress = await this.agentsService.getMemory(userId, 'progress_data');
      const trends = await this.agentsService.getMemory(userId, 'progress_trends');
      
      return {
        hasData: !!(goals || progress),
        goals: goals?.value || [],
        completedGoals: progress?.value?.completed || 0,
        activeGoals: progress?.value?.active || 0,
        streakDays: progress?.value?.streak || 0,
        weeklyProgress: progress?.value?.weeklyPercent || 0,
        overallProgress: progress?.value?.overall || 'Starting journey',
        trends: trends?.value || { improving: true, challenges: false },
      };
    } catch (error) {
      this.logger.error(`Failed to get progress data for ${userId}:`, error);
      return { hasData: false };
    }
  }

  private async updateProgressTracking(
    userId: string,
    interactionType: string,
    content: string,
    response: string
  ) {
    try {
      // Store the interaction for progress analysis
      await this.agentsService.storeMemory({
        userId,
        memoryType: 'interaction',
        key: `progress_${Date.now()}`,
        value: {
          type: interactionType,
          userMessage: content.substring(0, 200),
          response: response.substring(0, 200),
          timestamp: new Date().toISOString(),
        },
        importance: this.getInteractionImportance(interactionType),
      });

      // Update tracking metrics
      if (interactionType === 'check_progress') {
        await this.updateProgressMetrics(userId);
      }
    } catch (error) {
      this.logger.error('Failed to update progress tracking:', error);
    }
  }

  private async updateProgressMetrics(userId: string) {
    try {
      const currentData = await this.agentsService.getMemory(userId, 'progress_metrics');
      const metrics = {
        totalCheckins: 0,
        lastCheckin: null as string | null,
        weeklyCheckins: 0,
        ...(currentData?.value || {})
      };

      metrics.totalCheckins = (metrics.totalCheckins || 0) + 1;
      metrics.lastCheckin = new Date().toISOString();
      
      // Reset weekly counter if it's a new week
      const lastWeek = new Date(metrics.lastCheckin || 0);
      const now = new Date();
      if (now.getTime() - lastWeek.getTime() > 7 * 24 * 60 * 60 * 1000) {
        metrics.weeklyCheckins = 1;
      } else {
        metrics.weeklyCheckins = (metrics.weeklyCheckins || 0) + 1;
      }

      await this.agentsService.storeMemory({
        userId,
        memoryType: 'metrics',
        key: 'progress_metrics',
        value: metrics,
        importance: 6,
      });
    } catch (error) {
      this.logger.error('Failed to update progress metrics:', error);
    }
  }

  private getInteractionImportance(interactionType: string): number {
    const importanceMap = {
      check_progress: 7,
      milestone_celebration: 9,
      goal_review: 8,
      trend_analysis: 6,
      message: 5,
    };
    
    return importanceMap[interactionType as keyof typeof importanceMap] || 4;
  }

  async getTrackerStats(userId?: string) {
    return {
      trackerType: 'Progress Tracker',
      personality: this.trackerPersonality,
      capabilities: [
        'Goal progress monitoring',
        'Milestone celebration',
        'Trend analysis',
        'Data-driven insights',
        'Performance optimization',
        'Achievement tracking'
      ],
      metrics: this.progressMetrics,
      responseTypes: Object.keys(this.responseTemplates),
      totalTemplates: Object.values(this.responseTemplates).flat().length,
    };
  }
}