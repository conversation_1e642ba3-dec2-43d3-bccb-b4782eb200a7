import { Injectable, Logger } from '@nestjs/common';
import { AgentsService } from '../agents.service';

@Injectable()
export class IntakeSpecialist {
  private readonly logger = new Logger(IntakeSpecialist.name);
  
  // Intake specialist personality and assessment templates
  private readonly specialistPersonality = {
    tone: 'professional',
    style: 'thorough',
    expertise: ['user onboarding', 'needs assessment', 'goal identification', 'service matching'],
  };

  private readonly assessmentQuestions = {
    initial: [
      'Welcome! I\'m here to help you get the most out of our community. Let\'s start with understanding your goals. What brings you here today?',
      'Hello! I\'m your intake specialist. My role is to understand your needs and connect you with the right resources. What are you hoping to achieve?',
      'Hi there! I\'ll be guiding you through our intake process. This helps us personalize your experience. What\'s your main area of interest?',
    ],
    experience: [
      'Tell me about your experience with personal development. Are you just starting out, or have you been working on yourself for a while?',
      'What\'s your background with growth-focused communities? Have you been part of similar groups before?',
      'Help me understand your journey so far. What personal development work have you done previously?',
    ],
    goals: [
      'What are your top 3 goals you\'d like to work on in the next 6 months?',
      'If you could change one thing about your life in the next year, what would it be?',
      'What does success look like to you? Paint me a picture of your ideal outcome.',
    ],
    challenges: [
      'What are the biggest obstacles you\'ve faced in reaching your goals?',
      'What typically gets in your way when you\'re trying to make positive changes?',
      'Tell me about a time you struggled to maintain momentum. What happened?',
    ],
    preferences: [
      'How do you prefer to receive support? Do you like daily check-ins, weekly reviews, or something else?',
      'Are you more motivated by accountability, encouragement, or practical advice?',
      'What time of day are you most likely to engage with personal development content?',
    ],
    completion: [
      'Perfect! Based on our conversation, I have a good understanding of your needs. Let me connect you with the right resources.',
      'Excellent! Your intake assessment is complete. I\'ll now set up your personalized experience based on what you\'ve shared.',
      'Thank you for those thoughtful responses! I\'m ready to recommend the best path forward for you.',
    ]
  };

  private readonly assessmentStages = [
    'initial',
    'experience', 
    'goals',
    'challenges',
    'preferences',
    'completion'
  ];

  constructor(private readonly agentsService: AgentsService) {}

  async handleInteraction(
    userId: string,
    interactionType: string,
    content: string,
    context?: any
  ): Promise<string> {
    try {
      // Get user's assessment progress
      const assessmentState = await this.getAssessmentState(userId);
      
      // Generate response based on interaction type and assessment progress
      let response = await this.generateResponse(
        interactionType,
        content,
        assessmentState,
        context
      );

      // Update assessment state
      await this.updateAssessmentState(userId, interactionType, content, response);

      return response;
    } catch (error) {
      this.logger.error(`Failed to handle intake interaction for user ${userId}:`, error);
      return 'I apologize for the technical difficulty. Let me help you get started with a fresh assessment.';
    }
  }

  private async generateResponse(
    interactionType: string,
    content: string,
    assessmentState: any,
    context?: any
  ): Promise<string> {
    switch (interactionType) {
      case 'assessment':
      case 'greeting':
        return this.startAssessment(assessmentState);
      
      case 'message':
        return await this.continueAssessment(content, assessmentState);
      
      case 'restart':
        return this.restartAssessment();
      
      default:
        return this.getGeneralIntakeResponse(content, assessmentState);
    }
  }

  private startAssessment(assessmentState: any): string {
    if (assessmentState.completed) {
      return `Welcome back! I see you\'ve already completed your intake assessment. Would you like to:\n\n1. Review your profile\n2. Update your goals\n3. Start a new assessment\n\nWhat would you prefer?`;
    }
    
    if (assessmentState.currentStage) {
      return `I see we were in the middle of your assessment. Let\'s continue where we left off.\n\n${this.getQuestionForStage(assessmentState.currentStage)}`;
    }
    
    return this.getRandomQuestion('initial') + '\n\n*This assessment will take about 5-10 minutes and will help us personalize your experience.*';
  }

  private async continueAssessment(content: string, assessmentState: any): Promise<string> {
    // Store the user's response
    await this.storeAssessmentResponse(
      assessmentState.userId,
      assessmentState.currentStage || 'initial',
      content
    );
    
    // Move to next stage
    const nextStage = this.getNextStage(assessmentState.currentStage || 'initial');
    
    if (!nextStage) {
      // Assessment complete
      return await this.completeAssessment(assessmentState.userId);
    }
    
    // Continue with next question
    const acknowledgment = this.getAcknowledgment(content);
    const nextQuestion = this.getQuestionForStage(nextStage);
    
    return `${acknowledgment}\n\n${nextQuestion}`;
  }

  private restartAssessment(): string {
    return `No problem! Let\'s start fresh with your intake assessment.\n\n${this.getRandomQuestion('initial')}\n\n*This assessment will help us understand your needs and goals better.*`;
  }

  private getGeneralIntakeResponse(content: string, assessmentState: any): string {
    if (!assessmentState.completed) {
      return `I\'d love to help you with that! First, let\'s complete your intake assessment so I can understand your needs better.\n\n${this.getQuestionForStage(assessmentState.currentStage || 'initial')}`;
    }
    
    return `Based on your intake assessment, I can see you\'re focused on ${assessmentState.primaryGoals || 'personal growth'}. How can I assist you today?\n\nWould you like me to:\n- Connect you with your personal growth coach\n- Review your assessment results\n- Update your profile`;
  }

  private getRandomQuestion(stage: string): string {
    const questions = this.assessmentQuestions[stage as keyof typeof this.assessmentQuestions] || [];
    if (questions.length === 0) {
      return 'Tell me more about what you\'re looking for.';
    }
    const randomIndex = Math.floor(Math.random() * questions.length);
    const question = questions[randomIndex];
    return question || 'Tell me more about what you\'re looking for.';
  }

  private getQuestionForStage(stage: string): string {
    return this.getRandomQuestion(stage);
  }

  private getNextStage(currentStage: string): string | null {
    const currentIndex = this.assessmentStages.indexOf(currentStage);
    if (currentIndex === -1 || currentIndex >= this.assessmentStages.length - 1) {
      return null;
    }
    const nextStage = this.assessmentStages[currentIndex + 1];
    return nextStage || null;
  }

  private getAcknowledgment(content: string): string {
    const acknowledgments = [
      'Thank you for sharing that.',
      'I appreciate your openness.',
      'That\'s helpful to know.',
      'Great, that gives me good insight.',
      'Perfect, that information is very useful.',
    ];
    
    // Customize based on content length or sentiment
    if (content.length > 100) {
      return 'Thank you for that detailed response.';
    }
    
    const randomIndex = Math.floor(Math.random() * acknowledgments.length);
    const acknowledgment = acknowledgments[randomIndex];
    return acknowledgment || 'Thank you for that information.';
  }

  private async completeAssessment(userId: string): Promise<string> {
    try {
      // Get all assessment responses
      const responses = await this.getAssessmentResponses(userId);
      
      // Analyze responses to create profile
      const profile = this.analyzeAssessmentResponses(responses);
      
      // Store completed profile
      await this.storeUserProfile(userId, profile);
      
      // Generate completion response with recommendations
      return this.generateCompletionResponse(profile);
    } catch (error) {
      this.logger.error(`Failed to complete assessment for user ${userId}:`, error);
      return 'Your assessment is complete! I\'ll now connect you with the appropriate resources based on your responses.';
    }
  }

  private generateCompletionResponse(profile: any): string {
    const completion = this.getRandomQuestion('completion');
    
    let recommendations = '\n\n**Your Personalized Recommendations:**\n';
    
    if (profile.needsMotivation) {
      recommendations += '• I\'ll connect you with our Personal Growth Coach for regular motivation and support\n';
    }
    
    if (profile.hasSpecificGoals) {
      recommendations += '• Our Progress Tracker will help you monitor your goal achievement\n';
    }
    
    if (profile.experienceLevel === 'beginner') {
      recommendations += '• I\'ve set up a beginner-friendly resource library for you\n';
    }
    
    recommendations += '\n**Next Steps:**\n';
    recommendations += '1. Check your DMs for your personalized welcome package\n';
    recommendations += '2. Join our community channels that match your interests\n';
    recommendations += '3. Schedule your first coaching session if desired\n';
    
    return completion + recommendations + '\n\nWelcome to the community! 🎉';
  }

  private analyzeAssessmentResponses(responses: any): any {
    // Simple analysis of assessment responses
    const profile = {
      experienceLevel: 'intermediate',
      needsMotivation: false,
      hasSpecificGoals: false,
      preferredStyle: 'balanced',
      primaryFocus: 'general_growth',
    };
    
    // This would be more sophisticated in a real implementation
    if (responses.experience && responses.experience.toLowerCase().includes('new')) {
      profile.experienceLevel = 'beginner';
    }
    
    if (responses.challenges && responses.challenges.toLowerCase().includes('motivation')) {
      profile.needsMotivation = true;
    }
    
    if (responses.goals && responses.goals.length > 50) {
      profile.hasSpecificGoals = true;
    }
    
    return profile;
  }

  private async getAssessmentState(userId: string): Promise<any> {
    try {
      const state = await this.agentsService.getMemory(userId, 'intake_assessment_state');
      return state?.value || { userId, currentStage: null, completed: false };
    } catch (error) {
      this.logger.error(`Failed to get assessment state for ${userId}:`, error);
      return { userId, currentStage: null, completed: false };
    }
  }

  private async updateAssessmentState(
    userId: string,
    interactionType: string,
    content: string,
    response: string
  ) {
    try {
      const currentState = await this.getAssessmentState(userId);
      const nextStage = this.getNextStage(currentState.currentStage || 'initial');
      
      const newState = {
        ...currentState,
        currentStage: nextStage,
        completed: !nextStage,
        lastInteraction: new Date().toISOString(),
      };
      
      await this.agentsService.storeMemory({
        userId,
        memoryType: 'assessment',
        key: 'intake_assessment_state',
        value: newState,
        importance: 8,
      });
    } catch (error) {
      this.logger.error('Failed to update assessment state:', error);
    }
  }

  private async storeAssessmentResponse(userId: string, stage: string, response: string) {
    try {
      await this.agentsService.storeMemory({
        userId,
        memoryType: 'assessment',
        key: `intake_${stage}`,
        value: {
          stage,
          response: response.substring(0, 500), // Limit response length
          timestamp: new Date().toISOString(),
        },
        importance: 7,
      });
    } catch (error) {
      this.logger.error('Failed to store assessment response:', error);
    }
  }

  private async getAssessmentResponses(userId: string): Promise<any> {
    try {
      // Get all assessment responses for analysis
      const responses: Record<string, any> = {};
      
      for (const stage of this.assessmentStages.slice(0, -1)) { // Exclude 'completion'
        const memory = await this.agentsService.getMemory(userId, `intake_${stage}`);
        if (memory) {
          responses[stage] = memory.value.response;
        }
      }
      
      return responses;
    } catch (error) {
      this.logger.error(`Failed to get assessment responses for ${userId}:`, error);
      return {};
    }
  }

  private async storeUserProfile(userId: string, profile: any) {
    try {
      await this.agentsService.storeMemory({
        userId,
        memoryType: 'profile',
        key: 'intake_profile',
        value: {
          ...profile,
          completedAt: new Date().toISOString(),
          version: '1.0',
        },
        importance: 9,
      });
    } catch (error) {
      this.logger.error('Failed to store user profile:', error);
    }
  }

  async getIntakeStats(userId?: string) {
    return {
      specialistType: 'Intake Specialist',
      personality: this.specialistPersonality,
      capabilities: [
        'User onboarding',
        'Needs assessment',
        'Goal identification',
        'Service matching',
        'Profile creation',
        'Resource recommendations'
      ],
      assessmentStages: this.assessmentStages,
      questionCategories: Object.keys(this.assessmentQuestions),
      totalQuestions: Object.values(this.assessmentQuestions).flat().length,
    };
  }
}