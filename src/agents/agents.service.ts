import { RedisDatabaseService } from '@/core/database';
import {
    AIAgentConfig,
    AgentMemory,
    AgentType,
    InteractionType,
    MemoryType
} from '@/core/database/schema';
import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class AgentsService {
  private readonly logger = new Logger(AgentsService.name);

  constructor(
    private readonly redisDatabaseService: RedisDatabaseService,
  ) {}

  async getAgentConfig(guildId: string, agentType: string): Promise<AIAgentConfig | null> {
    try {
      const configKey = `agent_config:${guildId}:${agentType}`;
      const redis = this.redisDatabaseService.redis.getClient();
      const configStr = await redis.get(configKey);
      if (!configStr) return null;
      const config = JSON.parse(configStr);
      return config && config.enabled ? config : null;
    } catch (error) {
      this.logger.error(`Failed to get agent config for ${guildId}:${agentType}`, error);
      return null;
    }
  }

  async logInteraction(data: {
    userId: string;
    agentType: AgentType;
    interactionType: InteractionType;
    content: string;
    response?: string;
    guildId?: string;
    channelId?: string;
  }): Promise<void> {
    try {
      const interaction = {
        ...data,
        status: 'completed' as const,
        timestamp: new Date().toISOString(),
      };
      const interactionKey = `agent_interaction:${data.userId}:${Date.now()}`;
      const redis = this.redisDatabaseService.redis.getClient();
      await redis.setex(interactionKey, 86400, JSON.stringify(interaction)); // 24 hours TTL
    } catch (error) {
      this.logger.error('Failed to log agent interaction:', error);
    }
  }

  async storeMemory(data: {
    userId: string;
    memoryType: MemoryType;
    key: string;
    value: Record<string, any>;
    context?: string;
    importance?: number;
  }): Promise<void> {
    try {
      const memory = {
        ...data,
        importance: data.importance || 1,
        lastAccessedAt: new Date().toISOString(),
        accessCount: 0,
        createdAt: new Date().toISOString(),
      };
      const memoryKey = `agent_memory:${data.userId}:${data.key}`;
      const redis = this.redisDatabaseService.redis.getClient();
      await redis.set(memoryKey, JSON.stringify(memory));
    } catch (error) {
      this.logger.error('Failed to store agent memory:', error);
    }
  }

  async getMemory(userId: string, key: string): Promise<AgentMemory | null> {
    try {
      const memoryKey = `agent_memory:${userId}:${key}`;
      const redis = this.redisDatabaseService.redis.getClient();
      const memoryStr = await redis.get(memoryKey);
      
      if (memoryStr) {
        const memory = JSON.parse(memoryStr);
        if (!memory.deletedAt) {
          // Update access count
          const updatedMemory = {
            ...memory,
            accessCount: (memory.accessCount || 0) + 1,
            lastAccessedAt: new Date().toISOString()
          };
          await redis.set(memoryKey, JSON.stringify(updatedMemory));
          return updatedMemory;
        }
      }
      
      return null;
    } catch (error) {
      this.logger.error('Failed to get agent memory:', error);
      return null;
    }
  }
}