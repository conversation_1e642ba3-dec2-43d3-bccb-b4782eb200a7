import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-discord';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class DiscordStrategy extends PassportStrategy(Strategy, 'discord') {
  constructor(private configService: ConfigService) {
    super({
      clientID: configService.get<string>('DISCORD_CLIENT_ID') || configService.get<string>('BOT_CLIENT_ID'),
      clientSecret: configService.get<string>('DISCORD_CLIENT_SECRET') || configService.get<string>('BOT_CLIENT_SECRET'),
      callbackURL: '/api/auth/callback',
      scope: ['identify', 'guilds', 'email'], state: true,
    });
  }

  async validate(accessToken: string, refreshToken: string, profile: any) {
    return {
      discordId: profile.id,
      username: profile.username,
      email: profile.email,
      avatar: profile.avatar,
      accessToken,
      refreshToken,
    };
  }
}