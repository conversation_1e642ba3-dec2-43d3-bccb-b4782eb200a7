import { Injectable, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { SessionService } from '../../core/security/session.service';
import { UserService } from '../../core/security/user.service';
import { EncryptionService } from '../../core/security/encryption.service';
import { Request } from 'express';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private readonly jwtService: JwtService,
    private readonly sessionService: SessionService,
    private readonly userService: UserService,
    private readonly encryptionService: EncryptionService,
  ) {}

  async login(discordUser: any, req: Request) {
    try {
      // Create or update user
      const user = await this.userService.upsertUser({
        discordId: discordUser.discordId,
        username: discordUser.username,
        email: discordUser.email,
      });

      // Get user's organization information
      const userOrganization = await this.userService.getUserOrganization(user.discordId);

      // Create session
      const session = await this.sessionService.createSession(user.discordId, req);

      // Generate JWT token with organization info
      const payload = { 
        sub: user.discordId, 
        username: user.username,
        sessionId: session.sessionId,
        organizationId: userOrganization?.organization?.id || null,
        roles: userOrganization ? [userOrganization.role] : [],
        permissions: [], // TODO: Add permissions based on role
      };
      const accessToken = this.jwtService.sign(payload);

      return {
        accessToken,
        sessionToken: session.sessionId,
        user: {
          id: user.discordId,
          username: user.username,
          email: user.email,
          tier: userOrganization?.organization?.tier || 'free',
          organization: userOrganization?.organization || null,
          role: userOrganization?.role || null,
        },
      };
    } catch (error) {
      this.logger.error('Login failed:', error);
      throw new Error('Authentication failed');
    }
  }

  async validateSession(sessionToken: string, req: Request) {
    const session = await this.sessionService.validateSession(sessionToken, req);
    if (session && session.user) {
      // Get organization information for the user
      const userOrganization = await this.userService.getUserOrganization(session.user.discordId);
      
      // Add organization data to user object
      session.user = {
        ...session.user,
        tier: userOrganization?.organization?.tier || 'free',
        organization: userOrganization?.organization || null,
        role: userOrganization?.role || null,
      };
    }
    return session;
  }

  async logout(sessionToken: string) {
    await this.sessionService.revokeSession(sessionToken);
  }

  async generateCsrfToken(): Promise<string> {
    return this.encryptionService.generateCSRFToken();
  }

  async validateCsrfToken(token: string): Promise<boolean> {
    return this.encryptionService.validateCSRFToken(token);
  }

  async getUserOrganization(discordId: string) {
    return this.userService.getUserOrganization(discordId);
  }
}