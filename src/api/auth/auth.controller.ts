import {
    <PERSON>,
    Get,
    HttpStatus,
    Post,
    Req,
    Res,
    UseGuards
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Request, Response } from 'express';
import { AuthService } from './auth.service';

@ApiTags('auth')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Get('login')
  @ApiOperation({ summary: 'Initiate Discord OAuth login' })
  @ApiResponse({ status: 302, description: 'Redirect to Discord OAuth' })
  @UseGuards(AuthGuard('discord'))
  async login(@Req() req: Request) {
    // OAuth state will be generated by Discord strategy
  }

  @Get('callback')
  @ApiOperation({ summary: 'Discord OAuth callback' })
  @ApiResponse({ status: 200, description: 'Authentication successful' })
  @ApiResponse({ status: 401, description: 'Authentication failed' })
  @UseGuards(AuthGuard('discord'))
  async callback(@Req() req: Request, @Res() res: Response) {
    try {
      const user = req.user as any;
      const result = await this.authService.login(user, req);

      if (!req.query.state) throw new Error('OAuth state missing');
      const cookieOptions = {
        httpOnly: true, // Prevent XSS access to tokens
        secure: process.env.NODE_ENV === 'production', // HTTPS only in production
        sameSite: process.env.NODE_ENV === 'production' ? 'strict' as const : 'lax' as const,
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
        domain: process.env.NODE_ENV === 'production' ? '.sevalla.app' : undefined, // Cross-subdomain in prod
      };

      res.cookie('session_token', result.sessionToken, cookieOptions);
      res.cookie('access_token', result.accessToken, cookieOptions);

      // Redirect to frontend with success
      const frontendUrl = process.env.WEB_URL || 'http://localhost:3000';
      res.redirect(`${frontendUrl}/auth/success`);
    } catch (error) {
      const frontendUrl = process.env.WEB_URL || 'http://localhost:3000';
      res.redirect(`${frontendUrl}/auth/error`);
    }
  }

// Bot OAuth callback handled by frontend at /api/auth/bot-callback

  @Get('session')
  @ApiOperation({ summary: 'Get current session information' })
  @ApiResponse({ status: 200, description: 'Session information' })
  @ApiBearerAuth()
  async getSession(@Req() req: Request) {
    try {
      // Check for Discord token in Authorization header
      const authHeader = req.get('Authorization');
      if (authHeader?.startsWith('Bearer ')) {
        const token = authHeader.replace('Bearer ', '');
        
        // Validate Discord token
        const response = await fetch('https://discord.com/api/v10/users/@me', {
          headers: { 'Authorization': `Bearer ${token}` },
        });
        
        if (response.ok) {
          const discordUser = await response.json();
          
          // Get organization information for Discord user
          const userOrganization = await this.authService.getUserOrganization(discordUser.id);
          
          return {
            authenticated: true,
            user: {
              id: discordUser.id,
              username: discordUser.username,
              discriminator: discordUser.discriminator,
              avatar: discordUser.avatar,
              email: discordUser.email,
              tier: userOrganization?.organization?.tier || 'free',
              organization: userOrganization?.organization || null,
              role: userOrganization?.role || null,
            },
            session: {
              id: 'discord-session',
              type: 'discord-token',
            },
          };
        }
      }
      
      // Fallback to cookie-based session
      const sessionToken = req.cookies?.session_token;
      if (sessionToken) {
        const session = await this.authService.validateSession(sessionToken, req);
        if (session) {
          return {
            authenticated: true,
            user: session.user,
            session: {
              id: session.sessionId,
              expiresAt: session.expiresAt,
              lastAccessedAt: session.lastAccessedAt,
            },
          };
        }
      }
      
      return { authenticated: false };
    } catch (error) {
      return { authenticated: false };
    }
  }

  @Post('signout')
  @ApiOperation({ summary: 'Sign out and invalidate session' })
  @ApiResponse({ status: 200, description: 'Signed out successfully' })
  async signout(@Req() req: Request, @Res() res: Response) {
    const sessionToken = req.cookies?.session_token;
    
    if (sessionToken) {
      await this.authService.logout(sessionToken);
    }
    
    // Clear all auth cookies
    res.clearCookie('session_token');
    res.clearCookie('access_token');
    
    return res.status(HttpStatus.OK).json({ message: 'Signed out successfully' });
  }

  @Get('csrf-token')
  @ApiOperation({ summary: 'Get CSRF token' })
  @ApiResponse({ status: 200, description: 'CSRF token' })
  async getCsrfToken() {
    const token = await this.authService.generateCsrfToken();
    return { csrfToken: token };
  }

  @Get('config')
  @ApiOperation({ summary: 'Get API configuration and auth status' })
  @ApiResponse({ status: 200, description: 'API configuration and authentication info' })
  async getConfig(@Req() req: Request) {
    const isDevelopment = process.env.NODE_ENV !== 'production';
    const port = process.env.PORT || 8080;

    return {
      apiBaseUrl: isDevelopment
        ? `http://localhost:${port}`
        : `https://discordbot-energex-backend-nqzv2.sevalla.app`,
      environment: process.env.NODE_ENV || 'development',
      discord: {
        clientId: process.env.DISCORD_CLIENT_ID || process.env.BOT_CLIENT_ID,
botInviteUrl: process.env.DISCORD_CLIENT_ID || process.env.BOT_CLIENT_ID
          ? `https://discord.com/oauth2/authorize?client_id=${process.env.DISCORD_CLIENT_ID || process.env.BOT_CLIENT_ID}&permissions=8&scope=bot+applications.commands&response_type=code&redirect_uri=${process.env.WEB_URL || 'http://localhost:3000'}/api/auth/bot-callback`
          : null,
      },
      auth: {
        hasSessionToken: !!req.cookies?.session_token,
        hasAccessToken: !!req.cookies?.access_token,
        authHeader: !!req.get('Authorization'),
      },
      features: {
        oauthOnly: !process.env.DISCORD_TOKEN,
        aiAgents: !!process.env.ANTHROPIC_API_KEY,
      },
    };
  }

  @Get('bot-invite')
  @ApiOperation({ summary: 'Get Discord bot invite URL' })
  @ApiResponse({ status: 200, description: 'Bot invite URL' })
  async getBotInvite() {
    const clientId = process.env.DISCORD_CLIENT_ID || process.env.BOT_CLIENT_ID;
    const isDevelopment = process.env.NODE_ENV !== 'production';
    const port = process.env.PORT || 8080;

    if (!clientId) {
      return {
        error: 'Discord client ID not configured',
        clientId: null,
        inviteUrl: null,
      };
    }

const redirectUri = `${process.env.WEB_URL || 'http://localhost:3000'}/api/auth/bot-callback`;
    const inviteUrl = `https://discord.com/oauth2/authorize?client_id=${clientId}&permissions=8&scope=bot+applications.commands&response_type=code&redirect_uri=${encodeURIComponent(redirectUri)}`;

    return {
      clientId,
      inviteUrl,
      redirectUri,
    };
  }

  @Get('health')
  @ApiOperation({ summary: 'Authentication service health check' })
  @ApiResponse({ status: 200, description: 'Auth service health' })
  async healthCheck() {
    return {
      status: 'ok',
      service: 'auth',
      timestamp: new Date().toISOString(),
      discord_configured: !!process.env.DISCORD_CLIENT_ID,
      jwt_configured: !!process.env.JWT_SECRET,
    };
  }
}