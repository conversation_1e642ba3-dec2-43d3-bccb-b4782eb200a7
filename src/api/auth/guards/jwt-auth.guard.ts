import { ExecutionContext, Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  private readonly logger = new Logger(JwtAuthGuard.name);

  async canActivate(context: ExecutionContext): Promise<boolean> {
    try {
      const request = context.switchToHttp().getRequest();
      const authHeader = request.get('Authorization');

      console.log(`[JwtAuthGuard] Starting authentication check...`);
      console.log(`[JwtAuthGuard] Auth header: ${authHeader ? 'Present' : 'Missing'}`);
      this.logger.log(`Auth header: ${authHeader ? 'Present' : 'Missing'}`);

      // If we have a Bearer token, try to validate it
      if (authHeader?.startsWith('Bearer ')) {
        const token = authHeader.replace('Bearer ', '');
        console.log(`[JwtAuthGuard] Token length: ${token.length}, starts with: ${token.substring(0, 10)}...`);
        this.logger.log(`Token length: ${token.length}, starts with: ${token.substring(0, 10)}...`);

        // Check if it's a Discord token (Discord tokens have a specific format)
        if (this.isDiscordToken(token)) {
          console.log('[JwtAuthGuard] Detected Discord token, validating with Discord API...');
          this.logger.log('Detected Discord token, validating with Discord API...');
          try {
            // Validate Discord token directly with Discord API
            const response = await fetch('https://discord.com/api/v10/users/@me', {
              headers: { 'Authorization': `Bearer ${token}` },
            });

            console.log(`[JwtAuthGuard] Discord API response status: ${response.status}`);
            this.logger.log(`Discord API response status: ${response.status}`);

            if (response.ok) {
              const discordUser = await response.json();
              console.log(`[JwtAuthGuard] Discord user validated: ${discordUser.username}#${discordUser.discriminator}`);
              this.logger.log(`Discord user validated: ${discordUser.username}#${discordUser.discriminator}`);
              // Attach user info to request for use in controllers
              request.user = {
                userId: discordUser.id,
                username: discordUser.username,
                sessionId: 'discord-session',
              };
              console.log('[JwtAuthGuard] Discord authentication successful, returning true');
              return true;
            } else {
              const errorText = await response.text();
              console.log(`[JwtAuthGuard] Discord API validation failed: ${response.status} - ${errorText}`);
              this.logger.warn(`Discord API validation failed: ${response.status} - ${errorText}`);
            }
          } catch (error) {
            console.log('[JwtAuthGuard] Discord token validation error:', error);
            this.logger.error('Discord token validation error:', error);
            // If Discord validation fails, fall through to JWT validation
          }
        } else {
          console.log('[JwtAuthGuard] Token appears to be JWT, proceeding with JWT validation...');
          this.logger.log('Token appears to be JWT, proceeding with JWT validation...');
        }
      }

      // Fall back to JWT validation
      try {
        console.log('[JwtAuthGuard] Attempting JWT validation...');
        this.logger.log('Attempting JWT validation...');
        const result = (await super.canActivate(context)) as boolean;
        console.log(`[JwtAuthGuard] JWT validation result: ${result}`);
        return result;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.log('[JwtAuthGuard] JWT validation failed:', errorMessage);
        this.logger.warn('JWT validation failed:', errorMessage);
        throw new UnauthorizedException('Invalid or expired token');
      }
    } catch (error) {
      console.log('[JwtAuthGuard] Unexpected error in canActivate:', error);
      this.logger.error('Unexpected error in canActivate:', error);
      throw new UnauthorizedException('Authentication failed');
    }
  }

  private isDiscordToken(token: string): boolean {
    // Discord tokens typically have the format: base64.base64.base64
    // and are longer than typical JWT tokens
    return token.includes('.') && token.length > 50 && !token.startsWith('eyJ');
  }
}