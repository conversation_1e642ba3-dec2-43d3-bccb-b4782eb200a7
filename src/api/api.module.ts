import { Module } from '@nestjs/common';
import { AuthModule } from './auth/auth.module';
import { GuildsModule } from './guilds/guilds.module';
import { AdminModule } from './admin/admin.module';
import { WhopModule } from './whop/whop.module';
import { HealthModule } from './health/health.module';

@Module({
  imports: [
    AuthModule,
    HealthModule,
    GuildsModule,
    AdminModule,
    WhopModule,
  ],
})
export class ApiModule {}