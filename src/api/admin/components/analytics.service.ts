import { Injectable, Logger } from '@nestjs/common';
import { RedisDatabaseService } from '@/core/database';

@Injectable()
export class AnalyticsService {
  private readonly logger = new Logger(AnalyticsService.name);

  constructor(
    private readonly redisDatabaseService: RedisDatabaseService,
  ) {}

  async getDashboardStats() {
    try {
      const now = new Date();
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

      const redis = this.redisDatabaseService.redis.getClient();
      const [
        totalUsers,
        activeUsers,
        newUsersToday,
        newUsersWeek,
        totalGuilds,
        activeGuilds,
        totalSessions,
        activeSessions,
        totalInteractions,
        interactionsToday
      ] = await Promise.all([
        this.getUserCount(redis),
        this.getActiveUserCount(redis),
        this.getRecentUserCount(redis, oneDayAgo),
        this.getRecentUserCount(redis, oneWeekAgo),
        this.getGuildCount(redis),
        this.getActiveGuildCount(redis),
        this.getSessionCount(redis),
        this.getActiveSessionCount(redis),
        this.getInteractionCount(redis),
        this.getRecentInteractionCount(redis, oneDayAgo),
      ]);

      const userGrowthRate = totalUsers > 0 ? ((newUsersWeek / totalUsers) * 100) : 0;
      const activityRate = totalUsers > 0 ? ((activeUsers / totalUsers) * 100) : 0;

      return {
        overview: {
          totalUsers,
          activeUsers,
          totalGuilds,
          activeGuilds,
          activeSessions,
          totalInteractions,
        },
        growth: {
          newUsersToday,
          newUsersWeek,
          userGrowthRate: Math.round(userGrowthRate * 100) / 100,
          activityRate: Math.round(activityRate * 100) / 100,
        },
        engagement: {
          interactionsToday,
          averageInteractionsPerUser: totalUsers > 0 ? Math.round(totalInteractions / totalUsers) : 0,
          sessionUtilization: Math.round((activeSessions / Math.max(totalUsers, 1)) * 100),
        },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Failed to get dashboard stats:', error);
      throw error;
    }
  }

  async getUserAnalytics() {
    try {
      const now = new Date();
      const periods = {
        today: new Date(now.getTime() - 24 * 60 * 60 * 1000),
        week: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
        month: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000),
      };

      const analytics = await Promise.all([
        // Registration trends
        this.getUserRegistrationTrend(periods),
        // Activity patterns
        this.getUserActivityPatterns(),
        // Geographic distribution (if available)
        this.getUserDistribution(),
        // Top users by activity
        this.getTopActiveUsers(),
      ]);

      return {
        registrationTrend: analytics[0],
        activityPatterns: analytics[1],
        distribution: analytics[2],
        topUsers: analytics[3],
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Failed to get user analytics:', error);
      throw error;
    }
  }

  async getGuildAnalytics() {
    try {
      const [
        guildSizes,
        featureUsage,
        guildActivity,
        topGuilds
      ] = await Promise.all([
        this.getGuildSizeDistribution(),
        this.getFeatureUsageStats(),
        this.getGuildActivityStats(),
        this.getTopGuildsByActivity(),
      ]);

      return {
        sizeDistribution: guildSizes,
        featureUsage,
        activity: guildActivity,
        topGuilds,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Failed to get guild analytics:', error);
      throw error;
    }
  }

  async getAgentAnalytics() {
    try {
      const now = new Date();
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

      const redis = this.redisDatabaseService.redis.getClient();
      const [
        totalInteractions,
        interactionsToday,
        interactionsWeek,
        agentBreakdown,
        interactionTypes
      ] = await Promise.all([
        this.getInteractionCount(redis),
        this.getRecentInteractionCount(redis, oneDayAgo),
        this.getRecentInteractionCount(redis, oneWeekAgo),
        this.getAgentInteractionBreakdown(),
        this.getInteractionTypeBreakdown(),
      ]);

      return {
        overview: {
          totalInteractions,
          interactionsToday,
          interactionsWeek,
          averageDaily: Math.round(interactionsWeek / 7),
        },
        breakdown: {
          byAgent: agentBreakdown,
          byType: interactionTypes,
        },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Failed to get agent analytics:', error);
      throw error;
    }
  }

  private async getUserRegistrationTrend(periods: any) {
    try {
      const redis = this.redisDatabaseService.redis.getClient();
      return {
        today: await this.getRecentUserCount(redis, periods.today),
        week: await this.getRecentUserCount(redis, periods.week),
        month: await this.getRecentUserCount(redis, periods.month),
      };
    } catch (error) {
      this.logger.error('Failed to get user registration trend:', error);
      return { today: 0, week: 0, month: 0 };
    }
  }

  private async getUserActivityPatterns() {
    try {
      const redis = this.redisDatabaseService.redis.getClient();
      const activeUsers = await this.getActiveUserCount(redis);
      const totalUsers = await this.getUserCount(redis);

      return {
        activeUsers,
        inactiveUsers: totalUsers - activeUsers,
        activityRate: totalUsers > 0 ? Math.round((activeUsers / totalUsers) * 100) : 0,
      };
    } catch (error) {
      this.logger.error('Failed to get user activity patterns:', error);
      return { activeUsers: 0, inactiveUsers: 0, activityRate: 0 };
    }
  }

  private async getUserDistribution() {
    // Placeholder - would need actual geographic data
    return {
      regions: [
        { name: 'North America', users: 45, percentage: 45 },
        { name: 'Europe', users: 30, percentage: 30 },
        { name: 'Asia', users: 20, percentage: 20 },
        { name: 'Other', users: 5, percentage: 5 },
      ],
    };
  }

  private async getTopActiveUsers() {
    try {
      // Return placeholder data since detailed user queries would be complex with Redis
      return [
        { id: '1', username: 'user1', lastActivity: new Date(), interactionCount: 45 },
        { id: '2', username: 'user2', lastActivity: new Date(), interactionCount: 38 },
        { id: '3', username: 'user3', lastActivity: new Date(), interactionCount: 32 },
        { id: '4', username: 'user4', lastActivity: new Date(), interactionCount: 28 },
        { id: '5', username: 'user5', lastActivity: new Date(), interactionCount: 25 },
      ];
    } catch (error) {
      this.logger.error('Failed to get top active users:', error);
      return [];
    }
  }

  private async getGuildSizeDistribution() {
    try {
      const redis = this.redisDatabaseService.redis.getClient();
      const guildCount = await this.getGuildCount(redis);

      // Simulate guild size distribution based on total guild count
      return {
        small: Math.floor(guildCount * 0.6), // < 100 members
        medium: Math.floor(guildCount * 0.3), // 100-1000 members
        large: Math.floor(guildCount * 0.1), // > 1000 members
      };
    } catch (error) {
      this.logger.error('Failed to get guild size distribution:', error);
      return { small: 0, medium: 0, large: 0 };
    }
  }

  private async getFeatureUsageStats() {
    // Would analyze which features are most used across guilds
    return {
      welcome: { enabled: 85, usage: 92 },
      moderation: { enabled: 78, usage: 65 },
      music: { enabled: 95, usage: 88 },
      economy: { enabled: 60, usage: 45 },
      starboard: { enabled: 40, usage: 35 },
      aiAgents: { enabled: 25, usage: 80 },
    };
  }

  private async getGuildActivityStats() {
    try {
      const redis = this.redisDatabaseService.redis.getClient();
      const totalGuilds = await this.getGuildCount(redis);
      const activeGuilds = await this.getActiveGuildCount(redis);

      return {
        totalGuilds,
        activeGuilds,
        inactiveGuilds: totalGuilds - activeGuilds,
        activityRate: totalGuilds > 0 ? Math.round((activeGuilds / totalGuilds) * 100) : 0,
      };
    } catch (error) {
      this.logger.error('Failed to get guild activity stats:', error);
      return { totalGuilds: 0, activeGuilds: 0, inactiveGuilds: 0, activityRate: 0 };
    }
  }

  private async getTopGuildsByActivity() {
    try {
      // Return placeholder data since complex guild queries would be challenging with Redis
      return [
        { id: '1', name: 'Guild 1', lastActivity: new Date(), memberCount: 450 },
        { id: '2', name: 'Guild 2', lastActivity: new Date(), memberCount: 320 },
        { id: '3', name: 'Guild 3', lastActivity: new Date(), memberCount: 280 },
        { id: '4', name: 'Guild 4', lastActivity: new Date(), memberCount: 200 },
        { id: '5', name: 'Guild 5', lastActivity: new Date(), memberCount: 150 },
      ];
    } catch (error) {
      this.logger.error('Failed to get top guilds by activity:', error);
      return [];
    }
  }

  private async getAgentInteractionBreakdown() {
    // This would group interactions by agent type
    return [
      { agent: 'personal_growth_coach', count: 450, percentage: 45 },
      { agent: 'intake_specialist', count: 300, percentage: 30 },
      { agent: 'progress_tracker', count: 200, percentage: 20 },
      { agent: 'general', count: 50, percentage: 5 },
    ];
  }

  private async getInteractionTypeBreakdown() {
    // This would group interactions by type
    return [
      { type: 'message', count: 600, percentage: 60 },
      { type: 'command', count: 250, percentage: 25 },
      { type: 'assessment', count: 100, percentage: 10 },
      { type: 'check_in', count: 50, percentage: 5 },
    ];
  }

  // Redis helper methods
  private async getUserCount(redis: any): Promise<number> {
    try {
      const userKeys = await redis.keys('user:*');
      return userKeys.length;
    } catch (error) {
      this.logger.error('Failed to get user count:', error);
      return 0;
    }
  }

  private async getActiveUserCount(redis: any): Promise<number> {
    try {
      const activeUserCount = await redis.scard('active_users');
      return activeUserCount || 0;
    } catch (error) {
      this.logger.error('Failed to get active user count:', error);
      return 0;
    }
  }

  private async getRecentUserCount(redis: any, since: Date): Promise<number> {
    try {
      const userKeys = await redis.keys('user:*');
      let recentCount = 0;
      const sinceTime = since.getTime();
      
      for (const key of userKeys) {
        const userData = await redis.get(key);
        if (userData) {
          try {
            const user = JSON.parse(userData);
            const createdAt = new Date(user.createdAt);
            if (createdAt.getTime() >= sinceTime) {
              recentCount++;
            }
          } catch (parseError) {
            // Skip invalid JSON entries
          }
        }
      }
      return recentCount;
    } catch (error) {
      this.logger.error('Failed to get recent user count:', error);
      return 0;
    }
  }

  private async getGuildCount(redis: any): Promise<number> {
    try {
      const guildKeys = await redis.keys('guild:*');
      return guildKeys.length;
    } catch (error) {
      this.logger.error('Failed to get guild count:', error);
      return 0;
    }
  }

  private async getActiveGuildCount(redis: any): Promise<number> {
    try {
      const activeGuildCount = await redis.scard('active_guilds');
      return activeGuildCount || 0;
    } catch (error) {
      this.logger.error('Failed to get active guild count:', error);
      return 0;
    }
  }

  private async getSessionCount(redis: any): Promise<number> {
    try {
      const sessionKeys = await redis.keys('session:*');
      return sessionKeys.length;
    } catch (error) {
      this.logger.error('Failed to get session count:', error);
      return 0;
    }
  }

  private async getActiveSessionCount(redis: any): Promise<number> {
    try {
      const activeSessionKeys = await redis.keys('session:*:active');
      return activeSessionKeys.length;
    } catch (error) {
      this.logger.error('Failed to get active session count:', error);
      return 0;
    }
  }

  private async getInteractionCount(redis: any): Promise<number> {
    try {
      const interactionKeys = await redis.keys('interaction:*');
      return interactionKeys.length;
    } catch (error) {
      this.logger.error('Failed to get interaction count:', error);
      return 0;
    }
  }

  private async getRecentInteractionCount(redis: any, since: Date): Promise<number> {
    try {
      const interactionKeys = await redis.keys('interaction:*');
      let recentCount = 0;
      const sinceTime = since.getTime();
      
      for (const key of interactionKeys) {
        const interactionData = await redis.get(key);
        if (interactionData) {
          try {
            const interaction = JSON.parse(interactionData);
            const createdAt = new Date(interaction.createdAt);
            if (createdAt.getTime() >= sinceTime) {
              recentCount++;
            }
          } catch (parseError) {
            // Skip invalid JSON entries
          }
        }
      }
      return recentCount;
    } catch (error) {
      this.logger.error('Failed to get recent interaction count:', error);
      return 0;
    }
  }
}