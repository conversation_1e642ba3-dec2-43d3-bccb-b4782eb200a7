import { ForbiddenException, Injectable, Logger } from '@nestjs/common';
import { RedisDatabaseService } from '../../core/database/redis-database.service';
import { SessionService } from '../../core/security/session.service';
import { DiscordService } from '../../discord/discord.service';

@Injectable()
export class AdminService {
  private readonly logger = new Logger(AdminService.name);
  private readonly adminUserIds = process.env.ADMIN_USER_IDS?.split(',') || [];

  constructor(
    private readonly redisDatabaseService: RedisDatabaseService,
    private readonly sessionService: SessionService,
    private readonly discordService: DiscordService,
  ) {}

  async getActiveSessions(user: any) {
    this.checkAdminPermissions(user);
    
    try {
      // Get all session keys from Redis
      const redis = this.redisDatabaseService.redis.getClient();
      const sessionKeys = await redis.keys('session:*');
      const sessions = [];
      
      for (const key of sessionKeys.slice(0, 100)) { // Limit to 100
        const sessionStr = await redis.get(key);
        if (sessionStr) {
          const session = JSON.parse(sessionStr);
          if (!session.isRevoked) {
            // Get user info
            const userKey = `user:${session.userId}`;
            const userInfoStr = await redis.get(userKey);
            const userInfo = userInfoStr ? JSON.parse(userInfoStr) : null;
          
            sessions.push({
              sessionId: session.sessionId,
              userId: session.userId,
              username: userInfo?.username || 'Unknown',
              ipAddress: session.ipAddress,
              userAgent: session.userAgent,
              createdAt: session.createdAt,
              lastAccessedAt: session.lastAccessedAt,
              expiresAt: session.expiresAt,
            });
          }
        }
      }
      
      // Sort by lastAccessedAt descending
      sessions.sort((a, b) => new Date(b.lastAccessedAt).getTime() - new Date(a.lastAccessedAt).getTime());

      return {
        total: sessions.length,
        sessions,
      };
    } catch (error) {
      this.logger.error('Failed to get active sessions:', error);
      throw error;
    }
  }

  async getUserStats(user: any) {
    this.checkAdminPermissions(user);
    
    try {
      const redis = this.redisDatabaseService.redis.getClient();
      const userKeys = await redis.keys('user:*');
      let totalUsers = 0;
      let activeUsers = 0;
      let recentUsers = 0;
      
      const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
      
      for (const key of userKeys) {
        const userInfoStr = await redis.get(key);
        if (userInfoStr) {
          const userInfo = JSON.parse(userInfoStr);
          totalUsers++;
          if (userInfo.isActive) {
            activeUsers++;
          }
          if (userInfo.createdAt && new Date(userInfo.createdAt).getTime() >= thirtyDaysAgo) {
            recentUsers++;
          }
        }
      }

      return {
        totalUsers,
        activeUsers,
        inactiveUsers: totalUsers - activeUsers,
        recentUsers,
        userGrowthRate: totalUsers > 0 ? (recentUsers / totalUsers) * 100 : 0,
      };
    } catch (error) {
      this.logger.error('Failed to get user stats:', error);
      throw error;
    }
  }

  async getGuildStats(user: any) {
    this.checkAdminPermissions(user);
    
    try {
      const redis = this.redisDatabaseService.redis.getClient();
      const guildKeys = await redis.keys('guild:*');
      let totalGuilds = 0;
      let activeGuilds = 0;
      
      for (const key of guildKeys) {
        const guildInfoStr = await redis.get(key);
        if (guildInfoStr) {
          const guildInfo = JSON.parse(guildInfoStr);
          totalGuilds++;
          if (guildInfo.isActive) {
            activeGuilds++;
          }
        }
      }
      
      const discordGuilds = this.discordService.getGuildCount();

      return {
        totalGuilds,
        activeGuilds,
        inactiveGuilds: totalGuilds - activeGuilds,
        discordGuilds,
        syncStatus: discordGuilds === activeGuilds ? 'synced' : 'out_of_sync',
      };
    } catch (error) {
      this.logger.error('Failed to get guild stats:', error);
      throw error;
    }
  }

  async getSystemHealth(user: any) {
    this.checkAdminPermissions(user);
    
    try {
      const discordHealth = await this.discordService.healthCheck();
      const uptime = process.uptime();
      const memoryUsage = process.memoryUsage();

      return {
        discord: discordHealth,
        system: {
          uptime: Math.floor(uptime),
          uptimeFormatted: this.formatUptime(uptime),
          memory: {
            used: Math.round(memoryUsage.heapUsed / 1024 / 1024),
            total: Math.round(memoryUsage.heapTotal / 1024 / 1024),
            external: Math.round(memoryUsage.external / 1024 / 1024),
          },
          nodeVersion: process.version,
          platform: process.platform,
        },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Failed to get system health:', error);
      throw error;
    }
  }

  async revokeSession(sessionId: string, user: any) {
    this.checkAdminPermissions(user);
    
    try {
      await this.sessionService.revokeSession(sessionId);
      this.logger.log(`Session ${sessionId} revoked by admin ${user.userId}`);
      
      return {
        message: 'Session revoked successfully',
        sessionId,
        revokedBy: user.userId,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(`Failed to revoke session ${sessionId}:`, error);
      throw error;
    }
  }

  async cleanupExpiredSessions(user: any) {
    this.checkAdminPermissions(user);
    
    try {
      const cleaned = await this.sessionService.cleanupExpiredSessions();
      this.logger.log(`${cleaned} expired sessions cleaned up by admin ${user.userId}`);
      
      return {
        message: 'Expired sessions cleaned up',
        sessionsRemoved: cleaned,
        cleanedBy: user.userId,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Failed to cleanup expired sessions:', error);
      throw error;
    }
  }

  async getSystemLogs(user: any) {
    this.checkAdminPermissions(user);
    
    try {
      // In a real implementation, you would read from log files
      // For now, return basic log information
      return {
        message: 'Log access available',
        logFiles: [
          { name: 'application.log', size: '1.2MB', lastModified: new Date() },
          { name: 'error.log', size: '245KB', lastModified: new Date() },
          { name: 'discord.log', size: '512KB', lastModified: new Date() },
        ],
        note: 'Full log streaming requires additional implementation',
      };
    } catch (error) {
      this.logger.error('Failed to get system logs:', error);
      throw error;
    }
  }

  private checkAdminPermissions(user: any) {
    if (!user || !this.adminUserIds.includes(user.userId)) {
      throw new ForbiddenException('Admin permissions required');
    }
  }

  private formatUptime(uptime: number): string {
    const hours = Math.floor(uptime / 3600);
    const minutes = Math.floor((uptime % 3600) / 60);
    const seconds = Math.floor(uptime % 60);
    return `${hours}h ${minutes}m ${seconds}s`;
  }
}