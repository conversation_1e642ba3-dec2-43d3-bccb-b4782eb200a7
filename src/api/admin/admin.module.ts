import { <PERSON>du<PERSON> } from '@nestjs/common';
import { <PERSON>minController } from './admin.controller';
import { AdminService } from './admin.service';
import { SystemHealthService } from './components/system-health.service';
import { AnalyticsService } from './components/analytics.service';
import { AdminGuard } from './guards/admin.guard';
import { SecurityModule } from '../../core/security/security.module';
import { DatabaseModule } from '../../core/database/database.module';
import { DiscordModule } from '../../discord/discord.module';

@Module({
  imports: [SecurityModule, DatabaseModule, DiscordModule],
  controllers: [AdminController],
  providers: [
    AdminService,
    SystemHealthService,
    AnalyticsService,
    AdminGuard,
  ],
  exports: [AdminService, SystemHealthService, AnalyticsService],
})
export class AdminModule {}