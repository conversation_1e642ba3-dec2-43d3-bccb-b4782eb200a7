import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  UseGuards,
  Req,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { Request } from 'express';
import { AdminService } from './admin.service';
import { SystemHealthService } from './components/system-health.service';
import { AnalyticsService } from './components/analytics.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('admin')
@Controller('admin')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class AdminController {
  constructor(
    private readonly adminService: AdminService,
    private readonly systemHealthService: SystemHealthService,
    private readonly analyticsService: AnalyticsService,
  ) {}

  @Get('sessions')
  @ApiOperation({ summary: 'Get active sessions (admin only)' })
  @ApiResponse({ status: 200, description: 'Active sessions retrieved' })
  async getSessions(@Req() req: Request) {
    return await this.adminService.getActiveSessions(req.user);
  }

  @Get('users')
  @ApiOperation({ summary: 'Get user statistics (admin only)' })
  @ApiResponse({ status: 200, description: 'User statistics retrieved' })
  async getUserStats(@Req() req: Request) {
    return await this.adminService.getUserStats(req.user);
  }

  @Get('guilds')
  @ApiOperation({ summary: 'Get guild statistics (admin only)' })
  @ApiResponse({ status: 200, description: 'Guild statistics retrieved' })
  async getGuildStats(@Req() req: Request) {
    return await this.adminService.getGuildStats(req.user);
  }


  @Post('sessions/:sessionId/revoke')
  @ApiOperation({ summary: 'Revoke a session (admin only)' })
  @ApiResponse({ status: 200, description: 'Session revoked successfully' })
  async revokeSession(
    @Param('sessionId') sessionId: string,
    @Req() req: Request,
  ) {
    return await this.adminService.revokeSession(sessionId, req.user);
  }

  @Post('cleanup/sessions')
  @ApiOperation({ summary: 'Cleanup expired sessions (admin only)' })
  @ApiResponse({ status: 200, description: 'Sessions cleaned up' })
  async cleanupSessions(@Req() req: Request) {
    return await this.adminService.cleanupExpiredSessions(req.user);
  }

  @Get('logs')
  @ApiOperation({ summary: 'Get system logs (admin only)' })
  @ApiResponse({ status: 200, description: 'System logs retrieved' })
  async getLogs(@Req() req: Request) {
    return await this.adminService.getSystemLogs(req.user);
  }

  // System Health Endpoints
  @Get('system-health')
  @ApiOperation({ summary: 'Get comprehensive system health status' })
  @ApiResponse({ status: 200, description: 'System health retrieved' })
  async getSystemHealth() {
    return await this.systemHealthService.getSystemHealth();
  }

  @Get('system-health/metrics')
  @ApiOperation({ summary: 'Get detailed system metrics' })
  @ApiResponse({ status: 200, description: 'System metrics retrieved' })
  async getSystemMetrics() {
    return await this.systemHealthService.getSystemMetrics();
  }

  @Get('system-health/performance')
  @ApiOperation({ summary: 'Get performance metrics' })
  @ApiResponse({ status: 200, description: 'Performance metrics retrieved' })
  async getPerformanceMetrics() {
    return await this.systemHealthService.getPerformanceMetrics();
  }

  @Get('system-health/alerts')
  @ApiOperation({ summary: 'Get active system alerts' })
  @ApiResponse({ status: 200, description: 'System alerts retrieved' })
  async getActiveAlerts() {
    return await this.systemHealthService.getActiveAlerts();
  }

  // Analytics Endpoints
  @Get('analytics/dashboard')
  @ApiOperation({ summary: 'Get dashboard analytics overview' })
  @ApiResponse({ status: 200, description: 'Dashboard stats retrieved' })
  async getDashboardStats() {
    return await this.analyticsService.getDashboardStats();
  }

  @Get('analytics/users')
  @ApiOperation({ summary: 'Get user analytics and trends' })
  @ApiResponse({ status: 200, description: 'User analytics retrieved' })
  async getUserAnalytics() {
    return await this.analyticsService.getUserAnalytics();
  }

  @Get('analytics/guilds')
  @ApiOperation({ summary: 'Get guild analytics and statistics' })
  @ApiResponse({ status: 200, description: 'Guild analytics retrieved' })
  async getGuildAnalytics() {
    return await this.analyticsService.getGuildAnalytics();
  }

  @Get('analytics/agents')
  @ApiOperation({ summary: 'Get AI agent interaction analytics' })
  @ApiResponse({ status: 200, description: 'Agent analytics retrieved' })
  async getAgentAnalytics() {
    return await this.analyticsService.getAgentAnalytics();
  }
}