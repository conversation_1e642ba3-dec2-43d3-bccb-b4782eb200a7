import { Controller, Get } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { DatabaseService } from '@/core/database';
import { DiscordService } from '../../discord/discord.service';

@ApiTags('health')
@Controller('health')
export class HealthController {
  constructor(
    private discordService: DiscordService,
    private databaseService: DatabaseService,
  ) {}

  @Get()
  @ApiOperation({ summary: 'Get application health status' })
  @ApiResponse({ status: 200, description: 'Health check results' })
  async check() {
    const dbHealthy = await this.databaseService.healthCheck();
    const discordHealth = await this.discordService.healthCheck();
    
    return {
      status: dbHealthy && discordHealth.status === 'healthy' ? 'ok' : 'error',
      timestamp: new Date().toISOString(),
      info: {
        database: { status: dbHealthy ? 'up' : 'down' },
        discord: discordHealth,
      },
      details: {
        database: { status: dbHealthy ? 'up' : 'down' },
        discord: discordHealth,
      },
    };
  }

  @Get('simple')
  @ApiOperation({ summary: 'Simple health check endpoint' })
  @ApiResponse({ status: 200, description: 'Simple health status' })
  async simpleCheck() {
    const dbHealthy = await this.databaseService.healthCheck();
    const discordHealth = await this.discordService.healthCheck();

    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'discord-bot-nestjs',
      version: '1.0.0',
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development',
      port: process.env.PORT || '8080',
      database: dbHealthy ? 'configured' : 'error',
      discord_token: process.env.DISCORD_TOKEN ? 'configured' : 'missing',
      discord_status: discordHealth.status,
      discord_guilds: discordHealth.guilds,
    };
  }

  @Get('ready')
  @ApiOperation({ summary: 'Deployment readiness check - lightweight for load balancers' })
  @ApiResponse({ status: 200, description: 'Service is ready to accept traffic' })
  async readinessCheck() {
    try {
      return {
        status: 'ready',
        timestamp: new Date().toISOString(),
        service: 'discord-bot-nestjs',
        uptime: Math.floor(process.uptime()),
        pid: process.pid,
        config: {
          discord_token: !!process.env.DISCORD_TOKEN,
          database_url: !!process.env.DATABASE_URL,
        }
      };
    } catch (error) {
      return {
        status: 'ready',
        timestamp: new Date().toISOString(),
        service: 'discord-bot-nestjs',
        pid: process.pid,
      };
    }
  }

  private checkRequiredEnvVars(): boolean {
    const required = ['DISCORD_TOKEN', 'DATABASE_URL', 'PORT'];
    return required.every(env => !!process.env[env]);
  }
}