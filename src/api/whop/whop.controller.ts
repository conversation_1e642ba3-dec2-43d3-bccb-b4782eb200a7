import {
  Controller,
  Get,
  Post,
  Param,
  Body,
  UseGuards,
  Req,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { Request } from 'express';
import { WhopService } from './whop.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('whop')
@Controller('whop')
export class WhopController {
  constructor(private readonly whopService: WhopService) {}

  @Get('status')
  @ApiOperation({ summary: 'Get Whop integration status' })
  @ApiResponse({ status: 200, description: 'Whop status retrieved' })
  async getStatus() {
    return await this.whopService.getStatus();
  }

  @Get('user/:userId/memberships')
  @ApiOperation({ summary: 'Get user memberships from Whop' })
  @ApiResponse({ status: 200, description: 'User memberships retrieved' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async getUserMemberships(
    @Param('userId') userId: string,
    @Req() req: Request,
  ) {
    return await this.whopService.getUserMemberships(userId, req.user);
  }

  @Get('user/me')
  @ApiOperation({ summary: 'Get current user Whop information' })
  @ApiResponse({ status: 200, description: 'Current user Whop info retrieved' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async getCurrentUser(@Req() req: Request) {
    return await this.whopService.getCurrentUserInfo(req.user);
  }

  @Get('guild/:guildId/config')
  @ApiOperation({ summary: 'Get Whop configuration for guild' })
  @ApiResponse({ status: 200, description: 'Guild Whop config retrieved' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async getGuildConfig(
    @Param('guildId') guildId: string,
    @Req() req: Request,
  ) {
    return await this.whopService.getGuildConfig(guildId, req.user);
  }

  @Post('webhook/test')
  @ApiOperation({ summary: 'Test Whop webhook (internal)' })
  @ApiResponse({ status: 200, description: 'Webhook test completed' })
  async testWebhook(@Body() payload: any) {
    return await this.whopService.handleWebhookTest(payload);
  }

  @Get('company/:companyId/access-passes')
  @ApiOperation({ summary: 'Get company access passes' })
  @ApiResponse({ status: 200, description: 'Access passes retrieved' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async getAccessPasses(
    @Param('companyId') companyId: string,
    @Req() req: Request,
  ) {
    return await this.whopService.getCompanyAccessPasses(companyId, req.user);
  }

  @Get('user/:userId/access/:accessPassId')
  @ApiOperation({ summary: 'Validate user access to specific access pass' })
  @ApiResponse({ status: 200, description: 'Access validation result' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async validateUserAccess(
    @Param('userId') userId: string,
    @Param('accessPassId') accessPassId: string,
    @Req() req: Request,
  ) {
    return await this.whopService.validateUserAccess(userId, accessPassId, req.user);
  }

  @Post('webhook')
  @ApiOperation({ summary: 'Handle Whop webhooks for membership changes' })
  @ApiResponse({ status: 200, description: 'Webhook processed successfully' })
  async handleWebhook(@Body() payload: any) {
    return await this.whopService.handleWebhookTest(payload);
  }
}