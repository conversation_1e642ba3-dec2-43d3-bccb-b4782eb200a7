/**
 * Jest setup for Docker tests
 * Configures environment and utilities for Docker testing
 */

const { execSync } = require('child_process');

// Extend Jest timeout for Docker operations
jest.setTimeout(300000); // 5 minutes

// Global test configuration
global.dockerTestConfig = {
  imageName: 'discord-bot-test',
  containerPrefix: 'discord-bot-test',
  testPort: 18080,
  buildTimeout: 300000,
  startupTimeout: 60000
};

// Utility functions for Docker tests
global.dockerUtils = {
  /**
   * Execute Docker command with error handling
   */
  execDocker: (command, options = {}) => {
    try {
      return execSync(command, {
        encoding: 'utf8',
        timeout: options.timeout || 30000,
        stdio: options.stdio || 'pipe',
        ...options
      });
    } catch (error) {
      if (options.allowFailure) {
        return null;
      }
      throw error;
    }
  },

  /**
   * Check if Docker is available and running
   */
  isDockerAvailable: () => {
    try {
      execSync('docker info', { stdio: 'ignore', timeout: 5000 });
      return true;
    } catch (error) {
      return false;
    }
  },

  /**
   * Clean up Docker resources
   */
  cleanup: (imageName, containerName) => {
    try {
      // Stop and remove container
      execSync(`docker stop ${containerName}`, { stdio: 'ignore' });
      execSync(`docker rm ${containerName}`, { stdio: 'ignore' });
    } catch (e) {
      // Ignore errors if resources don't exist
    }

    try {
      // Remove image
      execSync(`docker rmi ${imageName}`, { stdio: 'ignore' });
    } catch (e) {
      // Ignore errors if image doesn't exist
    }
  },

  /**
   * Wait for container to reach a specific state
   */
  waitForContainer: async (containerName, targetState = 'running', timeout = 30000) => {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      try {
        const status = execSync(`docker ps -a --filter "name=${containerName}" --format "{{.Status}}"`, { 
          encoding: 'utf8' 
        }).trim();

        if (targetState === 'running' && status.includes('Up')) {
          return true;
        } else if (targetState === 'exited' && status.includes('Exited')) {
          return true;
        } else if (targetState === 'any' && (status.includes('Up') || status.includes('Exited'))) {
          return true;
        }
      } catch (error) {
        // Container might not exist yet
      }

      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    return false;
  },

  /**
   * Get container logs
   */
  getContainerLogs: (containerName, lines = 50) => {
    try {
      return execSync(`docker logs --tail ${lines} ${containerName}`, { 
        encoding: 'utf8' 
      });
    } catch (error) {
      return '';
    }
  },

  /**
   * Check if port is open
   */
  isPortOpen: (port, host = 'localhost') => {
    return new Promise((resolve) => {
      const net = require('net');
      const socket = new net.Socket();

      socket.setTimeout(2000);
      
      socket.on('connect', () => {
        socket.destroy();
        resolve(true);
      });

      socket.on('timeout', () => {
        socket.destroy();
        resolve(false);
      });

      socket.on('error', () => {
        resolve(false);
      });

      socket.connect(port, host);
    });
  }
};

// Skip Docker tests if Docker is not available
beforeAll(() => {
  if (!global.dockerUtils.isDockerAvailable()) {
    console.log('⚠️  Docker not available - skipping Docker tests');
    process.exit(0);
  }
});

// Log test start
beforeAll(() => {
  console.log('🐳 Starting Docker test suite...');
});

// Global error handling
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});