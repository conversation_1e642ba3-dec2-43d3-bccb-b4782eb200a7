/**
 * Global teardown for Docker tests
 * Cleans up the environment after all Docker tests complete
 */

const { execSync } = require('child_process');

module.exports = async () => {
  console.log('🧹 Tearing down Docker test environment...');

  try {
    // Clean up test containers
    const testContainers = [
      'discord-bot-test-container',
      'discord-bot-integration-container', 
      'discord-bot-test-runner'
    ];

    console.log('🛑 Stopping and removing test containers...');
    for (const container of testContainers) {
      try {
        execSync(`docker stop ${container}`, { stdio: 'ignore', timeout: 10000 });
        execSync(`docker rm ${container}`, { stdio: 'ignore' });
        console.log(`✅ Removed container: ${container}`);
      } catch (e) {
        // Container might not exist or already be stopped
      }
    }

    // Clean up test images
    const testImages = [
      'discord-bot-test',
      'discord-bot-integration-test',
      'discord-bot-test-builder',
      'discord-bot-test-cache-test',
      'discord-bot-integration-test-builder'
    ];

    console.log('🗑️  Removing test images...');
    for (const image of testImages) {
      try {
        execSync(`docker rmi ${image}`, { stdio: 'ignore' });
        console.log(`✅ Removed image: ${image}`);
      } catch (e) {
        // Image might not exist
      }
    }

    // Clean up dangling images from tests
    try {
      console.log('🧽 Cleaning up dangling images...');
      execSync('docker image prune -f', { stdio: 'ignore' });
      console.log('✅ Dangling images cleaned up');
    } catch (error) {
      console.warn('⚠️  Warning during image cleanup:', error.message);
    }

    // Clean up any test networks (if created)
    try {
      const networks = execSync('docker network ls --filter "name=discord-bot-test" --format "{{.Name}}"', { 
        encoding: 'utf8' 
      }).trim().split('\n').filter(Boolean);

      for (const network of networks) {
        try {
          execSync(`docker network rm ${network}`, { stdio: 'ignore' });
          console.log(`✅ Removed network: ${network}`);
        } catch (e) {
          // Network might be in use or not exist
        }
      }
    } catch (error) {
      // No test networks found or error listing them
    }

    // Clean up any test volumes (if created)
    try {
      const volumes = execSync('docker volume ls --filter "name=discord-bot-test" --format "{{.Name}}"', { 
        encoding: 'utf8' 
      }).trim().split('\n').filter(Boolean);

      for (const volume of volumes) {
        try {
          execSync(`docker volume rm ${volume}`, { stdio: 'ignore' });
          console.log(`✅ Removed volume: ${volume}`);
        } catch (e) {
          // Volume might be in use or not exist
        }
      }
    } catch (error) {
      // No test volumes found or error listing them
    }

    console.log('✅ Docker test environment teardown complete!');

  } catch (error) {
    console.error('❌ Error during Docker test teardown:', error.message);
    // Don't fail teardown - this could leave resources but shouldn't break CI
  }

  // Final system prune to clean up any remaining test artifacts
  try {
    console.log('🔄 Final cleanup...');
    execSync('docker system prune -f', { stdio: 'ignore', timeout: 30000 });
    console.log('✅ Final cleanup complete');
  } catch (error) {
    console.warn('⚠️  Warning during final cleanup:', error.message);
  }
};