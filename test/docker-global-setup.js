/**
 * Global setup for Docker tests
 * Prepares the environment before running any Docker tests
 */

const { execSync } = require('child_process');
const path = require('path');

module.exports = async () => {
  console.log('🔧 Setting up Docker test environment...');

  try {
    // Check if Docker is available
    execSync('docker info', { stdio: 'ignore', timeout: 5000 });
    console.log('✅ Docker is available');
  } catch (error) {
    console.error('❌ Docker is not available or not running');
    console.error('Please ensure Docker is installed and running before running Docker tests');
    process.exit(1);
  }

  try {
    // Check if we're in the correct directory
    const packageJsonPath = path.join(process.cwd(), 'package.json');
    const dockerfilePath = path.join(process.cwd(), 'Dockerfile');
    
    if (!require('fs').existsSync(packageJsonPath)) {
      throw new Error('package.json not found - ensure tests are run from project root');
    }
    
    if (!require('fs').existsSync(dockerfilePath)) {
      throw new Error('Dockerfile not found - ensure tests are run from project root');
    }
    
    console.log('✅ Project files found');
  } catch (error) {
    console.error('❌ Project setup error:', error.message);
    process.exit(1);
  }

  try {
    // Clean up any existing test resources
    console.log('🧹 Cleaning up existing test resources...');
    
    const testImages = [
      'discord-bot-test',
      'discord-bot-integration-test',
      'discord-bot-test-builder',
      'discord-bot-test-cache-test',
      'discord-bot-integration-test-builder'
    ];
    
    const testContainers = [
      'discord-bot-test-container',
      'discord-bot-integration-container',
      'discord-bot-test-runner'
    ];

    // Stop and remove test containers
    for (const container of testContainers) {
      try {
        execSync(`docker stop ${container}`, { stdio: 'ignore' });
        execSync(`docker rm ${container}`, { stdio: 'ignore' });
      } catch (e) {
        // Ignore errors if container doesn't exist
      }
    }

    // Remove test images
    for (const image of testImages) {
      try {
        execSync(`docker rmi ${image}`, { stdio: 'ignore' });
      } catch (e) {
        // Ignore errors if image doesn't exist
      }
    }

    console.log('✅ Test environment cleaned up');
  } catch (error) {
    console.warn('⚠️  Warning during cleanup:', error.message);
    // Don't fail setup due to cleanup issues
  }

  try {
    // Prune unused Docker resources
    console.log('🗑️  Pruning unused Docker resources...');
    execSync('docker system prune -f', { stdio: 'ignore' });
    console.log('✅ Docker resources pruned');
  } catch (error) {
    console.warn('⚠️  Warning during Docker prune:', error.message);
  }

  console.log('🚀 Docker test environment setup complete!');
};