{"displayName": "Docker Tests", "testMatch": ["<rootDir>/test/docker-*.test.js"], "testEnvironment": "node", "testTimeout": 300000, "maxWorkers": 1, "setupFilesAfterEnv": ["<rootDir>/test/setup-docker-tests.js"], "globalSetup": "<rootDir>/test/docker-global-setup.js", "globalTeardown": "<rootDir>/test/docker-global-teardown.js", "collectCoverageFrom": ["src/**/*.{ts,js}", "!src/**/*.spec.{ts,js}", "!src/**/*.test.{ts,js}"], "coverageDirectory": "coverage/docker", "coverageReporters": ["text", "lcov", "html"], "verbose": true, "detectOpenHandles": true, "forceExit": true}