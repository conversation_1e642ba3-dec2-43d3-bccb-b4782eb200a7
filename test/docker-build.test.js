/**
 * Docker Build Tests for Discord Bot NestJS Application
 * Comprehensive test suite for Docker build validation
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

describe('Docker Build Tests', () => {
  const imageName = 'discord-bot-test';
  const containerName = 'discord-bot-test-container';
  
  beforeAll(async () => {
    // Cleanup any existing test containers/images
    try {
      execSync(`docker stop ${containerName}`, { stdio: 'ignore' });
      execSync(`docker rm ${containerName}`, { stdio: 'ignore' });
    } catch (e) {
      // Ignore errors if container doesn't exist
    }
    
    try {
      execSync(`docker rmi ${imageName}`, { stdio: 'ignore' });
    } catch (e) {
      // Ignore errors if image doesn't exist
    }
  });

  afterAll(async () => {
    // Cleanup test containers/images
    try {
      execSync(`docker stop ${containerName}`, { stdio: 'ignore' });
      execSync(`docker rm ${containerName}`, { stdio: 'ignore' });
      execSync(`docker rmi ${imageName}`, { stdio: 'ignore' });
    } catch (e) {
      // Ignore cleanup errors
    }
  });

  describe('Dockerfile Validation', () => {
    test('Dockerfile exists and is readable', () => {
      const dockerfilePath = path.join(__dirname, '..', 'Dockerfile');
      expect(fs.existsSync(dockerfilePath)).toBe(true);
      expect(fs.statSync(dockerfilePath).isFile()).toBe(true);
      
      const dockerfileContent = fs.readFileSync(dockerfilePath, 'utf8');
      expect(dockerfileContent.length).toBeGreaterThan(0);
    });

    test('Dockerfile contains required stages', () => {
      const dockerfilePath = path.join(__dirname, '..', 'Dockerfile');
      const dockerfileContent = fs.readFileSync(dockerfilePath, 'utf8');
      
      expect(dockerfileContent).toMatch(/FROM.*AS builder/);
      expect(dockerfileContent).toMatch(/FROM.*AS runner/);
    });

    test('Dockerfile contains required COPY commands', () => {
      const dockerfilePath = path.join(__dirname, '..', 'Dockerfile');
      const dockerfileContent = fs.readFileSync(dockerfilePath, 'utf8');
      
      expect(dockerfileContent).toMatch(/COPY package\.json/);
      expect(dockerfileContent).toMatch(/COPY pnpm-lock\.yaml/);
      expect(dockerfileContent).toMatch(/COPY.*--from=builder/);
    });

    test('Dockerfile exposes correct port', () => {
      const dockerfilePath = path.join(__dirname, '..', 'Dockerfile');
      const dockerfileContent = fs.readFileSync(dockerfilePath, 'utf8');
      
      expect(dockerfileContent).toMatch(/EXPOSE 8080/);
    });

    test('Dockerfile includes health check', () => {
      const dockerfilePath = path.join(__dirname, '..', 'Dockerfile');
      const dockerfileContent = fs.readFileSync(dockerfilePath, 'utf8');
      
      expect(dockerfileContent).toMatch(/HEALTHCHECK/);
    });
  });

  describe('Docker Build Process', () => {
    test('Docker build completes successfully', async () => {
      const buildCommand = `docker build -t ${imageName} .`;
      
      expect(() => {
        execSync(buildCommand, { 
          stdio: 'pipe',
          cwd: path.join(__dirname, '..'),
          timeout: 300000 // 5 minutes timeout
        });
      }).not.toThrow();
    }, 300000); // 5 minutes timeout

    test('Docker image is created with correct tags', () => {
      const imagesOutput = execSync('docker images --format "{{.Repository}}:{{.Tag}}"', { 
        encoding: 'utf8' 
      });
      
      expect(imagesOutput).toContain(`${imageName}:latest`);
    });

    test('Docker image has correct metadata', () => {
      const inspectOutput = execSync(`docker inspect ${imageName}`, { 
        encoding: 'utf8' 
      });
      
      const imageData = JSON.parse(inspectOutput)[0];
      
      // Check exposed ports
      expect(imageData.Config.ExposedPorts).toHaveProperty('8080/tcp');
      
      // Check environment variables
      expect(imageData.Config.Env).toEqual(
        expect.arrayContaining([
          'NODE_ENV=production',
          'PORT=8080',
          'HOST=0.0.0.0'
        ])
      );
      
      // Check user
      expect(imageData.Config.User).toBe('nestjs');
      
      // Check working directory
      expect(imageData.Config.WorkingDir).toBe('/app');
    });
  });

  describe('Multi-stage Build Validation', () => {
    test('Builder stage creates dist folder', async () => {
      // Build only the builder stage
      const builderCommand = `docker build --target builder -t ${imageName}-builder .`;
      
      expect(() => {
        execSync(builderCommand, { 
          stdio: 'pipe',
          cwd: path.join(__dirname, '..'),
          timeout: 300000
        });
      }).not.toThrow();

      // Check if dist folder exists in builder stage
      const checkDistCommand = `docker run --rm ${imageName}-builder ls -la dist`;
      
      expect(() => {
        const output = execSync(checkDistCommand, { encoding: 'utf8' });
        expect(output).toContain('main.js');
      }).not.toThrow();
      
      // Cleanup builder image
      execSync(`docker rmi ${imageName}-builder`, { stdio: 'ignore' });
    }, 300000);

    test('Production stage has minimal dependencies', () => {
      const runCommand = `docker run --rm ${imageName} pnpm list --prod --depth=0`;
      
      expect(() => {
        const output = execSync(runCommand, { 
          encoding: 'utf8',
          timeout: 30000
        });
        
        // Should not contain devDependencies
        expect(output).not.toContain('@types/');
        expect(output).not.toContain('jest');
        expect(output).not.toContain('typescript');
        expect(output).not.toContain('eslint');
      }).not.toThrow();
    });

    test('Production image size is reasonable', () => {
      const sizeOutput = execSync(`docker images ${imageName} --format "{{.Size}}"`, { 
        encoding: 'utf8' 
      }).trim();
      
      // Parse size (assuming it's in MB or GB)
      const sizeMatch = sizeOutput.match(/^(\d+(?:\.\d+)?)(MB|GB)$/);
      expect(sizeMatch).not.toBeNull();
      
      const [, sizeValue, unit] = sizeMatch;
      const sizeInMB = unit === 'GB' ? parseFloat(sizeValue) * 1024 : parseFloat(sizeValue);
      
      // Image should be less than 1GB
      expect(sizeInMB).toBeLessThan(1024);
    });
  });

  describe('Container Runtime Tests', () => {
    test('Container starts without immediate exit', async () => {
      // Create container with minimal required env vars
      const createCommand = `docker create --name ${containerName} -e NODE_ENV=production -e PORT=8080 -e DATABASE_URL=postgresql://test:test@localhost:5432/test ${imageName}`;
      
      execSync(createCommand);
      
      // Start container
      execSync(`docker start ${containerName}`);
      
      // Wait a moment for startup
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Check if container is still running or exited gracefully
      const statusOutput = execSync(`docker ps -a --filter "name=${containerName}" --format "{{.Status}}"`, { 
        encoding: 'utf8' 
      }).trim();
      
      // Container should either be running or have exited due to missing Discord token (expected)
      expect(statusOutput).toMatch(/(Up|Exited)/);
    });

    test('Container exposes correct port', () => {
      const portOutput = execSync(`docker port ${containerName}`, { 
        encoding: 'utf8' 
      }).trim();
      
      // Should show port mapping or be empty if container exited
      // This is okay since we expect it to exit without proper Discord config
    });

    test('Container has correct file permissions', () => {
      const checkPermissions = `docker exec ${containerName} ls -la /app/scripts/production-start.sh 2>/dev/null || echo "Container not running"`;
      
      try {
        const output = execSync(checkPermissions, { encoding: 'utf8' });
        if (!output.includes('Container not running')) {
          expect(output).toMatch(/-rwx/); // Should be executable
        }
      } catch (e) {
        // Container might have exited, which is expected without proper config
      }
    });
  });

  describe('Health Check Tests', () => {
    test('Health check endpoint is configured', () => {
      const dockerfilePath = path.join(__dirname, '..', 'Dockerfile');
      const dockerfileContent = fs.readFileSync(dockerfilePath, 'utf8');
      
      expect(dockerfileContent).toMatch(/HEALTHCHECK.*\/api\/health/);
    });

    test('Health check has reasonable intervals', () => {
      const dockerfilePath = path.join(__dirname, '..', 'Dockerfile');
      const dockerfileContent = fs.readFileSync(dockerfilePath, 'utf8');
      
      const healthCheckMatch = dockerfileContent.match(/HEALTHCHECK\s+--interval=(\d+)s\s+--timeout=(\d+)s\s+--start-period=(\d+)s\s+--retries=(\d+)/);
      
      expect(healthCheckMatch).not.toBeNull();
      
      const [, interval, timeout, startPeriod, retries] = healthCheckMatch;
      
      // Reasonable values for a Discord bot
      expect(parseInt(interval)).toBeGreaterThanOrEqual(30); // At least 30s interval
      expect(parseInt(timeout)).toBeGreaterThanOrEqual(10);  // At least 10s timeout
      expect(parseInt(startPeriod)).toBeGreaterThanOrEqual(30); // At least 30s start period
      expect(parseInt(retries)).toBeGreaterThanOrEqual(3);   // At least 3 retries
    });
  });

  describe('Security Tests', () => {
    test('Container runs as non-root user', () => {
      const inspectOutput = execSync(`docker inspect ${imageName}`, { 
        encoding: 'utf8' 
      });
      
      const imageData = JSON.parse(inspectOutput)[0];
      expect(imageData.Config.User).toBe('nestjs');
    });

    test('Container has appropriate user and group setup', () => {
      const checkUserCommand = `docker run --rm ${imageName} id`;
      
      const output = execSync(checkUserCommand, { encoding: 'utf8' });
      
      expect(output).toMatch(/uid=1001\(nestjs\)/);
      expect(output).toMatch(/gid=1001\(nodejs\)/);
    });

    test('Sensitive files are not exposed', () => {
      const checkFilesCommand = `docker run --rm ${imageName} find /app -name "*.env*" -o -name "*.key" -o -name "*.pem" 2>/dev/null || true`;
      
      const output = execSync(checkFilesCommand, { encoding: 'utf8' });
      
      // Should not contain any sensitive files
      expect(output.trim()).toBe('');
    });
  });

  describe('Build Cache Tests', () => {
    test('Build uses cache mounts for efficiency', () => {
      const dockerfilePath = path.join(__dirname, '..', 'Dockerfile');
      const dockerfileContent = fs.readFileSync(dockerfilePath, 'utf8');
      
      expect(dockerfileContent).toMatch(/--mount=type=cache.*pnpm/);
    });

    test('Rebuild with no changes is faster', async () => {
      const startTime = Date.now();
      
      // Second build should be much faster due to caching
      execSync(`docker build -t ${imageName}-rebuild .`, { 
        stdio: 'pipe',
        cwd: path.join(__dirname, '..'),
        timeout: 120000 // 2 minutes should be enough for cached build
      });
      
      const buildTime = Date.now() - startTime;
      
      // Cached build should complete in under 2 minutes
      expect(buildTime).toBeLessThan(120000);
      
      // Cleanup rebuild image
      execSync(`docker rmi ${imageName}-rebuild`, { stdio: 'ignore' });
    }, 120000);
  });
});