
> discord-bot-nestjs@1.0.0 start:prod
> node dist/src/main.js

[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[NestFactory] [39m[32mStarting Nest application...[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[PanelConfigService] [39m[32mRegistered panel: AI Mastery Hub (ai_mastery_welcome)[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[PanelConfigService] [39m[32mRegistered panel: Wealth Creation Command Center (wealth_creation_hub)[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[PanelConfigService] [39m[32mRegistered panel: Networking & Business Hub (networking_business_hub)[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[PanelConfigService] [39m[32mRegistered panel: Developer Marketplace (dev_on_demand_marketplace)[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[PanelConfigService] [39m[32mRegistered panel: Personal Growth Hub (personal_growth_hub)[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[PanelConfigService] [39m[32mRegistered panel: Enterprise Support Center (enterprise_support_center)[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[PanelConfigService] [39m[32mRegistered panel: AI Assistants Hub (ai_assistants_special)[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[PanelConfigService] [39m[32mInitialized 7 default panel configurations[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[GuidelinesService] [39m[32mInitialized 8 default community guidelines[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mAppModule dependencies initialized[39m[38;5;3m +29ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mCoreModule dependencies initialized[39m[38;5;3m +1ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mPassportModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mApiModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mPassportModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mFeaturesModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mMusicModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mGamingModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mUtilityModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mHttpModule dependencies initialized[39m[38;5;3m +1ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mPrometheusModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mConfigHostModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mDiscoveryModule dependencies initialized[39m[38;5;3m +1ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mTerminusModule dependencies initialized[39m[38;5;3m +2ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mConfigModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mConfigModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mScheduleModule dependencies initialized[39m[38;5;3m +7ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[MetricsService] [39m[32mMetrics service initialized with Prometheus[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[PerformanceService] [39m[32mPerformance threshold set for response_time: warning=1000, critical=5000 ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[PerformanceService] [39m[32mPerformance threshold set for database_query_time: warning=500, critical=2000 ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[PerformanceService] [39m[32mPerformance threshold set for memory_heap_used: warning=524288000, critical=1073741824 bytes[39m
[33m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [33m   WARN[39m [38;5;3m[DistributedCacheService] [39m[33mRedis URL not configured, distributed cache disabled[39m
[33m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [33m   WARN[39m [38;5;3m[ExaSearchService] [39m[33mEXA_API_KEY not found in environment variables - users will need to provide their own keys[39m
[33m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [33m   WARN[39m [38;5;3m[ExaSearchService] [39m[33mEXA_API_KEY not found in environment variables - users will need to provide their own keys[39m
[33m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [33m   WARN[39m [38;5;3m[ExaSearchService] [39m[33mEXA_API_KEY not found in environment variables - users will need to provide their own keys[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mExaSearchModule dependencies initialized[39m[38;5;3m +2ms[39m
[33m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [33m   WARN[39m [38;5;3m[ApiKeyManagerService] [39m[33m⚠️ No API_KEY_ENCRYPTION_KEY found in environment, using generated key. Set this in production![39m
[33m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [33m   WARN[39m [38;5;3m[ApiKeyManagerService] [39m[33mGenerated key: 424e3e9c09aafe139890b7782c04d0b5884ab720fb4919448a076dfa7bfb3ffe[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mNecordModule dependencies initialized[39m[38;5;3m +1ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mJwtModule dependencies initialized[39m[38;5;3m +1ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mThrottlerModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mDatabaseModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mJwtModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mSecurityModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mWelcomeModule dependencies initialized[39m[38;5;3m +1ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mLevelingModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mEconomyModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mModerationModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mStarboardModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mRoleAccessModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mReactionRoleModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mUserCommandModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mAgentsModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[PanelFeaturesService] [39m[32mInitializing panel features service...[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [95m  DEBUG[39m [38;5;3m[PanelFeaturesService] [39m[95mGetting AI tools for category: undefined, query: undefined[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[UserManagementService] [39m[32mInitializing user management service...[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[UserManagementService] [39m[32mUser management service initialized[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mServicesModule dependencies initialized[39m[38;5;3m +0ms[39m
Query: SELECT * FROM ai_tools WHERE 1=1 ORDER BY popularity DESC, name ASC
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mCacheModule dependencies initialized[39m[38;5;3m +11ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mDiscordModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mInitializing panel lifecycle service...[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[UnifiedPanelFactory] [39m[32mInitializing panel definitions...[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[UnifiedPanelFactory] [39m[32mInitialized 11 panel definitions[39m
Query: SELECT * FROM panel_deployments WHERE status IN ('pending', 'running')
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mDevOnDemandModule dependencies initialized[39m[38;5;3m +2ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mAIAutomationModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mHealthModule dependencies initialized[39m[38;5;3m +1ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mCacheModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mMonitoringModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mMultiTenancyModule dependencies initialized[39m[38;5;3m +2ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mDynamicPanelsModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mAgentsModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mChannelPanelsModule dependencies initialized[39m[38;5;3m +1ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mAIChannelModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mSecurityEnhancedModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mWhopModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mAutomationModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mAuthModule dependencies initialized[39m[38;5;3m +1ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mAdminModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mGuildsModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mMetricsController {/api/metrics}:[39m[38;5;3m +24ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/metrics, GET} route[39m[38;5;3m +2ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/metrics/performance, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/metrics/system, GET} route[39m[38;5;3m +1ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/metrics/thresholds, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mHealthController {/api/health}:[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/health, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/health/detailed, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/health/ready, GET} route[39m[38;5;3m +1ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/health/live, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mAlertsController {/api/alerts}:[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/alerts, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/alerts/active, GET} route[39m[38;5;3m +1ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/alerts/critical, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/alerts/:id, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/alerts, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/alerts/:id/resolve, PATCH} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mPrometheusController {/api/metrics}:[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/metrics, GET} route[39m[38;5;3m +1ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mOrganizationController {/api/organizations}:[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/organizations, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mTenantController {/api/tenants}:[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/tenants, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mBillingController {/api/billing}:[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/billing, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mAuthController {/api/auth}:[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/auth/login, GET} route[39m[38;5;3m +1ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/auth/callback, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/auth/session, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/auth/signout, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/auth/csrf-token, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/auth/config, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/auth/bot-invite, GET} route[39m[38;5;3m +1ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/auth/health, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mHealthController {/api/health}:[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/health, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/health/simple, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/health/ready, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mGuildsController {/api/guilds}:[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/guilds/:guildId, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/guilds/:guildId/features, GET} route[39m[38;5;3m +1ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/guilds/:guildId/features/:feature, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/guilds/:guildId/features/:feature, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/guilds/:guildId/features/:feature, DELETE} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/guilds/:guildId/features/:feature, PUT} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/guilds/:guildId/features/:feature/enable, PUT} route[39m[38;5;3m +1ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/guilds/:guildId/features/:feature/disable, PUT} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/guilds/:guildId/channels, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/guilds/:guildId/roles, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/guilds/:guildId/members, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/guilds/:guildId/stats, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/guilds/:guildId/server-info, GET} route[39m[38;5;3m +1ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/guilds/:guildId/features/ai-agents/models, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mAdminController {/api/admin}:[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/admin/sessions, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/admin/users, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/admin/guilds, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/admin/sessions/:sessionId/revoke, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/admin/cleanup/sessions, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/admin/logs, GET} route[39m[38;5;3m +1ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/admin/system-health, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/admin/system-health/metrics, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/admin/system-health/performance, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/admin/system-health/alerts, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/admin/analytics/dashboard, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/admin/analytics/users, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/admin/analytics/guilds, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/admin/analytics/agents, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mWhopController {/api/whop}:[39m[38;5;3m +1ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/whop/status, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/whop/user/:userId/memberships, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/whop/user/me, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/whop/guild/:guildId/config, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/whop/webhook/test, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/whop/company/:companyId/access-passes, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/whop/user/:userId/access/:accessPassId, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/whop/webhook, POST} route[39m[38;5;3m +1ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[DiscordService] [39m[32mDiscord module initializing...[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[DiscordService] [39m[32m🔑 Discord token configured, attempting bot login...[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[DiscordService] [39m[32m🚀 Discord bot initialization completed[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[AIAgentIntegrationService] [39m[32m🤖 Initializing AI Agent Integration Service...[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[SchedulerService] [39m[32m🕐 Initializing AI Agent Scheduler...[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[SchedulerService] [39m[32m✅ AI Agent Scheduler initialized[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[ChannelRoutingService] [39m[32m🧭 Initializing Channel Routing Service...[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[ChannelRoutingService] [39m[32m✅ Channel Routing Service initialized[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[AIAgentIntegrationService] [39m[32m✅ AI Agent Integration Service initialized successfully[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[AIAgentIntegrationService] [39m[32m🎯 Available AI agents: Personal Growth Coach, Intake Specialist, Progress Tracker[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[AIChannelAutoSetupService] [39m[32m🤖 Initializing AI Channel Auto-Setup Service...[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[AIChannelAutoSetupService] [39m[32m📡 Discord event listeners configured for auto-setup[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[AIChannelAutoSetupService] [39m[32m🔍 Checking existing guilds for AI channel setup...[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[AIChannelAutoSetupService] [39m[32m✅ Initial setup complete: 0 guilds configured[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[AIChannelAutoSetupService] [39m[32m✅ AI Channel Auto-Setup Service initialized[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[EnhancedChannelPanelOrchestratorService] [39m[32m🚀 Initializing Enhanced Channel Panel Orchestrator...[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[AutoDeploymentService] [39m[32m🔄 Auto-deployment service initialized, waiting for Discord client...[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mLoaded 0 active deployments[39m
Query: SELECT panel_id FROM panel_versions GROUP BY panel_id
[31m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [31m  ERROR[39m [38;5;3m[PanelFeaturesService] [39m[31mFailed to get AI tools:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [31m  ERROR[39m [38;5;3m[PanelFeaturesService] [39m[31mSyntaxError: Unexpected token 'c', "conversati"... is not valid JSON[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [32m    LOG[39m [38;5;3m[PanelFeaturesService] [39m[32mPanel features service initialized[39m
Query: SELECT * FROM panel_versions WHERE panel_id = $1 ORDER BY version DESC
[31m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: SELECT * FROM panel_versions WHERE panel_id = $1 ORDER BY version DESC[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: there is no parameter $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mFailed to get version history for panel trading-markets_default:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: there is no parameter $1[39m
Query: SELECT * FROM panel_versions WHERE panel_id = $1 ORDER BY version DESC
[31m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: SELECT * FROM panel_versions WHERE panel_id = $1 ORDER BY version DESC[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: there is no parameter $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mFailed to get version history for panel announcement_default:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: there is no parameter $1[39m
Query: SELECT * FROM panel_versions WHERE panel_id = $1 ORDER BY version DESC
[31m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: SELECT * FROM panel_versions WHERE panel_id = $1 ORDER BY version DESC[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: there is no parameter $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mFailed to get version history for panel wealth-creation_default:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:28 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: there is no parameter $1[39m
Query: SELECT * FROM panel_versions WHERE panel_id = $1 ORDER BY version DESC
[31m[Nest] 8808  - [39m07/31/2025, 10:07:29 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: SELECT * FROM panel_versions WHERE panel_id = $1 ORDER BY version DESC[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:29 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: there is no parameter $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:29 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mFailed to get version history for panel ai-mastery_default:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:29 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: there is no parameter $1[39m
Query: SELECT * FROM panel_versions WHERE panel_id = $1 ORDER BY version DESC
[31m[Nest] 8808  - [39m07/31/2025, 10:07:29 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: SELECT * FROM panel_versions WHERE panel_id = $1 ORDER BY version DESC[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:29 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: there is no parameter $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:29 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mFailed to get version history for panel technical-support_default:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:29 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: there is no parameter $1[39m
Query: SELECT * FROM panel_versions WHERE panel_id = $1 ORDER BY version DESC
[31m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: SELECT * FROM panel_versions WHERE panel_id = $1 ORDER BY version DESC[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: there is no parameter $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mFailed to get version history for panel networking-business_default:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: there is no parameter $1[39m
Query: SELECT * FROM panel_versions WHERE panel_id = $1 ORDER BY version DESC
[32m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [32m    LOG[39m [38;5;3m[NestApplication] [39m[32mNest application successfully started[39m[38;5;3m +118ms[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [32m    LOG[39m [38;5;3m[Bootstrap] [39m[32m🚀 Application is running on: http://0.0.0.0:8080[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [32m    LOG[39m [38;5;3m[Bootstrap] [39m[32m📚 Swagger docs: http://0.0.0.0:8080/docs[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [32m    LOG[39m [38;5;3m[Bootstrap] [39m[32m🔗 Health check: http://0.0.0.0:8080/api/health[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [32m    LOG[39m [38;5;3m[Bootstrap] [39m[32m🤖 Discord bot is initializing...[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [32m    LOG[39m [38;5;3m[DiscordService] [39m[32m✅ Discord bot connected successfully as EnergeX-Bot#0443[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [32m    LOG[39m [38;5;3m[DiscordService] [39m[32m🎯 Bot is active in 1 guilds[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [32m    LOG[39m [38;5;3m[EnhancedChannelPanelOrchestratorService] [39m[32m🔧 Initializing orchestrator components...[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [32m    LOG[39m [38;5;3m[EnhancedChannelPanelOrchestratorService] [39m[32m🏭 Creating default panels...[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [95m  DEBUG[39m [38;5;3m[UnifiedPanelFactory] [39m[95mCreating panel of type: ai-mastery[39m
Query: SELECT * FROM user_panel_states WHERE user_id = $1
[32m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [32m    LOG[39m [38;5;3m[CommandsService] [39m[32mStarted refreshing application commands.[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: SELECT * FROM panel_versions WHERE panel_id = $1 ORDER BY version DESC[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: there is no parameter $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mFailed to get version history for panel creative-showcase_default:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: there is no parameter $1[39m
Query: SELECT * FROM panel_versions WHERE panel_id = $1 ORDER BY version DESC
[31m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: SELECT * FROM user_panel_states WHERE user_id = $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: there is no parameter $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [31m  ERROR[39m [38;5;3m[UserManagementService] [39m[31mFailed to get user state for default:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [31m  ERROR[39m [38;5;3m[UserManagementService] [39m[31merror: there is no parameter $1[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel default[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [32m    LOG[39m [38;5;3m[UnifiedPanelFactory] [39m[32mSuccessfully created ai-mastery panel: ai-mastery_default[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [32m    LOG[39m [38;5;3m[EnhancedChannelPanelOrchestratorService] [39m[32m📋 Enhanced registration: Ai mastery Panel (ID: ai-mastery_default)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [95m  DEBUG[39m [38;5;3m[EnhancedChannelPanelOrchestratorService] [39m[95m✅ Created default panel: ai-mastery[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [95m  DEBUG[39m [38;5;3m[UnifiedPanelFactory] [39m[95mCreating panel of type: wealth-creation[39m
Query: SELECT * FROM user_panel_states WHERE user_id = $1
[32m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [32m    LOG[39m [38;5;3m[CommandsService] [39m[32mSuccessfully reloaded application commands.[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: SELECT * FROM panel_versions WHERE panel_id = $1 ORDER BY version DESC[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: there is no parameter $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mFailed to get version history for panel gaming-entertainment_default:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: there is no parameter $1[39m
Query: SELECT * FROM panel_versions WHERE panel_id = $1 ORDER BY version DESC
[31m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: SELECT * FROM user_panel_states WHERE user_id = $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: there is no parameter $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [31m  ERROR[39m [38;5;3m[UserManagementService] [39m[31mFailed to get user state for default:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [31m  ERROR[39m [38;5;3m[UserManagementService] [39m[31merror: there is no parameter $1[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel wealth-creation_default in channel default[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [32m    LOG[39m [38;5;3m[UnifiedPanelFactory] [39m[32mSuccessfully created wealth-creation panel: wealth-creation_default[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [32m    LOG[39m [38;5;3m[EnhancedChannelPanelOrchestratorService] [39m[32m📋 Enhanced registration: Wealth creation Panel (ID: wealth-creation_default)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [95m  DEBUG[39m [38;5;3m[EnhancedChannelPanelOrchestratorService] [39m[95m✅ Created default panel: wealth-creation[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:30 AM [95m  DEBUG[39m [38;5;3m[UnifiedPanelFactory] [39m[95mCreating panel of type: personal-growth[39m
Query: SELECT * FROM user_panel_states WHERE user_id = $1
[31m[Nest] 8808  - [39m07/31/2025, 10:07:31 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: SELECT * FROM panel_versions WHERE panel_id = $1 ORDER BY version DESC[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:31 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: there is no parameter $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:31 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mFailed to get version history for panel community_default:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:31 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: there is no parameter $1[39m
Query: SELECT * FROM panel_versions WHERE panel_id = $1 ORDER BY version DESC
[31m[Nest] 8808  - [39m07/31/2025, 10:07:31 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: SELECT * FROM user_panel_states WHERE user_id = $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:31 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: there is no parameter $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:31 AM [31m  ERROR[39m [38;5;3m[UserManagementService] [39m[31mFailed to get user state for default:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:31 AM [31m  ERROR[39m [38;5;3m[UserManagementService] [39m[31merror: there is no parameter $1[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:31 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel personal-growth_default in channel default[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:31 AM [32m    LOG[39m [38;5;3m[UnifiedPanelFactory] [39m[32mSuccessfully created personal-growth panel: personal-growth_default[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:31 AM [32m    LOG[39m [38;5;3m[EnhancedChannelPanelOrchestratorService] [39m[32m📋 Enhanced registration: Personal growth Panel (ID: personal-growth_default)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:31 AM [95m  DEBUG[39m [38;5;3m[EnhancedChannelPanelOrchestratorService] [39m[95m✅ Created default panel: personal-growth[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:31 AM [95m  DEBUG[39m [38;5;3m[UnifiedPanelFactory] [39m[95mCreating panel of type: technical-support[39m
Query: SELECT * FROM user_panel_states WHERE user_id = $1
[31m[Nest] 8808  - [39m07/31/2025, 10:07:31 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: SELECT * FROM panel_versions WHERE panel_id = $1 ORDER BY version DESC[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:31 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: there is no parameter $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:31 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mFailed to get version history for panel educational-resources_default:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:31 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: there is no parameter $1[39m
Query: SELECT * FROM panel_versions WHERE panel_id = $1 ORDER BY version DESC
[31m[Nest] 8808  - [39m07/31/2025, 10:07:31 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: SELECT * FROM user_panel_states WHERE user_id = $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:31 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: there is no parameter $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:31 AM [31m  ERROR[39m [38;5;3m[UserManagementService] [39m[31mFailed to get user state for default:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:31 AM [31m  ERROR[39m [38;5;3m[UserManagementService] [39m[31merror: there is no parameter $1[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:31 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel technical-support_default in channel default[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:31 AM [32m    LOG[39m [38;5;3m[UnifiedPanelFactory] [39m[32mSuccessfully created technical-support panel: technical-support_default[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:31 AM [32m    LOG[39m [38;5;3m[EnhancedChannelPanelOrchestratorService] [39m[32m📋 Enhanced registration: Technical support Panel (ID: technical-support_default)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:31 AM [95m  DEBUG[39m [38;5;3m[EnhancedChannelPanelOrchestratorService] [39m[95m✅ Created default panel: technical-support[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:31 AM [95m  DEBUG[39m [38;5;3m[UnifiedPanelFactory] [39m[95mCreating panel of type: trading-markets[39m
Query: SELECT * FROM user_panel_states WHERE user_id = $1
[31m[Nest] 8808  - [39m07/31/2025, 10:07:31 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: SELECT * FROM panel_versions WHERE panel_id = $1 ORDER BY version DESC[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:31 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: there is no parameter $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:31 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mFailed to get version history for panel personal-growth_default:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:31 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: there is no parameter $1[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:31 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mLoaded version histories for 11 panels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:31 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mPanel lifecycle service initialized[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:31 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: SELECT * FROM user_panel_states WHERE user_id = $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:31 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: there is no parameter $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:31 AM [31m  ERROR[39m [38;5;3m[UserManagementService] [39m[31mFailed to get user state for default:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:31 AM [31m  ERROR[39m [38;5;3m[UserManagementService] [39m[31merror: there is no parameter $1[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:31 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel trading-markets_default in channel default[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:31 AM [32m    LOG[39m [38;5;3m[UnifiedPanelFactory] [39m[32mSuccessfully created trading-markets panel: trading-markets_default[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:31 AM [32m    LOG[39m [38;5;3m[EnhancedChannelPanelOrchestratorService] [39m[32m📋 Enhanced registration: Trading markets Panel (ID: trading-markets_default)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:31 AM [95m  DEBUG[39m [38;5;3m[EnhancedChannelPanelOrchestratorService] [39m[95m✅ Created default panel: trading-markets[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:31 AM [95m  DEBUG[39m [38;5;3m[UnifiedPanelFactory] [39m[95mCreating panel of type: community[39m
Query: SELECT * FROM user_panel_states WHERE user_id = $1
[31m[Nest] 8808  - [39m07/31/2025, 10:07:32 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: SELECT * FROM user_panel_states WHERE user_id = $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:32 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: there is no parameter $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:32 AM [31m  ERROR[39m [38;5;3m[UserManagementService] [39m[31mFailed to get user state for default:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:32 AM [31m  ERROR[39m [38;5;3m[UserManagementService] [39m[31merror: there is no parameter $1[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:32 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel community_default in channel default[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:32 AM [32m    LOG[39m [38;5;3m[UnifiedPanelFactory] [39m[32mSuccessfully created community panel: community_default[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:32 AM [32m    LOG[39m [38;5;3m[EnhancedChannelPanelOrchestratorService] [39m[32m📋 Enhanced registration: Community Panel (ID: community_default)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:32 AM [95m  DEBUG[39m [38;5;3m[EnhancedChannelPanelOrchestratorService] [39m[95m✅ Created default panel: community[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:32 AM [95m  DEBUG[39m [38;5;3m[UnifiedPanelFactory] [39m[95mCreating panel of type: creative-showcase[39m
Query: SELECT * FROM user_panel_states WHERE user_id = $1
[31m[Nest] 8808  - [39m07/31/2025, 10:07:32 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: SELECT * FROM user_panel_states WHERE user_id = $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:32 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: there is no parameter $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:32 AM [31m  ERROR[39m [38;5;3m[UserManagementService] [39m[31mFailed to get user state for default:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:32 AM [31m  ERROR[39m [38;5;3m[UserManagementService] [39m[31merror: there is no parameter $1[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:32 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel creative-showcase_default in channel default[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:32 AM [32m    LOG[39m [38;5;3m[UnifiedPanelFactory] [39m[32mSuccessfully created creative-showcase panel: creative-showcase_default[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:32 AM [32m    LOG[39m [38;5;3m[EnhancedChannelPanelOrchestratorService] [39m[32m📋 Enhanced registration: Creative showcase Panel (ID: creative-showcase_default)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:32 AM [95m  DEBUG[39m [38;5;3m[EnhancedChannelPanelOrchestratorService] [39m[95m✅ Created default panel: creative-showcase[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:32 AM [95m  DEBUG[39m [38;5;3m[UnifiedPanelFactory] [39m[95mCreating panel of type: educational-resources[39m
Query: SELECT * FROM user_panel_states WHERE user_id = $1
[31m[Nest] 8808  - [39m07/31/2025, 10:07:32 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: SELECT * FROM user_panel_states WHERE user_id = $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:32 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: there is no parameter $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:32 AM [31m  ERROR[39m [38;5;3m[UserManagementService] [39m[31mFailed to get user state for default:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:32 AM [31m  ERROR[39m [38;5;3m[UserManagementService] [39m[31merror: there is no parameter $1[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:32 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel educational-resources_default in channel default[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:32 AM [32m    LOG[39m [38;5;3m[UnifiedPanelFactory] [39m[32mSuccessfully created educational-resources panel: educational-resources_default[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:32 AM [32m    LOG[39m [38;5;3m[EnhancedChannelPanelOrchestratorService] [39m[32m📋 Enhanced registration: Educational resources Panel (ID: educational-resources_default)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:32 AM [95m  DEBUG[39m [38;5;3m[EnhancedChannelPanelOrchestratorService] [39m[95m✅ Created default panel: educational-resources[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:32 AM [95m  DEBUG[39m [38;5;3m[UnifiedPanelFactory] [39m[95mCreating panel of type: gaming-entertainment[39m
Query: SELECT * FROM user_panel_states WHERE user_id = $1
[31m[Nest] 8808  - [39m07/31/2025, 10:07:32 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: SELECT * FROM user_panel_states WHERE user_id = $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:32 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: there is no parameter $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:32 AM [31m  ERROR[39m [38;5;3m[UserManagementService] [39m[31mFailed to get user state for default:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:32 AM [31m  ERROR[39m [38;5;3m[UserManagementService] [39m[31merror: there is no parameter $1[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:32 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel gaming-entertainment_default in channel default[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:32 AM [32m    LOG[39m [38;5;3m[UnifiedPanelFactory] [39m[32mSuccessfully created gaming-entertainment panel: gaming-entertainment_default[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:32 AM [32m    LOG[39m [38;5;3m[EnhancedChannelPanelOrchestratorService] [39m[32m📋 Enhanced registration: Gaming entertainment Panel (ID: gaming-entertainment_default)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:32 AM [95m  DEBUG[39m [38;5;3m[EnhancedChannelPanelOrchestratorService] [39m[95m✅ Created default panel: gaming-entertainment[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:32 AM [95m  DEBUG[39m [38;5;3m[UnifiedPanelFactory] [39m[95mCreating panel of type: networking-business[39m
Query: SELECT * FROM user_panel_states WHERE user_id = $1
[31m[Nest] 8808  - [39m07/31/2025, 10:07:33 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: SELECT * FROM user_panel_states WHERE user_id = $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:33 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: there is no parameter $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:33 AM [31m  ERROR[39m [38;5;3m[UserManagementService] [39m[31mFailed to get user state for default:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:33 AM [31m  ERROR[39m [38;5;3m[UserManagementService] [39m[31merror: there is no parameter $1[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:33 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel networking-business_default in channel default[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:33 AM [32m    LOG[39m [38;5;3m[UnifiedPanelFactory] [39m[32mSuccessfully created networking-business panel: networking-business_default[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:33 AM [32m    LOG[39m [38;5;3m[EnhancedChannelPanelOrchestratorService] [39m[32m📋 Enhanced registration: Networking business Panel (ID: networking-business_default)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:33 AM [95m  DEBUG[39m [38;5;3m[EnhancedChannelPanelOrchestratorService] [39m[95m✅ Created default panel: networking-business[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:33 AM [95m  DEBUG[39m [38;5;3m[UnifiedPanelFactory] [39m[95mCreating panel of type: announcement[39m
Query: SELECT * FROM user_panel_states WHERE user_id = $1
[31m[Nest] 8808  - [39m07/31/2025, 10:07:33 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: SELECT * FROM user_panel_states WHERE user_id = $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:33 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: there is no parameter $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:33 AM [31m  ERROR[39m [38;5;3m[UserManagementService] [39m[31mFailed to get user state for default:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:33 AM [31m  ERROR[39m [38;5;3m[UserManagementService] [39m[31merror: there is no parameter $1[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:33 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel announcement_default in channel default[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:33 AM [32m    LOG[39m [38;5;3m[UnifiedPanelFactory] [39m[32mSuccessfully created announcement panel: announcement_default[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:33 AM [32m    LOG[39m [38;5;3m[EnhancedChannelPanelOrchestratorService] [39m[32m📋 Enhanced registration: Announcement Panel (ID: announcement_default)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:33 AM [95m  DEBUG[39m [38;5;3m[EnhancedChannelPanelOrchestratorService] [39m[95m✅ Created default panel: announcement[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:33 AM [32m    LOG[39m [38;5;3m[EnhancedChannelPanelOrchestratorService] [39m[32m🎉 Created 11 default panels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:33 AM [32m    LOG[39m [38;5;3m[EnhancedChannelPanelOrchestratorService] [39m[32m📊 Registered panels: 11[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:33 AM [32m    LOG[39m [38;5;3m[EnhancedChannelPanelOrchestratorService] [39m[32m✅ Enhanced orchestrator initialized successfully[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:40 AM [32m    LOG[39m [38;5;3m[AutoDeploymentService] [39m[32m🚀 Starting automatic panel deployment...[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:40 AM [32m    LOG[39m [38;5;3m[AutoDeploymentService] [39m[32m🎯 Auto-deploying panels in guild: 1394355426941730856[39m
Query: SELECT state FROM panel_states WHERE panel_id = $1
Query: SELECT event_type, COUNT(*) as count, MAX(created_at) as last_event
         FROM panel_analytics 
         WHERE panel_id = $1 
         GROUP BY event_type
[31m[Nest] 8808  - [39m07/31/2025, 10:07:40 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: SELECT event_type, COUNT(*) as count, MAX(created_at) as last_event
         FROM panel_analytics 
         WHERE panel_id = $1 
         GROUP BY event_type[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:40 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: there is no parameter $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:40 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[31mFailed to get analytics for panel health-check:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:40 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[31merror: there is no parameter $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:40 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: SELECT state FROM panel_states WHERE panel_id = $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:40 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: there is no parameter $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:40 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[31mFailed to get panel state for health-check:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:40 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[31merror: there is no parameter $1[39m
Query: SELECT state FROM panel_states WHERE panel_id = $1
Query: SELECT event_type, COUNT(*) as count, MAX(created_at) as last_event
         FROM panel_analytics 
         WHERE panel_id = $1 
         GROUP BY event_type
[31m[Nest] 8808  - [39m07/31/2025, 10:07:40 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: SELECT event_type, COUNT(*) as count, MAX(created_at) as last_event
         FROM panel_analytics 
         WHERE panel_id = $1 
         GROUP BY event_type[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:40 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: there is no parameter $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:40 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[31mFailed to get analytics for panel health-check:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:40 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[31merror: there is no parameter $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:40 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: SELECT state FROM panel_states WHERE panel_id = $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:40 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: there is no parameter $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:40 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[31mFailed to get panel state for health-check:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:40 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[31merror: there is no parameter $1[39m
Query: SELECT * FROM panel_errors 
       WHERE panel_id = $1 AND created_at > NOW() - INTERVAL '1 hour'
       ORDER BY created_at DESC
[31m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: SELECT * FROM panel_errors 
       WHERE panel_id = $1 AND created_at > NOW() - INTERVAL '1 hour'
       ORDER BY created_at DESC[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: relation "panel_errors" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mHealth check failed for panel health-check:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: relation "panel_errors" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [32m    LOG[39m [38;5;3m[AutoDeploymentService] [39m[32m🏥 Orchestrator health: degraded[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #growth-resources (Personal Growth)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #audit-log (STAFF)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #📢-announcements (📢 ANNOUNCEMENTS)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #🤝-project-collaboration (⚡ DEV ON DEMAND)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #💼-business-talks (🤝 NETWORKING & BUSINESS)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #spec-prd-plan (DEVELOPMENT)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #⚔️-war-rooms (🤝 NETWORKING & BUSINESS)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #📊-progress-tracking (⚡ DEV ON DEMAND)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #staff-chat (STAFF)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #💵-money-strategies (💰 WEALTH CREATION)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #🏆-success-stories (💰 WEALTH CREATION)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #🛠-ai-tools (🤖 AI MASTERY)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #dev-chat (DEVELOPMENT)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #✂️-clippers-section (🤝 NETWORKING & BUSINESS)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #personal-growth-support (Personal Growth)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #rules (📢 ANNOUNCEMENTS)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #📝-project-requests (⚡ DEV ON DEMAND)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #🗨-chat (💬 COMMUNITY)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #🧠-mindset-coaching (🌱 PERSONAL GROWTH)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #🎯-goal-tracking (🌱 PERSONAL GROWTH)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #🎮-gaming-general (🎮 GAMING)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #❓-faq (🔧 SUPPORT)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #🎫-support-tickets (🔧 SUPPORT)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #🎓-ai-tutorials (🤖 AI MASTERY)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #🚀-entrepreneurship (💰 WEALTH CREATION)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #ai-agents (AI Assistants)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #⏰-productivity-tips (🌱 PERSONAL GROWTH)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #📰-news (📢 ANNOUNCEMENTS)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #progress-wins (Personal Growth)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #moderator-only (STAFF)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #💎-premium-dev (DEVELOPMENT)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #staff-logs (STAFF)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #🌐-networking (🤝 NETWORKING & BUSINESS)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #introductions (💬 COMMUNITY)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #ai-news (🤖 AI MASTERY)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #💎-premium-chat (💬 COMMUNITY)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #ai-coding (🤖 AI MASTERY)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #general (💬 COMMUNITY)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #welcome (💬 COMMUNITY)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #🖼-media (💬 COMMUNITY)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #⚙️-automation (🤖 AI MASTERY)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #💳-subscriptions (💰 WEALTH CREATION)[39m
[95m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [95m  DEBUG[39m [38;5;3m[AutoDeploymentService] [39m[95m✅ Valid channel: #links-dump (💬 COMMUNITY)[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [32m    LOG[39m [38;5;3m[AutoDeploymentService] [39m[32m📋 Deploying panels to 43 channels across 13 categories[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1399415250067132417[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1396530278662672508[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:41 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1396352345046257665[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:42 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:42 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:42 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:42 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:42 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:42 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1396595002511065150[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:42 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:42 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:42 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:42 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:42 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:42 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1396595084576817263[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:42 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:42 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:42 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:42 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:42 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:42 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1396573886534914058[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:42 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:42 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:42 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:42 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:42 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:42 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1396595106743717888[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:43 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:43 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:43 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:43 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:43 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:43 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1396595026099699753[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:43 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:43 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:43 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:43 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:43 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:43 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1394712565531148440[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:43 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:43 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:43 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:43 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:43 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:43 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1396594819224309852[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:44 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:44 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:44 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:44 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:44 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:44 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1396594859330113586[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:44 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:44 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:44 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:44 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:44 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:44 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1396594755064041482[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:44 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:44 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:44 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:44 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:44 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:44 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1394721457946493089[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:45 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:45 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:45 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:45 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:45 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:45 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1396595128831053905[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:45 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:45 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:45 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:45 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:45 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:45 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1399415247596556380[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:45 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:45 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:45 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:45 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:45 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:45 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1394499361454555207[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:46 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:46 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:46 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:46 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:46 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:46 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1396594980239179850[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:46 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:46 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:46 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:46 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:46 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:46 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1396352387475832925[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:46 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:46 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:46 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:46 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:46 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:46 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1396594898458902650[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:46 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:46 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:46 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:46 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:46 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:46 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1396594937902010390[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:47 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:47 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:47 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:47 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:47 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:47 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1396352981326237708[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:47 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:47 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:47 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:47 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:47 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:47 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1396352928037601380[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:47 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:47 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:47 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:47 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:47 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:47 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1396352904247644271[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:48 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:48 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:48 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:48 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:48 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:48 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1396594735988084817[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:48 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:48 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:48 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:48 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:48 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:48 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1396594839298244689[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:48 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:48 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:48 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:48 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:48 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:48 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1398998363243810939[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:48 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:48 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:48 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:48 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:48 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:48 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1396594919279169681[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:49 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:49 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:49 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:49 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:49 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:49 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1396352364843241592[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:49 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:49 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:49 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:49 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:49 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:49 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1399415248657842329[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:52 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:52 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:52 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:52 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:52 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:52 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1394499506480877689[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:53 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:53 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:53 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:53 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:53 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:53 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1396536930253930559[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:53 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:53 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:53 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:53 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:53 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:53 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1396530507566551051[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:53 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:53 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:53 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:53 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:53 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:53 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1396595063332667633[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:53 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:53 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:53 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:53 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:53 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:53 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1396529869730353283[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:54 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:54 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:54 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:54 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:54 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:54 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1396530075054116934[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:54 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:54 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:54 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:54 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:54 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:54 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1396536876801720330[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:54 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:54 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:54 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:54 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:54 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:54 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1396529967935787090[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:55 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:55 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:55 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:55 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:55 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:55 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1396530432513933487[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:55 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:55 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:55 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:55 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:55 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:55 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1396529696442810390[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:55 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:55 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:55 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:55 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:55 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:55 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1396352417976553553[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:55 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:55 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:55 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:55 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:55 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:55 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1396594777985777725[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1396537062202806274[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[32mStarting deployment of panel ai-mastery_default to 1 channels[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [32m    LOG[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[32mActivating panel ai-mastery_default in channel 1396530178079064154[39m
Query: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
[31m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: INSERT INTO panel_deployments (id, panel_id, config, targets, status, started_at, completed_at, results, errors)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mPanel deployment failed:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: column "targets" of relation "panel_deployments" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [32m    LOG[39m [38;5;3m[AutoDeploymentService] [39m[32m📊 Personal Growth: 0/3 panels deployed successfully[39m
[33m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [33m   WARN[39m [38;5;3m[AutoDeploymentService] [39m[33m⚠️ Personal Growth failed deployments:[39m
[33m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [33m   WARN[39m [38;5;3m[AutoDeploymentService] [39m[33mObject:[39m
[
  "1399415250067132417",
  "1399415247596556380",
  "1399415248657842329"
]

[32m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [32m    LOG[39m [38;5;3m[AutoDeploymentService] [39m[32m📊 STAFF: 0/4 panels deployed successfully[39m
[33m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [33m   WARN[39m [38;5;3m[AutoDeploymentService] [39m[33m⚠️ STAFF failed deployments:[39m
[33m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [33m   WARN[39m [38;5;3m[AutoDeploymentService] [39m[33mObject:[39m
[
  "1396530278662672508",
  "1394712565531148440",
  "1394499506480877689",
  "1396530507566551051"
]

[32m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [32m    LOG[39m [38;5;3m[AutoDeploymentService] [39m[32m📊 📢 ANNOUNCEMENTS: 0/3 panels deployed successfully[39m
[33m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [33m   WARN[39m [38;5;3m[AutoDeploymentService] [39m[33m⚠️ 📢 ANNOUNCEMENTS failed deployments:[39m
[33m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [33m   WARN[39m [38;5;3m[AutoDeploymentService] [39m[33mObject:[39m
[
  "1396352345046257665",
  "1394499361454555207",
  "1396352364843241592"
]

[32m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [32m    LOG[39m [38;5;3m[AutoDeploymentService] [39m[32m📊 ⚡ DEV ON DEMAND: 0/3 panels deployed successfully[39m
[33m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [33m   WARN[39m [38;5;3m[AutoDeploymentService] [39m[33m⚠️ ⚡ DEV ON DEMAND failed deployments:[39m
[33m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [33m   WARN[39m [38;5;3m[AutoDeploymentService] [39m[33mObject:[39m
[
  "1396595002511065150",
  "1396595026099699753",
  "1396594980239179850"
]

[32m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [32m    LOG[39m [38;5;3m[AutoDeploymentService] [39m[32m📊 🤝 NETWORKING & BUSINESS: 0/4 panels deployed successfully[39m
[33m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [33m   WARN[39m [38;5;3m[AutoDeploymentService] [39m[33m⚠️ 🤝 NETWORKING & BUSINESS failed deployments:[39m
[33m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [33m   WARN[39m [38;5;3m[AutoDeploymentService] [39m[33mObject:[39m
[
  "1396595084576817263",
  "1396595106743717888",
  "1396595128831053905",
  "1396595063332667633"
]

[32m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [32m    LOG[39m [38;5;3m[AutoDeploymentService] [39m[32m📊 DEVELOPMENT: 0/3 panels deployed successfully[39m
[33m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [33m   WARN[39m [38;5;3m[AutoDeploymentService] [39m[33m⚠️ DEVELOPMENT failed deployments:[39m
[33m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [33m   WARN[39m [38;5;3m[AutoDeploymentService] [39m[33mObject:[39m
[
  "1396573886534914058",
  "1394721457946493089",
  "1396536930253930559"
]

[32m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [32m    LOG[39m [38;5;3m[AutoDeploymentService] [39m[32m📊 💰 WEALTH CREATION: 0/4 panels deployed successfully[39m
[33m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [33m   WARN[39m [38;5;3m[AutoDeploymentService] [39m[33m⚠️ 💰 WEALTH CREATION failed deployments:[39m
[33m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [33m   WARN[39m [38;5;3m[AutoDeploymentService] [39m[33mObject:[39m
[
  "1396594819224309852",
  "1396594859330113586",
  "1396594839298244689",
  "1396537062202806274"
]

[32m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [32m    LOG[39m [38;5;3m[AutoDeploymentService] [39m[32m📊 🤖 AI MASTERY: 0/5 panels deployed successfully[39m
[33m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [33m   WARN[39m [38;5;3m[AutoDeploymentService] [39m[33m⚠️ 🤖 AI MASTERY failed deployments:[39m
[33m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [33m   WARN[39m [38;5;3m[AutoDeploymentService] [39m[33mObject:[39m
[
  "1396594755064041482",
  "1396594735988084817",
  "1396530075054116934",
  "1396529967935787090",
  "1396594777985777725"
]

[32m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [32m    LOG[39m [38;5;3m[AutoDeploymentService] [39m[32m📊 💬 COMMUNITY: 0/7 panels deployed successfully[39m
[33m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [33m   WARN[39m [38;5;3m[AutoDeploymentService] [39m[33m⚠️ 💬 COMMUNITY failed deployments:[39m
[33m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [33m   WARN[39m [38;5;3m[AutoDeploymentService] [39m[33mObject:[39m
[
  "1396352387475832925",
  "1396529869730353283",
  "1396536876801720330",
  "1396530432513933487",
  "1396529696442810390",
  "1396352417976553553",
  "1396530178079064154"
]

[32m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [32m    LOG[39m [38;5;3m[AutoDeploymentService] [39m[32m📊 🌱 PERSONAL GROWTH: 0/3 panels deployed successfully[39m
[33m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [33m   WARN[39m [38;5;3m[AutoDeploymentService] [39m[33m⚠️ 🌱 PERSONAL GROWTH failed deployments:[39m
[33m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [33m   WARN[39m [38;5;3m[AutoDeploymentService] [39m[33mObject:[39m
[
  "1396594898458902650",
  "1396594937902010390",
  "1396594919279169681"
]

[32m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [32m    LOG[39m [38;5;3m[AutoDeploymentService] [39m[32m📊 🎮 GAMING: 0/1 panels deployed successfully[39m
[33m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [33m   WARN[39m [38;5;3m[AutoDeploymentService] [39m[33m⚠️ 🎮 GAMING failed deployments:[39m
[33m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [33m   WARN[39m [38;5;3m[AutoDeploymentService] [39m[33mObject:[39m
[
  "1396352981326237708"
]

[32m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [32m    LOG[39m [38;5;3m[AutoDeploymentService] [39m[32m📊 🔧 SUPPORT: 0/2 panels deployed successfully[39m
[33m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [33m   WARN[39m [38;5;3m[AutoDeploymentService] [39m[33m⚠️ 🔧 SUPPORT failed deployments:[39m
[33m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [33m   WARN[39m [38;5;3m[AutoDeploymentService] [39m[33mObject:[39m
[
  "1396352928037601380",
  "1396352904247644271"
]

[32m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [32m    LOG[39m [38;5;3m[AutoDeploymentService] [39m[32m📊 AI Assistants: 0/1 panels deployed successfully[39m
[33m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [33m   WARN[39m [38;5;3m[AutoDeploymentService] [39m[33m⚠️ AI Assistants failed deployments:[39m
[33m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [33m   WARN[39m [38;5;3m[AutoDeploymentService] [39m[33mObject:[39m
[
  "1398998363243810939"
]

[32m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [32m    LOG[39m [38;5;3m[AutoDeploymentService] [39m[32m✅ Auto-deployment completed![39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [32m    LOG[39m [38;5;3m[AutoDeploymentService] [39m[32m   📈 Successful: 0[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [32m    LOG[39m [38;5;3m[AutoDeploymentService] [39m[32m   ❌ Failed: 43[39m
[33m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [33m   WARN[39m [38;5;3m[AutoDeploymentService] [39m[33m❌ Failed channel deployments:[39m
[33m[Nest] 8808  - [39m07/31/2025, 10:07:56 AM [33m   WARN[39m [38;5;3m[AutoDeploymentService] [39m[33mObject:[39m
[
  "1399415250067132417",
  "1396530278662672508",
  "1396352345046257665",
  "1396595002511065150",
  "1396595084576817263",
  "1396573886534914058",
  "1396595106743717888",
  "1396595026099699753",
  "1394712565531148440",
  "1396594819224309852",
  "1396594859330113586",
  "1396594755064041482",
  "1394721457946493089",
  "1396595128831053905",
  "1399415247596556380",
  "1394499361454555207",
  "1396594980239179850",
  "1396352387475832925",
  "1396594898458902650",
  "1396594937902010390",
  "1396352981326237708",
  "1396352928037601380",
  "1396352904247644271",
  "1396594735988084817",
  "1396594839298244689",
  "1398998363243810939",
  "1396594919279169681",
  "1396352364843241592",
  "1399415248657842329",
  "1394499506480877689",
  "1396536930253930559",
  "1396530507566551051",
  "1396595063332667633",
  "1396529869730353283",
  "1396530075054116934",
  "1396536876801720330",
  "1396529967935787090",
  "1396530432513933487",
  "1396529696442810390",
  "1396352417976553553",
  "1396594777985777725",
  "1396537062202806274",
  "1396530178079064154"
]

Query: SELECT state FROM panel_states WHERE panel_id = $1
Query: SELECT event_type, COUNT(*) as count, MAX(created_at) as last_event
         FROM panel_analytics 
         WHERE panel_id = $1 
         GROUP BY event_type
[31m[Nest] 8808  - [39m07/31/2025, 10:07:57 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: SELECT event_type, COUNT(*) as count, MAX(created_at) as last_event
         FROM panel_analytics 
         WHERE panel_id = $1 
         GROUP BY event_type[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:57 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: there is no parameter $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:57 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[31mFailed to get analytics for panel health-check:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:57 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[31merror: there is no parameter $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:57 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: SELECT state FROM panel_states WHERE panel_id = $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:57 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: there is no parameter $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:57 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[31mFailed to get panel state for health-check:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:57 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[31merror: there is no parameter $1[39m
Query: SELECT state FROM panel_states WHERE panel_id = $1
Query: SELECT event_type, COUNT(*) as count, MAX(created_at) as last_event
         FROM panel_analytics 
         WHERE panel_id = $1 
         GROUP BY event_type
[31m[Nest] 8808  - [39m07/31/2025, 10:07:57 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: SELECT state FROM panel_states WHERE panel_id = $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:57 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: there is no parameter $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:57 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[31mFailed to get panel state for health-check:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:57 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[31merror: there is no parameter $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:57 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: SELECT event_type, COUNT(*) as count, MAX(created_at) as last_event
         FROM panel_analytics 
         WHERE panel_id = $1 
         GROUP BY event_type[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:57 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: there is no parameter $1[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:57 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[31mFailed to get analytics for panel health-check:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:57 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelCoreService] [39m[31merror: there is no parameter $1[39m
Query: SELECT * FROM panel_errors 
       WHERE panel_id = $1 AND created_at > NOW() - INTERVAL '1 hour'
       ORDER BY created_at DESC
[31m[Nest] 8808  - [39m07/31/2025, 10:07:57 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31mDatabase query failed: SELECT * FROM panel_errors 
       WHERE panel_id = $1 AND created_at > NOW() - INTERVAL '1 hour'
       ORDER BY created_at DESC[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:57 AM [31m  ERROR[39m [38;5;3m[DatabaseService] [39m[31merror: relation "panel_errors" does not exist[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:57 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31mHealth check failed for panel health-check:[39m
[31m[Nest] 8808  - [39m07/31/2025, 10:07:57 AM [31m  ERROR[39m [38;5;3m[ConsolidatedPanelLifecycleService] [39m[31merror: relation "panel_errors" does not exist[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:57 AM [32m    LOG[39m [38;5;3m[AutoDeploymentService] [39m[32m📊 Final Deployment Statistics:[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:57 AM [32m    LOG[39m [38;5;3m[AutoDeploymentService] [39m[32m   🎯 Total active deployments: 0[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:57 AM [32m    LOG[39m [38;5;3m[AutoDeploymentService] [39m[32m   📋 Panel stats:[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:57 AM [32m    LOG[39m [38;5;3m[AutoDeploymentService] [39m[32mObject:[39m
{
  "total": 11,
  "enabled": 11,
  "registered": 11
}

[32m[Nest] 8808  - [39m07/31/2025, 10:07:57 AM [32m    LOG[39m [38;5;3m[EnhancedChannelPanelOrchestratorService] [39m[32mCache invalidation requested for panel: undefined, channel: undefined[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:57 AM [32m    LOG[39m [38;5;3m[AutoDeploymentService] [39m[32m✅ Post-deployment cleanup completed[39m
[32m[Nest] 8808  - [39m07/31/2025, 10:07:57 AM [32m    LOG[39m [38;5;3m[AutoDeploymentService] [39m[32m⏰ Scheduling periodic panel updates every 6 hours[39m
