# Database Migration Guide for Panel Refactoring

After the major panel services refactoring, several new database tables are required for the consolidated services to function properly.

## Quick Fix

Run the migration script to create all required tables:

```bash
npm run db:migrate:panels
```

## Manual Migration (Alternative)

If you prefer to run migrations manually:

```bash
node scripts/run-panel-migrations.js
```

## Required Tables

The refactoring created consolidated services that require these new tables:

### Core Panel Tables
- `panel_deployments` - Panel deployment tracking
- `panel_states` - Panel state management
- `panel_analytics` - Panel analytics and events
- `panel_content` - Panel content storage
- `panel_versions` - Panel versioning
- `panel_errors` - Panel error tracking

### User Management Tables
- `user_panel_states` - User preferences and interactions
- `user_subscriptions` - User subscription management
- `feature_usage` - Feature usage tracking

### Panel Features Tables
- `ai_tools` - AI tools catalog
- `user_tool_interactions` - User tool interaction tracking
- `habit_tracking` - Habit tracking data
- `financial_calculations` - Financial calculation history
- `troubleshooting_guides` - Troubleshooting knowledge base

### Agent System Tables
- `agent_memory` - Agent memory storage
- `agent_sessions` - Agent session management
- `agent_interactions` - Agent interaction tracking

## Error Recovery

If you see errors like:
```
ERROR [DatabaseService] error: relation "panel_deployments" does not exist
```

This means the database tables haven't been created yet. Simply run:

```bash
npm run db:migrate:panels
```

## Graceful Degradation

The consolidated services are designed to handle missing tables gracefully during startup:
- They will log warnings about missing tables
- They will continue to operate with empty state
- No functionality will crash the application

However, full functionality requires the database tables to be present.

## Migration Safety

The migration scripts are designed to be:
- **Idempotent**: Safe to run multiple times
- **Non-destructive**: Won't drop existing data
- **Backward compatible**: Works with existing database schemas

## Verification

After running migrations, the script will verify that all critical tables exist and report the status.

## Troubleshooting

### Connection Issues
- Ensure database credentials are correct in environment variables
- Check `DATABASE_HOST`, `DATABASE_NAME`, `DATABASE_USER`, `DATABASE_PASSWORD`

### Permission Issues
- Ensure the database user has CREATE TABLE privileges
- Grant necessary permissions as shown in the migration files

### Migration Failures
- Check the console output for specific error messages
- Most errors related to "already exists" are harmless
- For persistent issues, check database logs