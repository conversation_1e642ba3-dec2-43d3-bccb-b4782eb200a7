# Docker Build Tests

Comprehensive test suite for validating Docker build process, container functionality, and production readiness of the Discord Bot NestJS application.

## Overview

This test suite provides thorough validation of:
- Docker build process and multi-stage builds
- Container security and best practices
- Runtime behavior and health checks
- Integration testing with minimal dependencies
- Performance and resource usage validation

## Test Files

### 1. `test/docker-build.test.js`
Core Docker build validation tests including:
- **Dockerfile Validation**: Structure, syntax, and required commands
- **Build Process**: Multi-stage build validation and artifact verification
- **Image Metadata**: Environment variables, ports, user configuration
- **Security**: Non-root user, file permissions, sensitive data protection
- **Cache Efficiency**: Build cache usage and performance

### 2. `test/docker-integration.test.js`
Container runtime and integration tests including:
- **Container Lifecycle**: Startup, shutdown, restart behavior
- **Environment Configuration**: Variable validation and error handling
- **Health Checks**: Endpoint availability and status reporting
- **Resource Usage**: Memory and CPU consumption monitoring
- **Network**: Port exposure and connectivity validation
- **File System**: Application files and permissions verification

### 3. `scripts/test-docker-build.sh`
Standalone bash script for comprehensive Docker testing:
- **Prerequisites Check**: Docker availability and project setup
- **Build Validation**: Full build process with timing
- **Security Testing**: User configuration and sensitive file detection
- **Performance Testing**: Build cache efficiency and resource usage
- **Production Readiness**: Complete validation pipeline

## Running Tests

### Jest-based Tests

```bash
# Run all Docker tests
pnpm run test:docker

# Run only quick build tests
pnpm run test:docker:quick

# Run with verbose output
pnpm run test:docker -- --verbose
```

### Standalone Script

```bash
# Run comprehensive build validation
pnpm run test:docker:build

# Or run directly
./scripts/test-docker-build.sh
```

## Test Configuration

### Jest Configuration (`test/jest-docker.json`)
- **Timeout**: 5 minutes for build operations
- **Workers**: Single worker to prevent resource conflicts
- **Environment**: Node.js with Docker utilities
- **Global Setup/Teardown**: Automated cleanup

### Environment Variables
Tests use isolated test containers and images:
- **Image Name**: `discord-bot-test`, `discord-bot-integration-test`
- **Container Names**: `discord-bot-test-container`, etc.
- **Test Port**: `18080` (to avoid conflicts)

## Test Categories

### 1. Dockerfile Structure Tests
- ✅ Multi-stage build configuration
- ✅ Required commands (WORKDIR, COPY, RUN, EXPOSE, CMD, HEALTHCHECK)
- ✅ Security best practices (non-root user)
- ✅ Port exposure (8080)
- ✅ Environment variables

### 2. Build Process Tests
- ✅ Successful build completion
- ✅ Image creation and tagging
- ✅ Build artifact validation
- ✅ Production dependency installation
- ✅ Cache mount utilization

### 3. Multi-stage Build Tests
- ✅ Builder stage functionality
- ✅ Artifact transfer to production stage
- ✅ Image size optimization
- ✅ Development dependency exclusion

### 4. Security Tests
- ✅ Non-root user execution (nestjs:nodejs)
- ✅ File permission validation
- ✅ Sensitive file exclusion (.env, .key, .pem)
- ✅ User/group configuration

### 5. Runtime Tests
- ✅ Container startup behavior
- ✅ Environment variable validation
- ✅ Graceful shutdown handling
- ✅ Health check functionality
- ✅ Port accessibility

### 6. Integration Tests
- ✅ Database connection validation
- ✅ Discord token configuration
- ✅ API endpoint availability
- ✅ Log output verification
- ✅ Error handling

### 7. Performance Tests
- ✅ Build time optimization
- ✅ Image size validation
- ✅ Memory usage monitoring
- ✅ CPU consumption tracking
- ✅ Cache efficiency

## Prerequisites

### Required Tools
- **Docker**: Engine version 20.10+
- **Node.js**: Version 18+
- **pnpm**: Version 10.12.4+
- **Jest**: Testing framework
- **curl**: For health check validation

### System Requirements
- **Memory**: Minimum 2GB available for Docker
- **Disk Space**: 5GB for build artifacts and images
- **Network**: Internet access for base image downloads

## Expected Behavior

### Normal Operation
1. **Build Success**: Clean build without errors
2. **Container Start**: Successful startup with proper configuration
3. **Health Check**: Responsive health endpoint
4. **Graceful Exit**: Proper shutdown on missing Discord token

### Expected Failures
- **Missing Environment Variables**: Container exits with error code
- **Database Connection**: May fail but shouldn't crash startup
- **Discord Token**: Invalid token causes graceful exit

## Troubleshooting

### Common Issues

#### Build Failures
```bash
# Check Docker daemon
docker info

# Clean build cache
docker builder prune -f

# Rebuild without cache
docker build --no-cache -t discord-bot-test .
```

#### Container Issues
```bash
# Check container logs
docker logs discord-bot-test-container

# Inspect container state
docker inspect discord-bot-test-container

# Check resource usage
docker stats discord-bot-test-container
```

#### Test Failures
```bash
# Run tests with debug output
DEBUG=* pnpm run test:docker

# Clean test environment
docker system prune -f
docker volume prune -f
```

### Environment Issues

#### Port Conflicts
Tests use port 18080 to avoid conflicts. If still experiencing issues:
```bash
# Check port usage
netstat -tulpn | grep 18080
# or
ss -tulpn | grep 18080
```

#### Permission Issues
Ensure Docker can be run without sudo:
```bash
sudo usermod -aG docker $USER
# Logout and login again
```

## CI/CD Integration

### GitHub Actions Example
```yaml
name: Docker Build Tests
on: [push, pull_request]

jobs:
  docker-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '18'
      - run: pnpm install
      - run: pnpm run test:docker:build
      - run: pnpm run test:docker
```

### Local Development
```bash
# Quick validation before commit
pnpm run test:docker:quick

# Full validation before push
pnpm run test:docker:build && pnpm run test:docker
```

## Maintenance

### Regular Updates
1. **Base Image**: Update Node.js base image versions
2. **Dependencies**: Keep pnpm and package versions current
3. **Security**: Regular security scanning of images
4. **Performance**: Monitor build times and image sizes

### Test Monitoring
- **Build Times**: Should remain under 5 minutes
- **Image Size**: Target under 1GB for production
- **Memory Usage**: Container should use <512MB at startup
- **Test Duration**: Full suite should complete in <10 minutes

## Security Considerations

### Image Security
- ✅ Non-root user execution
- ✅ Minimal attack surface
- ✅ No sensitive data in layers
- ✅ Regular base image updates

### Test Security
- ✅ Isolated test environment
- ✅ Cleanup of test artifacts
- ✅ No production data in tests
- ✅ Secure environment variable handling

## Performance Benchmarks

### Build Performance
- **Initial Build**: <5 minutes
- **Cached Build**: <1 minute
- **Image Size**: <1GB
- **Layer Count**: <20 layers

### Runtime Performance
- **Startup Time**: <30 seconds
- **Memory Usage**: <512MB initial
- **Health Check**: <5 seconds response
- **Shutdown Time**: <10 seconds

## Contributing

When adding new Docker tests:

1. **Follow Patterns**: Use existing test structure and utilities
2. **Add Cleanup**: Ensure proper resource cleanup
3. **Document Changes**: Update this README
4. **Test Isolation**: Don't interfere with other tests
5. **Performance**: Consider impact on test suite duration

### Test Template
```javascript
describe('New Docker Feature Tests', () => {
  const testImage = 'discord-bot-feature-test';
  const testContainer = 'discord-bot-feature-container';
  
  afterEach(() => {
    global.dockerUtils.cleanup(testImage, testContainer);
  });
  
  test('should validate new feature', () => {
    // Test implementation
  });
});
```