const fs = require('fs');
const path = require('path');

// List of files that need drizzle imports
const filesToFix = [
  'src/features/ai-channel/api-key-manager.service.ts',
  'src/features/ai-automation/ai-automation.service.ts',
  'src/core/multi-tenancy/organization.service.ts',
  'src/core/services/membership.service.ts',
  'src/core/security/session.service.ts',
  'src/discord/discord.service.ts',
  'src/discord/events/discord-events.service.ts',
  'src/features/channel-panels/services/trading-database.service.ts',
  'src/features/channel-panels/services/dynamic-content.service.ts',
  'src/features/channel-panels/services/networking-database.service.ts'
];

// The import statement to add
const drizzleImport = "import { eq, and, or, desc, asc, count, sql } from 'drizzle-orm';";

filesToFix.forEach(filePath => {
  const fullPath = path.join(__dirname, filePath);
  
  try {
    let content = fs.readFileSync(fullPath, 'utf8');
    
    // Check if drizzle-orm import already exists
    if (content.includes("from 'drizzle-orm'")) {
      console.log(`✓ ${filePath} already has drizzle-orm imports`);
      return;
    }
    
    // Find the position after the last import statement
    const importRegex = /^import .* from .*;$/gm;
    let lastImportMatch = null;
    let match;
    
    while ((match = importRegex.exec(content)) !== null) {
      lastImportMatch = match;
    }
    
    if (lastImportMatch) {
      // Insert the drizzle import after the last import
      const insertPosition = lastImportMatch.index + lastImportMatch[0].length;
      content = content.slice(0, insertPosition) + '\n' + drizzleImport + content.slice(insertPosition);
      
      fs.writeFileSync(fullPath, content, 'utf8');
      console.log(`✅ Fixed ${filePath}`);
    } else {
      console.log(`⚠️  Could not find import statements in ${filePath}`);
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
  }
});

console.log('\nDone! All drizzle imports have been added.');
