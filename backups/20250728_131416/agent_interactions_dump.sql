-- Table: agent_interactions
-- Generated on: 2025-07-28T18:15:03.830Z

CREATE TABLE IF NOT EXISTS agent_interactions (
  id integer NOT NULL DEFAULT nextval('agent_interactions_id_seq'::regclass),
  user_relationship_id integer NOT NULL,
  agent_type character varying(100) NOT NULL,
  interaction_type character varying(100) NOT NULL,
  channel_type character varying(50) NOT NULL,
  channel_id character varying(255),
  message_id character varying(255),
  user_message text,
  agent_response text,
  conversation_context jsonb DEFAULT '{}'::jsonb,
  sentiment_score real,
  emotional_state character varying(50),
  topics_discussed ARRAY DEFAULT '{}'::text[],
  action_items ARRAY DEFAULT '{}'::text[],
  user_responded boolean DEFAULT false,
  response_time_minutes integer,
  response_quality character varying(50),
  interaction_successful boolean DEFAULT true,
  effectiveness_score integer DEFAULT 50,
  led_to_positive_action boolean DEFAULT false,
  requires_followup boolean DEFAULT false,
  followup_scheduled_for timestamp without time zone,
  followup_completed boolean DEFAULT false,
  interaction_started_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
  interaction_completed_at timestamp without time zone,
  created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp with time zone DEFAULT now(),
  user_id text NOT NULL DEFAULT ''::text
);

