-- Table: agent_memory
-- Generated on: 2025-07-28T18:15:03.627Z

CREATE TABLE IF NOT EXISTS agent_memory (
  id integer NOT NULL DEFAULT nextval('agent_memory_id_seq'::regclass),
  user_relationship_id integer NOT NULL,
  memory_type character varying(50) NOT NULL,
  content text NOT NULL,
  summary text,
  tags ARRAY DEFAULT '{}'::text[],
  importance_score integer DEFAULT 5,
  emotional_weight character varying(50),
  related_memories ARRAY DEFAULT '{}'::integer[],
  triggers ARRAY DEFAULT '{}'::text[],
  memory_date date,
  conversation_id integer,
  times_recalled integer DEFAULT 0,
  last_recalled_at timestamp without time zone,
  relevance_score real DEFAULT 1.0,
  is_active boolean DEFAULT true,
  is_verified boolean DEFAULT false,
  needs_followup boolean DEFAULT false,
  created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
  deleted_at timestamp with time zone,
  user_id text NOT NULL DEFAULT ''::text
);

