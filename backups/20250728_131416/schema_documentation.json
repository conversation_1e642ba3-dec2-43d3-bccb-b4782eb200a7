{"timestamp": "2025-07-28T18:15:03.067Z", "database": "ep-summer-waterfall-addfdey0-pooler.c-2.us-east-1.aws.neon.tech", "tables": {"agent_memory": {"columns": [{"column_name": "id", "data_type": "integer", "character_maximum_length": null, "is_nullable": "NO", "column_default": "nextval('agent_memory_id_seq'::regclass)", "udt_name": "int4"}, {"column_name": "user_relationship_id", "data_type": "integer", "character_maximum_length": null, "is_nullable": "NO", "column_default": null, "udt_name": "int4"}, {"column_name": "memory_type", "data_type": "character varying", "character_maximum_length": 50, "is_nullable": "NO", "column_default": null, "udt_name": "<PERSON><PERSON><PERSON>"}, {"column_name": "content", "data_type": "text", "character_maximum_length": null, "is_nullable": "NO", "column_default": null, "udt_name": "text"}, {"column_name": "summary", "data_type": "text", "character_maximum_length": null, "is_nullable": "YES", "column_default": null, "udt_name": "text"}, {"column_name": "tags", "data_type": "ARRAY", "character_maximum_length": null, "is_nullable": "YES", "column_default": "'{}'::text[]", "udt_name": "_text"}, {"column_name": "importance_score", "data_type": "integer", "character_maximum_length": null, "is_nullable": "YES", "column_default": "5", "udt_name": "int4"}, {"column_name": "emotional_weight", "data_type": "character varying", "character_maximum_length": 50, "is_nullable": "YES", "column_default": null, "udt_name": "<PERSON><PERSON><PERSON>"}, {"column_name": "related_memories", "data_type": "ARRAY", "character_maximum_length": null, "is_nullable": "YES", "column_default": "'{}'::integer[]", "udt_name": "_int4"}, {"column_name": "triggers", "data_type": "ARRAY", "character_maximum_length": null, "is_nullable": "YES", "column_default": "'{}'::text[]", "udt_name": "_text"}, {"column_name": "memory_date", "data_type": "date", "character_maximum_length": null, "is_nullable": "YES", "column_default": null, "udt_name": "date"}, {"column_name": "conversation_id", "data_type": "integer", "character_maximum_length": null, "is_nullable": "YES", "column_default": null, "udt_name": "int4"}, {"column_name": "times_recalled", "data_type": "integer", "character_maximum_length": null, "is_nullable": "YES", "column_default": "0", "udt_name": "int4"}, {"column_name": "last_recalled_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "is_nullable": "YES", "column_default": null, "udt_name": "timestamp"}, {"column_name": "relevance_score", "data_type": "real", "character_maximum_length": null, "is_nullable": "YES", "column_default": "1.0", "udt_name": "float4"}, {"column_name": "is_active", "data_type": "boolean", "character_maximum_length": null, "is_nullable": "YES", "column_default": "true", "udt_name": "bool"}, {"column_name": "is_verified", "data_type": "boolean", "character_maximum_length": null, "is_nullable": "YES", "column_default": "false", "udt_name": "bool"}, {"column_name": "needs_followup", "data_type": "boolean", "character_maximum_length": null, "is_nullable": "YES", "column_default": "false", "udt_name": "bool"}, {"column_name": "created_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "udt_name": "timestamp"}, {"column_name": "updated_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "udt_name": "timestamp"}, {"column_name": "deleted_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "is_nullable": "YES", "column_default": null, "udt_name": "timestamptz"}, {"column_name": "user_id", "data_type": "text", "character_maximum_length": null, "is_nullable": "NO", "column_default": "''::text", "udt_name": "text"}], "rowCount": 0, "constraints": [{"constraint_name": "agent_memory_valid_importance", "constraint_type": "c", "definition": "CHECK (((importance_score >= 1) AND (importance_score <= 10)))"}, {"constraint_name": "agent_memory_valid_relevance", "constraint_type": "c", "definition": "CHECK (((relevance_score >= (0.0)::double precision) AND (relevance_score <= (1.0)::double precision)))"}, {"constraint_name": "agent_memory_pkey", "constraint_type": "p", "definition": "PRIMARY KEY (id)"}, {"constraint_name": "agent_memory_user_relationship_id_fkey", "constraint_type": "f", "definition": "FOREIGN KEY (user_relationship_id) REFERENCES user_relationships(id) ON DELETE CASCADE"}], "indexes": [{"indexname": "agent_memory_pkey", "indexdef": "CREATE UNIQUE INDEX agent_memory_pkey ON public.agent_memory USING btree (id)"}, {"indexname": "idx_agent_memory_user_relationship", "indexdef": "CREATE INDEX idx_agent_memory_user_relationship ON public.agent_memory USING btree (user_relationship_id)"}, {"indexname": "idx_agent_memory_type", "indexdef": "CREATE INDEX idx_agent_memory_type ON public.agent_memory USING btree (memory_type)"}, {"indexname": "idx_agent_memory_importance", "indexdef": "CREATE INDEX idx_agent_memory_importance ON public.agent_memory USING btree (importance_score DESC)"}, {"indexname": "idx_agent_memory_tags", "indexdef": "CREATE INDEX idx_agent_memory_tags ON public.agent_memory USING gin (tags)"}, {"indexname": "idx_agent_memory_triggers", "indexdef": "CREATE INDEX idx_agent_memory_triggers ON public.agent_memory USING gin (triggers)"}, {"indexname": "idx_agent_memory_active", "indexdef": "CREATE INDEX idx_agent_memory_active ON public.agent_memory USING btree (is_active)"}, {"indexname": "agent_memory_user_id_idx", "indexdef": "CREATE INDEX agent_memory_user_id_idx ON public.agent_memory USING btree (user_id)"}, {"indexname": "agent_memory_memory_type_idx", "indexdef": "CREATE INDEX agent_memory_memory_type_idx ON public.agent_memory USING btree (memory_type)"}, {"indexname": "agent_memory_created_at_idx", "indexdef": "CREATE INDEX agent_memory_created_at_idx ON public.agent_memory USING btree (created_at)"}]}, "agent_interactions": {"columns": [{"column_name": "id", "data_type": "integer", "character_maximum_length": null, "is_nullable": "NO", "column_default": "nextval('agent_interactions_id_seq'::regclass)", "udt_name": "int4"}, {"column_name": "user_relationship_id", "data_type": "integer", "character_maximum_length": null, "is_nullable": "NO", "column_default": null, "udt_name": "int4"}, {"column_name": "agent_type", "data_type": "character varying", "character_maximum_length": 100, "is_nullable": "NO", "column_default": null, "udt_name": "<PERSON><PERSON><PERSON>"}, {"column_name": "interaction_type", "data_type": "character varying", "character_maximum_length": 100, "is_nullable": "NO", "column_default": null, "udt_name": "<PERSON><PERSON><PERSON>"}, {"column_name": "channel_type", "data_type": "character varying", "character_maximum_length": 50, "is_nullable": "NO", "column_default": null, "udt_name": "<PERSON><PERSON><PERSON>"}, {"column_name": "channel_id", "data_type": "character varying", "character_maximum_length": 255, "is_nullable": "YES", "column_default": null, "udt_name": "<PERSON><PERSON><PERSON>"}, {"column_name": "message_id", "data_type": "character varying", "character_maximum_length": 255, "is_nullable": "YES", "column_default": null, "udt_name": "<PERSON><PERSON><PERSON>"}, {"column_name": "user_message", "data_type": "text", "character_maximum_length": null, "is_nullable": "YES", "column_default": null, "udt_name": "text"}, {"column_name": "agent_response", "data_type": "text", "character_maximum_length": null, "is_nullable": "YES", "column_default": null, "udt_name": "text"}, {"column_name": "conversation_context", "data_type": "jsonb", "character_maximum_length": null, "is_nullable": "YES", "column_default": "'{}'::jsonb", "udt_name": "jsonb"}, {"column_name": "sentiment_score", "data_type": "real", "character_maximum_length": null, "is_nullable": "YES", "column_default": null, "udt_name": "float4"}, {"column_name": "emotional_state", "data_type": "character varying", "character_maximum_length": 50, "is_nullable": "YES", "column_default": null, "udt_name": "<PERSON><PERSON><PERSON>"}, {"column_name": "topics_discussed", "data_type": "ARRAY", "character_maximum_length": null, "is_nullable": "YES", "column_default": "'{}'::text[]", "udt_name": "_text"}, {"column_name": "action_items", "data_type": "ARRAY", "character_maximum_length": null, "is_nullable": "YES", "column_default": "'{}'::text[]", "udt_name": "_text"}, {"column_name": "user_responded", "data_type": "boolean", "character_maximum_length": null, "is_nullable": "YES", "column_default": "false", "udt_name": "bool"}, {"column_name": "response_time_minutes", "data_type": "integer", "character_maximum_length": null, "is_nullable": "YES", "column_default": null, "udt_name": "int4"}, {"column_name": "response_quality", "data_type": "character varying", "character_maximum_length": 50, "is_nullable": "YES", "column_default": null, "udt_name": "<PERSON><PERSON><PERSON>"}, {"column_name": "interaction_successful", "data_type": "boolean", "character_maximum_length": null, "is_nullable": "YES", "column_default": "true", "udt_name": "bool"}, {"column_name": "effectiveness_score", "data_type": "integer", "character_maximum_length": null, "is_nullable": "YES", "column_default": "50", "udt_name": "int4"}, {"column_name": "led_to_positive_action", "data_type": "boolean", "character_maximum_length": null, "is_nullable": "YES", "column_default": "false", "udt_name": "bool"}, {"column_name": "requires_followup", "data_type": "boolean", "character_maximum_length": null, "is_nullable": "YES", "column_default": "false", "udt_name": "bool"}, {"column_name": "followup_scheduled_for", "data_type": "timestamp without time zone", "character_maximum_length": null, "is_nullable": "YES", "column_default": null, "udt_name": "timestamp"}, {"column_name": "followup_completed", "data_type": "boolean", "character_maximum_length": null, "is_nullable": "YES", "column_default": "false", "udt_name": "bool"}, {"column_name": "interaction_started_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "udt_name": "timestamp"}, {"column_name": "interaction_completed_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "is_nullable": "YES", "column_default": null, "udt_name": "timestamp"}, {"column_name": "created_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "udt_name": "timestamp"}, {"column_name": "updated_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "is_nullable": "YES", "column_default": "now()", "udt_name": "timestamptz"}, {"column_name": "user_id", "data_type": "text", "character_maximum_length": null, "is_nullable": "NO", "column_default": "''::text", "udt_name": "text"}], "rowCount": 0, "constraints": [{"constraint_name": "agent_interactions_valid_sentiment", "constraint_type": "c", "definition": "CHECK (((sentiment_score >= ('-1.0'::numeric)::double precision) AND (sentiment_score <= (1.0)::double precision)))"}, {"constraint_name": "agent_interactions_valid_effectiveness", "constraint_type": "c", "definition": "CHECK (((effectiveness_score >= 0) AND (effectiveness_score <= 100)))"}, {"constraint_name": "agent_interactions_pkey", "constraint_type": "p", "definition": "PRIMARY KEY (id)"}, {"constraint_name": "agent_interactions_user_relationship_id_fkey", "constraint_type": "f", "definition": "FOREIGN KEY (user_relationship_id) REFERENCES user_relationships(id) ON DELETE CASCADE"}], "indexes": [{"indexname": "agent_interactions_pkey", "indexdef": "CREATE UNIQUE INDEX agent_interactions_pkey ON public.agent_interactions USING btree (id)"}, {"indexname": "idx_agent_interactions_user_relationship", "indexdef": "CREATE INDEX idx_agent_interactions_user_relationship ON public.agent_interactions USING btree (user_relationship_id)"}, {"indexname": "idx_agent_interactions_agent_type", "indexdef": "CREATE INDEX idx_agent_interactions_agent_type ON public.agent_interactions USING btree (agent_type)"}, {"indexname": "idx_agent_interactions_created_at", "indexdef": "CREATE INDEX idx_agent_interactions_created_at ON public.agent_interactions USING btree (created_at DESC)"}, {"indexname": "idx_agent_interactions_requires_followup", "indexdef": "CREATE INDEX idx_agent_interactions_requires_followup ON public.agent_interactions USING btree (requires_followup)"}, {"indexname": "idx_agent_interactions_sentiment", "indexdef": "CREATE INDEX idx_agent_interactions_sentiment ON public.agent_interactions USING btree (sentiment_score)"}, {"indexname": "agent_interactions_agent_type_idx", "indexdef": "CREATE INDEX agent_interactions_agent_type_idx ON public.agent_interactions USING btree (agent_type)"}, {"indexname": "agent_interactions_interaction_type_idx", "indexdef": "CREATE INDEX agent_interactions_interaction_type_idx ON public.agent_interactions USING btree (interaction_type)"}, {"indexname": "agent_interactions_created_at_idx", "indexdef": "CREATE INDEX agent_interactions_created_at_idx ON public.agent_interactions USING btree (created_at)"}, {"indexname": "agent_interactions_user_id_idx", "indexdef": "CREATE INDEX agent_interactions_user_id_idx ON public.agent_interactions USING btree (user_id)"}]}, "users": {"columns": [{"column_name": "id", "data_type": "character varying", "character_maximum_length": 255, "is_nullable": "NO", "column_default": null, "udt_name": "<PERSON><PERSON><PERSON>"}, {"column_name": "username", "data_type": "character varying", "character_maximum_length": 255, "is_nullable": "NO", "column_default": null, "udt_name": "<PERSON><PERSON><PERSON>"}, {"column_name": "discriminator", "data_type": "character varying", "character_maximum_length": 4, "is_nullable": "YES", "column_default": null, "udt_name": "<PERSON><PERSON><PERSON>"}, {"column_name": "global_name", "data_type": "character varying", "character_maximum_length": 255, "is_nullable": "YES", "column_default": null, "udt_name": "<PERSON><PERSON><PERSON>"}, {"column_name": "avatar", "data_type": "character varying", "character_maximum_length": 255, "is_nullable": "YES", "column_default": null, "udt_name": "<PERSON><PERSON><PERSON>"}, {"column_name": "avatar_url", "data_type": "text", "character_maximum_length": null, "is_nullable": "YES", "column_default": null, "udt_name": "text"}, {"column_name": "email", "data_type": "character varying", "character_maximum_length": 255, "is_nullable": "YES", "column_default": null, "udt_name": "<PERSON><PERSON><PERSON>"}, {"column_name": "verified", "data_type": "boolean", "character_maximum_length": null, "is_nullable": "YES", "column_default": "false", "udt_name": "bool"}, {"column_name": "locale", "data_type": "character varying", "character_maximum_length": 10, "is_nullable": "YES", "column_default": null, "udt_name": "<PERSON><PERSON><PERSON>"}, {"column_name": "flags", "data_type": "integer", "character_maximum_length": null, "is_nullable": "YES", "column_default": "0", "udt_name": "int4"}, {"column_name": "premium_type", "data_type": "integer", "character_maximum_length": null, "is_nullable": "YES", "column_default": "0", "udt_name": "int4"}, {"column_name": "public_flags", "data_type": "integer", "character_maximum_length": null, "is_nullable": "YES", "column_default": "0", "udt_name": "int4"}, {"column_name": "mfa_enabled", "data_type": "boolean", "character_maximum_length": null, "is_nullable": "YES", "column_default": "false", "udt_name": "bool"}, {"column_name": "banner", "data_type": "character varying", "character_maximum_length": 255, "is_nullable": "YES", "column_default": null, "udt_name": "<PERSON><PERSON><PERSON>"}, {"column_name": "banner_url", "data_type": "text", "character_maximum_length": null, "is_nullable": "YES", "column_default": null, "udt_name": "text"}, {"column_name": "accent_color", "data_type": "integer", "character_maximum_length": null, "is_nullable": "YES", "column_default": null, "udt_name": "int4"}, {"column_name": "access_token_encrypted", "data_type": "text", "character_maximum_length": null, "is_nullable": "YES", "column_default": null, "udt_name": "text"}, {"column_name": "refresh_token_encrypted", "data_type": "text", "character_maximum_length": null, "is_nullable": "YES", "column_default": null, "udt_name": "text"}, {"column_name": "token_type", "data_type": "character varying", "character_maximum_length": 50, "is_nullable": "YES", "column_default": "'Bearer'::character varying", "udt_name": "<PERSON><PERSON><PERSON>"}, {"column_name": "token_scope", "data_type": "text", "character_maximum_length": null, "is_nullable": "YES", "column_default": "'identify guilds'::text", "udt_name": "text"}, {"column_name": "token_expires_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "is_nullable": "YES", "column_default": null, "udt_name": "timestamp"}, {"column_name": "token_issued_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "is_nullable": "YES", "column_default": null, "udt_name": "timestamp"}, {"column_name": "is_active", "data_type": "boolean", "character_maximum_length": null, "is_nullable": "YES", "column_default": "true", "udt_name": "bool"}, {"column_name": "is_banned", "data_type": "boolean", "character_maximum_length": null, "is_nullable": "YES", "column_default": "false", "udt_name": "bool"}, {"column_name": "banned_reason", "data_type": "text", "character_maximum_length": null, "is_nullable": "YES", "column_default": null, "udt_name": "text"}, {"column_name": "banned_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "is_nullable": "YES", "column_default": null, "udt_name": "timestamp"}, {"column_name": "banned_by", "data_type": "character varying", "character_maximum_length": 255, "is_nullable": "YES", "column_default": null, "udt_name": "<PERSON><PERSON><PERSON>"}, {"column_name": "first_login_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "udt_name": "timestamp"}, {"column_name": "last_login_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "is_nullable": "YES", "column_default": null, "udt_name": "timestamp"}, {"column_name": "last_activity_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "is_nullable": "YES", "column_default": null, "udt_name": "timestamp"}, {"column_name": "created_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "udt_name": "timestamp"}, {"column_name": "updated_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "udt_name": "timestamp"}, {"column_name": "deleted_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "is_nullable": "YES", "column_default": null, "udt_name": "timestamptz"}, {"column_name": "discord_id", "data_type": "character varying", "character_maximum_length": 50, "is_nullable": "NO", "column_default": null, "udt_name": "<PERSON><PERSON><PERSON>"}, {"column_name": "balance", "data_type": "integer", "character_maximum_length": null, "is_nullable": "NO", "column_default": "0", "udt_name": "int4"}, {"column_name": "experience", "data_type": "integer", "character_maximum_length": null, "is_nullable": "NO", "column_default": "0", "udt_name": "int4"}, {"column_name": "preferences", "data_type": "jsonb", "character_maximum_length": null, "is_nullable": "YES", "column_default": null, "udt_name": "jsonb"}, {"column_name": "profile", "data_type": "jsonb", "character_maximum_length": null, "is_nullable": "YES", "column_default": null, "udt_name": "jsonb"}, {"column_name": "user_id", "data_type": "character varying", "character_maximum_length": 50, "is_nullable": "YES", "column_default": null, "udt_name": "<PERSON><PERSON><PERSON>"}, {"column_name": "access_token", "data_type": "text", "character_maximum_length": null, "is_nullable": "YES", "column_default": null, "udt_name": "text"}, {"column_name": "refresh_token", "data_type": "text", "character_maximum_length": null, "is_nullable": "YES", "column_default": null, "udt_name": "text"}], "rowCount": 0, "constraints": [{"constraint_name": "users_username_length", "constraint_type": "c", "definition": "CHECK (((length((username)::text) >= 2) AND (length((username)::text) <= 32)))"}, {"constraint_name": "users_discriminator_format", "constraint_type": "c", "definition": "CHECK ((((discriminator)::text ~ '^[0-9]{4}$'::text) OR (discriminator IS NULL)))"}, {"constraint_name": "users_email_format", "constraint_type": "c", "definition": "CHECK ((((email)::text ~ '^[^@]+@[^@]+\\.[^@]+$'::text) OR (email IS NULL)))"}, {"constraint_name": "users_pkey", "constraint_type": "p", "definition": "PRIMARY KEY (id)"}, {"constraint_name": "users_discord_id_unique", "constraint_type": "u", "definition": "UNIQUE (discord_id)"}], "indexes": [{"indexname": "users_discord_id_unique", "indexdef": "CREATE UNIQUE INDEX users_discord_id_unique ON public.users USING btree (discord_id)"}, {"indexname": "users_pkey", "indexdef": "CREATE UNIQUE INDEX users_pkey ON public.users USING btree (id)"}, {"indexname": "idx_users_username", "indexdef": "CREATE INDEX idx_users_username ON public.users USING btree (username)"}, {"indexname": "idx_users_email", "indexdef": "CREATE INDEX idx_users_email ON public.users USING btree (email)"}, {"indexname": "idx_users_is_active", "indexdef": "CREATE INDEX idx_users_is_active ON public.users USING btree (is_active)"}, {"indexname": "idx_users_last_login", "indexdef": "CREATE INDEX idx_users_last_login ON public.users USING btree (last_login_at DESC)"}, {"indexname": "idx_users_created_at", "indexdef": "CREATE INDEX idx_users_created_at ON public.users USING btree (created_at DESC)"}, {"indexname": "idx_users_token_expires", "indexdef": "CREATE INDEX idx_users_token_expires ON public.users USING btree (token_expires_at)"}, {"indexname": "users_discord_id_idx", "indexdef": "CREATE INDEX users_discord_id_idx ON public.users USING btree (discord_id)"}, {"indexname": "users_created_at_idx", "indexdef": "CREATE INDEX users_created_at_idx ON public.users USING btree (created_at)"}, {"indexname": "users_username_idx", "indexdef": "CREATE INDEX users_username_idx ON public.users USING btree (username)"}]}}}