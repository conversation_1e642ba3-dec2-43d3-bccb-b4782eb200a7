# Database Schema Analysis Report

Generated on: 2025-07-28T18:15:00Z

## Summary

This report documents the current state of the problematic tables (`agent_memory`, `agent_interactions`, `users`) and identifies schema mismatches between the database and the Drizzle ORM entity definitions.

## Schema Mismatches Identified

### 1. `agent_memory` Table

**Database Schema Issues:**
- Has `user_relationship_id` column (integer, NOT NULL) with foreign key constraint
- Missing columns from entity definition:
  - `key` (varchar)
  - `value` (jsonb)
  - `context` (text)
  - `importance` (integer)
  - `access_count` (integer)
  - `last_accessed_at` (timestamp)
  - `expires_at` (timestamp)
  - `metadata` (jsonb)
  
**Extra columns in database not in entity:**
- `content` (text)
- `summary` (text)
- `importance_score` (integer)
- `emotional_weight` (varchar)
- `related_memories` (integer[])
- `triggers` (text[])
- `memory_date` (date)
- `conversation_id` (integer)
- `times_recalled` (integer)
- `last_recalled_at` (timestamp)
- `relevance_score` (real)
- `is_active` (boolean)
- `is_verified` (boolean)
- `needs_followup` (boolean)

**Entity expects:** Direct `user_id` reference to users table
**Database has:** `user_relationship_id` reference to user_relationships table

### 2. `agent_interactions` Table

**Database Schema Issues:**
- Has `user_relationship_id` column (integer, NOT NULL) with foreign key constraint
- Missing columns from entity definition:
  - `content` (text)
  - `response` (text)
  - `status` (varchar)
  - `context` (jsonb)
  - `metadata` (jsonb)
  - `sentiment_score` (integer)
  - `tags` (text[])
  
**Extra columns in database not in entity:**
- `channel_type` (varchar)
- `user_message` (text)
- `agent_response` (text)
- `conversation_context` (jsonb)
- `sentiment_score` (real) - type mismatch, entity expects integer
- `emotional_state` (varchar)
- `topics_discussed` (text[])
- `action_items` (text[])
- `user_responded` (boolean)
- `response_time_minutes` (integer)
- `response_quality` (varchar)
- `interaction_successful` (boolean)
- `effectiveness_score` (integer)
- `led_to_positive_action` (boolean)
- `requires_followup` (boolean)
- `followup_scheduled_for` (timestamp)
- `followup_completed` (boolean)
- `interaction_started_at` (timestamp)
- `interaction_completed_at` (timestamp)

**Entity expects:** Direct `user_id` reference to users table
**Database has:** `user_relationship_id` reference to user_relationships table

### 3. `users` Table

**Database Schema Issues:**
- Primary key is `id` (varchar) instead of using base entity structure
- Has duplicate/legacy columns:
  - `discord_id` (varchar) - should be the primary identifier
  - `user_id` (varchar) - duplicate/legacy
  - `discriminator` (varchar) - both in main columns and in entity
  - `avatar_url` (text) - both in main columns and in entity
  - `access_token` (text) - unencrypted legacy column
  - `refresh_token` (text) - unencrypted legacy column
  
**Extra columns in database not in entity:**
- `global_name` (varchar)
- `avatar` (varchar)
  - `verified` (boolean)
- `locale` (varchar)
- `flags` (integer)
- `premium_type` (integer)
- `public_flags` (integer)
- `mfa_enabled` (boolean)
- `banner` (varchar)
- `banner_url` (text)
- `accent_color` (integer)
- `access_token_encrypted` (text)
- `refresh_token_encrypted` (text)
- `token_type` (varchar)
- `token_scope` (text)
- `token_issued_at` (timestamp)
- `is_banned` (boolean)
- `banned_reason` (text)
- `banned_at` (timestamp)
- `banned_by` (varchar)
- `first_login_at` (timestamp)
- `last_login_at` (timestamp)

**Missing base entity columns:**
- The table doesn't follow the standard base entity pattern with auto-incrementing integer ID

## Additional Finding: user_relationships Table

The `user_relationships` table exists in the database with 0 rows. This table appears to be an intermediary table that tracks user-guild relationships and engagement metrics. The `agent_memory` and `agent_interactions` tables are currently configured to reference this table via `user_relationship_id` foreign keys.

**user_relationships table structure:**
- `id` (integer, PRIMARY KEY)
- `user_id` (varchar, NOT NULL) - references Discord user ID
- `guild_id` (varchar, NOT NULL) - references Discord guild ID
- Plus 31 additional columns for tracking engagement, goals, and interaction metrics

## Key Issues Summary

1. **Foreign Key Misalignment**: `agent_memory` and `agent_interactions` tables reference `user_relationships` table instead of directly referencing `users`. This creates a dependency on guild context for AI interactions, which may not always be appropriate
2. **Column Mismatches**: Significant differences between database schema and entity definitions
3. **Type Mismatches**: Some columns have different data types (e.g., sentiment_score)
4. **Primary Key Pattern**: Users table uses varchar ID instead of standard integer pattern
5. **Duplicate Columns**: Users table has both encrypted and unencrypted token columns
6. **Missing Indexes**: Some expected indexes from entity definitions are missing

## Recommended Actions

1. **Backup Status**: ✅ Complete backup created with 0 rows in all tables (empty database)
2. **Migration Strategy**: Need to create migrations to:
   - Drop foreign key constraints on `user_relationship_id`
   - Add missing columns to match entity definitions
   - Remove extra columns not in entity definitions
   - Standardize column types
   - Update foreign key relationships to use direct user references
   - Clean up duplicate columns in users table

## Files Generated

- `agent_memory_data.json` - Data backup (0 rows)
- `agent_memory_dump.sql` - SQL dump for restore
- `agent_interactions_data.json` - Data backup (0 rows)
- `agent_interactions_dump.sql` - SQL dump for restore
- `users_data.json` - Data backup (0 rows)
- `users_dump.sql` - SQL dump for restore
- `schema_documentation.json` - Complete schema metadata
- `backup_report.txt` - Backup summary
- `schema_analysis_report.md` - This analysis document
