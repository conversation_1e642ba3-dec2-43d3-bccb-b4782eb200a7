-- Table: users
-- Generated on: 2025-07-28T18:15:04.027Z

CREATE TABLE IF NOT EXISTS users (
  id character varying(255) NOT NULL,
  username character varying(255) NOT NULL,
  discriminator character varying(4),
  global_name character varying(255),
  avatar character varying(255),
  avatar_url text,
  email character varying(255),
  verified boolean DEFAULT false,
  locale character varying(10),
  flags integer DEFAULT 0,
  premium_type integer DEFAULT 0,
  public_flags integer DEFAULT 0,
  mfa_enabled boolean DEFAULT false,
  banner character varying(255),
  banner_url text,
  accent_color integer,
  access_token_encrypted text,
  refresh_token_encrypted text,
  token_type character varying(50) DEFAULT 'Bearer'::character varying,
  token_scope text DEFAULT 'identify guilds'::text,
  token_expires_at timestamp without time zone,
  token_issued_at timestamp without time zone,
  is_active boolean DEFAULT true,
  is_banned boolean DEFAULT false,
  banned_reason text,
  banned_at timestamp without time zone,
  banned_by character varying(255),
  first_login_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
  last_login_at timestamp without time zone,
  last_activity_at timestamp without time zone,
  created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
  deleted_at timestamp with time zone,
  discord_id character varying(50) NOT NULL,
  balance integer NOT NULL DEFAULT 0,
  experience integer NOT NULL DEFAULT 0,
  preferences jsonb,
  profile jsonb,
  user_id character varying(50),
  access_token text,
  refresh_token text
);

