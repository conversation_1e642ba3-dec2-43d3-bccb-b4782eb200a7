#!/bin/bash

echo "🔧 Final Cleanup Script: Addressing remaining critical TypeScript issues..."

# Fix exactOptionalPropertyTypes assignments
find src -name "*.ts" -exec sed -i 's/: null/: undefined/g' {} \;

# Fix Property assignments with undefined checks
find src -name "*.ts" -exec sed -i 's/\(\w\+\): UserApiKeys | null/\1: UserApiKeys | undefined/g' {} \;

# Fix crypto imports 
find src -name "*.ts" -exec sed -i 's/import { createHash, createCipher, createDecipher } from '\''crypto'\'';/import { createHash } from '\''crypto'\'';/g' {} \;

# Fix legacy database references
find src -name "*.ts" -exec sed -i 's/this\.db\./this.redisDatabaseService./g' {} \;

# Fix remaining exactOptionalPropertyTypes by adding conditionals
find src -name "*.ts" -exec sed -i 's/ttl: undefined/...(ttl && { ttl })/g' {} \;

echo "✅ Final cleanup operations completed"