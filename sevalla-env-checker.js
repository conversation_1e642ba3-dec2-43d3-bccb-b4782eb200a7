#!/usr/bin/env node

/**
 * Sevalla Environment Variables Checker
 * 
 * This script helps you verify that your Sevalla environment variables
 * match your local .env file and identifies missing or incorrect values.
 */

require('dotenv').config();

console.log('🔍 SEVALLA ENVIRONMENT VARIABLES CHECKER');
console.log('=' .repeat(60));
console.log('This tool helps you verify your Sevalla environment setup');
console.log('');

function checkSevallaEnvironment() {
  console.log('📋 REQUIRED ENVIRONMENT VARIABLES FOR SEVALLA:');
  console.log('-'.repeat(50));
  
  const requiredVars = [
    {
      name: 'DATABASE_URL',
      description: 'PostgreSQL connection string for Neon database',
      example: 'postgresql://user:pass@host:port/db?sslmode=require',
      critical: true
    },
    {
      name: 'DISCORD_TOKEN', 
      description: 'Discord bot token from Discord Developer Portal',
      example: 'MTM5NDUyMTQ3MTg2MjMwODg4NA.GWam6x.JQ8u5lCOVmRl6hnpeUkXYG4Mt-4Epy634ZKOT4',
      critical: true
    },
    {
      name: 'BOT_CLIENT_ID',
      description: 'Discord application client ID',
      example: '1394521471862308884',
      critical: true
    },
    {
      name: 'BOT_CLIENT_SECRET',
      description: 'Discord application client secret',
      example: '1IOBGoqWI8s4DxeX4SgF6G3dLH7vsAXD',
      critical: true
    },
    {
      name: 'WEB_URL',
      description: 'Frontend URL on Sevalla',
      example: 'https://discordbot-energex-jkhvk.sevalla.app',
      critical: true
    },
    {
      name: 'NEXT_PUBLIC_API_ENDPOINT',
      description: 'Backend API URL on Sevalla',
      example: 'https://discordbot-energex-backend-nqzv2.sevalla.app',
      critical: true
    },
    {
      name: 'NODE_ENV',
      description: 'Node environment (should be "production" for Sevalla)',
      example: 'production',
      critical: true
    },
    {
      name: 'PORT',
      description: 'Port number (should be 8080 for Sevalla)',
      example: '8080',
      critical: true
    },
    {
      name: 'ENABLE_ENV_LOGIN',
      description: 'Environment login setting (should be "false" for production)',
      example: 'false',
      critical: false
    }
  ];

  const optionalVars = [
    {
      name: 'INTERNAL_API_ENDPOINT',
      description: 'Internal API endpoint for branch communication',
      example: 'https://discordbot-energex-backend-nqzv2.sevalla.app'
    },
    {
      name: 'WHOP_API_KEY',
      description: 'Whop API key for integration',
      example: 'your_whop_api_key_here'
    },
    {
      name: 'NEXT_PUBLIC_WHOP_APP_ID',
      description: 'Whop application ID',
      example: 'your_whop_app_id_here'
    }
  ];

  let criticalMissing = 0;
  let criticalPresent = 0;

  console.log('🔴 CRITICAL VARIABLES (must be set):');
  requiredVars.forEach(variable => {
    const value = process.env[variable.name];
    const isSet = value && value.trim() !== '';
    
    if (variable.critical) {
      if (isSet) {
        console.log(`✅ ${variable.name}`);
        console.log(`   Value: ${variable.name.includes('TOKEN') || variable.name.includes('SECRET') ? '[HIDDEN]' : value}`);
        criticalPresent++;
      } else {
        console.log(`❌ ${variable.name} - MISSING`);
        console.log(`   Description: ${variable.description}`);
        console.log(`   Example: ${variable.example}`);
        criticalMissing++;
      }
    } else {
      if (isSet) {
        console.log(`✅ ${variable.name}: ${value}`);
      } else {
        console.log(`⚠️  ${variable.name} - Not set (recommended)`);
        console.log(`   Description: ${variable.description}`);
        console.log(`   Example: ${variable.example}`);
      }
    }
    console.log('');
  });

  console.log('🟡 OPTIONAL VARIABLES:');
  optionalVars.forEach(variable => {
    const value = process.env[variable.name];
    const isSet = value && value.trim() !== '';
    
    if (isSet) {
      console.log(`✅ ${variable.name}: ${variable.name.includes('KEY') ? '[HIDDEN]' : value}`);
    } else {
      console.log(`⚪ ${variable.name} - Not set (optional)`);
      console.log(`   Description: ${variable.description}`);
    }
    console.log('');
  });

  console.log('📊 SUMMARY:');
  console.log(`✅ Critical variables present: ${criticalPresent}`);
  console.log(`❌ Critical variables missing: ${criticalMissing}`);
  console.log('');

  if (criticalMissing > 0) {
    console.log('🚨 CRITICAL ISSUES FOUND!');
    console.log('Your Sevalla deployment will fail without these variables.');
    console.log('');
    console.log('🔧 HOW TO FIX IN SEVALLA:');
    console.log('1. Go to https://app.sevalla.com/');
    console.log('2. Select your backend project: discordbot-energex-backend-nqzv2');
    console.log('3. Go to "Environment Variables" tab');
    console.log('4. Add the missing variables listed above');
    console.log('5. Redeploy your application');
    console.log('');
  } else {
    console.log('🎉 ALL CRITICAL VARIABLES ARE SET!');
    console.log('Your environment configuration should work on Sevalla.');
    console.log('');
  }

  // Specific Sevalla deployment checks
  console.log('🌐 SEVALLA-SPECIFIC CHECKS:');
  console.log('-'.repeat(30));

  // Check URLs
  const webUrl = process.env.WEB_URL;
  const apiUrl = process.env.NEXT_PUBLIC_API_ENDPOINT;

  if (webUrl && webUrl.includes('sevalla.app')) {
    console.log('✅ WEB_URL is a valid Sevalla URL');
  } else {
    console.log('❌ WEB_URL is not a Sevalla URL or missing');
    console.log('   Should be: https://your-frontend-name.sevalla.app');
  }

  if (apiUrl && apiUrl.includes('sevalla.app')) {
    console.log('✅ API endpoint is a valid Sevalla URL');
  } else {
    console.log('❌ API endpoint is not a Sevalla URL or missing');
    console.log('   Should be: https://discordbot-energex-backend-nqzv2.sevalla.app');
  }

  // Check NODE_ENV
  if (process.env.NODE_ENV === 'production') {
    console.log('✅ NODE_ENV is set to production');
  } else {
    console.log('❌ NODE_ENV should be "production" for Sevalla');
  }

  // Check PORT
  if (process.env.PORT === '8080') {
    console.log('✅ PORT is set to 8080 (Sevalla default)');
  } else {
    console.log('❌ PORT should be "8080" for Sevalla');
  }

  console.log('');

  // Generate copy-paste commands for Sevalla
  console.log('📋 COPY-PASTE COMMANDS FOR SEVALLA ENVIRONMENT:');
  console.log('-'.repeat(50));
  console.log('Copy these commands and set them in your Sevalla environment variables:');
  console.log('');

  requiredVars.forEach(variable => {
    const value = process.env[variable.name];
    if (value && variable.critical) {
      console.log(`${variable.name}="${value}"`);
    }
  });

  console.log('');
  console.log('🔗 SEVALLA DASHBOARD LINKS:');
  console.log('Backend Project: https://app.sevalla.com/app/discordbot-energex-backend-nqzv2/overview');
  console.log('Environment Variables: https://app.sevalla.com/app/discordbot-energex-backend-nqzv2/environment');
  console.log('Deployment Logs: https://app.sevalla.com/app/discordbot-energex-backend-nqzv2/logs');
  console.log('');

  console.log('🆘 TROUBLESHOOTING STEPS:');
  console.log('1. Set all missing environment variables in Sevalla dashboard');
  console.log('2. Ensure DATABASE_URL includes ?sslmode=require');
  console.log('3. Verify Discord token is valid and not expired');
  console.log('4. Check that Neon database is active and accessible');
  console.log('5. Redeploy after setting environment variables');
  console.log('6. Check Sevalla deployment logs for specific errors');

  return criticalMissing === 0;
}

// Run the checker
const isValid = checkSevallaEnvironment();
process.exit(isValid ? 0 : 1);
