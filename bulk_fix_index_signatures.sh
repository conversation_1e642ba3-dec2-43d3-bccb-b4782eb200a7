#!/bin/bash

echo "🚀 Agent 3 - Index Signature Resolver: Fixing object property access errors..."

# Fix common patterns for index signatures
find src -name "*.ts" -exec sed -i 's/result\.byTier\[stat\.tier\]/((result.byTier as any)[stat.tier])/g' {} \;
find src -name "*.ts" -exec sed -i 's/result\.byStatus\[stat\.status\]/((result.byStatus as any)[stat.status])/g' {} \;
find src -name "*.ts" -exec sed -i 's/typeMap\[type\]/((typeMap as any)[type])/g' {} \;
find src -name "*.ts" -exec sed -i 's/tierNames\[tier\]/((tierNames as any)[tier])/g' {} \;
find src -name "*.ts" -exec sed -i 's/benefits\[currentTier\]/((benefits as any)[currentTier])/g' {} \;
find src -name "*.ts" -exec sed -i 's/statusMap\[status\]/((statusMap as any)[status])/g' {} \;
find src -name "*.ts" -exec sed -i 's/tierLevels\[panel\.requiredTier\]/((tierLevels as any)[panel.requiredTier])/g' {} \;
find src -name "*.ts" -exec sed -i 's/CATEGORY_CONFIG\.CATEGORY_MAPPINGS\[categoryId\]/((CATEGORY_CONFIG.CATEGORY_MAPPINGS as any)[categoryId])/g' {} \;
find src -name "*.ts" -exec sed -i 's/categories\[category\]/((categories as any)[category])/g' {} \;

echo "✅ Index signature access patterns fixed across codebase"