#!/bin/bash

# Agent 5 - Bulk Pattern Automator: Fix lambda parameter types across codebase

echo "🚀 Agent 5 - Bulk Pattern Automator: Fixing lambda parameter types..."

# Fix .filter(param => with explicit types
find src -name "*.ts" -exec sed -i 's/\.filter(\([a-zA-Z_][a-zA-Z0-9_]*\) =>/\.filter((\1: any) =>/g' {} \;

# Fix .map(param => with explicit types  
find src -name "*.ts" -exec sed -i 's/\.map(\([a-zA-Z_][a-zA-Z0-9_]*\) =>/\.map((\1: any) =>/g' {} \;

# Fix .find(param => with explicit types
find src -name "*.ts" -exec sed -i 's/\.find(\([a-zA-Z_][a-zA-Z0-9_]*\) =>/\.find((\1: any) =>/g' {} \;

# Fix .forEach(param => with explicit types
find src -name "*.ts" -exec sed -i 's/\.forEach(\([a-zA-Z_][a-zA-Z0-9_]*\) =>/\.forEach((\1: any) =>/g' {} \;

# Fix .some(param => with explicit types
find src -name "*.ts" -exec sed -i 's/\.some(\([a-zA-Z_][a-zA-Z0-9_]*\) =>/\.some((\1: any) =>/g' {} \;

# Fix .every(param => with explicit types
find src -name "*.ts" -exec sed -i 's/\.every(\([a-zA-Z_][a-zA-Z0-9_]*\) =>/\.every((\1: any) =>/g' {} \;

# Fix .reduce(param => with explicit types
find src -name "*.ts" -exec sed -i 's/\.reduce(\([a-zA-Z_][a-zA-Z0-9_]*\) =>/\.reduce((\1: any) =>/g' {} \;

echo "✅ Lambda parameter types fixed across codebase"