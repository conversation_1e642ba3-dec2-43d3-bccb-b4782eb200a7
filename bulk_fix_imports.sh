#!/bin/bash

echo "🔧 Bulk fixing PostgreSQL imports to Redis..."

# Find all TypeScript files with PostgreSQL imports
FILES=$(find src -name "*.ts" -exec grep -l "DATABASE_CONNECTION\|NodePgDatabase\|from.*drizzle-orm" {} \;)

for file in $FILES; do
    echo "Fixing: $file"
    
    # Replace drizzle-orm imports with Redis service import
    sed -i 's/import.*from.*drizzle-orm.*;//g' "$file"
    sed -i 's/import.*NodePgDatabase.*from.*drizzle-orm\/node-postgres.*;//g' "$file"
    sed -i 's/import.*DATABASE_CONNECTION.*from.*database\.service.*;//g' "$file"
    
    # Add Redis service import if not present
    if ! grep -q "RedisDatabaseService" "$file"; then
        # Find the line number of the last import
        last_import_line=$(grep -n "^import" "$file" | tail -1 | cut -d: -f1)
        if [ ! -z "$last_import_line" ]; then
            # Insert Redis import after the last import
            sed -i "${last_import_line}a\\import { RedisDatabaseService } from '../core/database/redis-database.service';" "$file" 2>/dev/null ||
            sed -i "${last_import_line}a\\import { RedisDatabaseService } from '../../core/database/redis-database.service';" "$file" 2>/dev/null ||
            sed -i "${last_import_line}a\\import { RedisDatabaseService } from '../../../core/database/redis-database.service';" "$file" 2>/dev/null
        fi
    fi
    
    # Fix constructor injections
    sed -i 's/@Inject(DATABASE_CONNECTION).*private.*db:.*NodePgDatabase[^,]*/private readonly redisDatabaseService: RedisDatabaseService/g' "$file"
    sed -i 's/@Inject(DATABASE_CONNECTION).*private.*db:.*NodePgDatabase.*/private readonly redisDatabaseService: RedisDatabaseService,/g' "$file"
    
    # Remove remaining Inject imports if no longer needed
    if ! grep -q "@Inject(" "$file"; then
        sed -i 's/Inject,\s*//g' "$file"
        sed -i 's/,\s*Inject//g' "$file"
        sed -i 's/{ Inject }/{ }/g' "$file"
        sed -i 's/import { },/import/g' "$file"
        sed -i 's/import { }.*from.*@nestjs\/common.*;//g' "$file"
    fi
    
done

echo "✅ Bulk fix completed!"
echo "Files processed: $(echo "$FILES" | wc -l)"