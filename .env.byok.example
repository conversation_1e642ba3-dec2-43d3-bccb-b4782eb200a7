# EnergeX Discord Bot - Production Environment Configuration
# Copy this file to .env.local (development) or .env (production) and configure the values

# =============================================================================
# ENVIRONMENT SETTINGS
# =============================================================================

NODE_ENV="production"
PORT=3000

# =============================================================================
# CORE BYOK SETTINGS
# =============================================================================

# Enable/disable BYOK functionality
BYOK_ENABLED=true

# Master encryption key for BYOK (REQUIRED - must be at least 32 characters)
# Generate a secure random key for production: openssl rand -base64 32
BYOK_MASTER_KEY=your-secure-master-key-32-characters-minimum-required-here

# Maximum number of keys allowed per provider per user (default: 5)
BYOK_MAX_KEYS_PER_PROVIDER=5

# Maximum total number of keys allowed per user (default: 25)
BYOK_MAX_KEYS_PER_USER=25

# Timeout for key validation requests in milliseconds (default: 10000)
BYOK_VALIDATION_TIMEOUT=10000

# Encryption algorithm to use (default: aes-256-gcm)
BYOK_ENCRYPTION_ALGORITHM=aes-256-gcm

# Enable audit logging for BYOK operations (default: true)
BYOK_AUDIT_LOGGING=true

# =============================================================================
# RATE LIMITING SETTINGS
# =============================================================================

# Enable rate limiting for BYOK endpoints (default: true)
BYOK_RATE_LIMITING_ENABLED=true

# Maximum requests per minute per IP (default: 30)
BYOK_RATE_LIMIT_PER_MINUTE=30

# Maximum requests per hour per IP (default: 1000)
BYOK_RATE_LIMIT_PER_HOUR=1000

# =============================================================================
# MONITORING AND ALERTING
# =============================================================================

# Enable monitoring and metrics collection (default: false)
BYOK_MONITORING_ENABLED=false

# Endpoint for sending metrics (optional)
BYOK_METRICS_ENDPOINT=https://your-metrics-service.com/api/metrics

# Webhook URL for alerts (optional)
BYOK_ALERT_WEBHOOK=https://your-alert-service.com/webhook

# =============================================================================
# DEBUG AND DEVELOPMENT
# =============================================================================

# Enable debug logging (default: false)
BYOK_DEBUG=false

# =============================================================================
# SECURITY RECOMMENDATIONS
# =============================================================================

# 1. BYOK_MASTER_KEY should be:
#    - At least 32 characters long
#    - Randomly generated (use: openssl rand -base64 32)
#    - Stored securely (use environment variables, not in code)
#    - Rotated regularly in production
#    - Different for each environment (dev, staging, prod)

# 2. Rate limiting should be enabled in production to prevent abuse

# 3. Audit logging should be enabled for compliance and security monitoring

# 4. Monitoring should be enabled in production for operational visibility

# =============================================================================
# EXAMPLE PRODUCTION VALUES
# =============================================================================

# Production example (uncomment and modify):
# BYOK_ENABLED=true
# BYOK_MASTER_KEY=AbCdEfGhIjKlMnOpQrStUvWxYz1234567890+/==
# BYOK_MAX_KEYS_PER_PROVIDER=10
# BYOK_MAX_KEYS_PER_USER=50
# BYOK_VALIDATION_TIMEOUT=15000
# BYOK_RATE_LIMITING_ENABLED=true
# BYOK_RATE_LIMIT_PER_MINUTE=20
# BYOK_RATE_LIMIT_PER_HOUR=500
# BYOK_MONITORING_ENABLED=true
# BYOK_METRICS_ENDPOINT=https://metrics.yourcompany.com/api/byok
# BYOK_ALERT_WEBHOOK=https://alerts.yourcompany.com/webhook/byok
# BYOK_AUDIT_LOGGING=true
# BYOK_DEBUG=false

# =============================================================================
# PRODUCTION DATABASE CONFIGURATION
# =============================================================================

# Neon PostgreSQL Database URL (REQUIRED)
DATABASE_URL="postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"

# Database Connection Pool Settings
DB_POOL_MAX=20                    # Maximum connections in pool
DB_POOL_MIN=2                     # Minimum connections to maintain
DB_IDLE_TIMEOUT=30000            # Close idle connections after 30s
DB_CONNECTION_TIMEOUT=5000       # Connection establishment timeout
DB_ACQUIRE_TIMEOUT=10000         # Connection acquisition timeout
DB_QUERY_TIMEOUT=30000           # Individual query timeout
DB_STATEMENT_TIMEOUT=60000       # Statement execution timeout
DB_APPLICATION_NAME="energex-discord-bot"

# =============================================================================
# DISCORD BOT CONFIGURATION
# =============================================================================

# Discord Bot Token (REQUIRED)
DISCORD_TOKEN="your_discord_bot_token_here"

# Discord Application Settings
DISCORD_CLIENT_ID="your_discord_client_id"
DISCORD_CLIENT_SECRET="your_discord_client_secret"
DISCORD_REDIRECT_URI="https://yourdomain.com/api/auth/callback"

# Admin User IDs (comma-separated Discord user IDs)
ADMIN_USER_IDS="your_discord_user_id,another_admin_id"

# =============================================================================
# PRODUCTION MONITORING & LOGGING
# =============================================================================

# Logging Configuration
LOG_LEVEL="info"                 # debug, info, warn, error
LOG_FORMAT="json"                # json, text
LOG_FILE_ENABLED=true
LOG_FILE_PATH="/var/log/energex-bot.log"

# Performance Monitoring
ENABLE_PERFORMANCE_MONITORING=true
PERFORMANCE_METRICS_INTERVAL=60000  # 1 minute
SLOW_QUERY_THRESHOLD=1000           # Log queries slower than 1s

# Health Check Configuration
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30000         # 30 seconds
HEALTH_CHECK_TIMEOUT=5000           # 5 second timeout

# =============================================================================
# DEVELOPMENT VALUES
# =============================================================================

# Development example (for local development):
# BYOK_ENABLED=true
# BYOK_MASTER_KEY=dev-master-key-32-characters-long-for-testing-only
# BYOK_MAX_KEYS_PER_PROVIDER=3
# BYOK_MAX_KEYS_PER_USER=10
# BYOK_VALIDATION_TIMEOUT=5000
# BYOK_RATE_LIMITING_ENABLED=false
# BYOK_MONITORING_ENABLED=false
# BYOK_AUDIT_LOGGING=true
# BYOK_DEBUG=true
