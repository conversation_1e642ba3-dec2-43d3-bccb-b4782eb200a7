{"parser": "@typescript-eslint/parser", "parserOptions": {"project": "tsconfig.json", "sourceType": "module"}, "plugins": ["@typescript-eslint/eslint-plugin"], "extends": ["eslint:recommended", "prettier"], "root": true, "env": {"node": true, "jest": true}, "ignorePatterns": [".eslintrc.js", "dist/**/*"], "rules": {"@typescript-eslint/interface-name-prefix": "off", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-explicit-any": "off", "no-unused-vars": "off", "no-undef": "off"}}