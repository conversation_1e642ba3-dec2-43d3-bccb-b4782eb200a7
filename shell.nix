{ pkgs ? import <nixpkgs> {} }:

pkgs.mkShell {
  buildInputs = with pkgs; [
    # Node.js and package managers
    nodejs_18
    nodePackages.pnpm
    nodePackages.npm
    
    # Build tools
    python3
    gcc
    gnumake
    pkg-config
    
    # System dependencies for Discord bot
    openssl
    libsodium
    ffmpeg
    
    # Process management
    systemd
    tmux
    screen
    
    # Development tools
    git
    curl
    jq
  ];

  shellHook = ''
    echo "🚀 Discord Bot Development Environment"
    echo "Node.js version: $(node --version)"
    echo "pnpm version: $(pnpm --version)"
    echo ""
    echo "Available commands:"
    echo "  pnpm install     - Install dependencies"
    echo "  pnpm run build   - Build the application"
    echo "  pnpm run start   - Start the bot"
    echo "  pnpm run dev     - Start in development mode"
    echo "  ./scripts/start-bot.sh - Start with process management"
    echo ""
    
    # Set environment variables for consistent builds
    export NODE_ENV=development
    export FORCE_COLOR=1
    export NPM_CONFIG_FUND=false
    export NPM_CONFIG_AUDIT=false
    
    # Ensure pnpm store is accessible
    export PNPM_HOME="$HOME/.local/share/pnpm"
    export PATH="$PNPM_HOME:$PATH"
    
    # Create necessary directories
    mkdir -p logs
    mkdir -p scripts
  '';

  # Environment variables
  NODE_ENV = "development";
  FORCE_COLOR = "1";
}
