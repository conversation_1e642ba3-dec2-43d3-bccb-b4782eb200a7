{"name": "discord-bot-nestjs", "version": "1.0.0", "description": "NestJS Discord Bot with REST API", "author": "", "private": true, "license": "MIT", "scripts": {"build": "nest build", "build:force": "tsc --noEmitOnError false --skipL<PERSON><PERSON><PERSON><PERSON> || echo 'Build completed with errors, but continuing...'", "build:clean": "rm -rf dist && nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "dev": "nest start --watch", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/src/main.js", "start:prod:managed": "./scripts/production-start.sh", "start:managed": "./scripts/start-bot.sh start", "stop:managed": "./scripts/start-bot.sh stop", "restart:managed": "./scripts/start-bot.sh restart", "status": "./scripts/start-bot.sh status", "monitor": "./scripts/start-bot.sh monitor", "dev:runner": "./scripts/dev-runner.sh dev", "prod:runner": "./scripts/dev-runner.sh prod", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "lint:fix": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:docker": "jest --config ./test/jest-docker.json", "test:docker:build": "./scripts/test-docker-build.sh", "test:docker:quick": "jest --config ./test/jest-docker.json --testNamePattern=\"Dockerfile|Build Process\"", "clean": "rm -rf node_modules dist logs && pnpm install", "clean:cache": "pnpm store prune && rm -rf node_modules/.cache", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:fix": "node scripts/minimal-fix.js", "db:fix:sql": "bash scripts/fix-db-schema.sh", "db:migrate:panels": "node scripts/run-panel-migrations.js", "logs": "./scripts/start-bot.sh logs", "health": "curl -f http://localhost:8080/api/health || echo '<PERSON><PERSON> is not responding'"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/openai": "^1.3.23", "@discordjs/rest": "^2.5.1", "@discordjs/voice": "^0.16.1", "@mastra/core": "^0.11.0", "@mastra/engine": "0.1.0-alpha.84", "@nestjs/axios": "3.0.0", "@nestjs/cache-manager": "^2.1.0", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/core": "^10.0.0", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^10.0.0", "@nestjs/passport": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^4.0.0", "@nestjs/swagger": "^7.0.0", "@nestjs/terminus": "^10.3.0", "@nestjs/throttler": "^5.0.0", "@nestjs/typeorm": "^10.0.0", "@ngrok/ngrok": "^1.5.1", "@whop/api": "^0.0.36", "@willsoto/nestjs-prometheus": "^6.0.0", "ai": "^4.3.19", "bcrypt": "^5.1.0", "cache-manager": "^5.2.4", "cache-manager-redis-store": "^3.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "discord-api-types": "^0.37.120", "discord.js": "^14.21.0", "dotenv": "^16.3.1", "drizzle-orm": "^0.33.0", "express": "^4.21.2", "express-rate-limit": "^7.1.5", "ffmpeg-static": "^5.2.0", "helmet": "^7.1.0", "ioredis": "^5.3.2", "necord": "6.0.0", "ngrok": "5.0.0-beta.2", "node-cron": "^4.2.1", "passport": "^0.6.0", "passport-custom": "^1.1.1", "passport-discord": "^0.1.4", "passport-jwt": "^4.0.1", "pg": "^8.16.3", "prom-client": "^15.1.0", "reflect-metadata": "^0.1.14", "rxjs": "^7.8.1", "typeorm": "^0.3.17", "uuid": "^9.0.1", "winston": "^3.11.0", "zod": "^3.22.4"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bcrypt": "^5.0.0", "@types/cache-manager": "^4.0.6", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/node-cron": "^3.0.11", "@types/passport-discord": "^0.1.14", "@types/passport-jwt": "^3.0.9", "@types/pg": "^8.15.4", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "drizzle-kit": "^0.31.4", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "engines": {"node": ">=18.17.0"}, "packageManager": "pnpm@10.12.4"}