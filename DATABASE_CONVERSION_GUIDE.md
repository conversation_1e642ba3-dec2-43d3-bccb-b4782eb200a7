# Database Operation Conversion Guide

## Overview
This document outlines the conversion from Drizzle ORM operations to Redis-based service calls.

## ✅ Completed Conversions

### Dev-on-Demand Service
- ✅ All Drizzle operations converted to Redis service calls
- ✅ Guild operations: `findByDiscordId`, `updateSettings`, `create`
- ✅ User operations: `findByDiscordId`, `updatePreferences`, `create`, `find`

### Agents Service
- ✅ Already using direct Redis operations (no conversion needed)

### Welcome Service
- ⚠️ Partially converted (needs completion)

## 🔄 Common Conversion Patterns

### 1. User Operations

#### Find by Discord ID
```typescript
// OLD (Drizzle)
const userResults = await this.db.select().from(users).where(eq(users.discordId, discordId));
const user = userResults[0];

// NEW (Redis Service)  
const user = await this.redisDatabaseService.users.findByDiscordId(discordId);
```

#### Create User
```typescript
// OLD (Drizzle)
const newUserResults = await this.db.insert(users).values({
  discordId: interaction.user.id,
  username: interaction.user.username,
  preferences: { devRequests: [] },
}).returning();
const user = newUserResults[0];

// NEW (Redis Service)
const user = await this.redisDatabaseService.users.create({
  discordId: interaction.user.id,
  username: interaction.user.username,
  email: '',
  isActive: true,
  experience: 0,
  balance: 0,
  preferences: { devRequests: [] },
  profile: {},
  roles: []
});
```

#### Update User Preferences
```typescript
// OLD (Drizzle)
await this.db.update(users).set({ preferences: updatedPreferences }).where(eq(users.id, user.id));

// NEW (Redis Service)
await this.redisDatabaseService.users.updatePreferences(user.id, updatedPreferences);
```

#### Get All Users
```typescript
// OLD (Drizzle)
const allUsers = await this.db.select().from(users);

// NEW (Redis Service)
const allUsers = await this.redisDatabaseService.users.find();
```

### 2. Guild Operations

#### Find by Discord ID
```typescript
// OLD (Drizzle)
const guildResults = await this.db.select().from(guilds).where(eq(guilds.discordId, guildId));
const guild = guildResults[0];

// NEW (Redis Service)
const guild = await this.redisDatabaseService.guilds.findByDiscordId(guildId);
```

#### Create Guild
```typescript
// OLD (Drizzle)
const newGuildResults = await this.db.insert(guilds).values({
  discordId: interaction.guild.id,
  name: interaction.guild.name,
  settings: { devOnDemand: { enabled: false } },
}).returning();
const guild = newGuildResults[0];

// NEW (Redis Service)
const guild = await this.redisDatabaseService.guilds.create({
  discordId: interaction.guild.id,
  name: interaction.guild.name,
  settings: { devOnDemand: { enabled: false } },
  isActive: true,
  ownerDiscordId: interaction.guild.ownerId || interaction.user.id,
  welcomeEnabled: false,
  starboardEnabled: false
});
```

#### Update Guild Settings
```typescript
// OLD (Drizzle)
await this.db.update(guilds).set({ settings: updatedSettings }).where(eq(guilds.id, guild.id));

// NEW (Redis Service)
await this.redisDatabaseService.guilds.updateSettings(guild.id, updatedSettings);
```

## 🛠️ Required Service Imports

### Add Import
```typescript
import { RedisDatabaseService } from '../../core/database/redis-database.service';
```

### Update Constructor
```typescript
constructor(
  private readonly redisDatabaseService: RedisDatabaseService,
  // ... other dependencies
) {}
```

### Remove Old Imports
```typescript
// Remove these:
import { eq, and, or } from 'drizzle-orm';
import { users, guilds } from '../../core/database/schema';
```

## 📋 Services Still Needing Conversion

### High Priority
1. **Moderation Service** - User discipline tracking
2. **AI Automation Services** - Smart notifications, auto-moderation
3. **Role Access Service** - Permission management
4. **Leveling Service** - User experience tracking
5. **Economy Service** - Balance management

### Medium Priority
6. **Starboard Service** - Message reactions
7. **Reaction Role Service** - Auto role assignment
8. **Project Tracking Service** - Development projects
9. **Member Showcase Service** - User profiles

### Lower Priority
10. **Channel Management Services** - Dynamic content
11. **Panel Orchestration Services** - UI components

## 🔍 Finding Services to Fix

### Command to Find Services with Database Operations
```bash
find /home/<USER>/Discordbot-EnergeX/src -name "*.service.ts" -exec grep -l "this\.db\." {} \;
```

### Count Remaining Operations
```bash
grep -rn "this\.db\." /home/<USER>/Discordbot-EnergeX/src --include="*.service.ts" | wc -l
```

## ⚡ Quick Fix Script Usage

For services with simple patterns, use the automated script:
```bash
./fix-database-operations.sh
```

## 🚨 Manual Fix Requirements

Some operations require manual attention:
- Complex queries with joins
- Custom business logic
- Error handling specifics
- Transaction operations

## 🧪 Testing After Conversion

1. Verify service imports are correct
2. Check constructor dependencies
3. Test basic CRUD operations
4. Validate error handling
5. Confirm data integrity

## 📊 Progress Tracking

- ✅ **Completed**: Dev-on-Demand Service, Agents Service
- 🔄 **In Progress**: Welcome Service  
- ❌ **Remaining**: ~15 services with 117+ database operations

## 🎯 Next Steps

1. Complete Welcome Service conversion
2. Fix Moderation Service (critical for server management)
3. Fix AI Automation Services (core functionality)
4. Systematically work through remaining services
5. Add any missing repository methods as needed