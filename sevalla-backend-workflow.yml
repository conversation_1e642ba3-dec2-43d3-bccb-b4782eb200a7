# GitHub Actions Workflow for Sevalla Backend Deployment
# This file needs to be manually moved to .github/workflows/sevalla-backend.yml
# due to GitHub App permissions restrictions

name: Deploy Backend to <PERSON><PERSON><PERSON>

on:
  push:
    branches: [ backend, staging, master ]
  pull_request:
    branches: [ backend ]

jobs:
  build-and-test:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18.x'
        cache: 'pnpm'

    - name: Install pnpm
      uses: pnpm/action-setup@v2
      with:
        version: 10.12.4

    - name: Install dependencies
      run: pnpm install

    - name: Build application
      run: pnpm run build

    - name: Run tests
      run: |
        pnpm run test:discord:all
        pnpm run test:whop

    - name: Test health endpoint
      run: |
        pnpm run start:prod &
        sleep 10
        curl -f http://localhost:8080/health
        
  deploy:
    needs: build-and-test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/backend' || github.ref == 'refs/heads/staging' || github.ref == 'refs/heads/master'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Deploy to Sevalla
      uses: sevalla/github-action@v1
      with:
        api-key: ${{ secrets.SEVALLA_API_KEY }}
        project-id: ${{ secrets.SEVALLA_BACKEND_PROJECT_ID }}
        environment: ${{ github.ref == 'refs/heads/master' && 'production' || github.ref == 'refs/heads/staging' && 'staging' || 'development' }}
        
    - name: Verify deployment
      run: |
        sleep 30
        curl -f ${{ secrets.SEVALLA_BACKEND_URL }}/health