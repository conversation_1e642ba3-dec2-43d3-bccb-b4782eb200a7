require('dotenv').config();
const { Client } = require('pg');

const client = new Client({
  connectionString: process.env.DATABASE_URL,
  ssl: { rejectUnauthorized: false }
});

const alterCommands = [
  // Add missing columns to panel_deployments table
  "ALTER TABLE panel_deployments ADD COLUMN IF NOT EXISTS needs_cleanup BOOLEAN DEFAULT FALSE NOT NULL",
  "ALTER TABLE panel_deployments ADD COLUMN IF NOT EXISTS last_interaction_at TIMESTAMP WITH TIME ZONE"
];

const indexes = [
  // Complete the missing indexes
  'CREATE INDEX IF NOT EXISTS panel_deployments_cleanup_idx ON panel_deployments (needs_cleanup)',
  'CREATE INDEX IF NOT EXISTS panel_deployments_interaction_idx ON panel_deployments (last_interaction_at)'
];

(async () => {
  try {
    await client.connect();
    console.log('🔌 Connected to database');
    
    // Add missing columns
    console.log('🔧 Adding missing columns to panel_deployments...');
    for (const alterCommand of alterCommands) {
      try {
        await client.query(alterCommand);
        console.log('✅ Column added/verified');
      } catch (error) {
        console.log(`⚠️  Column operation failed: ${error.message}`);
      }
    }
    
    // Create remaining indexes
    console.log('\n📊 Creating remaining indexes...');
    for (const indexSql of indexes) {
      try {
        await client.query(indexSql);
        console.log('✅ Index created');
      } catch (error) {
        console.log(`⚠️  Index creation failed: ${error.message}`);
      }
    }
    
    console.log('\n🎉 Panel deployment table completion successful!');
    
  } catch (error) {
    console.error('❌ Database operation failed:', error.message);
  } finally {
    await client.end();
  }
})();