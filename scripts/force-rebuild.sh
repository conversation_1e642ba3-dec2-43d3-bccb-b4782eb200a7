#!/bin/bash

# Force Docker rebuild by updating cache-busting argument
# This script can be used to trigger a fresh deployment when Docker cache is causing issues

set -euo pipefail

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${BLUE}🔄 Force Docker Rebuild Script${NC}"
echo ""

# Generate unique cache bust value
CACHE_BUST=$(date +%s)
echo -e "${YELLOW}Generated cache bust value: ${CACHE_BUST}${NC}"

# Update Dockerfile with new cache bust value
sed -i "s/ARG CACHE_BUST=.*/ARG CACHE_BUST=${CACHE_BUST}/" Dockerfile

# Update the comment timestamp
TIMESTAMP=$(date '+%Y-%m-%d-%H:%M')
sed -i "s/# Cache bust: .*/# Cache bust: ${TIMESTAMP} - Force rebuild ${CACHE_BUST}/" Dockerfile

echo -e "${GREEN}✅ Updated Dockerfile with new cache bust values${NC}"
echo ""

# Show the changes
echo -e "${BLUE}Changes made:${NC}"
grep -n "CACHE_BUST\|Cache bust:" Dockerfile

echo ""
echo -e "${YELLOW}Next steps:${NC}"
echo "1. git add Dockerfile"
echo "2. git commit -m 'Force Docker rebuild with cache bust ${CACHE_BUST}'"
echo "3. git push origin backend"
echo ""
echo -e "${GREEN}This will force a complete Docker rebuild from the script layer onwards.${NC}"
