#!/usr/bin/env node

/**
 * Robust Database Schema Fix Script
 * 
 * This script handles the schema issues in the correct order
 * and provides detailed error handling.
 */

require('dotenv').config();
const { Pool } = require('pg');

// Database connection
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
});

async function checkTableExists(tableName) {
  try {
    const result = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = $1
      );
    `, [tableName]);
    return result.rows[0].exists;
  } catch (error) {
    console.error(`Error checking table ${tableName}:`, error.message);
    return false;
  }
}

async function checkColumnExists(tableName, columnName) {
  try {
    const result = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = $1 
        AND column_name = $2
      );
    `, [tableName, columnName]);
    return result.rows[0].exists;
  } catch (error) {
    console.error(`Error checking column ${columnName} in ${tableName}:`, error.message);
    return false;
  }
}

async function executeQuery(query, params = []) {
  try {
    const result = await pool.query(query, params);
    return result;
  } catch (error) {
    console.error('Query error:', error.message);
    throw error;
  }
}

async function fixSchema() {
  console.log('🔧 Starting robust database schema fix...\n');

  try {
    await pool.query('BEGIN');

    // 1. Create ai_agent_config table if missing
    const hasAiAgentConfig = await checkTableExists('ai_agent_config');
    if (!hasAiAgentConfig) {
      console.log('📊 Creating ai_agent_config table...');
      await executeQuery(`
        CREATE TABLE IF NOT EXISTS "ai_agent_config" (
          "id" serial PRIMARY KEY NOT NULL,
          "created_at" timestamp with time zone DEFAULT now() NOT NULL,
          "updated_at" timestamp with time zone DEFAULT now() NOT NULL,
          "deleted_at" timestamp with time zone,
          "guild_id" text NOT NULL,
          "agent_type" varchar(50) NOT NULL,
          "enabled" boolean DEFAULT true NOT NULL,
          "configuration" jsonb,
          "default_channel_id" text,
          "log_channel_id" text,
          "permissions" jsonb,
          "settings" jsonb,
          "last_used_at" timestamp with time zone
        );
      `);
    }

    // 2. Create agent_memory table if missing
    const hasAgentMemory = await checkTableExists('agent_memory');
    if (!hasAgentMemory) {
      console.log('📊 Creating agent_memory table...');
      await executeQuery(`
        CREATE TABLE IF NOT EXISTS "agent_memory" (
          "id" serial PRIMARY KEY NOT NULL,
          "created_at" timestamp with time zone DEFAULT now() NOT NULL,
          "updated_at" timestamp with time zone DEFAULT now() NOT NULL,
          "deleted_at" timestamp with time zone,
          "user_id" text NOT NULL,
          "memory_type" varchar(50) NOT NULL,
          "key" varchar(200) NOT NULL,
          "value" jsonb NOT NULL,
          "context" text,
          "importance" integer DEFAULT 1 NOT NULL,
          "access_count" integer DEFAULT 0 NOT NULL,
          "last_accessed_at" timestamp with time zone,
          "expires_at" timestamp with time zone,
          "tags" text[],
          "metadata" jsonb
        );
      `);
    } else {
      // Check if user_id column exists in agent_memory
      const hasUserId = await checkColumnExists('agent_memory', 'user_id');
      if (!hasUserId) {
        console.log('📊 Adding user_id column to agent_memory...');
        await executeQuery(`
          ALTER TABLE "agent_memory" 
          ADD COLUMN IF NOT EXISTS "user_id" text NOT NULL DEFAULT '';
        `);
      }
    }

    // 3. Create agent_interactions table if missing
    const hasAgentInteractions = await checkTableExists('agent_interactions');
    if (!hasAgentInteractions) {
      console.log('📊 Creating agent_interactions table...');
      await executeQuery(`
        CREATE TABLE IF NOT EXISTS "agent_interactions" (
          "id" serial PRIMARY KEY NOT NULL,
          "created_at" timestamp with time zone DEFAULT now() NOT NULL,
          "updated_at" timestamp with time zone DEFAULT now() NOT NULL,
          "deleted_at" timestamp with time zone,
          "user_id" text NOT NULL,
          "agent_type" varchar(50) NOT NULL,
          "interaction_type" varchar(50) NOT NULL,
          "content" text NOT NULL,
          "response" text,
          "status" varchar(20) DEFAULT 'completed' NOT NULL,
          "context" jsonb,
          "metadata" jsonb,
          "channel_id" text,
          "message_id" text,
          "guild_id" text,
          "sentiment_score" integer DEFAULT 0,
          "tags" text[]
        );
      `);
    } else {
      // Check if updated_at column exists in agent_interactions
      const hasUpdatedAt = await checkColumnExists('agent_interactions', 'updated_at');
      if (!hasUpdatedAt) {
        console.log('📊 Adding updated_at column to agent_interactions...');
        await executeQuery(`
          ALTER TABLE "agent_interactions" 
          ADD COLUMN IF NOT EXISTS "updated_at" timestamp with time zone DEFAULT now() NOT NULL;
        `);
      }
    }

    // 4. Create users table if missing
    const hasUsers = await checkTableExists('users');
    if (!hasUsers) {
      console.log('📊 Creating users table...');
      await executeQuery(`
        CREATE TABLE IF NOT EXISTS "users" (
          "id" serial PRIMARY KEY NOT NULL,
          "created_at" timestamp with time zone DEFAULT now() NOT NULL,
          "updated_at" timestamp with time zone DEFAULT now() NOT NULL,
          "deleted_at" timestamp with time zone,
          "discord_id" varchar(50) NOT NULL UNIQUE,
          "username" varchar(100) NOT NULL,
          "email" varchar(255),
          "is_active" boolean DEFAULT true NOT NULL,
          "last_activity_at" timestamp with time zone,
          "preferences" jsonb,
          "profile" jsonb,
          "access_token" text,
          "refresh_token" text,
          "token_expires_at" timestamp with time zone,
          "experience" integer DEFAULT 0 NOT NULL,
          "balance" integer DEFAULT 0 NOT NULL,
          "user_id" varchar(50),
          "discriminator" varchar(10),
          "avatar_url" text
        );
      `);
    }

    // 5. Create indexes (only for existing columns)
    console.log('📊 Creating indexes...');
    const hasAgentMemoryUserId = await checkColumnExists('agent_memory', 'user_id');
    const hasAgentInteractionsUpdatedAt = await checkColumnExists('agent_interactions', 'updated_at');
    
    await executeQuery(`
      CREATE INDEX IF NOT EXISTS "ai_agent_config_guild_id_idx" ON "ai_agent_config" USING btree ("guild_id");
      CREATE INDEX IF NOT EXISTS "ai_agent_config_agent_type_idx" ON "ai_agent_config" USING btree ("agent_type");
    `);
    
    if (hasAgentMemoryUserId) {
      await executeQuery(`
        CREATE INDEX IF NOT EXISTS "agent_memory_user_id_idx" ON "agent_memory" USING btree ("user_id");
      `);
    }
    
    await executeQuery(`
      CREATE INDEX IF NOT EXISTS "agent_memory_memory_type_idx" ON "agent_memory" USING btree ("memory_type");
    `);
    
    if (hasAgentInteractionsUpdatedAt) {
      await executeQuery(`
        CREATE INDEX IF NOT EXISTS "agent_interactions_user_id_idx" ON "agent_interactions" USING btree ("user_id");
      `);
    }
    
    await executeQuery(`
      CREATE INDEX IF NOT EXISTS "agent_interactions_agent_type_idx" ON "agent_interactions" USING btree ("agent_type");
      CREATE INDEX IF NOT EXISTS "users_discord_id_idx" ON "users" USING btree ("discord_id");
    `);

    // 6. Insert default configuration
    console.log('📊 Inserting default configuration...');
    await executeQuery(`
      INSERT INTO "ai_agent_config" (guild_id, agent_type, enabled, configuration, settings) 
      VALUES 
      ('1394355426941730856', 'general', true, '{"channels":["general"],"personality":{"tone":"friendly","style":"helpful"}}', '{"maxResponsesPerHour":50}')
      ON CONFLICT DO NOTHING;
    `);

    await pool.query('COMMIT');
    console.log('✅ All schema fixes applied successfully!');

    // Final verification
    console.log('\n🔍 Final verification:');
    const tables = ['ai_agent_config', 'agent_memory', 'agent_interactions', 'users'];
    for (const table of tables) {
      const exists = await checkTableExists(table);
      console.log(`  ${table}: ${exists ? '✅' : '❌'}`);
    }

  } catch (error) {
    await pool.query('ROLLBACK');
    console.error('❌ Error applying fixes:', error.message);
    throw error;
  }
}

async function main() {
  try {
    await fixSchema();
  } catch (error) {
    console.error('❌ Script failed:', error.message);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

if (require.main === module) {
  main().catch(console.error);
}