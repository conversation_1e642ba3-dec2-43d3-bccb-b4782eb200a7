require('dotenv').config();
const { Client } = require('pg');

const client = new Client({
  connectionString: process.env.DATABASE_URL,
  ssl: { rejectUnauthorized: false }
});

const additionalTables = [
  {
    name: 'panel_versions',
    sql: `CREATE TABLE IF NOT EXISTS panel_versions (
      id SERIAL PRIMARY KEY,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
      deleted_at TIMESTAMP WITH TIME ZONE,
      panel_id VARCHAR(100) NOT NULL,
      version VARCHAR(50) NOT NULL,
      config JSONB DEFAULT '{}',
      content JSONB DEFAULT '{}',
      is_active BOOLEAN DEFAULT FALSE,
      created_by VARCHAR(50),
      notes TEXT
    )`
  }
];

const alterCommands = [
  // Add missing column aliases for backward compatibility
  "ALTER TABLE panel_deployments ADD COLUMN IF NOT EXISTS status VARCHAR(20)",
  "ALTER TABLE panel_deployments ADD COLUMN IF NOT EXISTS config JSONB",
  
  // Update status column to mirror deployment_status
  "UPDATE panel_deployments SET status = deployment_status WHERE status IS NULL",
  "UPDATE panel_deployments SET config = deployment_config WHERE config IS NULL"
];

const indexes = [
  'CREATE INDEX IF NOT EXISTS panel_versions_panel_id_idx ON panel_versions (panel_id)',
  'CREATE INDEX IF NOT EXISTS panel_versions_version_idx ON panel_versions (version)',
  'CREATE INDEX IF NOT EXISTS panel_versions_active_idx ON panel_versions (is_active)',
  'CREATE INDEX IF NOT EXISTS panel_deployments_status_idx ON panel_deployments (status)'
];

(async () => {
  try {
    await client.connect();
    console.log('🔌 Connected to database');
    
    // Create missing tables
    for (const table of additionalTables) {
      try {
        console.log(`📄 Creating table: ${table.name}`);
        await client.query(table.sql);
        console.log(`✅ Table created: ${table.name}`);
      } catch (error) {
        if (error.code === '42P07') {
          console.log(`⚠️  Table already exists: ${table.name}`);
        } else {
          console.error(`❌ Failed to create ${table.name}:`, error.message);
        }
      }
    }
    
    // Add missing columns
    console.log('\n🔧 Adding missing columns...');
    for (const alterCommand of alterCommands) {
      try {
        await client.query(alterCommand);
        console.log('✅ Column operation completed');
      } catch (error) {
        console.log(`⚠️  Column operation failed: ${error.message}`);
      }
    }
    
    // Create indexes
    console.log('\n📊 Creating indexes...');
    for (const indexSql of indexes) {
      try {
        await client.query(indexSql);
        console.log('✅ Index created');
      } catch (error) {
        console.log(`⚠️  Index creation failed: ${error.message}`);
      }
    }
    
    // Insert some default panel versions
    console.log('\n🌱 Inserting default panel versions...');
    const panelTypes = [
      'announcement', 'community', 'ai-mastery', 'wealth-creation', 
      'personal-growth', 'technical-support', 'networking-business',
      'gaming-entertainment', 'trading-markets', 'creative-showcase', 
      'educational-resources'
    ];
    
    for (const panelType of panelTypes) {
      try {
        await client.query(`
          INSERT INTO panel_versions (panel_id, version, config, content, is_active)
          VALUES ($1, '1.0.0', '{}', '{}', true)
          ON CONFLICT DO NOTHING
        `, [`${panelType}_default`]);
        console.log(`✅ Default version created for ${panelType}`);
      } catch (error) {
        console.log(`⚠️  Failed to create version for ${panelType}: ${error.message}`);
      }
    }
    
    console.log('\n🎉 Additional schema fixes completed!');
    
  } catch (error) {
    console.error('❌ Database operation failed:', error.message);
  } finally {
    await client.end();
  }
})();