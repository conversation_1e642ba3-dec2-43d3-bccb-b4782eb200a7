#!/usr/bin/env node

/**
 * Fix Users Table ID Generation Script
 * 
 * This script specifically fixes the users table ID column to ensure
 * it properly auto-generates IDs using PostgreSQL's serial type.
 */

require('dotenv').config();
const { Pool } = require('pg');

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
});

async function executeQuery(query, params = []) {
  try {
    console.log(`Executing: ${query.substring(0, 100)}...`);
    const result = await pool.query(query, params);
    return result;
  } catch (error) {
    console.error('Query error:', error.message);
    throw error;
  }
}

async function checkSequence(sequenceName) {
  try {
    const result = await pool.query(`
      SELECT EXISTS (
        SELECT FROM pg_sequences 
        WHERE schemaname = 'public' 
        AND sequencename = $1
      );
    `, [sequenceName]);
    return result.rows[0].exists;
  } catch (error) {
    console.error(`Error checking sequence ${sequenceName}:`, error.message);
    return false;
  }
}

async function fixUsersTable() {
  console.log('🔧 Fixing users table ID generation...\n');

  try {
    // First, check the current data type of the ID column
    const typeCheckResult = await executeQuery(`
      SELECT data_type, column_default, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'users' 
      AND column_name = 'id';
    `);
    
    if (typeCheckResult.rows.length > 0) {
      const columnInfo = typeCheckResult.rows[0];
      console.log(`Current ID column type: ${columnInfo.data_type}`);
      console.log(`Current default: ${columnInfo.column_default || 'none'}`);
      console.log(`Is nullable: ${columnInfo.is_nullable}`);
    }

    // Step 1: Check if the sequence exists
    const hasSequence = await checkSequence('users_id_seq');
    
    if (!hasSequence) {
      console.log('Creating sequence for users.id...');
      await executeQuery(`CREATE SEQUENCE IF NOT EXISTS users_id_seq;`);
    }

    // Step 2: Get the current maximum ID (handle both text and integer types)
    let maxId = 0;
    try {
      const maxIdResult = await executeQuery(`
        SELECT CASE 
          WHEN MAX(id::integer) IS NULL THEN 0 
          ELSE MAX(id::integer) 
        END as max_id 
        FROM users 
        WHERE id ~ '^[0-9]+$';
      `);
      maxId = maxIdResult.rows[0].max_id;
      console.log(`Current max ID in users table: ${maxId}`);
    } catch (e) {
      console.log('Could not determine max ID, starting from 1');
      maxId = 0;
    }

    // Step 3: Set the sequence to start after the current max ID
    await executeQuery(`SELECT setval('users_id_seq', $1, true);`, [maxId]);

    // Step 4: Alter the column to use the sequence as default
    console.log('Setting default value for users.id column...');
    await executeQuery(`
      ALTER TABLE users 
      ALTER COLUMN id 
      SET DEFAULT nextval('users_id_seq'::regclass);
    `);

    // Step 5: Ensure the column is NOT NULL
    console.log('Ensuring id column has NOT NULL constraint...');
    await executeQuery(`
      ALTER TABLE users 
      ALTER COLUMN id 
      SET NOT NULL;
    `);

    // Step 6: Ensure the sequence is owned by the column
    console.log('Setting sequence ownership...');
    try {
      await executeQuery(`
        ALTER SEQUENCE users_id_seq 
        OWNED BY users.id;
      `);
    } catch (e) {
      console.log('Could not set sequence ownership, continuing...');
    }

    console.log('\n✅ Users table ID generation fixed successfully!');

    // Test the fix by checking if we can insert a user without specifying ID
    console.log('\n🧪 Testing the fix...');
    
    // First, check if we have a test user
    const testUserResult = await executeQuery(`
      SELECT id, discord_id, username 
      FROM users 
      WHERE discord_id = 'test_user_12345' 
      LIMIT 1;
    `);

    if (testUserResult.rows.length === 0) {
      // Insert a test user without specifying ID
      const insertResult = await executeQuery(`
        INSERT INTO users (discord_id, username, email, is_active, created_at, updated_at)
        VALUES ('test_user_12345', 'Test User', '<EMAIL>', true, NOW(), NOW())
        RETURNING id, discord_id, username;
      `);
      
      if (insertResult.rows.length > 0) {
        console.log(`✅ Test successful! Created user with auto-generated ID: ${insertResult.rows[0].id}`);
        
        // Clean up test user
        await executeQuery(`DELETE FROM users WHERE discord_id = 'test_user_12345';`);
        console.log('🧹 Test user cleaned up.');
      }
    } else {
      console.log('✅ Test user already exists with ID:', testUserResult.rows[0].id);
    }

    // Display current sequence value
    const seqResult = await executeQuery(`SELECT last_value FROM users_id_seq;`);
    console.log(`\n📊 Current sequence value: ${seqResult.rows[0].last_value}`);

  } catch (error) {
    console.error('\n❌ Error fixing users table:', error.message);
    console.error('Full error:', error);
  } finally {
    await pool.end();
  }
}

fixUsersTable().catch(console.error);
