#!/usr/bin/env node

/**
 * Comprehensive Fix for Users Table ID Column
 * 
 * This script converts the users.id column from varchar to serial (auto-incrementing integer)
 */

require('dotenv').config();
const { Pool } = require('pg');

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
});

async function executeQuery(query, params = []) {
  try {
    console.log(`Executing: ${query.substring(0, 100)}...`);
    const result = await pool.query(query, params);
    return result;
  } catch (error) {
    console.error('Query error:', error.message);
    throw error;
  }
}

async function fixUsersTable() {
  console.log('🔧 Starting comprehensive users table ID fix...\n');

  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    
    // Step 1: Check current table structure
    console.log('📊 Checking current table structure...');
    const columnInfo = await client.query(`
      SELECT 
        column_name,
        data_type,
        column_default,
        is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'users' 
      AND column_name = 'id';
    `);
    
    if (columnInfo.rows.length === 0) {
      throw new Error('Users table or ID column not found!');
    }
    
    const idColumn = columnInfo.rows[0];
    console.log(`Current ID column type: ${idColumn.data_type}`);
    console.log(`Current default: ${idColumn.column_default || 'none'}\n`);
    
    // Step 2: Check if we need to convert the column type
    if (idColumn.data_type !== 'integer' && idColumn.data_type !== 'bigint') {
      console.log('⚠️  ID column is not integer type. Need to convert it.');
      
      // Step 2a: Create a temporary column
      console.log('Creating temporary ID column...');
      await client.query(`
        ALTER TABLE users 
        ADD COLUMN id_temp SERIAL;
      `);
      
      // Step 2b: Update temporary column with existing numeric IDs
      console.log('Copying numeric IDs to temporary column...');
      await client.query(`
        UPDATE users 
        SET id_temp = id::integer 
        WHERE id ~ '^[0-9]+$';
      `);
      
      // Step 2c: Handle any non-numeric IDs by assigning new values
      const nonNumericCount = await client.query(`
        SELECT COUNT(*) as count 
        FROM users 
        WHERE id !~ '^[0-9]+$' OR id IS NULL;
      `);
      
      if (parseInt(nonNumericCount.rows[0].count) > 0) {
        console.log(`Found ${nonNumericCount.rows[0].count} non-numeric IDs. They will get new IDs.`);
      }
      
      // Step 2d: Drop the old ID column
      console.log('Dropping old ID column...');
      await client.query(`ALTER TABLE users DROP COLUMN id;`);
      
      // Step 2e: Rename temporary column to id
      console.log('Renaming temporary column to id...');
      await client.query(`ALTER TABLE users RENAME COLUMN id_temp TO id;`);
      
      // Step 2f: Set as primary key
      console.log('Setting ID as primary key...');
      await client.query(`ALTER TABLE users ADD PRIMARY KEY (id);`);
      
    } else {
      console.log('✅ ID column is already integer type.');
      
      // Just ensure it has proper serial default
      console.log('Ensuring proper serial configuration...');
      
      // Create sequence if it doesn't exist
      await client.query(`
        DO $$
        BEGIN
          IF NOT EXISTS (SELECT 1 FROM pg_sequences WHERE sequencename = 'users_id_seq') THEN
            CREATE SEQUENCE users_id_seq;
          END IF;
        END $$;
      `);
      
      // Get max ID
      const maxIdResult = await client.query(`SELECT COALESCE(MAX(id), 0) as max_id FROM users;`);
      const maxId = Math.max(1, parseInt(maxIdResult.rows[0].max_id));
      
      // Set sequence value
      await client.query(`SELECT setval('users_id_seq', $1, true);`, [maxId]);
      
      // Set column default
      await client.query(`
        ALTER TABLE users 
        ALTER COLUMN id 
        SET DEFAULT nextval('users_id_seq'::regclass);
      `);
      
      // Set sequence ownership
      await client.query(`
        ALTER SEQUENCE users_id_seq 
        OWNED BY users.id;
      `);
    }
    
    // Step 3: Verify the fix
    console.log('\n🧪 Testing the fix...');
    
    // Insert a test record
    const testResult = await client.query(`
      INSERT INTO users (discord_id, username, email, is_active, created_at, updated_at)
      VALUES ('test_auto_id_' || NOW()::text, 'Test Auto ID User', '<EMAIL>', true, NOW(), NOW())
      RETURNING id, discord_id, username;
    `);
    
    if (testResult.rows.length > 0) {
      const testUser = testResult.rows[0];
      console.log(`✅ Successfully created user with auto-generated ID: ${testUser.id}`);
      
      // Clean up test user
      await client.query(`DELETE FROM users WHERE id = $1;`, [testUser.id]);
      console.log('🧹 Test user cleaned up.');
    }
    
    // Get final column info
    const finalInfo = await client.query(`
      SELECT 
        column_name,
        data_type,
        column_default,
        is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'users' 
      AND column_name = 'id';
    `);
    
    console.log('\n✅ Final ID column configuration:');
    console.log(`   Type: ${finalInfo.rows[0].data_type}`);
    console.log(`   Default: ${finalInfo.rows[0].column_default}`);
    console.log(`   Nullable: ${finalInfo.rows[0].is_nullable}`);
    
    await client.query('COMMIT');
    console.log('\n🎉 Users table ID fix completed successfully!');
    
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('\n❌ Error during fix:', error.message);
    console.error('Full error:', error);
    throw error;
  } finally {
    client.release();
    await pool.end();
  }
}

// Also update the UserService to not include ID when creating users
async function createUserServiceUpdateScript() {
  const updateScript = `
// Update UserService to not include ID in insert statements
// The ID will be auto-generated by the database

// In createUser method, ensure we're not passing an ID:
const newUser = {
  discordId: userData.discordId,
  username: userData.username,
  email: userData.email,
  // Remove any id field here
  // ... other fields
};

// The insert will automatically generate the ID
const result = await this.db.insert(users).values(newUser).returning();
`;

  console.log('\n📝 Note: Update your UserService createUser method to not include ID in the insert.');
  console.log('The database will now auto-generate IDs for new users.');
}

fixUsersTable()
  .then(() => createUserServiceUpdateScript())
  .catch(console.error);
