#!/usr/bin/env node

/**
 * Database Schema Fix Script
 * 
 * This script fixes the missing database schema issues mentioned in the error logs:
 * 1. Missing ai_agent_config table
 * 2. Missing user_id column in agent_memory table
 * 3. Missing updated_at column in agent_interactions table
 * 4. Issues with users table id column constraints
 * 
 * Run with: node scripts/fix-database-schema.js
 */

const { Pool } = require('pg');
const path = require('path');
const fs = require('fs');

// Database connection configuration
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:password@localhost:5432/discordbot',
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
});

const schemaFixSQL = `
-- Fix missing schema issues for Discord bot
-- This migration addresses the errors found in the logs:

-- 1. Ensure ai_agent_config table exists
CREATE TABLE IF NOT EXISTS "ai_agent_config" (
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"deleted_at" timestamp with time zone,
	"guild_id" text NOT NULL,
	"agent_type" varchar(50) NOT NULL,
	"enabled" boolean DEFAULT true NOT NULL,
	"configuration" jsonb,
	"default_channel_id" text,
	"log_channel_id" text,
	"permissions" jsonb,
	"settings" jsonb,
	"last_used_at" timestamp with time zone
);

-- 2. Ensure agent_memory table exists with user_id column
CREATE TABLE IF NOT EXISTS "agent_memory" (
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"deleted_at" timestamp with time zone,
	"user_id" text NOT NULL,
	"memory_type" varchar(50) NOT NULL,
	"key" varchar(200) NOT NULL,
	"value" jsonb NOT NULL,
	"context" text,
	"importance" integer DEFAULT 1 NOT NULL,
	"access_count" integer DEFAULT 0 NOT NULL,
	"last_accessed_at" timestamp with time zone,
	"expires_at" timestamp with time zone,
	"tags" text[],
	"metadata" jsonb
);

-- 3. Ensure agent_interactions table exists with updated_at column
CREATE TABLE IF NOT EXISTS "agent_interactions" (
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"deleted_at" timestamp with time zone,
	"user_id" text NOT NULL,
	"agent_type" varchar(50) NOT NULL,
	"interaction_type" varchar(50) NOT NULL,
	"content" text NOT NULL,
	"response" text,
	"status" varchar(20) DEFAULT 'completed' NOT NULL,
	"context" jsonb,
	"metadata" jsonb,
	"channel_id" text,
	"message_id" text,
	"guild_id" text,
	"sentiment_score" integer DEFAULT 0,
	"tags" text[]
);

-- 4. Ensure users table has proper structure
CREATE TABLE IF NOT EXISTS "users" (
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"deleted_at" timestamp with time zone,
	"discord_id" varchar(50) NOT NULL UNIQUE,
	"username" varchar(100) NOT NULL,
	"email" varchar(255),
	"is_active" boolean DEFAULT true NOT NULL,
	"last_activity_at" timestamp with time zone,
	"preferences" jsonb,
	"profile" jsonb,
	"access_token" text,
	"refresh_token" text,
	"token_expires_at" timestamp with time zone,
	"experience" integer DEFAULT 0 NOT NULL,
	"balance" integer DEFAULT 0 NOT NULL,
	"user_id" varchar(50),
	"discriminator" varchar(10),
	"avatar_url" text
);

-- Add missing columns to existing tables
DO $$
BEGIN
    -- Add user_id to agent_memory if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agent_memory' AND column_name = 'user_id') THEN
        ALTER TABLE "agent_memory" ADD COLUMN "user_id" text NOT NULL DEFAULT '';
    END IF;
    
    -- Add updated_at to agent_interactions if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agent_interactions' AND column_name = 'updated_at') THEN
        ALTER TABLE "agent_interactions" ADD COLUMN "updated_at" timestamp with time zone DEFAULT now() NOT NULL;
    END IF;
    
    -- Ensure discord_id is unique in users
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE table_name = 'users' AND constraint_name = 'users_discord_id_unique') THEN
        ALTER TABLE "users" ADD CONSTRAINT "users_discord_id_unique" UNIQUE ("discord_id");
    END IF;
END
$$;

-- Create essential indexes
CREATE INDEX IF NOT EXISTS "ai_agent_config_guild_id_idx" ON "ai_agent_config" USING btree ("guild_id");
CREATE INDEX IF NOT EXISTS "ai_agent_config_agent_type_idx" ON "ai_agent_config" USING btree ("agent_type");
CREATE INDEX IF NOT EXISTS "agent_memory_user_id_idx" ON "agent_memory" USING btree ("user_id");
CREATE INDEX IF NOT EXISTS "agent_memory_memory_type_idx" ON "agent_memory" USING btree ("memory_type");
CREATE INDEX IF NOT EXISTS "agent_interactions_user_id_idx" ON "agent_interactions" USING btree ("user_id");
CREATE INDEX IF NOT EXISTS "agent_interactions_agent_type_idx" ON "agent_interactions" USING btree ("agent_type");
CREATE INDEX IF NOT EXISTS "users_discord_id_idx" ON "users" USING btree ("discord_id");

-- Insert default configuration for the guild mentioned in logs
INSERT INTO "ai_agent_config" (guild_id, agent_type, enabled, configuration, settings) 
VALUES 
('1394355426941730856', 'general', true, '{"channels":["general"],"personality":{"tone":"friendly","style":"helpful"}}', '{"maxResponsesPerHour":50}')
ON CONFLICT DO NOTHING;
`;

async function runSchemaFix() {
  console.log('🔧 Starting database schema fix...');
  
  try {
    console.log('📋 Checking database connection...');
    const client = await pool.connect();
    
    try {
      console.log('🗄️ Running schema fixes...');
      await client.query(schemaFixSQL);
      console.log('✅ Schema fixes completed successfully!');
      
      // Verify the fixes
      console.log('🔍 Verifying fixes...');
      const tables = ['ai_agent_config', 'agent_memory', 'agent_interactions', 'users'];
      
      for (const table of tables) {
        const result = await client.query(`SELECT COUNT(*) as count FROM ${table}`);
        console.log(`📊 Table ${table}: ${result.rows[0].count} records`);
      }
      
      console.log('🎉 Database schema verification completed!');
      
    } finally {
      client.release();
    }
    
  } catch (error) {
    console.error('❌ Error running schema fix:', error.message);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run the fix if called directly
if (require.main === module) {
  runSchemaFix().catch(console.error);
}

module.exports = { runSchemaFix };