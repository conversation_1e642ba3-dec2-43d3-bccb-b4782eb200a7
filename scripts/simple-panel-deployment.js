require('dotenv').config();
const { Client } = require('pg');

const client = new Client({
  connectionString: process.env.DATABASE_URL,
  ssl: { rejectUnauthorized: false }
});

const channelIds = [
  // ANNOUNCEMENTS
  '1396352345046257665', // 📢-announcements
  '1396352364843241592', // 📰-news
  '1394499361454555207', // 📋-rules
  
  // COMMUNITY
  '1396529696442810390', // welcome
  '1396529869730353283', // introductions
  '1396352417976553553', // 🖼-media
  '1396530178079064154', // links-dump
  '1396536876801720330', // 💎-premium-chat
  
  // AI MASTERY
  '1396535106825498635', // 🤖-ai-tools
  '1396534857636765737', // 📚-tutorials
  '1396535054480613407', // 📰-ai-news
  '1396535178896207932', // 💻-coding
  '1396535202149986314', // ⚙️-automation
  
  // WEALTH CREATION
  '1396537049045983262', // 💰-money-strategies
  '1396537097049968702', // 🏆-success-stories
  '1396537124925861958', // 🚀-entrepreneur-chat
  '1396537151098134588', // 💼-subscriptions
  
  // PERSONAL GROWTH
  '1396537233948909680', // 🧠-mindset
  '1396537255088947210', // 🎯-goals
  '1396537278598299718', // ⚡-productivity
  
  // TECHNICAL SUPPORT
  '1396537319652298763', // 🛠-tech-support
  '1396537342850203729', // ❓-help
  
  // NETWORKING & BUSINESS
  '1396595103518289952', // ⚔️-war-room
  '1396595124535160932', // 🤝-networking
];

const panelTypeMapping = {
  // Announcements
  '1396352345046257665': 'announcement',
  '1396352364843241592': 'announcement', 
  '1394499361454555207': 'announcement',
  
  // Community
  '1396529696442810390': 'community',
  '1396529869730353283': 'community',
  '1396352417976553553': 'community',
  '1396530178079064154': 'community',
  '1396536876801720330': 'community',
  
  // AI Mastery
  '1396535106825498635': 'ai-mastery',
  '1396534857636765737': 'ai-mastery',
  '1396535054480613407': 'ai-mastery',
  '1396535178896207932': 'ai-mastery',
  '1396535202149986314': 'ai-mastery',
  
  // Wealth Creation
  '1396537049045983262': 'wealth-creation',
  '1396537097049968702': 'wealth-creation',
  '1396537124925861958': 'wealth-creation',
  '1396537151098134588': 'wealth-creation',
  
  // Personal Growth
  '1396537233948909680': 'personal-growth',  
  '1396537255088947210': 'personal-growth',
  '1396537278598299718': 'personal-growth',
  
  // Technical Support
  '1396537319652298763': 'technical-support',
  '1396537342850203729': 'technical-support',
  
  // Networking & Business
  '1396595103518289952': 'networking-business',
  '1396595124535160932': 'networking-business',
};

async function deployPanelsToDatabase() {
  console.log('🚀 Starting simple panel database deployment...');
  
  try {
    await client.connect();
    console.log('🔌 Connected to database');
    
    let successful = 0;
    let failed = 0;
    
    for (const channelId of channelIds) {
      const panelType = panelTypeMapping[channelId];
      if (!panelType) {
        console.log(`⚠️  No panel type mapping for channel ${channelId}`);
        failed++;
        continue;
      }
      
      try {
        // Check if deployment already exists
        const existingQuery = 'SELECT id FROM panel_deployments WHERE channel_id = $1 AND panel_id = $2';
        const existingResult = await client.query(existingQuery, [channelId, `${panelType}_${channelId}`]);
        
        if (existingResult.rows.length > 0) {
          // Update existing deployment
          await client.query(`
            UPDATE panel_deployments SET 
              deployment_status = $1, status = $1, is_active = $2, last_update_at = NOW()
            WHERE channel_id = $3 AND panel_id = $4
          `, ['active', true, channelId, `${panelType}_${channelId}`]);
        } else {
          // Insert new deployment record
          await client.query(`
            INSERT INTO panel_deployments (
              channel_id, guild_id, panel_id, deployment_status, 
              deployment_config, is_active, last_update_at,
              status, config
            ) VALUES ($1, $2, $3, $4, $5, $6, NOW(), $4, $5)
          `, [
            channelId,
            '1394497888019546133', // Your guild ID
            `${panelType}_${channelId}`,
            'active',
            JSON.stringify({ autoUpdate: true, updateInterval: 3600000 }),
            true
          ]);
        }
        
        console.log(`✅ Deployed ${panelType} panel to channel ${channelId}`);
        successful++;
        
      } catch (error) {
        console.log(`❌ Failed to deploy to channel ${channelId}: ${error.message}`);
        failed++;
      }
    }
    
    console.log(`\n📊 Deployment Summary:`);
    console.log(`   ✅ Successful: ${successful}`);
    console.log(`   ❌ Failed: ${failed}`);
    
    // Show current deployment count
    const countQuery = 'SELECT COUNT(*) as count FROM panel_deployments WHERE is_active = true';
    const countResult = await client.query(countQuery);
    console.log(`   📋 Total active deployments: ${countResult.rows[0].count}`);
    
    console.log('\n🎉 Simple panel deployment completed!');
    
  } catch (error) {
    console.error('❌ Deployment failed:', error.message);
  } finally {
    await client.end();
  }
}

deployPanelsToDatabase();