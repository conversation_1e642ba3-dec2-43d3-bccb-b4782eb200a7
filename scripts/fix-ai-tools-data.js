require('dotenv').config();
const { Client } = require('pg');

const client = new Client({
  connectionString: process.env.DATABASE_URL,
  ssl: { rejectUnauthorized: false }
});

(async () => {
  try {
    await client.connect();
    console.log('🔌 Connected to database');
    
    // Check current ai_tools data
    const checkQuery = 'SELECT id, name, features, tags FROM ai_tools LIMIT 5';
    const checkResult = await client.query(checkQuery);
    
    console.log('📊 Current AI tools data:');
    for (const row of checkResult.rows) {
      console.log(`   ${row.name}: features=${typeof row.features}, tags=${typeof row.tags}`);
    }
    
    // Fix invalid JSON data by clearing and reinserting properly
    console.log('\n🔧 Fixing AI tools data...');
    await client.query('DELETE FROM ai_tools');
    
    // Insert properly formatted data
    const aiToolsData = [
      {
        name: 'ChatGPT',
        description: 'Advanced AI language model for conversations and content creation',
        category: 'conversational-ai',
        url: 'https://chat.openai.com',
        pricing: 'Free tier + paid plans',
        features: ['conversation', 'content-creation', 'coding', 'analysis'],
        rating: 4.8,
        popularity: 100,
        tags: ['ai', 'gpt', 'openai', 'chatbot']
      },
      {
        name: 'Claude',
        description: 'AI assistant for analysis, writing, math, coding, creative tasks',
        category: 'conversational-ai',
        url: 'https://claude.ai',
        pricing: 'Free tier + paid plans',
        features: ['analysis', 'writing', 'coding', 'creative'],
        rating: 4.7,
        popularity: 95,
        tags: ['ai', 'anthropic', 'assistant', 'analysis']
      },
      {
        name: 'GitHub Copilot',
        description: 'AI-powered code completion and assistance',
        category: 'coding',
        url: 'https://github.com/features/copilot',
        pricing: '$10/month',
        features: ['code-completion', 'code-generation', 'ide-integration'],
        rating: 4.5,
        popularity: 85,
        tags: ['coding', 'github', 'ai', 'development']
      },
      {
        name: 'Midjourney',
        description: 'AI-powered image generation and art creation',
        category: 'image-generation',
        url: 'https://midjourney.com',
        pricing: 'Subscription plans',
        features: ['image-generation', 'art-creation', 'style-transfer'],
        rating: 4.6,
        popularity: 90,
        tags: ['ai', 'image', 'art', 'generation']
      },
      {
        name: 'Stable Diffusion',
        description: 'Open-source AI image generation model',
        category: 'image-generation',
        url: 'https://stability.ai',
        pricing: 'Free (open source)',
        features: ['image-generation', 'customizable', 'local-deployment'],
        rating: 4.3,
        popularity: 75,
        tags: ['ai', 'open-source', 'image', 'diffusion']
      }
    ];
    
    for (const tool of aiToolsData) {
      try {
        await client.query(`
          INSERT INTO ai_tools (name, description, category, url, pricing, features, rating, popularity, tags)
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        `, [
          tool.name,
          tool.description,
          tool.category,
          tool.url,
          tool.pricing,
          JSON.stringify(tool.features),
          tool.rating,
          tool.popularity,
          JSON.stringify(tool.tags)
        ]);
        console.log(`✅ Inserted ${tool.name}`);
      } catch (error) {
        console.log(`❌ Failed to insert ${tool.name}: ${error.message}`);
      }
    }
    
    // Verify the fix
    const verifyQuery = 'SELECT COUNT(*) as count FROM ai_tools';
    const verifyResult = await client.query(verifyQuery);
    console.log(`\n✅ AI tools table now has ${verifyResult.rows[0].count} records`);
    
    console.log('\n🎉 AI tools data fixed!');
    
  } catch (error) {
    console.error('❌ Fix failed:', error.message);
  } finally {
    await client.end();
  }
})();