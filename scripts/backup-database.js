const { Pool } = require('pg');
const fs = require('fs').promises;
const path = require('path');
require('dotenv').config();

const backupDir = process.argv[2] || `backups/${new Date().toISOString().replace(/[:.]/g, '-')}`;

async function createBackup() {
  console.log('Starting database backup...');
  console.log(`Backup directory: ${backupDir}`);
  
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
  });

  try {
    // Ensure backup directory exists
    await fs.mkdir(backupDir, { recursive: true });

    // Tables to backup
    const tables = ['agent_memory', 'agent_interactions', 'users'];
    
    // Create schema documentation
    const schemaDoc = {
      timestamp: new Date().toISOString(),
      database: process.env.DATABASE_URL.split('@')[1]?.split('/')[0] || 'unknown',
      tables: {}
    };

    for (const table of tables) {
      console.log(`\nBacking up table: ${table}`);
      
      // Get table schema
      const schemaQuery = `
        SELECT 
          column_name,
          data_type,
          character_maximum_length,
          is_nullable,
          column_default,
          udt_name
        FROM information_schema.columns
        WHERE table_name = $1
        ORDER BY ordinal_position;
      `;
      
      const schemaResult = await pool.query(schemaQuery, [table]);
      schemaDoc.tables[table] = {
        columns: schemaResult.rows,
        rowCount: 0,
        constraints: []
      };

      // Get constraints
      const constraintQuery = `
        SELECT
          conname AS constraint_name,
          contype AS constraint_type,
          pg_get_constraintdef(oid) AS definition
        FROM pg_constraint
        WHERE conrelid = $1::regclass;
      `;
      
      try {
        const constraintResult = await pool.query(constraintQuery, [table]);
        schemaDoc.tables[table].constraints = constraintResult.rows;
      } catch (err) {
        console.log(`Warning: Could not fetch constraints for ${table}:`, err.message);
      }

      // Get indexes
      const indexQuery = `
        SELECT
          indexname,
          indexdef
        FROM pg_indexes
        WHERE tablename = $1;
      `;
      
      try {
        const indexResult = await pool.query(indexQuery, [table]);
        schemaDoc.tables[table].indexes = indexResult.rows;
      } catch (err) {
        console.log(`Warning: Could not fetch indexes for ${table}:`, err.message);
      }

      // Count rows
      const countResult = await pool.query(`SELECT COUNT(*) FROM ${table}`);
      schemaDoc.tables[table].rowCount = parseInt(countResult.rows[0].count);
      
      // Export data
      const dataResult = await pool.query(`SELECT * FROM ${table}`);
      
      // Save data to JSON file
      await fs.writeFile(
        path.join(backupDir, `${table}_data.json`),
        JSON.stringify(dataResult.rows, null, 2)
      );
      
      console.log(`  - Exported ${dataResult.rows.length} rows`);
      console.log(`  - Schema: ${schemaResult.rows.length} columns`);
      console.log(`  - Saved to ${table}_data.json`);

      // Create SQL dump for table
      const sqlDump = await createSQLDump(table, schemaResult.rows, dataResult.rows);
      await fs.writeFile(
        path.join(backupDir, `${table}_dump.sql`),
        sqlDump
      );
    }

    // Save schema documentation
    await fs.writeFile(
      path.join(backupDir, 'schema_documentation.json'),
      JSON.stringify(schemaDoc, null, 2)
    );

    // Create a summary report
    const report = `Database Backup Report
=====================
Timestamp: ${new Date().toISOString()}
Backup Directory: ${backupDir}

Tables Backed Up:
${tables.map(t => `- ${t}: ${schemaDoc.tables[t].rowCount} rows`).join('\n')}

Files Created:
${tables.map(t => `- ${t}_data.json\n- ${t}_dump.sql`).join('\n')}
- schema_documentation.json

Schema Summary:
${tables.map(t => `
${t}:
  Columns: ${schemaDoc.tables[t].columns.length}
  Constraints: ${schemaDoc.tables[t].constraints.length}
  Indexes: ${schemaDoc.tables[t].indexes?.length || 0}
`).join('\n')}
`;

    await fs.writeFile(
      path.join(backupDir, 'backup_report.txt'),
      report
    );

    console.log('\n' + report);
    console.log('Backup completed successfully!');

  } catch (error) {
    console.error('Backup failed:', error);
    throw error;
  } finally {
    await pool.end();
  }
}

function createSQLDump(tableName, columns, rows) {
  let sql = `-- Table: ${tableName}\n`;
  sql += `-- Generated on: ${new Date().toISOString()}\n\n`;
  
  // Create table statement
  sql += `CREATE TABLE IF NOT EXISTS ${tableName} (\n`;
  sql += columns.map(col => {
    let def = `  ${col.column_name} ${col.data_type}`;
    if (col.character_maximum_length) {
      def += `(${col.character_maximum_length})`;
    }
    if (col.is_nullable === 'NO') {
      def += ' NOT NULL';
    }
    if (col.column_default) {
      def += ` DEFAULT ${col.column_default}`;
    }
    return def;
  }).join(',\n');
  sql += '\n);\n\n';
  
  // Insert statements
  if (rows.length > 0) {
    sql += `-- Data for ${tableName}\n`;
    rows.forEach(row => {
      const columns = Object.keys(row).filter(k => row[k] !== null);
      const values = columns.map(col => {
        const val = row[col];
        if (val === null) return 'NULL';
        if (typeof val === 'object') return `'${JSON.stringify(val).replace(/'/g, "''")}'::jsonb`;
        if (typeof val === 'string') return `'${val.replace(/'/g, "''")}'`;
        if (val instanceof Date) return `'${val.toISOString()}'`;
        return val;
      });
      
      sql += `INSERT INTO ${tableName} (${columns.join(', ')}) VALUES (${values.join(', ')});\n`;
    });
  }
  
  return sql;
}

// Run the backup
createBackup().catch(console.error);
