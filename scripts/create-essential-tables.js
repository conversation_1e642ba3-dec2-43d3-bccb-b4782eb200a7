require('dotenv').config();
const { Client } = require('pg');

const client = new Client({
  connectionString: process.env.DATABASE_URL,
  ssl: { rejectUnauthorized: false }
});

const essentialTables = [
  {
    name: 'panel_states',
    sql: `CREATE TABLE IF NOT EXISTS panel_states (
      id SERIAL PRIMARY KEY,
      panel_id VARCHAR(100) NOT NULL UNIQUE,
      state JSONB NOT NULL DEFAULT '{}',
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
    )`
  },
  {
    name: 'panel_analytics',
    sql: `CREATE TABLE IF NOT EXISTS panel_analytics (
      id SERIAL PRIMARY KEY,
      panel_id VARCHAR(100) NOT NULL,
      event_type VARCHAR(50) NOT NULL,
      event_data JSONB,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
    )`
  },
  {
    name: 'panel_content',
    sql: `CREATE TABLE IF NOT EXISTS panel_content (
      id SERIAL PRIMARY KEY,
      panel_id VARCHAR(100) NOT NULL UNIQUE,
      content JSONB NOT NULL,
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
    )`
  },
  {
    name: 'user_panel_states',
    sql: `CREATE TABLE IF NOT EXISTS user_panel_states (
      id SERIAL PRIMARY KEY,
      user_id VARCHAR(50) NOT NULL UNIQUE,
      preferences JSONB DEFAULT '{}',
      panel_history JSONB DEFAULT '[]',
      active_interactions JSONB DEFAULT '[]',
      last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      settings JSONB DEFAULT '{}',
      notifications JSONB DEFAULT '{}',
      achievements JSONB DEFAULT '[]',
      stats JSONB DEFAULT '{}',
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
    )`
  },
  {
    name: 'ai_tools',
    sql: `CREATE TABLE IF NOT EXISTS ai_tools (
      id SERIAL PRIMARY KEY,
      name VARCHAR(200) NOT NULL,
      description TEXT,
      category VARCHAR(100),
      url VARCHAR(500),
      pricing VARCHAR(200),
      features JSONB DEFAULT '[]',
      rating DECIMAL(3,2) DEFAULT 0.0,
      popularity INTEGER DEFAULT 0,
      tags JSONB DEFAULT '[]',
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
    )`
  },
  {
    name: 'troubleshooting_guides',
    sql: `CREATE TABLE IF NOT EXISTS troubleshooting_guides (
      id SERIAL PRIMARY KEY,
      title VARCHAR(300) NOT NULL,
      category VARCHAR(100) NOT NULL,
      sub_category VARCHAR(100),
      symptoms JSONB DEFAULT '[]',
      diagnostic_steps JSONB DEFAULT '[]',
      solutions JSONB DEFAULT '[]',
      common_causes JSONB DEFAULT '[]',
      prevention_tips JSONB DEFAULT '[]',
      related_issues JSONB DEFAULT '[]',
      difficulty VARCHAR(20) DEFAULT 'medium',
      estimated_time VARCHAR(50),
      relevance_score INTEGER DEFAULT 0,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
    )`
  }
];

(async () => {
  try {
    await client.connect();
    console.log('🔌 Connected to database');
    
    for (const table of essentialTables) {
      try {
        console.log(`📄 Creating table: ${table.name}`);
        await client.query(table.sql);
        console.log(`✅ Table created: ${table.name}`);
      } catch (error) {
        if (error.code === '42P07') {
          console.log(`⚠️  Table already exists: ${table.name}`);
        } else {
          console.error(`❌ Failed to create ${table.name}:`, error.message);
        }
      }
    }
    
    // Create indexes
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_panel_states_panel_id ON panel_states (panel_id)',
      'CREATE INDEX IF NOT EXISTS idx_panel_analytics_panel_id ON panel_analytics (panel_id)',
      'CREATE INDEX IF NOT EXISTS idx_panel_analytics_event_type ON panel_analytics (event_type)',
      'CREATE INDEX IF NOT EXISTS idx_user_panel_states_user_id ON user_panel_states (user_id)',
      'CREATE INDEX IF NOT EXISTS idx_ai_tools_category ON ai_tools (category)',
      'CREATE INDEX IF NOT EXISTS idx_troubleshooting_guides_category ON troubleshooting_guides (category)'
    ];
    
    console.log('\n📊 Creating indexes...');
    for (const indexSql of indexes) {
      try {
        await client.query(indexSql);
        console.log('✅ Index created');
      } catch (error) {
        console.log(`⚠️  Index creation failed: ${error.message}`);
      }
    }
    
    // Insert sample data
    console.log('\n🌱 Inserting sample data...');
    
    // Sample AI tools
    const aiToolsData = [
      "('ChatGPT', 'Advanced AI language model for conversations and content creation', 'conversational-ai', 'https://chat.openai.com', 'Free tier + paid plans', '[\"conversation\", \"content-creation\", \"coding\", \"analysis\"]', 4.8, 100, '[\"ai\", \"gpt\", \"openai\", \"chatbot\"]')",
      "('Claude', 'AI assistant for analysis, writing, math, coding, creative tasks', 'conversational-ai', 'https://claude.ai', 'Free tier + paid plans', '[\"analysis\", \"writing\", \"coding\", \"creative\"]', 4.7, 95, '[\"ai\", \"anthropic\", \"assistant\", \"analysis\"]')",
      "('GitHub Copilot', 'AI-powered code completion and assistance', 'coding', 'https://github.com/features/copilot', '$10/month', '[\"code-completion\", \"code-generation\", \"ide-integration\"]', 4.5, 85, '[\"coding\", \"github\", \"ai\", \"development\"]')"
    ];
    
    try {
      await client.query(`
        INSERT INTO ai_tools (name, description, category, url, pricing, features, rating, popularity, tags) VALUES 
        ${aiToolsData.join(', ')}
        ON CONFLICT DO NOTHING
      `);
      console.log('✅ Sample AI tools inserted');
    } catch (error) {
      console.log(`⚠️  AI tools insertion failed: ${error.message}`);
    }
    
    // Sample troubleshooting guides
    const troubleshootingData = [
      "('Discord Bot Not Responding', 'discord', 'bot-issues', '[\"Bot offline\", \"Commands not working\", \"No response\"]', '[\"Check bot status\", \"Verify token\", \"Check permissions\", \"Review logs\"]', '[\"Restart bot\", \"Update token\", \"Fix permissions\", \"Check code errors\"]', '[\"Invalid token\", \"Missing permissions\", \"Code errors\", \"Server issues\"]', '[\"Regular monitoring\", \"Token security\", \"Permission audits\"]', 'medium', '15-30 minutes', 10)",
      "('Database Connection Failed', 'database', 'connection', '[\"Connection timeout\", \"Authentication failed\", \"Cannot connect\"]', '[\"Check credentials\", \"Verify network\", \"Test connection\", \"Review configuration\"]', '[\"Update credentials\", \"Fix network issues\", \"Restart database\", \"Update configuration\"]', '[\"Wrong credentials\", \"Network issues\", \"Database down\", \"Configuration errors\"]', '[\"Regular backups\", \"Monitor connections\", \"Secure credentials\"]', 'medium', '10-20 minutes', 9)"
    ];
    
    try {
      await client.query(`
        INSERT INTO troubleshooting_guides (title, category, sub_category, symptoms, diagnostic_steps, solutions, common_causes, prevention_tips, difficulty, estimated_time, relevance_score) VALUES 
        ${troubleshootingData.join(', ')}
        ON CONFLICT DO NOTHING
      `);
      console.log('✅ Sample troubleshooting guides inserted');
    } catch (error) {
      console.log(`⚠️  Troubleshooting guides insertion failed: ${error.message}`);
    }
    
    console.log('\n🎉 Essential tables creation completed!');
    
  } catch (error) {
    console.error('❌ Database operation failed:', error.message);
  } finally {
    await client.end();
  }
})();