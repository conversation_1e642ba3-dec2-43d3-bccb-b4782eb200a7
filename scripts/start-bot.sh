#!/usr/bin/env bash

# Discord Bot Startup Script with Process Management
# This script ensures the bot runs consistently and restarts on failure

set -euo pipefail

# Configuration
BOT_NAME="discord-bot-energex"
LOG_DIR="./logs"
PID_FILE="./logs/${BOT_NAME}.pid"
LOG_FILE="./logs/${BOT_NAME}.log"
ERROR_LOG="./logs/${BOT_NAME}.error.log"
MAX_RESTARTS=5
RESTART_DELAY=10

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" | tee -a "$ERROR_LOG"
}

success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1" | tee -a "$LOG_FILE"
}

# Create log directory
mkdir -p "$LOG_DIR"

# Function to check if bot is running
is_running() {
    if [[ -f "$PID_FILE" ]]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0
        else
            rm -f "$PID_FILE"
            return 1
        fi
    fi
    return 1
}

# Function to stop the bot
stop_bot() {
    if is_running; then
        local pid=$(cat "$PID_FILE")
        log "Stopping Discord bot (PID: $pid)..."
        kill "$pid" 2>/dev/null || true
        
        # Wait for graceful shutdown
        local count=0
        while ps -p "$pid" > /dev/null 2>&1 && [[ $count -lt 10 ]]; do
            sleep 1
            ((count++))
        done
        
        # Force kill if still running
        if ps -p "$pid" > /dev/null 2>&1; then
            warning "Force killing bot process..."
            kill -9 "$pid" 2>/dev/null || true
        fi
        
        rm -f "$PID_FILE"
        success "Bot stopped successfully"
    else
        log "Bot is not running"
    fi
}

# Function to start the bot
start_bot() {
    if is_running; then
        warning "Bot is already running (PID: $(cat "$PID_FILE"))"
        return 0
    fi

    log "Starting Discord bot..."
    
    # Check if dependencies are installed
    if [[ ! -d "node_modules" ]]; then
        log "Installing dependencies..."
        pnpm install
    fi
    
    # Build the application
    log "Building application..."
    if ! pnpm run build 2>>"$ERROR_LOG"; then
        error "Build failed! Check $ERROR_LOG for details"
        return 1
    fi
    
    # Start the bot in background
    log "Launching bot process..."
    nohup pnpm run start:prod >> "$LOG_FILE" 2>> "$ERROR_LOG" &
    local pid=$!
    
    # Save PID
    echo "$pid" > "$PID_FILE"
    
    # Wait a moment and check if it's still running
    sleep 3
    if ps -p "$pid" > /dev/null 2>&1; then
        success "Bot started successfully (PID: $pid)"
        return 0
    else
        error "Bot failed to start"
        rm -f "$PID_FILE"
        return 1
    fi
}

# Function to restart the bot
restart_bot() {
    log "Restarting Discord bot..."
    stop_bot
    sleep 2
    start_bot
}

# Function to monitor and auto-restart
monitor_bot() {
    local restart_count=0
    
    log "Starting bot monitoring..."
    
    while true; do
        if ! is_running; then
            if [[ $restart_count -lt $MAX_RESTARTS ]]; then
                warning "Bot is not running. Attempting restart ($((restart_count + 1))/$MAX_RESTARTS)..."
                
                if start_bot; then
                    restart_count=0
                    success "Bot restarted successfully"
                else
                    ((restart_count++))
                    error "Failed to restart bot (attempt $restart_count/$MAX_RESTARTS)"
                    
                    if [[ $restart_count -ge $MAX_RESTARTS ]]; then
                        error "Maximum restart attempts reached. Stopping monitor."
                        break
                    fi
                fi
                
                sleep "$RESTART_DELAY"
            else
                error "Maximum restart attempts reached. Manual intervention required."
                break
            fi
        fi
        
        sleep 30  # Check every 30 seconds
    done
}

# Function to show status
status() {
    if is_running; then
        local pid=$(cat "$PID_FILE")
        success "Bot is running (PID: $pid)"
        
        # Show recent logs
        echo ""
        echo "Recent logs:"
        tail -n 10 "$LOG_FILE" 2>/dev/null || echo "No logs available"
    else
        warning "Bot is not running"
    fi
}

# Function to show logs
show_logs() {
    local lines=${1:-50}
    echo "=== Bot Logs (last $lines lines) ==="
    tail -n "$lines" "$LOG_FILE" 2>/dev/null || echo "No logs available"
    
    echo ""
    echo "=== Error Logs (last $lines lines) ==="
    tail -n "$lines" "$ERROR_LOG" 2>/dev/null || echo "No error logs"
}

# Main script logic
case "${1:-}" in
    start)
        start_bot
        ;;
    stop)
        stop_bot
        ;;
    restart)
        restart_bot
        ;;
    status)
        status
        ;;
    monitor)
        monitor_bot
        ;;
    logs)
        show_logs "${2:-50}"
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|status|monitor|logs [lines]}"
        echo ""
        echo "Commands:"
        echo "  start   - Start the Discord bot"
        echo "  stop    - Stop the Discord bot"
        echo "  restart - Restart the Discord bot"
        echo "  status  - Show bot status"
        echo "  monitor - Start monitoring and auto-restart"
        echo "  logs    - Show recent logs (default: 50 lines)"
        exit 1
        ;;
esac
