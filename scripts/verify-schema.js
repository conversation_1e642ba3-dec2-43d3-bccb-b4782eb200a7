#!/usr/bin/env node

/**
 * Database Schema Verification and Fix Script
 * 
 * This script verifies the current database state and applies necessary fixes
 * to resolve the schema issues mentioned in the error logs.
 */

require('dotenv').config();
const { drizzle } = require('drizzle-orm/node-postgres');
const { Pool } = require('pg');

// Database connection
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
});

const db = drizzle(pool);

async function checkTableExists(tableName) {
  try {
    const result = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = $1
      );
    `, [tableName]);
    return result.rows[0].exists;
  } catch (error) {
    console.error(`Error checking table ${tableName}:`, error.message);
    return false;
  }
}

async function checkColumnExists(tableName, columnName) {
  try {
    const result = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = $1 
        AND column_name = $2
      );
    `, [tableName, columnName]);
    return result.rows[0].exists;
  } catch (error) {
    console.error(`Error checking column ${columnName} in ${tableName}:`, error.message);
    return false;
  }
}

async function fixSchema() {
  console.log('🔍 Starting database schema verification...\n');

  const issues = [];

  // Check ai_agent_config table
  const hasAiAgentConfig = await checkTableExists('ai_agent_config');
  console.log(`📊 ai_agent_config table: ${hasAiAgentConfig ? '✅ EXISTS' : '❌ MISSING'}`);
  if (!hasAiAgentConfig) issues.push('ai_agent_config table missing');

  // Check agent_memory table and user_id column
  const hasAgentMemory = await checkTableExists('agent_memory');
  console.log(`📊 agent_memory table: ${hasAgentMemory ? '✅ EXISTS' : '❌ MISSING'}`);
  if (!hasAgentMemory) {
    issues.push('agent_memory table missing');
  } else {
    const hasUserId = await checkColumnExists('agent_memory', 'user_id');
    console.log(`  └─ user_id column: ${hasUserId ? '✅ EXISTS' : '❌ MISSING'}`);
    if (!hasUserId) issues.push('agent_memory.user_id column missing');
  }

  // Check agent_interactions table and updated_at column
  const hasAgentInteractions = await checkTableExists('agent_interactions');
  console.log(`📊 agent_interactions table: ${hasAgentInteractions ? '✅ EXISTS' : '❌ MISSING'}`);
  if (!hasAgentInteractions) {
    issues.push('agent_interactions table missing');
  } else {
    const hasUpdatedAt = await checkColumnExists('agent_interactions', 'updated_at');
    console.log(`  └─ updated_at column: ${hasUpdatedAt ? '✅ EXISTS' : '❌ MISSING'}`);
    if (!hasUpdatedAt) issues.push('agent_interactions.updated_at column missing');
  }

  // Check users table structure
  const hasUsers = await checkTableExists('users');
  console.log(`📊 users table: ${hasUsers ? '✅ EXISTS' : '❌ MISSING'}`);
  if (hasUsers) {
    const hasId = await checkColumnExists('users', 'id');
    console.log(`  └─ id column: ${hasId ? '✅ EXISTS' : '❌ MISSING'}`);
    
    const hasDiscordId = await checkColumnExists('users', 'discord_id');
    console.log(`  └─ discord_id column: ${hasDiscordId ? '✅ EXISTS' : '❌ MISSING'}`);
  }

  console.log('\n' + '='.repeat(50));

  if (issues.length === 0) {
    console.log('🎉 All database schema checks passed! No issues found.');
    return;
  }

  console.log(`❌ Found ${issues.length} schema issue(s):`);
  issues.forEach(issue => console.log(`   - ${issue}`));

  console.log('\n🔧 Applying fixes...');

  try {
    // Create missing tables and columns
    await pool.query(`
      -- Create ai_agent_config if missing
      CREATE TABLE IF NOT EXISTS "ai_agent_config" (
        "id" serial PRIMARY KEY NOT NULL,
        "created_at" timestamp with time zone DEFAULT now() NOT NULL,
        "updated_at" timestamp with time zone DEFAULT now() NOT NULL,
        "deleted_at" timestamp with time zone,
        "guild_id" text NOT NULL,
        "agent_type" varchar(50) NOT NULL,
        "enabled" boolean DEFAULT true NOT NULL,
        "configuration" jsonb,
        "default_channel_id" text,
        "log_channel_id" text,
        "permissions" jsonb,
        "settings" jsonb,
        "last_used_at" timestamp with time zone
      );

      -- Create agent_memory if missing
      CREATE TABLE IF NOT EXISTS "agent_memory" (
        "id" serial PRIMARY KEY NOT NULL,
        "created_at" timestamp with time zone DEFAULT now() NOT NULL,
        "updated_at" timestamp with time zone DEFAULT now() NOT NULL,
        "deleted_at" timestamp with time zone,
        "user_id" text NOT NULL,
        "memory_type" varchar(50) NOT NULL,
        "key" varchar(200) NOT NULL,
        "value" jsonb NOT NULL,
        "context" text,
        "importance" integer DEFAULT 1 NOT NULL,
        "access_count" integer DEFAULT 0 NOT NULL,
        "last_accessed_at" timestamp with time zone,
        "expires_at" timestamp with time zone,
        "tags" text[],
        "metadata" jsonb
      );

      -- Create agent_interactions if missing
      CREATE TABLE IF NOT EXISTS "agent_interactions" (
        "id" serial PRIMARY KEY NOT NULL,
        "created_at" timestamp with time zone DEFAULT now() NOT NULL,
        "updated_at" timestamp with time zone DEFAULT now() NOT NULL,
        "deleted_at" timestamp with time zone,
        "user_id" text NOT NULL,
        "agent_type" varchar(50) NOT NULL,
        "interaction_type" varchar(50) NOT NULL,
        "content" text NOT NULL,
        "response" text,
        "status" varchar(20) DEFAULT 'completed' NOT NULL,
        "context" jsonb,
        "metadata" jsonb,
        "channel_id" text,
        "message_id" text,
        "guild_id" text,
        "sentiment_score" integer DEFAULT 0,
        "tags" text[]
      );

      -- Create users if missing
      CREATE TABLE IF NOT EXISTS "users" (
        "id" serial PRIMARY KEY NOT NULL,
        "created_at" timestamp with time zone DEFAULT now() NOT NULL,
        "updated_at" timestamp with time zone DEFAULT now() NOT NULL,
        "deleted_at" timestamp with time zone,
        "discord_id" varchar(50) NOT NULL UNIQUE,
        "username" varchar(100) NOT NULL,
        "email" varchar(255),
        "is_active" boolean DEFAULT true NOT NULL,
        "last_activity_at" timestamp with time zone,
        "preferences" jsonb,
        "profile" jsonb,
        "access_token" text,
        "refresh_token" text,
        "token_expires_at" timestamp with time zone,
        "experience" integer DEFAULT 0 NOT NULL,
        "balance" integer DEFAULT 0 NOT NULL,
        "user_id" varchar(50),
        "discriminator" varchar(10),
        "avatar_url" text
      );

      -- Add missing columns to existing tables
      DO $$
      BEGIN
          -- Add user_id to agent_memory if missing
          IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agent_memory' AND column_name = 'user_id') THEN
              ALTER TABLE "agent_memory" ADD COLUMN "user_id" text NOT NULL DEFAULT '';
          END IF;
          
          -- Add updated_at to agent_interactions if missing
          IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agent_interactions' AND column_name = 'updated_at') THEN
              ALTER TABLE "agent_interactions" ADD COLUMN "updated_at" timestamp with time zone DEFAULT now() NOT NULL;
          END IF;
          
          -- Ensure discord_id has unique constraint
          IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE table_name = 'users' AND constraint_name = 'users_discord_id_unique') THEN
              ALTER TABLE "users" ADD CONSTRAINT "users_discord_id_unique" UNIQUE ("discord_id");
          END IF;
      END
      $$;

      -- Create essential indexes
      CREATE INDEX IF NOT EXISTS "ai_agent_config_guild_id_idx" ON "ai_agent_config" USING btree ("guild_id");
      CREATE INDEX IF NOT EXISTS "ai_agent_config_agent_type_idx" ON "ai_agent_config" USING btree ("agent_type");
      CREATE INDEX IF NOT EXISTS "agent_memory_user_id_idx" ON "agent_memory" USING btree ("user_id");
      CREATE INDEX IF NOT EXISTS "agent_memory_memory_type_idx" ON "agent_memory" USING btree ("memory_type");
      CREATE INDEX IF NOT EXISTS "agent_interactions_user_id_idx" ON "agent_interactions" USING btree ("user_id");
      CREATE INDEX IF NOT EXISTS "agent_interactions_agent_type_idx" ON "agent_interactions" USING btree ("agent_type");
      CREATE INDEX IF NOT EXISTS "users_discord_id_idx" ON "users" USING btree ("discord_id");

      -- Insert default configuration for the guild mentioned in logs
      INSERT INTO "ai_agent_config" (guild_id, agent_type, enabled, configuration, settings) 
      VALUES 
      ('1394355426941730856', 'general', true, '{"channels":["general"],"personality":{"tone":"friendly","style":"helpful"}}', '{"maxResponsesPerHour":50}')
      ON CONFLICT DO NOTHING;
    `);

    console.log('✅ Schema fixes applied successfully!');
    
    // Re-verify
    console.log('\n🔍 Re-verifying schema...');
    const verification = await pool.query(`
      SELECT 'ai_agent_config' as table_name, COUNT(*) as count FROM ai_agent_config
      UNION ALL
      SELECT 'agent_memory' as table_name, COUNT(*) as count FROM agent_memory
      UNION ALL
      SELECT 'agent_interactions' as table_name, COUNT(*) as count FROM agent_interactions
      UNION ALL
      SELECT 'users' as table_name, COUNT(*) as count FROM users;
    `);
    
    verification.rows.forEach(row => {
      console.log(`📊 ${row.table_name}: ${row.count} rows`);
    });

  } catch (error) {
    console.error('❌ Error applying fixes:', error.message);
  }
}

async function main() {
  try {
    await fixSchema();
  } catch (error) {
    console.error('❌ Script failed:', error.message);
  } finally {
    await pool.end();
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { fixSchema };