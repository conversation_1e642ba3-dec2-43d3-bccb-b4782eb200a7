require('dotenv').config();
const { Client } = require('pg');

const client = new Client({
  connectionString: process.env.DATABASE_URL,
  ssl: { rejectUnauthorized: false }
});

async function cleanupFailedDeployments() {
  console.log('🧹 Starting cleanup of failed panel deployments...');
  
  try {
    await client.connect();
    console.log('🔌 Connected to database');
    
    // First, let's see what failed deployments we have
    const failedQuery = `
      SELECT id, channel_id, guild_id, panel_id, deployment_status, error_state
      FROM panel_deployments 
      WHERE deployment_status = 'failed' OR needs_cleanup = true
      ORDER BY created_at DESC
    `;
    
    const failedResult = await client.query(failedQuery);
    console.log(`📊 Found ${failedResult.rows.length} failed/cleanup deployments:`);
    
    for (const row of failedResult.rows) {
      console.log(`   - Panel: ${row.panel_id}, Channel: ${row.channel_id}, Status: ${row.deployment_status}`);
    }
    
    if (failedResult.rows.length === 0) {
      console.log('✅ No failed deployments found!');
      return;
    }
    
    // Clean up stale deployments (older than 24 hours with failed status)
    const cleanupQuery = `
      DELETE FROM panel_deployments 
      WHERE (deployment_status = 'failed' OR needs_cleanup = true)
      AND created_at < NOW() - INTERVAL '24 hours'
    `;
    
    const cleanupResult = await client.query(cleanupQuery);
    console.log(`🗑️  Cleaned up ${cleanupResult.rowCount} stale failed deployments`);
    
    // Reset recent failed deployments to allow retry
    const resetQuery = `
      UPDATE panel_deployments 
      SET deployment_status = 'inactive',
          needs_cleanup = false,
          retry_count = 0,
          error_state = NULL,
          last_update_at = NOW()
      WHERE deployment_status = 'failed'
      AND created_at >= NOW() - INTERVAL '24 hours'
    `;
    
    const resetResult = await client.query(resetQuery);
    console.log(`🔄 Reset ${resetResult.rowCount} recent failed deployments for retry`);
    
    // Show current deployment stats
    const statsQuery = `
      SELECT 
        deployment_status,
        COUNT(*) as count
      FROM panel_deployments 
      GROUP BY deployment_status
      ORDER BY deployment_status
    `;
    
    const statsResult = await client.query(statsQuery);
    console.log('\n📊 Current deployment status:');
    for (const row of statsResult.rows) {
      console.log(`   ${row.deployment_status}: ${row.count}`);
    }
    
    console.log('\n✅ Cleanup completed! You can now run the deployment script.');
    
  } catch (error) {
    console.error('❌ Cleanup failed:', error.message);
  } finally {
    await client.end();
  }
}

cleanupFailedDeployments();