require('dotenv').config();
const { Client } = require('pg');

const client = new Client({
  connectionString: process.env.DATABASE_URL,
  ssl: { rejectUnauthorized: false }
});

async function checkPanelStatus() {
  console.log('📊 Checking panel deployment status...');
  
  try {
    await client.connect();
    console.log('🔌 Connected to database');
    
    // Check if panel_deployments table exists and has data
    const existsQuery = `
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'panel_deployments'
      );
    `;
    
    const existsResult = await client.query(existsQuery);
    console.log(`📋 Panel deployments table exists: ${existsResult.rows[0].exists}`);
    
    if (!existsResult.rows[0].exists) {
      console.log('⚠️  Panel deployments table does not exist - this explains the previous errors!');
      return;
    }
    
    // Get all deployments
    const allQuery = `
      SELECT 
        id, channel_id, guild_id, panel_id, deployment_status, 
        is_active, needs_cleanup, created_at, last_update_at
      FROM panel_deployments 
      ORDER BY created_at DESC
      LIMIT 20
    `;
    
    const allResult = await client.query(allQuery);
    console.log(`📋 Total deployments found: ${allResult.rows.length}`);
    
    if (allResult.rows.length === 0) {
      console.log('📝 No panel deployments found - panels need to be deployed!');
      console.log('   Run: npm run deploy:panels or node src/features/channel-panels/scripts/deploy-panels.ts');
      return;
    }
    
    // Show recent deployments
    console.log('\n📋 Recent deployments:');
    for (const row of allResult.rows) {
      console.log(`   ${row.panel_id} -> ${row.channel_id} (${row.deployment_status}) - ${row.created_at}`);
    }
    
    // Get deployment statistics
    const statsQuery = `
      SELECT 
        deployment_status,
        is_active,
        COUNT(*) as count
      FROM panel_deployments 
      GROUP BY deployment_status, is_active
      ORDER BY deployment_status, is_active
    `;
    
    const statsResult = await client.query(statsQuery);
    console.log('\n📊 Deployment statistics:');
    for (const row of statsResult.rows) {
      console.log(`   ${row.deployment_status} (active: ${row.is_active}): ${row.count}`);
    }
    
    // Check for problematic deployments
    const problemsQuery = `
      SELECT COUNT(*) as count FROM panel_deployments 
      WHERE deployment_status = 'failed' 
         OR needs_cleanup = true 
         OR (deployment_status = 'active' AND last_update_at < NOW() - INTERVAL '1 day')
    `;
    
    const problemsResult = await client.query(problemsQuery);
    const problemCount = parseInt(problemsResult.rows[0].count);
    
    if (problemCount > 0) {
      console.log(`\n⚠️  Found ${problemCount} problematic deployments that may need attention`);
    } else {
      console.log('\n✅ All deployments look healthy!');
    }
    
  } catch (error) {
    console.error('❌ Status check failed:', error.message);
  } finally {
    await client.end();
  }
}

checkPanelStatus();