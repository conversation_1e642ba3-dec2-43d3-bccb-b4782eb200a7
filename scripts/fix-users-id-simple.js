#!/usr/bin/env node

/**
 * Simple Fix for Users Table ID Generation
 * 
 * This script ensures that new users can be created with auto-generated IDs
 * even if the ID column is varchar type.
 */

require('dotenv').config();
const { Pool } = require('pg');

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
});

async function executeQuery(query, params = []) {
  try {
    console.log(`Executing: ${query.substring(0, 100)}...`);
    const result = await pool.query(query, params);
    return result;
  } catch (error) {
    console.error('Query error:', error.message);
    throw error;
  }
}

async function createIdGenerationFunction() {
  console.log('🔧 Creating ID generation solution for users table...\n');

  try {
    // Step 1: Create a sequence if it doesn't exist
    console.log('Creating sequence for ID generation...');
    await executeQuery(`
      CREATE SEQUENCE IF NOT EXISTS users_id_seq START WITH 1;
    `);

    // Step 2: Get the current maximum numeric ID
    console.log('Finding current maximum ID...');
    const maxIdResult = await executeQuery(`
      SELECT COALESCE(MAX(CAST(id AS INTEGER)), 0) as max_id 
      FROM users 
      WHERE id ~ '^[0-9]+$';
    `);
    
    const maxId = parseInt(maxIdResult.rows[0].max_id) || 0;
    console.log(`Current max ID: ${maxId}`);

    // Step 3: Set the sequence to start after the current max
    if (maxId > 0) {
      await executeQuery(`SELECT setval('users_id_seq', $1, true);`, [maxId]);
      console.log(`Sequence set to start at: ${maxId + 1}`);
    }

    // Step 4: Create a function to generate next ID
    console.log('\nCreating ID generation function...');
    await executeQuery(`
      CREATE OR REPLACE FUNCTION next_user_id() 
      RETURNS TEXT AS $$
      BEGIN
        RETURN nextval('users_id_seq')::TEXT;
      END;
      $$ LANGUAGE plpgsql;
    `);

    // Step 5: Set default value for ID column to use the function
    console.log('Setting default value for ID column...');
    await executeQuery(`
      ALTER TABLE users 
      ALTER COLUMN id 
      SET DEFAULT next_user_id();
    `);

    console.log('\n✅ ID generation setup completed!');

    // Step 6: Test the solution
    console.log('\n🧪 Testing the fix...');
    
    const testResult = await executeQuery(`
      INSERT INTO users (discord_id, username, email, is_active, created_at, updated_at)
      VALUES ('test_' || NOW()::text, 'Test User', '<EMAIL>', true, NOW(), NOW())
      RETURNING id, discord_id, username;
    `);
    
    if (testResult.rows.length > 0) {
      const testUser = testResult.rows[0];
      console.log(`✅ Successfully created user with auto-generated ID: ${testUser.id}`);
      
      // Clean up
      await executeQuery(`DELETE FROM users WHERE id = $1;`, [testUser.id]);
      console.log('🧹 Test user cleaned up.');
    }

    // Display current sequence value
    const seqResult = await executeQuery(`SELECT last_value FROM users_id_seq;`);
    console.log(`\n📊 Current sequence value: ${seqResult.rows[0].last_value}`);

    console.log('\n🎉 Users table can now auto-generate IDs!');
    console.log('\n📝 Note: When creating users in your application:');
    console.log('   - Do NOT include the "id" field in INSERT statements');
    console.log('   - The database will automatically generate sequential IDs');

  } catch (error) {
    console.error('\n❌ Error:', error.message);
    console.error('Details:', error);
  } finally {
    await pool.end();
  }
}

createIdGenerationFunction().catch(console.error);
