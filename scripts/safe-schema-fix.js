#!/usr/bin/env node

const { Client } = require('pg');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const DATABASE_URL = process.env.DATABASE_URL;

if (!DATABASE_URL) {
  console.error('❌ DATABASE_URL not found in environment variables');
  process.exit(1);
}

async function tableExists(client, tableName) {
  const result = await client.query(`
    SELECT EXISTS (
      SELECT FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name = $1
    );
  `, [tableName]);
  return result.rows[0].exists;
}

async function columnExists(client, tableName, columnName) {
  const result = await client.query(`
    SELECT EXISTS (
      SELECT FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name = $1 
      AND column_name = $2
    );
  `, [tableName, columnName]);
  return result.rows[0].exists;
}

async function createIndexSafely(client, indexName, tableName, columns) {
  // Check if all columns exist
  const columnArray = Array.isArray(columns) ? columns : [columns];
  for (const col of columnArray) {
    if (!await columnExists(client, tableName, col)) {
      console.log(`   ⚠️  Skipping index ${indexName} - column ${col} doesn't exist in table ${tableName}`);
      return;
    }
  }
  
  const columnList = columnArray.map(col => `"${col}"`).join(', ');
  const query = `CREATE INDEX IF NOT EXISTS "${indexName}" ON "${tableName}" (${columnList})`;
  
  try {
    await client.query(query);
    console.log(`   ✅ Created/verified index: ${indexName}`);
  } catch (error) {
    console.log(`   ⚠️  Failed to create index ${indexName}: ${error.message}`);
  }
}

async function runMigration() {
  const client = new Client({
    connectionString: DATABASE_URL,
  });

  try {
    console.log('🔧 Starting safe database schema fix...');
    await client.connect();
    console.log('✅ Connected to database');

    // First, let's check what tables already exist
    console.log('\n📋 Checking existing tables...');
    const existingTables = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_type = 'BASE TABLE'
      ORDER BY table_name;
    `);
    
    console.log('Existing tables:');
    existingTables.rows.forEach(row => {
      console.log(`   - ${row.table_name}`);
    });

    // Create enums first
    console.log('\n📋 Creating enums...');
    await client.query(`
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'organization_tier') THEN
          CREATE TYPE organization_tier AS ENUM ('free', 'starter', 'professional', 'enterprise', 'custom');
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'organization_status') THEN
          CREATE TYPE organization_status AS ENUM ('active', 'suspended', 'trial', 'cancelled');
        END IF;
      END
      $$;
    `);

    // Create missing tables
    const tables = [
      {
        name: 'users',
        query: `
          CREATE TABLE IF NOT EXISTS "users" (
            "id" serial PRIMARY KEY NOT NULL,
            "created_at" timestamp with time zone DEFAULT now() NOT NULL,
            "updated_at" timestamp with time zone DEFAULT now() NOT NULL,
            "deleted_at" timestamp with time zone,
            "discord_id" varchar(50) NOT NULL,
            "username" varchar(100) NOT NULL,
            "email" varchar(255),
            "is_active" boolean DEFAULT true NOT NULL,
            "last_activity_at" timestamp with time zone,
            "preferences" jsonb,
            "profile" jsonb,
            "access_token" text,
            "refresh_token" text,
            "token_expires_at" timestamp with time zone,
            "experience" integer DEFAULT 0 NOT NULL,
            "balance" integer DEFAULT 0 NOT NULL,
            "user_id" varchar(50),
            "discriminator" varchar(10),
            "avatar_url" text
          );
        `
      },
      {
        name: 'guilds',
        query: `
          CREATE TABLE IF NOT EXISTS "guilds" (
            "id" serial PRIMARY KEY NOT NULL,
            "created_at" timestamp with time zone DEFAULT now() NOT NULL,
            "updated_at" timestamp with time zone DEFAULT now() NOT NULL,
            "deleted_at" timestamp with time zone,
            "discord_id" varchar(50) NOT NULL,
            "name" varchar(100) NOT NULL,
            "icon" text,
            "is_active" boolean DEFAULT true NOT NULL,
            "settings" jsonb,
            "features" jsonb,
            "last_activity_at" timestamp with time zone,
            "owner_discord_id" varchar(50),
            "welcome_enabled" boolean DEFAULT false NOT NULL,
            "welcome_channel_id" varchar(50),
            "welcome_message" text,
            "welcome_roles" jsonb,
            "starboard_enabled" boolean DEFAULT false NOT NULL,
            "starboard_channel_id" varchar(50),
            "starboard_threshold" integer DEFAULT 3 NOT NULL,
            "guild_id" varchar(50)
          );
        `
      },
      {
        name: 'ai_agent_config',
        query: `
          CREATE TABLE IF NOT EXISTS "ai_agent_config" (
            "id" serial PRIMARY KEY NOT NULL,
            "created_at" timestamp with time zone DEFAULT now() NOT NULL,
            "updated_at" timestamp with time zone DEFAULT now() NOT NULL,
            "deleted_at" timestamp with time zone,
            "guild_id" text NOT NULL,
            "agent_type" varchar(50) NOT NULL,
            "enabled" boolean DEFAULT true NOT NULL,
            "configuration" jsonb,
            "default_channel_id" text,
            "log_channel_id" text,
            "permissions" jsonb,
            "settings" jsonb,
            "last_used_at" timestamp with time zone
          );
        `
      },
      {
        name: 'agent_interactions',
        query: `
          CREATE TABLE IF NOT EXISTS "agent_interactions" (
            "id" serial PRIMARY KEY NOT NULL,
            "created_at" timestamp with time zone DEFAULT now() NOT NULL,
            "updated_at" timestamp with time zone DEFAULT now() NOT NULL,
            "deleted_at" timestamp with time zone,
            "user_id" text NOT NULL,
            "agent_type" varchar(50) NOT NULL,
            "interaction_type" varchar(50) NOT NULL,
            "content" text NOT NULL,
            "response" text,
            "status" varchar(20) DEFAULT 'completed' NOT NULL,
            "context" jsonb,
            "metadata" jsonb,
            "channel_id" text,
            "message_id" text,
            "guild_id" text,
            "sentiment_score" integer DEFAULT 0,
            "tags" text[]
          );
        `
      },
      {
        name: 'agent_memory',
        query: `
          CREATE TABLE IF NOT EXISTS "agent_memory" (
            "id" serial PRIMARY KEY NOT NULL,
            "created_at" timestamp with time zone DEFAULT now() NOT NULL,
            "updated_at" timestamp with time zone DEFAULT now() NOT NULL,
            "deleted_at" timestamp with time zone,
            "user_id" text NOT NULL,
            "memory_type" varchar(50) NOT NULL,
            "key" varchar(200) NOT NULL,
            "value" jsonb NOT NULL,
            "context" text,
            "importance" integer DEFAULT 1 NOT NULL,
            "access_count" integer DEFAULT 0 NOT NULL,
            "last_accessed_at" timestamp with time zone,
            "expires_at" timestamp with time zone,
            "tags" text[],
            "metadata" jsonb
          );
        `
      }
    ];

    // Create each table
    for (const table of tables) {
      if (!await tableExists(client, table.name)) {
        console.log(`\n📋 Creating table: ${table.name}...`);
        await client.query(table.query);
        console.log(`   ✅ Created table: ${table.name}`);
      } else {
        console.log(`\n📋 Table ${table.name} already exists`);
      }
    }

    // Add missing columns to existing tables
    console.log('\n📋 Checking for missing columns...');
    
    // Check and add missing columns for users table
    if (await tableExists(client, 'users')) {
      if (!await columnExists(client, 'users', 'discord_id')) {
        console.log('   Adding discord_id to users table...');
        await client.query(`ALTER TABLE "users" ADD COLUMN "discord_id" varchar(50) NOT NULL`);
      }
    }

    // Add unique constraints
    console.log('\n📋 Adding unique constraints...');
    const constraints = [
      { table: 'users', column: 'discord_id', name: 'users_discord_id_unique' },
      { table: 'guilds', column: 'discord_id', name: 'guilds_discord_id_unique' },
      { table: 'sessions', column: 'session_id', name: 'sessions_session_id_unique' }
    ];

    for (const constraint of constraints) {
      if (await tableExists(client, constraint.table) && await columnExists(client, constraint.table, constraint.column)) {
        await client.query(`
          DO $$
          BEGIN
            IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                           WHERE table_name = '${constraint.table}' AND constraint_name = '${constraint.name}') THEN
              ALTER TABLE "${constraint.table}" ADD CONSTRAINT "${constraint.name}" UNIQUE ("${constraint.column}");
            END IF;
          END
          $$;
        `);
        console.log(`   ✅ Verified constraint: ${constraint.name}`);
      }
    }

    // Create indexes safely
    console.log('\n📋 Creating indexes...');
    
    // Define all indexes with their dependencies
    const indexes = [
      // Users indexes
      { name: 'users_discord_id_idx', table: 'users', columns: 'discord_id' },
      { name: 'users_created_at_idx', table: 'users', columns: 'created_at' },
      { name: 'users_username_idx', table: 'users', columns: 'username' },
      
      // Guilds indexes
      { name: 'guilds_discord_id_idx', table: 'guilds', columns: 'discord_id' },
      
      // AI Agent Config indexes
      { name: 'ai_agent_config_guild_id_idx', table: 'ai_agent_config', columns: 'guild_id' },
      { name: 'ai_agent_config_agent_type_idx', table: 'ai_agent_config', columns: 'agent_type' },
      
      // Agent Interactions indexes
      { name: 'agent_interactions_user_id_idx', table: 'agent_interactions', columns: 'user_id' },
      { name: 'agent_interactions_agent_type_idx', table: 'agent_interactions', columns: 'agent_type' },
      { name: 'agent_interactions_interaction_type_idx', table: 'agent_interactions', columns: 'interaction_type' },
      { name: 'agent_interactions_created_at_idx', table: 'agent_interactions', columns: 'created_at' },
      
      // Agent Memory indexes
      { name: 'agent_memory_user_id_idx', table: 'agent_memory', columns: 'user_id' },
      { name: 'agent_memory_memory_type_idx', table: 'agent_memory', columns: 'memory_type' },
      { name: 'agent_memory_created_at_idx', table: 'agent_memory', columns: 'created_at' },
    ];

    for (const index of indexes) {
      if (await tableExists(client, index.table)) {
        await createIndexSafely(
          client, 
          index.name, 
          index.table, 
          index.columns
        );
      } else {
        console.log(`   ⚠️  Skipping index ${index.name} - table ${index.table} doesn't exist`);
      }
    }

    // Insert default data if needed
    console.log('\n📋 Inserting default data...');
    try {
      await client.query(`
        INSERT INTO "ai_agent_config" (guild_id, agent_type, enabled, configuration, settings) 
        VALUES 
        ('1394355426941730856', 'general', true, 
         '{"channels":["general"],"defaultAgent":"general","personality":{"tone":"friendly","style":"helpful"}}', 
         '{"maxResponsesPerHour":50,"cooldownSeconds":30}')
        ON CONFLICT DO NOTHING;
      `);
      console.log('   ✅ Default configuration inserted');
    } catch (error) {
      console.log('   ⚠️  Could not insert default data: ' + error.message);
    }

    // Verify the schema
    console.log('\n📊 Verifying database schema...');
    const verifyQuery = `
      SELECT table_name, 
             (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = t.table_name) as column_count
      FROM information_schema.tables t
      WHERE table_schema = 'public' 
        AND table_type = 'BASE TABLE'
      ORDER BY table_name;
    `;
    
    const result = await client.query(verifyQuery);
    console.log('\n✅ Database tables:');
    result.rows.forEach(row => {
      console.log(`   - ${row.table_name}: ${row.column_count} columns`);
    });

    console.log('\n🎉 Database schema fix completed successfully!');
    console.log('\n💡 Next steps:');
    console.log('   1. Stop the current bot process if running');
    console.log('   2. Run "npm run build" to rebuild the application');
    console.log('   3. Run "npm run start" to start the Discord bot');
    console.log('   4. Check logs for any remaining issues');

  } catch (error) {
    console.error('❌ Error during migration:', error);
    throw error;
  } finally {
    await client.end();
  }
}

// Run the migration
runMigration().catch(error => {
  console.error('Migration failed:', error);
  process.exit(1);
});
