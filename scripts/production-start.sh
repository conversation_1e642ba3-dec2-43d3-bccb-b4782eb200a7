#!/bin/bash

# Production Startup Script for Discord Bot
# This script handles startup issues and provides better error reporting

set -euo pipefail

# Colors for logging
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1"
}

warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

# Function to check environment variables
check_environment() {
    log "Checking environment variables..."
    
    local missing_vars=()
    local required_vars=(
        "DATABASE_URL"
        "NODE_ENV"
        "PORT"
    )
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            missing_vars+=("$var")
        fi
    done
    
    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        error "Missing required environment variables: ${missing_vars[*]}"
        error "Please check your deployment environment configuration"
        return 1
    fi
    
    success "Environment variables check passed"
    return 0
}

# Function to check database connectivity
check_database() {
    log "Checking database connectivity..."
    
    if [[ -z "${DATABASE_URL:-}" ]]; then
        error "DATABASE_URL not set"
        return 1
    fi
    
    # Simple connection test using node
    if node -e "
        const { Client } = require('pg');
        const client = new Client({ connectionString: process.env.DATABASE_URL });
        client.connect()
            .then(() => {
                console.log('Database connection successful');
                client.end();
                process.exit(0);
            })
            .catch(err => {
                console.error('Database connection failed:', err.message);
                process.exit(1);
            });
    " 2>/dev/null; then
        success "Database connectivity check passed"
        return 0
    else
        warning "Database connectivity check failed (will retry during startup)"
        return 0  # Don't fail startup, let the app handle retries
    fi
}

# Function to validate Discord token (optional)
check_discord_token() {
    log "Checking Discord configuration..."

    if [[ -z "${DISCORD_TOKEN:-}" ]]; then
        warning "DISCORD_TOKEN not set - running in OAuth-only mode"
        warning "Discord bot functionality will be disabled"
        return 0
    fi

    # Basic token format validation
    if [[ ! "$DISCORD_TOKEN" =~ ^[A-Za-z0-9._-]+$ ]]; then
        error "DISCORD_TOKEN appears to have invalid format"
        return 1
    fi

    success "Discord token format validation passed - bot mode enabled"
    return 0
}

# Function to check if build exists
check_build() {
    log "Checking if application is built..."
    
    if [[ ! -d "dist" ]]; then
        error "Build directory 'dist' not found"
        error "Make sure 'pnpm run build' was executed successfully"
        return 1
    fi
    
    if [[ ! -f "dist/src/main.js" ]]; then
        error "Main application file 'dist/src/main.js' not found"
        error "Build appears to be incomplete"
        return 1
    fi
    
    success "Build validation passed"
    return 0
}

# Function to check if port is available
check_port() {
    local port=${PORT:-8080}
    log "Checking if port $port is available..."

    if command -v netstat >/dev/null 2>&1; then
        if netstat -tuln | grep -q ":$port "; then
            error "Port $port is already in use"
            return 1
        fi
    elif command -v ss >/dev/null 2>&1; then
        if ss -tuln | grep -q ":$port "; then
            error "Port $port is already in use"
            return 1
        fi
    fi

    success "Port $port is available"
    return 0
}

# Function to wait for application to be ready
wait_for_ready() {
    local port=${PORT:-8080}
    local max_wait=60
    local wait_time=0

    log "Waiting for application to be ready on port $port..."

    while [[ $wait_time -lt $max_wait ]]; do
        if curl -f "http://localhost:$port/api/health/ready" >/dev/null 2>&1; then
            success "Application is ready and responding"
            return 0
        fi

        sleep 2
        ((wait_time += 2))

        if [[ $((wait_time % 10)) -eq 0 ]]; then
            log "Still waiting... ($wait_time/${max_wait}s)"
        fi
    done

    error "Application did not become ready within $max_wait seconds"
    return 1
}

# Function to start the application with retries
start_application() {
    local max_retries=3
    local retry_count=0

    # Check port availability first
    if ! check_port; then
        error "Cannot start application - port conflict"
        return 1
    fi

    while [[ $retry_count -lt $max_retries ]]; do
        log "Starting Discord bot application (attempt $((retry_count + 1))/$max_retries)..."

        # Start the application in background with better error handling
        node dist/src/main.js 2>&1 &
        local app_pid=$!

        # Wait a moment for startup
        sleep 5

        # Check if process is still running
        if ! ps -p $app_pid > /dev/null 2>&1; then
            error "Application process died immediately"
            # Try to get error output
            wait $app_pid 2>/dev/null || error "Exit code: $?"
            ((retry_count++))
            continue
        fi

        # Wait for application to be ready
        if wait_for_ready; then
            success "Application started successfully (PID: $app_pid)"

            # Wait for the process to complete (blocking)
            wait $app_pid
            local exit_code=$?

            if [[ $exit_code -eq 0 ]]; then
                success "Application completed successfully"
                return 0
            else
                error "Application exited with code: $exit_code"
            fi
        else
            error "Application failed to become ready"
            kill $app_pid 2>/dev/null || true
        fi

        ((retry_count++))

        if [[ $retry_count -lt $max_retries ]]; then
            warning "Retrying in 10 seconds..."
            sleep 10
        else
            error "Maximum retry attempts reached. Application startup failed."
            return 1
        fi
    done
}

# Function to show system information
show_system_info() {
    log "System Information:"
    echo "  Node.js version: $(node --version)"
    echo "  npm version: $(npm --version)"
    echo "  pnpm version: $(pnpm --version 2>/dev/null || echo 'not available')"
    echo "  Working directory: $(pwd)"
    echo "  User: $(whoami)"
    echo "  Environment: ${NODE_ENV:-'not set'}"
    echo "  Port: ${PORT:-'not set'}"
    echo ""
}

# Main startup sequence
main() {
    log "🚀 Starting Discord Bot Production Deployment"
    echo ""
    
    # Show system information
    show_system_info
    
    # Run all checks
    log "Running pre-startup checks..."
    
    if ! check_environment; then
        error "Environment check failed"
        exit 1
    fi
    
    if ! check_discord_token; then
        error "Discord token validation failed"
        exit 1
    fi
    
    if ! check_build; then
        error "Build validation failed"
        exit 1
    fi
    
    # Optional database check (non-blocking)
    check_database || true
    
    success "All pre-startup checks passed"
    echo ""
    
    # Start the application
    log "🤖 Launching Discord bot..."
    start_application
}

# Handle script termination
trap 'error "Production startup script terminated unexpectedly"' EXIT

# Run main function
main "$@"

# Remove trap on successful completion
trap - EXIT
success "Production startup completed successfully"
