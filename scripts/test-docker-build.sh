#!/bin/bash

# Docker Build Test Runner
# Comprehensive script to validate Docker build process and container functionality

set -euo pipefail

# Colors for logging
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
IMAGE_NAME="discord-bot-test"
CONTAINER_NAME="discord-bot-test-runner"
TEST_PORT="19080"
BUILD_TIMEOUT=300
STARTUP_TIMEOUT=60

# Logging functions
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1"
}

warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

# Cleanup function
cleanup() {
    log "Cleaning up test resources..."
    
    # Stop and remove container
    docker stop $CONTAINER_NAME 2>/dev/null || true
    docker rm $CONTAINER_NAME 2>/dev/null || true
    
    # Remove test images
    docker rmi $IMAGE_NAME 2>/dev/null || true
    docker rmi ${IMAGE_NAME}-builder 2>/dev/null || true
    docker rmi ${IMAGE_NAME}-cache-test 2>/dev/null || true
    
    # Clean up dangling images
    docker image prune -f 2>/dev/null || true
    
    success "Cleanup completed"
}

# Trap to ensure cleanup on exit
trap cleanup EXIT

# Function to check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if Docker is installed and running
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        error "Docker daemon is not running"
        exit 1
    fi
    
    # Check if required files exist
    if [[ ! -f "Dockerfile" ]]; then
        error "Dockerfile not found in current directory"
        exit 1
    fi
    
    if [[ ! -f "package.json" ]]; then
        error "package.json not found in current directory"
        exit 1
    fi
    
    success "Prerequisites check passed"
}

# Function to test Dockerfile syntax and structure
test_dockerfile_structure() {
    log "Testing Dockerfile structure..."
    
    local dockerfile_content=$(cat Dockerfile)
    
    # Check for multi-stage build
    if ! echo "$dockerfile_content" | grep -q "FROM.*AS builder"; then
        error "Dockerfile missing builder stage"
        return 1
    fi
    
    if ! echo "$dockerfile_content" | grep -q "FROM.*AS runner"; then
        error "Dockerfile missing runner stage"
        return 1
    fi
    
    # Check for required commands
    local required_commands=("WORKDIR" "COPY" "RUN" "EXPOSE" "CMD" "HEALTHCHECK")
    for cmd in "${required_commands[@]}"; do
        if ! echo "$dockerfile_content" | grep -q "^$cmd"; then
            error "Dockerfile missing required command: $cmd"
            return 1
        fi
    done
    
    # Check for security best practices
    if ! echo "$dockerfile_content" | grep -q "USER.*nestjs"; then
        warning "Dockerfile doesn't specify non-root user"
    fi
    
    success "Dockerfile structure validation passed"
}

# Function to test Docker build process
test_docker_build() {
    log "Testing Docker build process..."
    
    local start_time=$(date +%s)
    
    # Build the image with timeout
    if timeout $BUILD_TIMEOUT docker build -t $IMAGE_NAME . 2>&1 | tee build.log; then
        local end_time=$(date +%s)
        local build_time=$((end_time - start_time))
        success "Docker build completed in ${build_time} seconds"
    else
        error "Docker build failed or timed out"
        cat build.log
        return 1
    fi
    
    # Check if image was created
    if ! docker images | grep -q $IMAGE_NAME; then
        error "Docker image was not created"
        return 1
    fi
    
    success "Docker build test passed"
}

# Function to test multi-stage build efficiency
test_multistage_build() {
    log "Testing multi-stage build efficiency..."
    
    # Build only the builder stage
    if docker build --target builder -t ${IMAGE_NAME}-builder . &> /dev/null; then
        success "Builder stage builds successfully"
    else
        error "Builder stage build failed"
        return 1
    fi
    
    # Check if build artifacts exist in builder stage
    if docker run --rm ${IMAGE_NAME}-builder ls dist/src/main.js &> /dev/null; then
        success "Build artifacts present in builder stage"
    else
        error "Build artifacts missing in builder stage"
        return 1
    fi
    
    # Compare image sizes
    local builder_size=$(docker images ${IMAGE_NAME}-builder --format "{{.Size}}")
    local final_size=$(docker images $IMAGE_NAME --format "{{.Size}}")
    
    log "Builder stage size: $builder_size"
    log "Final image size: $final_size"
    
    success "Multi-stage build test passed"
}

# Function to test image metadata
test_image_metadata() {
    log "Testing image metadata..."
    
    local image_data=$(docker inspect $IMAGE_NAME)
    
    # Check exposed ports
    if echo "$image_data" | jq -r '.[0].Config.ExposedPorts' | grep -q "8080/tcp"; then
        success "Correct port exposed"
    else
        error "Port 8080 not exposed"
        return 1
    fi
    
    # Check environment variables
    local env_vars=$(echo "$image_data" | jq -r '.[0].Config.Env[]')
    
    if echo "$env_vars" | grep -q "NODE_ENV=production"; then
        success "NODE_ENV set correctly"
    else
        error "NODE_ENV not set to production"
        return 1
    fi
    
    if echo "$env_vars" | grep -q "PORT=8080"; then
        success "PORT set correctly"
    else
        error "PORT not set correctly"
        return 1
    fi
    
    # Check user
    local user=$(echo "$image_data" | jq -r '.[0].Config.User')
    if [[ "$user" == "nestjs" ]]; then
        success "Non-root user set correctly"
    else
        error "User not set to nestjs"
        return 1
    fi
    
    success "Image metadata test passed"
}

# Function to test container startup
test_container_startup() {
    log "Testing container startup..."
    
    # Create container with minimal config
    docker create --name $CONTAINER_NAME \
        -p $TEST_PORT:8080 \
        -e NODE_ENV=production \
        -e PORT=8080 \
        -e DATABASE_URL=postgresql://test:test@localhost:5432/test \
        $IMAGE_NAME
    
    # Start container
    docker start $CONTAINER_NAME
    
    # Wait for startup and check status
    local wait_time=0
    while [[ $wait_time -lt $STARTUP_TIMEOUT ]]; do
        local status=$(docker ps --filter "name=$CONTAINER_NAME" --format "{{.Status}}")
        
        if [[ "$status" == *"Up"* ]]; then
            success "Container started successfully"
            return 0
        elif [[ "$status" == *"Exited"* ]]; then
            # Check exit code
            local exit_code=$(docker inspect $CONTAINER_NAME --format='{{.State.ExitCode}}')
            if [[ "$exit_code" == "0" ]]; then
                success "Container started and exited gracefully (expected without Discord token)"
                return 0
            else
                warning "Container exited with code $exit_code (may be expected)"
                # Show logs for debugging
                log "Container logs:"
                docker logs $CONTAINER_NAME | tail -20
                return 0  # Don't fail - exit might be expected
            fi
        fi
        
        sleep 2
        ((wait_time += 2))
    done
    
    error "Container startup test timed out"
    docker logs $CONTAINER_NAME
    return 1
}

# Function to test health check
test_health_check() {
    log "Testing health check configuration..."
    
    local health_config=$(docker inspect $IMAGE_NAME --format='{{json .Config.Healthcheck}}')
    
    if [[ "$health_config" != "null" && "$health_config" != "{}" ]]; then
        success "Health check configured"
        
        # Check health check command
        local health_cmd=$(echo "$health_config" | jq -r '.Test[]' | grep -v "CMD-SHELL")
        if echo "$health_cmd" | grep -q "curl.*health"; then
            success "Health check command is correct"
        else
            warning "Health check command may be incorrect: $health_cmd"
        fi
    else
        error "Health check not configured"
        return 1
    fi
    
    success "Health check test passed"
}

# Function to test build cache efficiency
test_build_cache() {
    log "Testing build cache efficiency..."
    
    local start_time=$(date +%s)
    
    # Second build should be much faster due to caching
    if docker build -t ${IMAGE_NAME}-cache-test . &> /dev/null; then
        local end_time=$(date +%s)
        local cache_build_time=$((end_time - start_time))
        
        log "Cached build completed in ${cache_build_time} seconds"
        
        # Cached build should be significantly faster
        if [[ $cache_build_time -lt 60 ]]; then
            success "Build cache is working efficiently"
        else
            warning "Cached build took longer than expected"
        fi
    else
        error "Cached build failed"
        return 1
    fi
    
    success "Build cache test passed"
}

# Function to test security aspects
test_security() {
    log "Testing security aspects..."
    
    # Check if container runs as non-root
    local user_info=$(docker run --rm $IMAGE_NAME id 2>/dev/null || echo "Container failed to start")
    
    if echo "$user_info" | grep -q "uid=1001"; then
        success "Container runs as non-root user"
    else
        warning "Container user check failed or container didn't start"
    fi
    
    # Check for sensitive files
    local sensitive_files=$(docker run --rm $IMAGE_NAME find /app -name "*.env*" -o -name "*.key" -o -name "*.pem" 2>/dev/null || true)
    
    if [[ -z "$sensitive_files" ]]; then
        success "No sensitive files found in image"
    else
        warning "Potential sensitive files found: $sensitive_files"
    fi
    
    success "Security test passed"
}

# Function to run Node.js specific tests
test_nodejs_setup() {
    log "Testing Node.js setup..."
    
    # Check Node.js version
    local node_version=$(docker run --rm $IMAGE_NAME node --version 2>/dev/null || echo "Failed")
    if [[ "$node_version" == *"v18"* ]]; then
        success "Correct Node.js version: $node_version"
    else
        error "Incorrect or missing Node.js version: $node_version"
        return 1
    fi
    
    # Check pnpm installation
    local pnpm_version=$(docker run --rm $IMAGE_NAME pnpm --version 2>/dev/null || echo "Failed")
    if [[ "$pnpm_version" != "Failed" ]]; then
        success "pnpm installed: $pnpm_version"
    else
        error "pnpm not installed properly"
        return 1
    fi
    
    # Check if production dependencies are installed
    local deps_check=$(docker run --rm $IMAGE_NAME ls /app/node_modules 2>/dev/null | wc -l || echo "0")
    if [[ $deps_check -gt 10 ]]; then
        success "Production dependencies installed"
    else
        error "Production dependencies missing or insufficient"
        return 1
    fi
    
    success "Node.js setup test passed"
}

# Main test runner
main() {
    log "🚀 Starting Docker Build Test Suite"
    echo ""
    
    # Show system information
    log "System Information:"
    echo "  Docker version: $(docker --version)"
    echo "  Build context: $(pwd)"
    echo "  Test image: $IMAGE_NAME"
    echo "  Test container: $CONTAINER_NAME"
    echo ""
    
    local failed_tests=()
    
    # Run all tests
    local tests=(
        "check_prerequisites"
        "test_dockerfile_structure" 
        "test_docker_build"
        "test_multistage_build"
        "test_image_metadata"
        "test_nodejs_setup"
        "test_container_startup"
        "test_health_check"
        "test_security"
        "test_build_cache"
    )
    
    for test in "${tests[@]}"; do
        log "Running test: $test"
        
        if $test; then
            success "✅ $test passed"
        else
            error "❌ $test failed"
            failed_tests+=("$test")
        fi
        
        echo ""
    done
    
    # Summary
    log "🎯 Test Summary"
    echo ""
    
    if [[ ${#failed_tests[@]} -eq 0 ]]; then
        success "🎉 All tests passed!"
        echo ""
        success "Docker build is production ready!"
        exit 0
    else
        error "❌ ${#failed_tests[@]} test(s) failed:"
        for test in "${failed_tests[@]}"; do
            echo "  - $test"
        done
        echo ""
        error "Please fix the issues before deploying to production"
        exit 1
    fi
}

# Run main function with all arguments
main "$@"