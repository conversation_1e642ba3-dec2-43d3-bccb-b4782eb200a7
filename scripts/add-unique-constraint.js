require('dotenv').config();
const { Client } = require('pg');

const client = new Client({
  connectionString: process.env.DATABASE_URL,
  ssl: { rejectUnauthorized: false }
});

(async () => {
  try {
    await client.connect();
    console.log('🔌 Connected to database');
    
    // Add unique constraint for ON CONFLICT
    const constraintQuery = `
      ALTER TABLE panel_deployments 
      ADD CONSTRAINT panel_deployments_channel_panel_unique 
      UNIQUE (channel_id, panel_id)
    `;
    
    try {
      await client.query(constraintQuery);
      console.log('✅ Added unique constraint on (channel_id, panel_id)');
    } catch (error) {
      if (error.code === '42P07') {
        console.log('⚠️  Unique constraint already exists');
      } else {
        console.log(`⚠️  Failed to add constraint: ${error.message}`);
      }
    }
    
    // Also create a simpler version without ON CONFLICT for now
    console.log('\n🔧 Testing simple insert...');
    
    try {
      await client.query(`
        INSERT INTO panel_deployments (
          channel_id, guild_id, panel_id, deployment_status, 
          deployment_config, is_active, last_update_at,
          status, config
        ) VALUES ($1, $2, $3, $4, $5, $6, NOW(), $4, $5)
      `, [
        'test_channel',
        'test_guild',
        'test_panel',
        'active',
        JSON.stringify({ test: true }),
        true
      ]);
      console.log('✅ Test insert successful');
      
      // Clean up test record
      await client.query("DELETE FROM panel_deployments WHERE channel_id = 'test_channel'");
      console.log('✅ Test record cleaned up');
      
    } catch (error) {
      console.log(`❌ Test insert failed: ${error.message}`);
    }
    
    console.log('\n🎉 Constraint setup completed!');
    
  } catch (error) {
    console.error('❌ Operation failed:', error.message);
  } finally {
    await client.end();
  }
})();