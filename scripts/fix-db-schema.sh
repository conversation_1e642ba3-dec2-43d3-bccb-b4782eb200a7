#!/bin/bash

# Database Schema Fix Script
# This script fixes the missing database schema issues mentioned in the error logs

set -e

echo "🔧 Starting database schema fix..."

# Check if .env exists
if [ ! -f .env ]; then
    echo "❌ .env file not found!"
    exit 1
fi

# Source the .env file
source .env

# Check if DATABASE_URL is set
if [ -z "$DATABASE_URL" ]; then
    echo "❌ DATABASE_URL not found in .env"
    exit 1
fi

echo "📋 Connecting to database..."

# Create a temporary SQL file
cat > /tmp/fix_schema.sql << 'EOF'
-- Fix missing schema issues for Discord bot
-- This addresses errors from the logs:

-- 1. Ensure ai_agent_config table exists
CREATE TABLE IF NOT EXISTS "ai_agent_config" (
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"deleted_at" timestamp with time zone,
	"guild_id" text NOT NULL,
	"agent_type" varchar(50) NOT NULL,
	"enabled" boolean DEFAULT true NOT NULL,
	"configuration" jsonb,
	"default_channel_id" text,
	"log_channel_id" text,
	"permissions" jsonb,
	"settings" jsonb,
	"last_used_at" timestamp with time zone
);

-- 2. Ensure agent_memory table exists with all required columns
CREATE TABLE IF NOT EXISTS "agent_memory" (
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"deleted_at" timestamp with time zone,
	"user_id" text NOT NULL,
	"memory_type" varchar(50) NOT NULL,
	"key" varchar(200) NOT NULL,
	"value" jsonb NOT NULL,
	"context" text,
	"importance" integer DEFAULT 1 NOT NULL,
	"access_count" integer DEFAULT 0 NOT NULL,
	"last_accessed_at" timestamp with time zone,
	"expires_at" timestamp with time zone,
	"tags" text[],
	"metadata" jsonb
);

-- 3. Ensure agent_interactions table exists with all required columns
CREATE TABLE IF NOT EXISTS "agent_interactions" (
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"deleted_at" timestamp with time zone,
	"user_id" text NOT NULL,
	"agent_type" varchar(50) NOT NULL,
	"interaction_type" varchar(50) NOT NULL,
	"content" text NOT NULL,
	"response" text,
	"status" varchar(20) DEFAULT 'completed' NOT NULL,
	"context" jsonb,
	"metadata" jsonb,
	"channel_id" text,
	"message_id" text,
	"guild_id" text,
	"sentiment_score" integer DEFAULT 0,
	"tags" text[]
);

-- 4. Ensure users table exists with proper structure
CREATE TABLE IF NOT EXISTS "users" (
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"deleted_at" timestamp with time zone,
	"discord_id" varchar(50) NOT NULL UNIQUE,
	"username" varchar(100) NOT NULL,
	"email" varchar(255),
	"is_active" boolean DEFAULT true NOT NULL,
	"last_activity_at" timestamp with time zone,
	"preferences" jsonb,
	"profile" jsonb,
	"access_token" text,
	"refresh_token" text,
	"token_expires_at" timestamp with time zone,
	"experience" integer DEFAULT 0 NOT NULL,
	"balance" integer DEFAULT 0 NOT NULL,
	"user_id" varchar(50),
	"discriminator" varchar(10),
	"avatar_url" text
);

-- Add missing columns to existing tables safely
DO $$
BEGIN
    -- Add user_id to agent_memory if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agent_memory' AND column_name = 'user_id') THEN
        ALTER TABLE "agent_memory" ADD COLUMN "user_id" text NOT NULL DEFAULT '';
    END IF;
    
    -- Add updated_at to agent_interactions if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'agent_interactions' AND column_name = 'updated_at') THEN
        ALTER TABLE "agent_interactions" ADD COLUMN "updated_at" timestamp with time zone DEFAULT now() NOT NULL;
    END IF;
    
    -- Ensure discord_id has unique constraint
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE table_name = 'users' AND constraint_name = 'users_discord_id_unique') THEN
        ALTER TABLE "users" ADD CONSTRAINT "users_discord_id_unique" UNIQUE ("discord_id");
    END IF;
END
$$;

-- Create essential indexes
CREATE INDEX IF NOT EXISTS "ai_agent_config_guild_id_idx" ON "ai_agent_config" USING btree ("guild_id");
CREATE INDEX IF NOT EXISTS "ai_agent_config_agent_type_idx" ON "ai_agent_config" USING btree ("agent_type");
CREATE INDEX IF NOT EXISTS "agent_memory_user_id_idx" ON "agent_memory" USING btree ("user_id");
CREATE INDEX IF NOT EXISTS "agent_memory_memory_type_idx" ON "agent_memory" USING btree ("memory_type");
CREATE INDEX IF NOT EXISTS "agent_interactions_user_id_idx" ON "agent_interactions" USING btree ("user_id");
CREATE INDEX IF NOT EXISTS "agent_interactions_agent_type_idx" ON "agent_interactions" USING btree ("agent_type");
CREATE INDEX IF NOT EXISTS "users_discord_id_idx" ON "users" USING btree ("discord_id");

-- Insert default configuration for the guild mentioned in logs to prevent errors
INSERT INTO "ai_agent_config" (guild_id, agent_type, enabled, configuration, settings) 
VALUES 
('1394355426941730856', 'general', true, '{"channels":["general"],"personality":{"tone":"friendly","style":"helpful"}}', '{"maxResponsesPerHour":50}')
ON CONFLICT DO NOTHING;

-- Verify the fix by checking table counts
echo "📊 Database Schema Verification:";
SELECT 'ai_agent_config' as table_name, COUNT(*) as row_count FROM ai_agent_config
UNION ALL
SELECT 'agent_memory' as table_name, COUNT(*) as row_count FROM agent_memory
UNION ALL
SELECT 'agent_interactions' as table_name, COUNT(*) as row_count FROM agent_interactions
UNION ALL
SELECT 'users' as table_name, COUNT(*) as row_count FROM users;
EOF

echo "🗄️ Running database schema fixes..."

# Execute the SQL file using psql
if command -v psql &> /dev/null; then
    # Extract connection parameters from DATABASE_URL
    PROTOCOL="${DATABASE_URL%%://*}"
    REMAINDER="${DATABASE_URL#*://}"
    USER_PASS_HOST_PORT_DB="${REMAINDER%%?*}"
    
    USER="${USER_PASS_HOST_PORT_DB%%:*}"
    PASS_HOST_PORT_DB="${USER_PASS_HOST_PORT_DB#*:}"
    PASS="${PASS_HOST_PORT_DB%%@*}"
    HOST_PORT_DB="${PASS_HOST_PORT_DB#*@}"
    
    HOST="${HOST_PORT_DB%%:*}"
    PORT_DB="${HOST_PORT_DB#*:}"
    PORT="${PORT_DB%%/*}"
    DB="${PORT_DB#*/}"
    
    # Run the SQL commands
    PGPASSWORD="${PASS}" psql -h "${HOST}" -p "${PORT}" -U "${USER}" -d "${DB}" -f /tmp/fix_schema.sql
    
    echo "✅ Database schema fix completed successfully!"
    echo ""
    echo "🎉 The following issues should now be resolved:"
    echo "   - Missing 'ai_agent_config' table"
    echo "   - Missing 'user_id' column in 'agent_memory' table"
    echo "   - Missing 'updated_at' column in 'agent_interactions' table"
    echo "   - Users table ID constraint issues"
    echo ""
    echo "💡 You can now restart your Discord bot to verify the fixes."
    
else
    echo "❌ PostgreSQL client (psql) not found!"
    echo "Please install PostgreSQL client or run the SQL manually:"
    echo "cat /tmp/fix_schema.sql | psql $DATABASE_URL"
    exit 1
fi

# Clean up
rm -f /tmp/fix_schema.sql
echo "🧹 Cleanup completed"