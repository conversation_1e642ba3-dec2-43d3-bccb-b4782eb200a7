#!/bin/bash

# Deployment Debugging Script
# Use this to diagnose deployment issues

set -euo pipefail

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

success() {
    echo -e "${GREEN}[OK]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# Function to check environment
check_env() {
    log "=== Environment Check ==="
    
    echo "Node.js: $(node --version)"
    echo "npm: $(npm --version)"
    echo "pnpm: $(pnpm --version 2>/dev/null || echo 'not available')"
    echo "Working Directory: $(pwd)"
    echo "User: $(whoami)"
    echo ""
    
    # Check environment variables
    log "Environment Variables:"
    local env_vars=("NODE_ENV" "PORT" "DISCORD_TOKEN" "DATABASE_URL")
    
    for var in "${env_vars[@]}"; do
        if [[ -n "${!var:-}" ]]; then
            if [[ "$var" == "DISCORD_TOKEN" || "$var" == "DATABASE_URL" ]]; then
                echo "  $var: [REDACTED - ${#!var} characters]"
            else
                echo "  $var: ${!var}"
            fi
        else
            error "  $var: NOT SET"
        fi
    done
    echo ""
}

# Function to check build
check_build() {
    log "=== Build Check ==="
    
    if [[ -d "dist" ]]; then
        success "dist directory exists"
        
        if [[ -f "dist/src/main.js" ]]; then
            success "main.js exists"
            local size=$(stat -c%s "dist/src/main.js" 2>/dev/null || echo "unknown")
            echo "  Size: $size bytes"
        else
            error "main.js missing"
        fi
        
        # Check for other important files
        local files=("dist/src/app.module.js" "dist/src/discord" "dist/src/api")
        for file in "${files[@]}"; do
            if [[ -e "$file" ]]; then
                success "$file exists"
            else
                warning "$file missing"
            fi
        done
    else
        error "dist directory missing - build required"
    fi
    echo ""
}

# Function to check dependencies
check_deps() {
    log "=== Dependencies Check ==="
    
    if [[ -d "node_modules" ]]; then
        success "node_modules exists"
        
        # Check critical dependencies
        local deps=("@nestjs/core" "discord.js" "necord" "drizzle-orm")
        for dep in "${deps[@]}"; do
            if [[ -d "node_modules/$dep" ]]; then
                success "$dep installed"
            else
                error "$dep missing"
            fi
        done
    else
        error "node_modules missing - install required"
    fi
    echo ""
}

# Function to test database connection
test_database() {
    log "=== Database Connection Test ==="
    
    if [[ -z "${DATABASE_URL:-}" ]]; then
        error "DATABASE_URL not set"
        return 1
    fi
    
    # Test database connection
    if node -e "
        const { Client } = require('pg');
        const client = new Client({ connectionString: process.env.DATABASE_URL });
        client.connect()
            .then(() => {
                console.log('✅ Database connection successful');
                return client.query('SELECT NOW()');
            })
            .then(result => {
                console.log('✅ Database query successful:', result.rows[0].now);
                client.end();
            })
            .catch(err => {
                console.error('❌ Database error:', err.message);
                process.exit(1);
            });
    " 2>/dev/null; then
        success "Database connection test passed"
    else
        error "Database connection test failed"
    fi
    echo ""
}

# Function to test Discord token
test_discord() {
    log "=== Discord Token Test ==="
    
    if [[ -z "${DISCORD_TOKEN:-}" ]]; then
        error "DISCORD_TOKEN not set"
        return 1
    fi
    
    # Basic token format check
    if [[ "$DISCORD_TOKEN" =~ ^[A-Za-z0-9._-]+$ ]]; then
        success "Token format appears valid"
    else
        error "Token format appears invalid"
    fi
    
    # Test token by creating a simple Discord client
    if node -e "
        const { Client, GatewayIntentBits } = require('discord.js');
        const client = new Client({ intents: [GatewayIntentBits.Guilds] });
        
        client.once('ready', () => {
            console.log('✅ Discord token is valid');
            console.log('✅ Bot logged in as:', client.user.tag);
            client.destroy();
        });
        
        client.on('error', (error) => {
            console.error('❌ Discord error:', error.message);
            process.exit(1);
        });
        
        client.login(process.env.DISCORD_TOKEN).catch(err => {
            console.error('❌ Discord login failed:', err.message);
            process.exit(1);
        });
        
        // Timeout after 10 seconds
        setTimeout(() => {
            console.error('❌ Discord login timeout');
            process.exit(1);
        }, 10000);
    " 2>/dev/null; then
        success "Discord token test passed"
    else
        error "Discord token test failed"
    fi
    echo ""
}

# Function to simulate startup
simulate_startup() {
    log "=== Startup Simulation ==="
    
    log "Attempting to start application..."
    
    # Try to start the app with timeout
    timeout 30s node dist/src/main.js &
    local pid=$!
    
    sleep 5
    
    if ps -p $pid > /dev/null 2>&1; then
        success "Application started successfully"
        
        # Test health endpoint
        sleep 2
        if curl -f http://localhost:8080/api/health 2>/dev/null; then
            success "Health endpoint responding"
        else
            warning "Health endpoint not responding"
        fi
        
        # Kill the test process
        kill $pid 2>/dev/null || true
    else
        error "Application failed to start"
    fi
    echo ""
}

# Function to show recent logs
show_logs() {
    log "=== Recent Logs ==="
    
    if [[ -f "logs/discord-bot-energex.log" ]]; then
        echo "Recent application logs:"
        tail -n 20 "logs/discord-bot-energex.log"
    else
        warning "No application logs found"
    fi
    
    if [[ -f "logs/discord-bot-energex.error.log" ]]; then
        echo ""
        echo "Recent error logs:"
        tail -n 10 "logs/discord-bot-energex.error.log"
    else
        warning "No error logs found"
    fi
    echo ""
}

# Main function
main() {
    echo "🔍 Discord Bot Deployment Debugging"
    echo "===================================="
    echo ""
    
    check_env
    check_build
    check_deps
    
    # Only run network tests if basic checks pass
    if [[ -d "dist" && -d "node_modules" ]]; then
        test_database
        test_discord
        simulate_startup
    else
        warning "Skipping network tests due to missing build or dependencies"
    fi
    
    show_logs
    
    echo "🔍 Debugging complete!"
    echo ""
    echo "Common fixes:"
    echo "  - Run 'pnpm install' if dependencies are missing"
    echo "  - Run 'pnpm run build' if build is missing"
    echo "  - Check environment variables in deployment settings"
    echo "  - Verify Discord token and database URL are correct"
}

# Run main function
main "$@"
