[Unit]
Description=Discord Bot EnergeX
After=network.target
Wants=network.target

[Service]
Type=forking
User=%i
Group=%i
WorkingDirectory=/home/<USER>/Discordbot-EnergeX
ExecStart=/home/<USER>/Discordbot-EnergeX/scripts/start-bot.sh start
ExecStop=/home/<USER>/Discordbot-EnergeX/scripts/start-bot.sh stop
ExecReload=/home/<USER>/Discordbot-EnergeX/scripts/start-bot.sh restart
PIDFile=/home/<USER>/Discordbot-EnergeX/logs/discord-bot-energex.pid

# Restart policy
Restart=always
RestartSec=10
StartLimitInterval=60
StartLimitBurst=3

# Environment
Environment=NODE_ENV=production
Environment=PATH=/home/<USER>/.nvm/versions/node/v22.17.0/bin:/usr/local/bin:/usr/bin:/bin
Environment=HOME=/home/<USER>

# Security
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=/home/<USER>/Discordbot-EnergeX

# Logging
StandardOutput=append:/home/<USER>/Discordbot-EnergeX/logs/systemd.log
StandardError=append:/home/<USER>/Discordbot-EnergeX/logs/systemd.error.log

[Install]
WantedBy=multi-user.target
