require('dotenv').config();
const { Client } = require('pg');

const client = new Client({
  connectionString: process.env.DATABASE_URL,
  ssl: { rejectUnauthorized: false }
});

const fixTables = [
  {
    name: 'panel_deployments',
    sql: `CREATE TABLE IF NOT EXISTS panel_deployments (
      id SERIAL PRIMARY KEY,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
      deleted_at TIMESTAMP WITH TIME ZONE,
      channel_id VARCHAR(50) NOT NULL,
      guild_id VARCHAR(50) NOT NULL,
      panel_id VARCHAR(100) NOT NULL,
      message_id VARCHAR(50),
      deployment_status VARCHAR(20) NOT NULL DEFAULT 'active',
      last_update_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
      last_successful_update TIMES<PERSON>MP WITH TIME ZONE,
      panel_version VARCHAR(50) DEFAULT '1.0.0',
      content_hash VARCHAR(64),
      deployment_config JSONB,
      discord_state JSONB,
      deployment_metrics JSONB,
      error_state JSONB,
      is_active BOOLEAN DEFAULT TRUE NOT NULL,
      needs_cleanup BOOLEAN DEFAULT FALSE NOT NULL,
      scheduled_update_at TIMESTAMP WITH TIME ZONE,
      retry_count INTEGER DEFAULT 0 NOT NULL,
      max_retries INTEGER DEFAULT 3 NOT NULL,
      interaction_count INTEGER DEFAULT 0 NOT NULL,
      last_interaction_at TIMESTAMP WITH TIME ZONE,
      metadata JSONB
    )`
  },
  {
    name: 'deployment_logs',
    sql: `CREATE TABLE IF NOT EXISTS deployment_logs (
      id SERIAL PRIMARY KEY,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
      deleted_at TIMESTAMP WITH TIME ZONE,
      deployment_id INTEGER NOT NULL,
      log_level VARCHAR(10) NOT NULL,
      message TEXT NOT NULL,
      context JSONB,
      timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
    )`
  }
];

const alterCommands = [
  // Add deleted_at column to existing tables if they don't have it
  "ALTER TABLE panel_states ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE",
  "ALTER TABLE panel_analytics ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE",
  "ALTER TABLE panel_content ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE",
  "ALTER TABLE user_panel_states ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE",
  "ALTER TABLE ai_tools ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE",
  "ALTER TABLE troubleshooting_guides ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE"
];

const indexes = [
  // Panel deployments indexes
  'CREATE INDEX IF NOT EXISTS panel_deployments_channel_id_idx ON panel_deployments (channel_id)',
  'CREATE INDEX IF NOT EXISTS panel_deployments_guild_id_idx ON panel_deployments (guild_id)',
  'CREATE INDEX IF NOT EXISTS panel_deployments_panel_id_idx ON panel_deployments (panel_id)',
  'CREATE INDEX IF NOT EXISTS panel_deployments_message_id_idx ON panel_deployments (message_id)',
  'CREATE INDEX IF NOT EXISTS panel_deployments_status_idx ON panel_deployments (deployment_status)',
  'CREATE INDEX IF NOT EXISTS panel_deployments_last_update_idx ON panel_deployments (last_update_at)',
  'CREATE INDEX IF NOT EXISTS panel_deployments_active_idx ON panel_deployments (is_active)',
  'CREATE INDEX IF NOT EXISTS panel_deployments_cleanup_idx ON panel_deployments (needs_cleanup)',
  'CREATE INDEX IF NOT EXISTS panel_deployments_scheduled_idx ON panel_deployments (scheduled_update_at)',
  'CREATE INDEX IF NOT EXISTS panel_deployments_channel_panel_unique ON panel_deployments (channel_id, panel_id)',
  'CREATE INDEX IF NOT EXISTS panel_deployments_content_hash_idx ON panel_deployments (content_hash)',
  'CREATE INDEX IF NOT EXISTS panel_deployments_interaction_idx ON panel_deployments (last_interaction_at)',
  
  // Deployment logs indexes
  'CREATE INDEX IF NOT EXISTS deployment_logs_deployment_id_idx ON deployment_logs (deployment_id)',
  'CREATE INDEX IF NOT EXISTS deployment_logs_log_level_idx ON deployment_logs (log_level)',
  'CREATE INDEX IF NOT EXISTS deployment_logs_timestamp_idx ON deployment_logs (timestamp)'
];

(async () => {
  try {
    await client.connect();
    console.log('🔌 Connected to database');
    
    // Create missing tables
    for (const table of fixTables) {
      try {
        console.log(`📄 Creating table: ${table.name}`);
        await client.query(table.sql);
        console.log(`✅ Table created: ${table.name}`);
      } catch (error) {
        if (error.code === '42P07') {
          console.log(`⚠️  Table already exists: ${table.name}`);
        } else {
          console.error(`❌ Failed to create ${table.name}:`, error.message);
        }
      }
    }
    
    // Add missing columns
    console.log('\n🔧 Adding missing columns...');
    for (const alterCommand of alterCommands) {
      try {
        await client.query(alterCommand);
        console.log('✅ Column added/verified');
      } catch (error) {
        console.log(`⚠️  Column operation failed: ${error.message}`);
      }
    }
    
    // Create indexes
    console.log('\n📊 Creating indexes...');
    for (const indexSql of indexes) {
      try {
        await client.query(indexSql);
        console.log('✅ Index created');
      } catch (error) {
        console.log(`⚠️  Index creation failed: ${error.message}`);
      }
    }
    
    console.log('\n🎉 Panel deployment table fixes completed!');
    
  } catch (error) {
    console.error('❌ Database operation failed:', error.message);
  } finally {
    await client.end();
  }
})();