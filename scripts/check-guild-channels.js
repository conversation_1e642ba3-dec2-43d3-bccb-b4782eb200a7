#!/usr/bin/env node

/**
 * Discord Guild Channel Checker Script
 * Fetches and displays all channels from a specific Discord guild
 */

const { Client, GatewayIntentBits, ChannelType } = require('discord.js');
require('dotenv').config();

const GUILD_ID = '1394355426941730856';

// Create Discord client
const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.GuildMembers
  ]
});

// Channel type mapping for better readability
const CHANNEL_TYPE_NAMES = {
  [ChannelType.GuildText]: 'Text Channel',
  [ChannelType.DM]: 'DM',
  [ChannelType.GuildVoice]: 'Voice Channel',
  [ChannelType.GroupDM]: 'Group DM',
  [ChannelType.GuildCategory]: 'Category',
  [ChannelType.GuildAnnouncement]: 'Announcement',
  [ChannelType.AnnouncementThread]: 'Announcement Thread',
  [ChannelType.PublicThread]: 'Public Thread',
  [ChannelType.PrivateThread]: 'Private Thread',
  [ChannelType.GuildStageVoice]: 'Stage Voice',
  [ChannelType.GuildDirectory]: 'Directory',
  [ChannelType.GuildForum]: 'Forum'
};

// Permission flags for readable output
const PERMISSION_FLAGS = {
  'ViewChannel': 'View Channel',
  'SendMessages': 'Send Messages',
  'ManageMessages': 'Manage Messages',
  'ManageChannels': 'Manage Channels',
  'ReadMessageHistory': 'Read Message History',
  'Connect': 'Connect (Voice)',
  'Speak': 'Speak (Voice)',
  'Administrator': 'Administrator'
};

async function checkGuildChannels() {
  try {
    console.log('🤖 Starting Discord Guild Channel Checker...\n');
    
    // Login to Discord
    await client.login(process.env.DISCORD_TOKEN);
    console.log(`✅ Logged in as ${client.user.tag}\n`);
    
    // Fetch the guild
    const guild = await client.guilds.fetch(GUILD_ID);
    console.log(`🏰 Guild: ${guild.name} (ID: ${guild.id})`);
    console.log(`👥 Members: ${guild.memberCount}`);
    console.log(`👑 Owner: ${guild.ownerId}\n`);
    
    // Fetch all channels
    const channels = await guild.channels.fetch();
    console.log(`📁 Total Channels Found: ${channels.size}\n`);
    
    // Organize channels by type
    const channelsByType = new Map();
    const channelsByCategory = new Map();
    
    channels.forEach(channel => {
      if (!channel) return;
      
      const type = CHANNEL_TYPE_NAMES[channel.type] || `Unknown (${channel.type})`;
      
      if (!channelsByType.has(type)) {
        channelsByType.set(type, []);
      }
      channelsByType.get(type).push(channel);
      
      // Group by category for text/voice channels
      if (channel.parent) {
        if (!channelsByCategory.has(channel.parent.name)) {
          channelsByCategory.set(channel.parent.name, []);
        }
        channelsByCategory.get(channel.parent.name).push(channel);
      } else if (channel.type !== ChannelType.GuildCategory) {
        if (!channelsByCategory.has('🔸 No Category')) {
          channelsByCategory.set('🔸 No Category', []);
        }
        channelsByCategory.get('🔸 No Category').push(channel);
      }
    });
    
    // Display channel statistics
    console.log('📊 CHANNEL STATISTICS:');
    console.log('=' .repeat(50));
    channelsByType.forEach((channelList, type) => {
      console.log(`${type}: ${channelList.length}`);
    });
    console.log('');
    
    // Display all channels organized by category
    console.log('📋 CHANNELS BY CATEGORY:');
    console.log('=' .repeat(50));
    
    // First show categories and their channels
    const categories = channels.filter(c => c?.type === ChannelType.GuildCategory);
    categories.forEach(category => {
      console.log(`\n📁 ${category.name} (Category)`);
      console.log(`   ID: ${category.id}`);
      console.log(`   Position: ${category.position}`);
      
      const categoryChannels = channels.filter(c => c?.parent?.id === category.id);
      categoryChannels
        .sort((a, b) => a.position - b.position)
        .forEach(channel => {
          const typeEmoji = getChannelEmoji(channel.type);
          console.log(`   ${typeEmoji} ${channel.name}`);
          console.log(`      ID: ${channel.id}`);
          console.log(`      Type: ${CHANNEL_TYPE_NAMES[channel.type]}`);
          console.log(`      Position: ${channel.position}`);
          
          if (channel.topic) {
            console.log(`      Topic: ${channel.topic.substring(0, 100)}${channel.topic.length > 100 ? '...' : ''}`);
          }
          
          if (channel.type === ChannelType.GuildVoice || channel.type === ChannelType.GuildStageVoice) {
            console.log(`      User Limit: ${channel.userLimit || 'Unlimited'}`);
            if (channel.bitrate) {
              console.log(`      Bitrate: ${channel.bitrate}bps`);
            }
          }
          
          if (channel.type === ChannelType.GuildText || channel.type === ChannelType.GuildAnnouncement) {
            console.log(`      NSFW: ${channel.nsfw ? 'Yes' : 'No'}`);
            if (channel.rateLimitPerUser) {
              console.log(`      Slowmode: ${channel.rateLimitPerUser}s`);
            }
          }
          
          console.log('');
        });
    });
    
    // Show channels without categories
    const uncategorizedChannels = channels.filter(c => 
      c && !c.parent && c.type !== ChannelType.GuildCategory
    );
    
    if (uncategorizedChannels.size > 0) {
      console.log('\n🔸 UNCATEGORIZED CHANNELS:');
      uncategorizedChannels
        .sort((a, b) => a.position - b.position)
        .forEach(channel => {
          const typeEmoji = getChannelEmoji(channel.type);
          console.log(`${typeEmoji} ${channel.name}`);
          console.log(`   ID: ${channel.id}`);
          console.log(`   Type: ${CHANNEL_TYPE_NAMES[channel.type]}`);
          console.log(`   Position: ${channel.position}`);
          
          if (channel.topic) {
            console.log(`   Topic: ${channel.topic.substring(0, 100)}${channel.topic.length > 100 ? '...' : ''}`);
          }
          console.log('');
        });
    }
    
    // Export channel data to JSON
    const channelData = {
      guild: {
        id: guild.id,
        name: guild.name,
        memberCount: guild.memberCount,
        ownerId: guild.ownerId
      },
      channels: channels.map(channel => {
        if (!channel) return null;
        
        return {
          id: channel.id,
          name: channel.name,
          type: channel.type,
          typeName: CHANNEL_TYPE_NAMES[channel.type],
          position: channel.position,
          parentId: channel.parent?.id || null,
          parentName: channel.parent?.name || null,
          topic: channel.topic || null,
          nsfw: channel.nsfw || false,
          rateLimitPerUser: channel.rateLimitPerUser || 0,
          userLimit: channel.userLimit || null,
          bitrate: channel.bitrate || null,
          createdAt: channel.createdAt?.toISOString(),
          permissions: channel.permissionOverwrites ? 
            channel.permissionOverwrites.cache.map(overwrite => ({
              id: overwrite.id,
              type: overwrite.type,
              allow: overwrite.allow.toArray(),
              deny: overwrite.deny.toArray()
            })) : []
        };
      }).filter(Boolean),
      statistics: {
        totalChannels: channels.size,
        byType: Object.fromEntries(channelsByType.entries().map(([type, list]) => [type, list.length])),
        categories: categories.size,
        uncategorized: uncategorizedChannels.size
      },
      exportedAt: new Date().toISOString()
    };
    
    // Save to file
    const fs = require('fs');
    const path = require('path');
    const outputFile = path.join(__dirname, `guild-${GUILD_ID}-channels.json`);
    
    fs.writeFileSync(outputFile, JSON.stringify(channelData, null, 2));
    console.log(`💾 Channel data exported to: ${outputFile}\n`);
    
    // Display summary
    console.log('📊 SUMMARY:');
    console.log('=' .repeat(30));
    console.log(`Total Channels: ${channels.size}`);
    console.log(`Categories: ${categories.size}`);
    console.log(`Text Channels: ${channelsByType.get('Text Channel')?.length || 0}`);
    console.log(`Voice Channels: ${channelsByType.get('Voice Channel')?.length || 0}`);
    console.log(`Announcement Channels: ${channelsByType.get('Announcement')?.length || 0}`);
    console.log(`Forum Channels: ${channelsByType.get('Forum')?.length || 0}`);
    console.log(`Stage Channels: ${channelsByType.get('Stage Voice')?.length || 0}`);
    
    console.log('\n✅ Channel check completed successfully!');
    
  } catch (error) {
    console.error('❌ Error checking guild channels:', error);
    
    if (error.code === 50001) {
      console.error('🔒 Bot does not have access to this guild. Make sure the bot is invited with proper permissions.');
    } else if (error.code === 10004) {
      console.error('🏰 Guild not found. Please check the guild ID.');
    } else if (error.message.includes('TOKEN_INVALID')) {
      console.error('🔑 Invalid bot token. Please check your DISCORD_TOKEN environment variable.');
    }
  } finally {
    // Cleanup
    client.destroy();
  }
}

function getChannelEmoji(type) {
  switch (type) {
    case ChannelType.GuildText:
      return '💬';
    case ChannelType.GuildVoice:
      return '🔊';
    case ChannelType.GuildCategory:
      return '📁';
    case ChannelType.GuildAnnouncement:
      return '📢';
    case ChannelType.GuildStageVoice:
      return '🎭';
    case ChannelType.GuildForum:
      return '💭';
    case ChannelType.PublicThread:
      return '🧵';
    case ChannelType.PrivateThread:
      return '🔒';
    default:
      return '❓';
  }
}

// Additional utility functions
async function checkChannelPermissions(channel) {
  try {
    const permissions = channel.permissionOverwrites.cache;
    const permissionData = [];
    
    permissions.forEach(overwrite => {
      const target = overwrite.type === 0 ? 'Role' : 'User';
      const allowPerms = overwrite.allow.toArray();
      const denyPerms = overwrite.deny.toArray();
      
      permissionData.push({
        target,
        id: overwrite.id,
        allow: allowPerms,
        deny: denyPerms
      });
    });
    
    return permissionData;
  } catch (error) {
    console.warn(`Could not check permissions for channel ${channel.name}:`, error.message);
    return [];
  }
}

async function getChannelMessages(channel, limit = 10) {
  try {
    if (channel.type !== ChannelType.GuildText && channel.type !== ChannelType.GuildAnnouncement) {
      return [];
    }
    
    const messages = await channel.messages.fetch({ limit });
    return messages.map(msg => ({
      id: msg.id,
      author: msg.author.tag,
      content: msg.content.substring(0, 100) + (msg.content.length > 100 ? '...' : ''),
      createdAt: msg.createdAt.toISOString()
    }));
  } catch (error) {
    console.warn(`Could not fetch messages for channel ${channel.name}:`, error.message);
    return [];
  }
}

// Error handling
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  process.exit(1);
});

// Check if Discord token is provided
if (!process.env.DISCORD_TOKEN) {
  console.error('❌ DISCORD_TOKEN environment variable is required');
  console.log('💡 Please set your Discord bot token in the .env file or environment variables');
  process.exit(1);
}

// Run the script
console.log('🚀 Discord Guild Channel Checker');
console.log('=' .repeat(40));
console.log(`Target Guild ID: ${GUILD_ID}`);
console.log('=' .repeat(40));

checkGuildChannels();