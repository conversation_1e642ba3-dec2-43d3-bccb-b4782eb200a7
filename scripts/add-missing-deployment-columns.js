require('dotenv').config();
const { Client } = require('pg');

const client = new Client({
  connectionString: process.env.DATABASE_URL,
  ssl: { rejectUnauthorized: false }
});

const missingColumns = [
  "ALTER TABLE panel_deployments ADD COLUMN IF NOT EXISTS targets JSONB DEFAULT '[]'",
  "ALTER TABLE panel_deployments ADD COLUMN IF NOT EXISTS started_at TIMESTAMP WITH TIME ZONE",
  "ALTER TABLE panel_deployments ADD COLUMN IF NOT EXISTS completed_at TIMESTAMP WITH TIME ZONE",
  "ALTER TABLE panel_deployments ADD COLUMN IF NOT EXISTS results JSONB DEFAULT '{}'",
  "ALTER TABLE panel_deployments ADD COLUMN IF NOT EXISTS errors JSONB DEFAULT '[]'"
];

(async () => {
  try {
    await client.connect();
    console.log('🔌 Connected to database');
    
    console.log('🔧 Adding missing deployment columns...');
    for (const alterCommand of missingColumns) {
      try {
        await client.query(alterCommand);
        console.log('✅ Column added/verified');
      } catch (error) {
        console.log(`⚠️  Column operation failed: ${error.message}`);
      }
    }
    
    // Update existing records to have proper values
    console.log('\n🔄 Updating existing deployment records...');
    
    const updateQuery = `
      UPDATE panel_deployments 
      SET 
        targets = COALESCE(targets, '[]'::jsonb),
        results = COALESCE(results, '{}'::jsonb),
        errors = COALESCE(errors, '[]'::jsonb),
        started_at = COALESCE(started_at, created_at),
        completed_at = COALESCE(completed_at, last_update_at)
      WHERE targets IS NULL 
        OR results IS NULL 
        OR errors IS NULL 
        OR started_at IS NULL 
        OR completed_at IS NULL
    `;
    
    const updateResult = await client.query(updateQuery);
    console.log(`✅ Updated ${updateResult.rowCount} deployment records`);
    
    console.log('\n🎉 Missing deployment columns added!');
    
  } catch (error) {
    console.error('❌ Operation failed:', error.message);
  } finally {
    await client.end();
  }
})();