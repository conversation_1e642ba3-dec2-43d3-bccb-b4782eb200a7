#!/usr/bin/env node

const { Client } = require('pg');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const DATABASE_URL = process.env.DATABASE_URL;

if (!DATABASE_URL) {
  console.error('❌ DATABASE_URL not found in environment variables');
  process.exit(1);
}

async function runMigration() {
  const client = new Client({
    connectionString: DATABASE_URL,
  });

  try {
    console.log('🔧 Starting comprehensive database schema fix...');
    await client.connect();
    console.log('✅ Connected to database');

    // Create enums first
    console.log('📋 Creating enums...');
    await client.query(`
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'organization_tier') THEN
          CREATE TYPE organization_tier AS ENUM ('free', 'starter', 'professional', 'enterprise', 'custom');
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'organization_status') THEN
          CREATE TYPE organization_status AS ENUM ('active', 'suspended', 'trial', 'cancelled');
        END IF;
      END
      $$;
    `);

    // 1. Create users table
    console.log('📋 Creating/updating users table...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS "users" (
        "id" serial PRIMARY KEY NOT NULL,
        "created_at" timestamp with time zone DEFAULT now() NOT NULL,
        "updated_at" timestamp with time zone DEFAULT now() NOT NULL,
        "deleted_at" timestamp with time zone,
        "discord_id" varchar(50) NOT NULL,
        "username" varchar(100) NOT NULL,
        "email" varchar(255),
        "is_active" boolean DEFAULT true NOT NULL,
        "last_activity_at" timestamp with time zone,
        "preferences" jsonb,
        "profile" jsonb,
        "access_token" text,
        "refresh_token" text,
        "token_expires_at" timestamp with time zone,
        "experience" integer DEFAULT 0 NOT NULL,
        "balance" integer DEFAULT 0 NOT NULL,
        "user_id" varchar(50),
        "discriminator" varchar(10),
        "avatar_url" text
      );
    `);

    // Add unique constraint for discord_id
    await client.query(`
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                       WHERE table_name = 'users' AND constraint_name = 'users_discord_id_unique') THEN
          ALTER TABLE "users" ADD CONSTRAINT "users_discord_id_unique" UNIQUE ("discord_id");
        END IF;
      END
      $$;
    `);

    // 2. Create guilds table
    console.log('📋 Creating/updating guilds table...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS "guilds" (
        "id" serial PRIMARY KEY NOT NULL,
        "created_at" timestamp with time zone DEFAULT now() NOT NULL,
        "updated_at" timestamp with time zone DEFAULT now() NOT NULL,
        "deleted_at" timestamp with time zone,
        "discord_id" varchar(50) NOT NULL,
        "name" varchar(100) NOT NULL,
        "icon" text,
        "is_active" boolean DEFAULT true NOT NULL,
        "settings" jsonb,
        "features" jsonb,
        "last_activity_at" timestamp with time zone,
        "owner_discord_id" varchar(50),
        "welcome_enabled" boolean DEFAULT false NOT NULL,
        "welcome_channel_id" varchar(50),
        "welcome_message" text,
        "welcome_roles" jsonb,
        "starboard_enabled" boolean DEFAULT false NOT NULL,
        "starboard_channel_id" varchar(50),
        "starboard_threshold" integer DEFAULT 3 NOT NULL,
        "guild_id" varchar(50)
      );
    `);

    // Add unique constraint for discord_id
    await client.query(`
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                       WHERE table_name = 'guilds' AND constraint_name = 'guilds_discord_id_unique') THEN
          ALTER TABLE "guilds" ADD CONSTRAINT "guilds_discord_id_unique" UNIQUE ("discord_id");
        END IF;
      END
      $$;
    `);

    // 3. Create sessions table
    console.log('📋 Creating/updating sessions table...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS "sessions" (
        "id" serial PRIMARY KEY NOT NULL,
        "created_at" timestamp with time zone DEFAULT now() NOT NULL,
        "updated_at" timestamp with time zone DEFAULT now() NOT NULL,
        "deleted_at" timestamp with time zone,
        "session_id" varchar(128) NOT NULL,
        "user_id" text NOT NULL,
        "encrypted_data" text,
        "expires_at" timestamp with time zone NOT NULL,
        "ip_address" varchar(45),
        "user_agent" text,
        "device_fingerprint" text,
        "is_revoked" boolean DEFAULT false NOT NULL,
        "last_accessed_at" timestamp with time zone,
        "metadata" jsonb
      );
    `);

    // Add unique constraint for session_id
    await client.query(`
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                       WHERE table_name = 'sessions' AND constraint_name = 'sessions_session_id_unique') THEN
          ALTER TABLE "sessions" ADD CONSTRAINT "sessions_session_id_unique" UNIQUE ("session_id");
        END IF;
      END
      $$;
    `);

    // 4. Create ai_agent_config table
    console.log('📋 Creating/updating ai_agent_config table...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS "ai_agent_config" (
        "id" serial PRIMARY KEY NOT NULL,
        "created_at" timestamp with time zone DEFAULT now() NOT NULL,
        "updated_at" timestamp with time zone DEFAULT now() NOT NULL,
        "deleted_at" timestamp with time zone,
        "guild_id" text NOT NULL,
        "agent_type" varchar(50) NOT NULL,
        "enabled" boolean DEFAULT true NOT NULL,
        "configuration" jsonb,
        "default_channel_id" text,
        "log_channel_id" text,
        "permissions" jsonb,
        "settings" jsonb,
        "last_used_at" timestamp with time zone
      );
    `);

    // 5. Create agent_interactions table
    console.log('📋 Creating/updating agent_interactions table...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS "agent_interactions" (
        "id" serial PRIMARY KEY NOT NULL,
        "created_at" timestamp with time zone DEFAULT now() NOT NULL,
        "updated_at" timestamp with time zone DEFAULT now() NOT NULL,
        "deleted_at" timestamp with time zone,
        "user_id" text NOT NULL,
        "agent_type" varchar(50) NOT NULL,
        "interaction_type" varchar(50) NOT NULL,
        "content" text NOT NULL,
        "response" text,
        "status" varchar(20) DEFAULT 'completed' NOT NULL,
        "context" jsonb,
        "metadata" jsonb,
        "channel_id" text,
        "message_id" text,
        "guild_id" text,
        "sentiment_score" integer DEFAULT 0,
        "tags" text[]
      );
    `);

    // 6. Create agent_memory table
    console.log('📋 Creating/updating agent_memory table...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS "agent_memory" (
        "id" serial PRIMARY KEY NOT NULL,
        "created_at" timestamp with time zone DEFAULT now() NOT NULL,
        "updated_at" timestamp with time zone DEFAULT now() NOT NULL,
        "deleted_at" timestamp with time zone,
        "user_id" text NOT NULL,
        "memory_type" varchar(50) NOT NULL,
        "key" varchar(200) NOT NULL,
        "value" jsonb NOT NULL,
        "context" text,
        "importance" integer DEFAULT 1 NOT NULL,
        "access_count" integer DEFAULT 0 NOT NULL,
        "last_accessed_at" timestamp with time zone,
        "expires_at" timestamp with time zone,
        "tags" text[],
        "metadata" jsonb
      );
    `);

    // 7. Create personal_growth_plans table
    console.log('📋 Creating/updating personal_growth_plans table...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS "personal_growth_plans" (
        "id" serial PRIMARY KEY NOT NULL,
        "created_at" timestamp with time zone DEFAULT now() NOT NULL,
        "updated_at" timestamp with time zone DEFAULT now() NOT NULL,
        "deleted_at" timestamp with time zone,
        "user_id" text NOT NULL,
        "title" varchar(200) NOT NULL,
        "description" text,
        "status" varchar(20) DEFAULT 'active' NOT NULL,
        "goals" jsonb,
        "milestones" jsonb,
        "start_date" timestamp with time zone,
        "target_completion_date" timestamp with time zone,
        "actual_completion_date" timestamp with time zone,
        "progress_percentage" integer DEFAULT 0 NOT NULL,
        "metadata" jsonb
      );
    `);

    // 8. Create user_relationships table
    console.log('📋 Creating/updating user_relationships table...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS "user_relationships" (
        "id" serial PRIMARY KEY NOT NULL,
        "created_at" timestamp with time zone DEFAULT now() NOT NULL,
        "updated_at" timestamp with time zone DEFAULT now() NOT NULL,
        "deleted_at" timestamp with time zone,
        "user_id" text NOT NULL,
        "target_user_id" text NOT NULL,
        "relationship_type" varchar(50) NOT NULL,
        "status" varchar(20) DEFAULT 'pending' NOT NULL,
        "notes" text,
        "metadata" jsonb,
        "established_at" timestamp with time zone
      );
    `);

    // 9. Create ai_channel_configs table
    console.log('📋 Creating/updating ai_channel_configs table...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS "ai_channel_configs" (
        "id" varchar(255) PRIMARY KEY NOT NULL,
        "guild_id" varchar(255) NOT NULL,
        "ai_channel_id" varchar(255) NOT NULL,
        "panel_message_id" varchar(255),
        "enabled" boolean DEFAULT true NOT NULL,
        "settings" jsonb NOT NULL,
        "last_panel_update" timestamp,
        "created_at" timestamp DEFAULT now() NOT NULL,
        "updated_at" timestamp DEFAULT now() NOT NULL
      );
    `);

    // Add unique constraint for guild_id
    await client.query(`
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                       WHERE table_name = 'ai_channel_configs' AND constraint_name = 'ai_channel_configs_guild_id_unique') THEN
          ALTER TABLE "ai_channel_configs" ADD CONSTRAINT "ai_channel_configs_guild_id_unique" UNIQUE ("guild_id");
        END IF;
      END
      $$;
    `);

    // 10. Create ai_chat_sessions table
    console.log('📋 Creating/updating ai_chat_sessions table...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS "ai_chat_sessions" (
        "id" varchar(255) PRIMARY KEY NOT NULL,
        "user_id" varchar(255) NOT NULL,
        "guild_id" varchar(255) NOT NULL,
        "agent_type" varchar(100) NOT NULL,
        "thread_id" varchar(255) NOT NULL,
        "channel_id" varchar(255) NOT NULL,
        "status" varchar(50) DEFAULT 'active' NOT NULL,
        "access_level" varchar(50) DEFAULT 'free' NOT NULL,
        "session_data" jsonb,
        "message_count" integer DEFAULT 0,
        "last_message_at" timestamp,
        "created_at" timestamp DEFAULT now() NOT NULL,
        "updated_at" timestamp DEFAULT now() NOT NULL,
        "archived_at" timestamp,
        "expires_at" timestamp
      );
    `);

    // 11. Create organizations table
    console.log('📋 Creating/updating organizations table...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS "organizations" (
        "id" serial PRIMARY KEY NOT NULL,
        "created_at" timestamp with time zone DEFAULT now() NOT NULL,
        "updated_at" timestamp with time zone DEFAULT now() NOT NULL,
        "deleted_at" timestamp with time zone,
        "name" varchar(255) NOT NULL,
        "slug" varchar(100) NOT NULL,
        "tier" organization_tier DEFAULT 'free' NOT NULL,
        "status" organization_status DEFAULT 'active' NOT NULL,
        "settings" jsonb DEFAULT '{}' NOT NULL,
        "limits" jsonb DEFAULT '{}' NOT NULL,
        "billing" jsonb DEFAULT '{}' NOT NULL,
        "owner_id" text NOT NULL,
        "logo_url" text,
        "website_url" text,
        "description" text,
        "contact_email" varchar(255),
        "timezone" varchar(50) DEFAULT 'UTC',
        "is_active" boolean DEFAULT true NOT NULL
      );
    `);

    // Add unique constraint for slug
    await client.query(`
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                       WHERE table_name = 'organizations' AND constraint_name = 'organizations_slug_unique') THEN
          ALTER TABLE "organizations" ADD CONSTRAINT "organizations_slug_unique" UNIQUE ("slug");
        END IF;
      END
      $$;
    `);

    // 12. Create organization_members table
    console.log('📋 Creating/updating organization_members table...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS "organization_members" (
        "id" serial PRIMARY KEY NOT NULL,
        "created_at" timestamp with time zone DEFAULT now() NOT NULL,
        "updated_at" timestamp with time zone DEFAULT now() NOT NULL,
        "deleted_at" timestamp with time zone,
        "organization_id" text NOT NULL,
        "user_id" text NOT NULL,
        "role" varchar(50) DEFAULT 'member' NOT NULL,
        "permissions" jsonb DEFAULT '[]' NOT NULL,
        "invited_by" text,
        "invited_at" timestamp with time zone,
        "joined_at" timestamp with time zone,
        "last_active_at" timestamp with time zone,
        "is_active" boolean DEFAULT true NOT NULL
      );
    `);

    // 13. Create organization_invitations table
    console.log('📋 Creating/updating organization_invitations table...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS "organization_invitations" (
        "id" serial PRIMARY KEY NOT NULL,
        "created_at" timestamp with time zone DEFAULT now() NOT NULL,
        "updated_at" timestamp with time zone DEFAULT now() NOT NULL,
        "deleted_at" timestamp with time zone,
        "organization_id" text NOT NULL,
        "email" varchar(255) NOT NULL,
        "role" varchar(50) DEFAULT 'member' NOT NULL,
        "permissions" jsonb DEFAULT '[]' NOT NULL,
        "invited_by" text NOT NULL,
        "token" varchar(255) NOT NULL,
        "expires_at" timestamp with time zone NOT NULL,
        "accepted_at" timestamp with time zone,
        "accepted_by" text,
        "is_active" boolean DEFAULT true NOT NULL
      );
    `);

    // Add unique constraint for token
    await client.query(`
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                       WHERE table_name = 'organization_invitations' AND constraint_name = 'organization_invitations_token_unique') THEN
          ALTER TABLE "organization_invitations" ADD CONSTRAINT "organization_invitations_token_unique" UNIQUE ("token");
        END IF;
      END
      $$;
    `);

    // 14. Create api_keys table
    console.log('📋 Creating/updating api_keys table...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS "api_keys" (
        "id" serial PRIMARY KEY NOT NULL,
        "created_at" timestamp with time zone DEFAULT now() NOT NULL,
        "updated_at" timestamp with time zone DEFAULT now() NOT NULL,
        "deleted_at" timestamp with time zone,
        "organization_id" text NOT NULL,
        "name" varchar(255) NOT NULL,
        "key_hash" text NOT NULL,
        "key_prefix" varchar(20) NOT NULL,
        "permissions" jsonb DEFAULT '[]' NOT NULL,
        "rate_limit" jsonb DEFAULT '{}' NOT NULL,
        "last_used" timestamp with time zone,
        "expires_at" timestamp with time zone,
        "is_active" boolean DEFAULT true NOT NULL,
        "created_by" text NOT NULL
      );
    `);

    // Add unique constraint for key_hash
    await client.query(`
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                       WHERE table_name = 'api_keys' AND constraint_name = 'api_keys_key_hash_unique') THEN
          ALTER TABLE "api_keys" ADD CONSTRAINT "api_keys_key_hash_unique" UNIQUE ("key_hash");
        END IF;
      END
      $$;
    `);

    // 15. Create audit_logs table
    console.log('📋 Creating/updating audit_logs table...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS "audit_logs" (
        "id" serial PRIMARY KEY NOT NULL,
        "created_at" timestamp with time zone DEFAULT now() NOT NULL,
        "updated_at" timestamp with time zone DEFAULT now() NOT NULL,
        "deleted_at" timestamp with time zone,
        "organization_id" text NOT NULL,
        "user_id" text NOT NULL,
        "action" varchar(100) NOT NULL,
        "resource" varchar(100) NOT NULL,
        "resource_id" text NOT NULL,
        "details" jsonb DEFAULT '{}' NOT NULL,
        "ip_address" varchar(45),
        "user_agent" text,
        "severity" varchar(20) DEFAULT 'low' NOT NULL,
        "session_id" text
      );
    `);

    // 16. Create security_events table
    console.log('📋 Creating/updating security_events table...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS "security_events" (
        "id" serial PRIMARY KEY NOT NULL,
        "created_at" timestamp with time zone DEFAULT now() NOT NULL,
        "updated_at" timestamp with time zone DEFAULT now() NOT NULL,
        "deleted_at" timestamp with time zone,
        "organization_id" text,
        "user_id" text,
        "type" varchar(100) NOT NULL,
        "severity" varchar(20) NOT NULL,
        "description" text NOT NULL,
        "metadata" jsonb DEFAULT '{}' NOT NULL,
        "resolved" boolean DEFAULT false NOT NULL,
        "resolved_by" text,
        "resolved_at" timestamp with time zone,
        "ip_address" varchar(45),
        "user_agent" text
      );
    `);

    // 17. Create feature_flags table
    console.log('📋 Creating/updating feature_flags table...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS "feature_flags" (
        "id" serial PRIMARY KEY NOT NULL,
        "created_at" timestamp with time zone DEFAULT now() NOT NULL,
        "updated_at" timestamp with time zone DEFAULT now() NOT NULL,
        "deleted_at" timestamp with time zone,
        "key" varchar(100) NOT NULL,
        "name" varchar(255) NOT NULL,
        "description" text,
        "enabled" boolean DEFAULT false NOT NULL,
        "rollout_percentage" varchar(3) DEFAULT '0' NOT NULL,
        "conditions" jsonb DEFAULT '[]' NOT NULL,
        "organization_overrides" jsonb DEFAULT '{}' NOT NULL,
        "user_overrides" jsonb DEFAULT '{}' NOT NULL,
        "is_active" boolean DEFAULT true NOT NULL
      );
    `);

    // Add unique constraint for key
    await client.query(`
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                       WHERE table_name = 'feature_flags' AND constraint_name = 'feature_flags_key_unique') THEN
          ALTER TABLE "feature_flags" ADD CONSTRAINT "feature_flags_key_unique" UNIQUE ("key");
        END IF;
      END
      $$;
    `);

    // 18. Create performance_metrics table
    console.log('📋 Creating/updating performance_metrics table...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS "performance_metrics" (
        "id" serial PRIMARY KEY NOT NULL,
        "created_at" timestamp with time zone DEFAULT now() NOT NULL,
        "updated_at" timestamp with time zone DEFAULT now() NOT NULL,
        "deleted_at" timestamp with time zone,
        "organization_id" text,
        "guild_id" text,
        "service" varchar(100) NOT NULL,
        "operation" varchar(100) NOT NULL,
        "duration" varchar(20) NOT NULL,
        "success" boolean NOT NULL,
        "error_type" varchar(100),
        "metadata" jsonb DEFAULT '{}' NOT NULL,
        "timestamp" timestamp with time zone DEFAULT now() NOT NULL
      );
    `);

    // Create all indexes
    console.log('📋 Creating indexes...');
    const indexQueries = [
      // Users indexes
      'CREATE INDEX IF NOT EXISTS "users_discord_id_idx" ON "users" ("discord_id")',
      'CREATE INDEX IF NOT EXISTS "users_created_at_idx" ON "users" ("created_at")',
      'CREATE INDEX IF NOT EXISTS "users_username_idx" ON "users" ("username")',
      
      // Guilds indexes
      'CREATE INDEX IF NOT EXISTS "guilds_discord_id_idx" ON "guilds" ("discord_id")',
      
      // Sessions indexes
      'CREATE INDEX IF NOT EXISTS "sessions_session_id_idx" ON "sessions" ("session_id")',
      'CREATE INDEX IF NOT EXISTS "sessions_user_id_idx" ON "sessions" ("user_id")',
      'CREATE INDEX IF NOT EXISTS "sessions_expires_at_idx" ON "sessions" ("expires_at")',
      
      // AI Agent Config indexes
      'CREATE INDEX IF NOT EXISTS "ai_agent_config_guild_id_idx" ON "ai_agent_config" ("guild_id")',
      'CREATE INDEX IF NOT EXISTS "ai_agent_config_agent_type_idx" ON "ai_agent_config" ("agent_type")',
      
      // Agent Interactions indexes
      'CREATE INDEX IF NOT EXISTS "agent_interactions_user_id_idx" ON "agent_interactions" ("user_id")',
      'CREATE INDEX IF NOT EXISTS "agent_interactions_agent_type_idx" ON "agent_interactions" ("agent_type")',
      'CREATE INDEX IF NOT EXISTS "agent_interactions_interaction_type_idx" ON "agent_interactions" ("interaction_type")',
      'CREATE INDEX IF NOT EXISTS "agent_interactions_created_at_idx" ON "agent_interactions" ("created_at")',
      
      // Agent Memory indexes
      'CREATE INDEX IF NOT EXISTS "agent_memory_user_id_idx" ON "agent_memory" ("user_id")',
      'CREATE INDEX IF NOT EXISTS "agent_memory_memory_type_idx" ON "agent_memory" ("memory_type")',
      'CREATE INDEX IF NOT EXISTS "agent_memory_created_at_idx" ON "agent_memory" ("created_at")',
      
      // Personal Growth Plans indexes
      'CREATE INDEX IF NOT EXISTS "personal_growth_plans_user_id_idx" ON "personal_growth_plans" ("user_id")',
      'CREATE INDEX IF NOT EXISTS "personal_growth_plans_status_idx" ON "personal_growth_plans" ("status")',
      
      // User Relationships indexes
      'CREATE INDEX IF NOT EXISTS "user_relationships_user_id_idx" ON "user_relationships" ("user_id")',
      'CREATE INDEX IF NOT EXISTS "user_relationships_target_user_id_idx" ON "user_relationships" ("target_user_id")',
      
      // Organizations indexes
      'CREATE INDEX IF NOT EXISTS "organizations_slug_idx" ON "organizations" ("slug")',
      'CREATE INDEX IF NOT EXISTS "organizations_tier_idx" ON "organizations" ("tier")',
      'CREATE INDEX IF NOT EXISTS "organizations_status_idx" ON "organizations" ("status")',
      'CREATE INDEX IF NOT EXISTS "organizations_owner_id_idx" ON "organizations" ("owner_id")',
      'CREATE INDEX IF NOT EXISTS "organizations_is_active_idx" ON "organizations" ("is_active")',
      
      // Organization Members indexes
      'CREATE INDEX IF NOT EXISTS "org_members_org_user_idx" ON "organization_members" ("organization_id", "user_id")',
      'CREATE INDEX IF NOT EXISTS "org_members_org_idx" ON "organization_members" ("organization_id")',
      'CREATE INDEX IF NOT EXISTS "org_members_user_idx" ON "organization_members" ("user_id")',
      'CREATE INDEX IF NOT EXISTS "org_members_role_idx" ON "organization_members" ("role")',
      'CREATE INDEX IF NOT EXISTS "org_members_is_active_idx" ON "organization_members" ("is_active")',
      
      // Organization Invitations indexes
      'CREATE INDEX IF NOT EXISTS "org_invitations_org_idx" ON "organization_invitations" ("organization_id")',
      'CREATE INDEX IF NOT EXISTS "org_invitations_email_idx" ON "organization_invitations" ("email")',
      'CREATE INDEX IF NOT EXISTS "org_invitations_token_idx" ON "organization_invitations" ("token")',
      'CREATE INDEX IF NOT EXISTS "org_invitations_expires_idx" ON "organization_invitations" ("expires_at")',
      'CREATE INDEX IF NOT EXISTS "org_invitations_is_active_idx" ON "organization_invitations" ("is_active")',
      
      // API Keys indexes
      'CREATE INDEX IF NOT EXISTS "api_keys_org_idx" ON "api_keys" ("organization_id")',
      'CREATE INDEX IF NOT EXISTS "api_keys_hash_idx" ON "api_keys" ("key_hash")',
      'CREATE INDEX IF NOT EXISTS "api_keys_prefix_idx" ON "api_keys" ("key_prefix")',
      'CREATE INDEX IF NOT EXISTS "api_keys_is_active_idx" ON "api_keys" ("is_active")',
      'CREATE INDEX IF NOT EXISTS "api_keys_expires_idx" ON "api_keys" ("expires_at")',
      
      // Audit Logs indexes
      'CREATE INDEX IF NOT EXISTS "audit_logs_org_idx" ON "audit_logs" ("organization_id")',
      'CREATE INDEX IF NOT EXISTS "audit_logs_user_idx" ON "audit_logs" ("user_id")',
      'CREATE INDEX IF NOT EXISTS "audit_logs_action_idx" ON "audit_logs" ("action")',
      'CREATE INDEX IF NOT EXISTS "audit_logs_resource_idx" ON "audit_logs" ("resource")',
      'CREATE INDEX IF NOT EXISTS "audit_logs_severity_idx" ON "audit_logs" ("severity")',
      'CREATE INDEX IF NOT EXISTS "audit_logs_timestamp_idx" ON "audit_logs" ("created_at")',
      
      // Security Events indexes
      'CREATE INDEX IF NOT EXISTS "security_events_org_idx" ON "security_events" ("organization_id")',
      'CREATE INDEX IF NOT EXISTS "security_events_user_idx" ON "security_events" ("user_id")',
      'CREATE INDEX IF NOT EXISTS "security_events_type_idx" ON "security_events" ("type")',
      'CREATE INDEX IF NOT EXISTS "security_events_severity_idx" ON "security_events" ("severity")',
      'CREATE INDEX IF NOT EXISTS "security_events_resolved_idx" ON "security_events" ("resolved")',
      'CREATE INDEX IF NOT EXISTS "security_events_timestamp_idx" ON "security_events" ("created_at")',
      
      // Feature Flags indexes
      'CREATE INDEX IF NOT EXISTS "feature_flags_key_idx" ON "feature_flags" ("key")',
      'CREATE INDEX IF NOT EXISTS "feature_flags_enabled_idx" ON "feature_flags" ("enabled")',
      'CREATE INDEX IF NOT EXISTS "feature_flags_is_active_idx" ON "feature_flags" ("is_active")',
      
      // Performance Metrics indexes
      'CREATE INDEX IF NOT EXISTS "performance_metrics_org_idx" ON "performance_metrics" ("organization_id")',
      'CREATE INDEX IF NOT EXISTS "performance_metrics_service_idx" ON "performance_metrics" ("service")',
      'CREATE INDEX IF NOT EXISTS "performance_metrics_operation_idx" ON "performance_metrics" ("operation")',
      'CREATE INDEX IF NOT EXISTS "performance_metrics_timestamp_idx" ON "performance_metrics" ("timestamp")',
      'CREATE INDEX IF NOT EXISTS "performance_metrics_success_idx" ON "performance_metrics" ("success")',
    ];

    for (const query of indexQueries) {
      await client.query(query);
    }

    // Verify all tables
    console.log('\n📊 Verifying database schema...');
    const verifyQuery = `
      SELECT table_name, 
             (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = t.table_name) as column_count
      FROM information_schema.tables t
      WHERE table_schema = 'public' 
        AND table_type = 'BASE TABLE'
      ORDER BY table_name;
    `;
    
    const result = await client.query(verifyQuery);
    console.log('\n✅ Database tables:');
    result.rows.forEach(row => {
      console.log(`   - ${row.table_name}: ${row.column_count} columns`);
    });

    console.log('\n🎉 Database schema fix completed successfully!');
    console.log('\n💡 Next steps:');
    console.log('   1. Run "npm run build" to rebuild the application');
    console.log('   2. Run "npm run start" to start the Discord bot');
    console.log('   3. Check logs for any remaining issues');

  } catch (error) {
    console.error('❌ Error during migration:', error);
    throw error;
  } finally {
    await client.end();
  }
}

// Run the migration
runMigration().catch(error => {
  console.error('Migration failed:', error);
  process.exit(1);
});
