#!/usr/bin/env node

/**
 * Panel Migrations Runner
 * 
 * This script runs the necessary database migrations for the consolidated panel services.
 * It should be run after the refactoring to ensure all required tables exist.
 */

// Load environment variables
require('dotenv').config();

const { Client } = require('pg');
const fs = require('fs');
const path = require('path');

// Database configuration from environment variables
const dbConfig = process.env.DATABASE_URL ? {
  connectionString: process.env.DATABASE_URL,
  ssl: { rejectUnauthorized: false }
} : {
  host: process.env.DATABASE_HOST || 'localhost',
  port: process.env.DATABASE_PORT || 5432,
  database: process.env.DATABASE_NAME || process.env.DB_NAME || 'postgres',
  user: process.env.DATABASE_USER || process.env.DB_USER || 'postgres',
  password: process.env.DATABASE_PASSWORD || process.env.DB_PASSWORD || 'password',
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
};

const migrationFiles = [
  '../src/core/database/migrations/concrete/002_create_panel_deployment_tables.sql',
  '../src/core/database/migrations/concrete/003_create_missing_panel_tables.sql'
];

async function runMigrations() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔌 Connecting to database...');
    await client.connect();
    console.log('✅ Connected to database');
    
    for (const migrationFile of migrationFiles) {
      const filePath = path.join(__dirname, migrationFile);
      
      if (!fs.existsSync(filePath)) {
        console.log(`⚠️  Migration file not found: ${filePath}`);
        continue;
      }
      
      console.log(`📄 Running migration: ${path.basename(migrationFile)}`);
      
      const sql = fs.readFileSync(filePath, 'utf8');
      
      try {
        await client.query(sql);
        console.log(`✅ Migration completed: ${path.basename(migrationFile)}`);
      } catch (error) {
        console.error(`❌ Migration failed: ${path.basename(migrationFile)}`);
        console.error('Error:', error.message);
        
        // Continue with other migrations even if one fails
        // This allows for idempotent migrations
        if (error.code !== '42P07') { // Table already exists
          console.error('Stack:', error.stack);
        }
      }
    }
    
    // Test that critical tables exist
    console.log('\n🔍 Verifying critical tables...');
    
    const criticalTables = [
      'panel_deployments',
      'panel_states', 
      'panel_analytics',
      'user_panel_states',
      'ai_tools',
      'troubleshooting_guides'
    ];
    
    for (const tableName of criticalTables) {
      try {
        const result = await client.query(
          `SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = $1
          )`,
          [tableName]
        );
        
        if (result.rows[0].exists) {
          console.log(`✅ Table exists: ${tableName}`);
        } else {
          console.log(`❌ Table missing: ${tableName}`);
        }
      } catch (error) {
        console.error(`❌ Error checking table ${tableName}:`, error.message);
      }
    }
    
    console.log('\n🎉 Database migration process completed!');
    
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    process.exit(1);
  } finally {
    await client.end();
  }
}

// Run the migrations
if (require.main === module) {
  runMigrations().catch(error => {
    console.error('❌ Migration process failed:', error);
    process.exit(1);
  });
}

module.exports = { runMigrations };