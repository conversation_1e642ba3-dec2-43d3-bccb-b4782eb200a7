#!/usr/bin/env node

/**
 * Discord Guild Channel Analysis Script
 * Analyzes the channel structure of guild 1394355426941730856
 */

const fs = require('fs');
const path = require('path');

const GUILD_ID = '1394355426941730856';

// Raw channel data from Discord MCP
const channelData = [
  { name: '📢 ANNOUNCEMENTS', id: '1396352166536548407', type: 'GuildCategory' },
  { name: 'DEVELOPMENT', id: '1394721385774973071', type: 'GuildCategory' },
  { name: 'audit-log', id: '1396530278662672508', type: 'GuildText' },
  { name: '📢-announcements', id: '1396352345046257665', type: 'GuildText' },
  { name: '🤝-project-collaboration', id: '1396595002511065150', type: 'GuildText' },
  { name: '💼-business-talks', id: '1396595084576817263', type: 'GuildText' },
  { name: 'spec-prd-plan', id: '1396573886534914058', type: 'GuildText' },
  { name: 'General', id: '1394355427612823727', type: 'GuildVoice' },
  { name: '⚔️-war-rooms', id: '1396595106743717888', type: 'GuildText' },
  { name: '📊-progress-tracking', id: '1396595026099699753', type: 'GuildText' },
  { name: '🤖 AI Mastery Voice', id: '1396595339569532981', type: 'GuildVoice' },
  { name: '🤝 NETWORKING & BUSINESS', id: '1396594691818127380', type: 'GuildCategory' },
  { name: 'staff-chat', id: '1394712565531148440', type: 'GuildText' },
  { name: '💵-money-strategies', id: '1396594819224309852', type: 'GuildText' },
  { name: '🏆-success-stories', id: '1396594859330113586', type: 'GuildText' },
  { name: '🛠-ai-tools', id: '1396594755064041482', type: 'GuildText' },
  { name: 'dev-chat', id: '1394721457946493089', type: 'GuildText' },
  { name: '✂️-clippers-section', id: '1396595128831053905', type: 'GuildText' },
  { name: 'rules', id: '1394499361454555207', type: 'GuildText' },
  { name: '📝-project-requests', id: '1396594980239179850', type: 'GuildText' },
  { name: '🗨-chat', id: '1396352387475832925', type: 'GuildText' },
  { name: '🧠-mindset-coaching', id: '1396594898458902650', type: 'GuildText' },
  { name: '🎯-goal-tracking', id: '1396594937902010390', type: 'GuildText' },
  { name: '🎮-gaming-general', id: '1396352981326237708', type: 'GuildText' },
  { name: '💰 WEALTH CREATION', id: '1396594632699416757', type: 'GuildCategory' },
  { name: '💬 COMMUNITY', id: '1396352189206757376', type: 'GuildCategory' },
  { name: '❓-faq', id: '1396352928037601380', type: 'GuildText' },
  { name: 'STAFF', id: '1394712469867466905', type: 'GuildCategory' },
  { name: '⚡ Dev Collaboration', id: '1396595386482823250', type: 'GuildVoice' },
  { name: '🎫-support-tickets', id: '1396352904247644271', type: 'GuildText' },
  { name: '🎓-ai-tutorials', id: '1396594735988084817', type: 'GuildText' },
  { name: '🚀-entrepreneurship', id: '1396594839298244689', type: 'GuildText' },
  { name: '⚡ DEV ON DEMAND', id: '1396594670179586218', type: 'GuildCategory' },
  { name: '⏰-productivity-tips', id: '1396594919279169681', type: 'GuildText' },
  { name: '📰-news', id: '1396352364843241592', type: 'GuildText' },
  { name: '🔧 SUPPORT', id: '1396352237315424278', type: 'GuildCategory' },
  { name: 'moderator-only', id: '1394499506480877689', type: 'GuildText' },
  { name: '💎-premium-dev', id: '1396536930253930559', type: 'GuildText' },
  { name: 'staff-logs', id: '1396530507566551051', type: 'GuildText' },
  { name: '🌐-networking', id: '1396595063332667633', type: 'GuildText' },
  { name: 'introductions', id: '1396529869730353283', type: 'GuildText' },
  { name: 'ai-news', id: '1396530075054116934', type: 'GuildText' },
  { name: '💎-premium-chat', id: '1396536876801720330', type: 'GuildText' },
  { name: '💼 Business Strategy', id: '1396595362537537557', type: 'GuildVoice' },
  { name: 'ai-coding', id: '1396529967935787090', type: 'GuildText' },
  { name: '🤖 AI MASTERY', id: '1396594612654702744', type: 'GuildCategory' },
  { name: 'general', id: '1396530432513933487', type: 'GuildText' },
  { name: 'Voice Channels', id: '1394355427612823725', type: 'GuildCategory' },
  { name: 'welcome', id: '1396529696442810390', type: 'GuildText' },
  { name: '🖼-media', id: '1396352417976553453', type: 'GuildText' },
  { name: '⚙️-automation', id: '1396594777985777725', type: 'GuildText' },
  { name: '🌱 PERSONAL GROWTH', id: '1396594648335777803', type: 'GuildCategory' },
  { name: '💳-subscriptions', id: '1396537062202806274', type: 'GuildText' },
  { name: 'links-dump', id: '1396530178079064154', type: 'GuildText' },
  { name: '🎮 GAMING', id: '1396352214796079115', type: 'GuildCategory' },
  { name: '💎 Premium Voice', id: '1396540761084002447', type: 'GuildVoice' },
  { name: 'AI Assistants', id: '1398998360605855827', type: 'GuildCategory' },
  { name: 'ai-agents', id: '1398998363243810939', type: 'GuildText' },
  { name: 'Personal Growth', id: '1399415246153842809', type: 'GuildCategory' },
  { name: 'progress-wins', id: '1399415248657842329', type: 'GuildText' },
  { name: 'growth-resources', id: '1399415250067132417', type: 'GuildText' },
  { name: 'personal-growth-support', id: '1399415247596556380', type: 'GuildText' }
];

function analyzeGuildChannels() {
  console.log('🏰 Discord Guild Channel Analysis');
  console.log('=' .repeat(50));
  console.log(`Guild ID: ${GUILD_ID}`);
  console.log(`Total Channels: ${channelData.length}`);
  console.log('');

  // Categorize channels by type
  const channelsByType = {
    categories: channelData.filter(c => c.type === 'GuildCategory'),
    textChannels: channelData.filter(c => c.type === 'GuildText'),
    voiceChannels: channelData.filter(c => c.type === 'GuildVoice')
  };

  console.log('📊 CHANNEL STATISTICS:');
  console.log('─'.repeat(30));
  console.log(`📁 Categories: ${channelsByType.categories.length}`);
  console.log(`💬 Text Channels: ${channelsByType.textChannels.length}`);
  console.log(`🔊 Voice Channels: ${channelsByType.voiceChannels.length}`);
  console.log('');

  // Analyze categories and their potential panel deployments
  console.log('📋 CATEGORY ANALYSIS:');
  console.log('=' .repeat(50));
  
  const categoryAnalysis = channelsByType.categories.map(category => {
    // Try to map text channels to categories based on naming patterns
    const relatedChannels = channelsByType.textChannels.filter(channel => {
      return isChannelRelatedToCategory(channel.name, category.name);
    });

    return {
      ...category,
      relatedChannels: relatedChannels,
      potentialPanelType: inferPanelType(category.name),
      channelCount: relatedChannels.length
    };
  });

  categoryAnalysis.forEach(category => {
    console.log(`\n📁 ${category.name}`);
    console.log(`   ID: ${category.id}`);
    console.log(`   Panel Type: ${category.potentialPanelType}`);
    console.log(`   Related Channels: ${category.channelCount}`);
    
    if (category.relatedChannels.length > 0) {
      category.relatedChannels.forEach(channel => {
        console.log(`   └─ 💬 ${channel.name} (${channel.id})`);
      });
    }
  });

  // Analyze standalone channels (not clearly categorized)
  const standaloneChannels = channelsByType.textChannels.filter(channel => {
    return !categoryAnalysis.some(cat => 
      cat.relatedChannels.some(related => related.id === channel.id)
    );
  });

  if (standaloneChannels.length > 0) {
    console.log('\n\n🔸 STANDALONE CHANNELS:');
    console.log('─'.repeat(30));
    standaloneChannels.forEach(channel => {
      console.log(`💬 ${channel.name} (${channel.id})`);
      console.log(`   Potential Panel: ${inferPanelTypeFromChannel(channel.name)}`);
    });
  }

  // Panel deployment analysis
  console.log('\n\n🚀 PANEL DEPLOYMENT ANALYSIS:');
  console.log('=' .repeat(50));
  
  const panelMapping = {
    'announcement': [],
    'community': [],
    'ai-mastery': [],
    'wealth-creation': [],
    'personal-growth': [],
    'networking-business': [],
    'technical-support': [],
    'gaming-entertainment': [],
    'ai-agent': [],
    'unknown': []
  };

  // Map all text channels to panel types
  channelsByType.textChannels.forEach(channel => {
    const panelType = inferPanelTypeFromChannel(channel.name);
    if (panelMapping[panelType]) {
      panelMapping[panelType].push(channel);
    } else {
      panelMapping['unknown'].push(channel);
    }
  });

  Object.entries(panelMapping).forEach(([panelType, channels]) => {
    if (channels.length > 0) {
      console.log(`\n📊 ${panelType.toUpperCase().replace('-', ' ')} (${channels.length} channels):`);
      channels.forEach(channel => {
        console.log(`   💬 ${channel.name} (${channel.id})`);
      });
    }
  });

  // Voice channels analysis
  console.log('\n\n🔊 VOICE CHANNELS:');
  console.log('─'.repeat(30));
  channelsByType.voiceChannels.forEach(channel => {
    console.log(`🔊 ${channel.name} (${channel.id})`);
  });

  // Generate recommendations
  console.log('\n\n💡 RECOMMENDATIONS:');
  console.log('=' .repeat(50));
  
  const recommendations = generateRecommendations(panelMapping, categoryAnalysis);
  recommendations.forEach((rec, index) => {
    console.log(`${index + 1}. ${rec}`);
  });

  // Export detailed analysis
  const analysis = {
    guildId: GUILD_ID,
    timestamp: new Date().toISOString(),
    statistics: {
      totalChannels: channelData.length,
      categories: channelsByType.categories.length,
      textChannels: channelsByType.textChannels.length,
      voiceChannels: channelsByType.voiceChannels.length
    },
    categories: categoryAnalysis,
    standaloneChannels,
    panelMapping,
    voiceChannels: channelsByType.voiceChannels,
    recommendations
  };

  // Save analysis to file
  const outputFile = path.join(__dirname, `guild-${GUILD_ID}-analysis.json`);
  fs.writeFileSync(outputFile, JSON.stringify(analysis, null, 2));
  
  console.log(`\n💾 Detailed analysis saved to: ${outputFile}`);
  
  return analysis;
}

function isChannelRelatedToCategory(channelName, categoryName) {
  const channelLower = channelName.toLowerCase();
  const categoryLower = categoryName.toLowerCase();
  
  // Remove emojis and special characters for comparison
  const cleanChannel = channelLower.replace(/[^\w\s-]/g, '').trim();
  const cleanCategory = categoryLower.replace(/[^\w\s-]/g, '').trim();
  
  // Check for keyword matches
  const categoryKeywords = cleanCategory.split(/\s+/);
  
  return categoryKeywords.some(keyword => {
    if (keyword.length < 3) return false; // Skip short words
    return cleanChannel.includes(keyword);
  });
}

function inferPanelType(categoryName) {
  const name = categoryName.toLowerCase();
  
  if (name.includes('announcement')) return 'announcement';
  if (name.includes('community')) return 'community';
  if (name.includes('ai') || name.includes('mastery')) return 'ai-mastery';
  if (name.includes('wealth') || name.includes('money')) return 'wealth-creation';
  if (name.includes('personal') || name.includes('growth')) return 'personal-growth';
  if (name.includes('network') || name.includes('business')) return 'networking-business';
  if (name.includes('support') || name.includes('help')) return 'technical-support';
  if (name.includes('gaming') || name.includes('game')) return 'gaming-entertainment';
  if (name.includes('assistant') || name.includes('agent')) return 'ai-agent';
  if (name.includes('dev') || name.includes('development')) return 'development';
  if (name.includes('staff') || name.includes('mod')) return 'staff';
  
  return 'unknown';
}

function inferPanelTypeFromChannel(channelName) {
  const name = channelName.toLowerCase();
  
  // Announcement patterns
  if (name.includes('announcement') || name.includes('news')) return 'announcement';
  
  // Community patterns
  if (name.includes('chat') || name.includes('general') || name.includes('welcome') || 
      name.includes('intro') || name.includes('media')) return 'community';
  
  // AI Mastery patterns
  if (name.includes('ai') || name.includes('tool') || name.includes('automation') ||
      name.includes('coding') || name.includes('tutorial')) return 'ai-mastery';
  
  // Wealth Creation patterns
  if (name.includes('money') || name.includes('wealth') || name.includes('entrepreneur') ||
      name.includes('success') || name.includes('subscription')) return 'wealth-creation';
  
  // Personal Growth patterns
  if (name.includes('mindset') || name.includes('goal') || name.includes('productivity') ||
      name.includes('growth') || name.includes('progress') || name.includes('win')) return 'personal-growth';
  
  // Networking Business patterns
  if (name.includes('network') || name.includes('business') || name.includes('collaboration') ||
      name.includes('project') || name.includes('war')) return 'networking-business';
  
  // Technical Support patterns
  if (name.includes('support') || name.includes('ticket') || name.includes('faq') ||
      name.includes('help')) return 'technical-support';
  
  // Gaming patterns
  if (name.includes('gaming') || name.includes('game')) return 'gaming-entertainment';
  
  // AI Agent patterns
  if (name.includes('agent') || name.includes('assistant')) return 'ai-agent';
  
  return 'unknown';
}

function generateRecommendations(panelMapping, categoryAnalysis) {
  const recommendations = [];
  
  // Check for missing panel handlers
  Object.entries(panelMapping).forEach(([panelType, channels]) => {
    if (channels.length > 0 && panelType !== 'unknown') {
      recommendations.push(
        `Deploy ${panelType} panels to ${channels.length} channels: ${channels.map(c => c.name).join(', ')}`
      );
    }
  });
  
  // Check for categories without clear panel mappings
  const unclearCategories = categoryAnalysis.filter(cat => 
    cat.potentialPanelType === 'unknown' && cat.relatedChannels.length > 0
  );
  
  if (unclearCategories.length > 0) {
    recommendations.push(
      `Review panel types for categories: ${unclearCategories.map(c => c.name).join(', ')}`
    );
  }
  
  // Check for standalone channels
  const standaloneCount = panelMapping['unknown'].length;
  if (standaloneCount > 0) {
    recommendations.push(
      `Review ${standaloneCount} channels with unclear panel types`
    );
  }
  
  // Voice channel recommendations
  const voiceChannels = channelData.filter(c => c.type === 'GuildVoice');
  if (voiceChannels.length > 0) {
    recommendations.push(
      `Consider voice-specific features for ${voiceChannels.length} voice channels`
    );
  }
  
  return recommendations;
}

// Run the analysis
if (require.main === module) {
  try {
    const analysis = analyzeGuildChannels();
    console.log('\n✅ Analysis completed successfully!');
  } catch (error) {
    console.error('❌ Error during analysis:', error);
  }
}

module.exports = {
  analyzeGuildChannels,
  channelData,
  GUILD_ID
};