#!/usr/bin/env node

/**
 * Minimal Database Schema Fix Script
 * 
 * This script fixes the critical schema issues one by one
 * with detailed error handling and verification.
 */

require('dotenv').config();
const { Pool } = require('pg');

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
});

async function executeQuery(query, params = []) {
  try {
    console.log(`Executing: ${query.substring(0, 100)}...`);
    const result = await pool.query(query, params);
    return result;
  } catch (error) {
    console.error('Query error:', error.message);
    return null;
  }
}

async function checkTable(tableName) {
  try {
    const result = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = $1
      );
    `, [tableName]);
    return result.rows[0].exists;
  } catch (error) {
    console.error(`Error checking ${tableName}:`, error.message);
    return false;
  }
}

async function checkColumn(tableName, columnName) {
  try {
    const result = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = $1 
        AND column_name = $2
      );
    `, [tableName, columnName]);
    return result.rows[0].exists;
  } catch (error) {
    console.error(`Error checking ${columnName} in ${tableName}:`, error.message);
    return false;
  }
}

async function fixSchema() {
  console.log('🔧 Starting minimal database fix...\n');

  try {
    // Step 1: Fix agent_memory table
    const hasAgentMemory = await checkTable('agent_memory');
    if (!hasAgentMemory) {
      console.log('Creating agent_memory table...');
      await executeQuery(`
        CREATE TABLE IF NOT EXISTS agent_memory (
          id SERIAL PRIMARY KEY,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          user_id TEXT NOT NULL,
          memory_type VARCHAR(50) NOT NULL,
          key VARCHAR(200) NOT NULL,
          value JSONB NOT NULL,
          context TEXT,
          importance INTEGER DEFAULT 1,
          access_count INTEGER DEFAULT 0,
          last_accessed_at TIMESTAMP WITH TIME ZONE,
          expires_at TIMESTAMP WITH TIME ZONE,
          tags TEXT[],
          metadata JSONB
        );
      `);
    } else {
      console.log('agent_memory table exists, checking columns...');
      const hasUserId = await checkColumn('agent_memory', 'user_id');
      if (!hasUserId) {
        console.log('Adding user_id column to agent_memory...');
        await executeQuery(`
          ALTER TABLE agent_memory ADD COLUMN user_id TEXT NOT NULL DEFAULT '';
        `);
      }
    }

    // Step 2: Fix agent_interactions table
    const hasAgentInteractions = await checkTable('agent_interactions');
    if (!hasAgentInteractions) {
      console.log('Creating agent_interactions table...');
      await executeQuery(`
        CREATE TABLE IF NOT EXISTS agent_interactions (
          id SERIAL PRIMARY KEY,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          user_id TEXT NOT NULL,
          agent_type VARCHAR(50) NOT NULL,
          interaction_type VARCHAR(50) NOT NULL,
          content TEXT NOT NULL,
          response TEXT,
          status VARCHAR(20) DEFAULT 'completed',
          context JSONB,
          metadata JSONB,
          channel_id TEXT,
          message_id TEXT,
          guild_id TEXT,
          sentiment_score INTEGER DEFAULT 0,
          tags TEXT[]
        );
      `);
    } else {
      console.log('agent_interactions table exists, checking columns...');
      const hasUpdatedAt = await checkColumn('agent_interactions', 'updated_at');
      if (!hasUpdatedAt) {
        console.log('Adding updated_at column to agent_interactions...');
        await executeQuery(`
          ALTER TABLE agent_interactions ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
        `);
      }
    }

    // Step 3: Fix ai_agent_config table
    const hasAiAgentConfig = await checkTable('ai_agent_config');
    if (!hasAiAgentConfig) {
      console.log('Creating ai_agent_config table...');
      await executeQuery(`
        CREATE TABLE IF NOT EXISTS ai_agent_config (
          id SERIAL PRIMARY KEY,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          guild_id TEXT NOT NULL,
          agent_type VARCHAR(50) NOT NULL,
          enabled BOOLEAN DEFAULT TRUE,
          configuration JSONB,
          default_channel_id TEXT,
          log_channel_id TEXT,
          permissions JSONB,
          settings JSONB,
          last_used_at TIMESTAMP WITH TIME ZONE
        );
      `);
    }

    // Step 4: Insert default config
    console.log('Inserting default configuration...');
    await executeQuery(`
      INSERT INTO ai_agent_config (guild_id, agent_type, enabled, configuration, settings) 
      VALUES ('1394355426941730856', 'general', true, '{"channels":["general"],"personality":{"tone":"friendly","style":"helpful"}}', '{"maxResponsesPerHour":50}')
      ON CONFLICT DO NOTHING;
    `);

    console.log('✅ Schema fixes completed successfully!');

    // Verification
    console.log('\n🔍 Verification:');
    const tables = ['agent_memory', 'agent_interactions', 'ai_agent_config'];
    for (const table of tables) {
      const exists = await checkTable(table);
      console.log(`  ${table}: ${exists ? '✅' : '❌'}`);
    }

  } catch (error) {
    console.error('❌ Critical error:', error.message);
  } finally {
    await pool.end();
  }
}

fixSchema().catch(console.error);