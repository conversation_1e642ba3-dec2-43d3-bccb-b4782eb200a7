#!/usr/bin/env node

const { Client } = require('pg');
const dotenv = require('dotenv');

dotenv.config();

const DATABASE_URL = process.env.DATABASE_URL;

if (!DATABASE_URL) {
  console.error('❌ DATABASE_URL not found in environment variables');
  process.exit(1);
}

async function checkAndFixColumns() {
  const client = new Client({
    connectionString: DATABASE_URL,
  });

  try {
    console.log('🔧 Checking and fixing column issues...');
    await client.connect();

    // Check columns in agent_interactions table
    console.log('\n📋 Checking agent_interactions table columns...');
    const columnsQuery = `
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns
      WHERE table_name = 'agent_interactions'
      ORDER BY ordinal_position;
    `;
    
    const result = await client.query(columnsQuery);
    console.log('Current columns in agent_interactions:');
    result.rows.forEach(row => {
      console.log(`   - ${row.column_name}: ${row.data_type} ${row.is_nullable === 'NO' ? 'NOT NULL' : 'NULL'}`);
    });

    // Check if user_id column exists
    const hasUserId = result.rows.some(row => row.column_name === 'user_id');
    
    if (!hasUserId) {
      console.log('\n⚠️  user_id column is missing from agent_interactions table');
      console.log('   Adding user_id column...');
      
      await client.query(`
        ALTER TABLE "agent_interactions" 
        ADD COLUMN "user_id" text NOT NULL DEFAULT '';
      `);
      
      console.log('   ✅ Added user_id column to agent_interactions');
    } else {
      console.log('\n✅ user_id column exists in agent_interactions');
    }

    // Now create the index
    console.log('\n📋 Creating missing index...');
    await client.query(`
      CREATE INDEX IF NOT EXISTS "agent_interactions_user_id_idx" 
      ON "agent_interactions" ("user_id");
    `);
    console.log('   ✅ Created/verified agent_interactions_user_id_idx');

    console.log('\n🎉 Column check and fix completed!');

  } catch (error) {
    console.error('❌ Error:', error);
    throw error;
  } finally {
    await client.end();
  }
}

checkAndFixColumns().catch(error => {
  console.error('Failed:', error);
  process.exit(1);
});
