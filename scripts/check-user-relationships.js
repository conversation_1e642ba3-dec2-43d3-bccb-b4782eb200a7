const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({ connectionString: process.env.DATABASE_URL });

async function checkUserRelationships() {
  try {
    const result = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'user_relationships'
      );
    `);
    
    if (result.rows[0].exists) {
      console.log('user_relationships table exists');
      
      const schema = await pool.query(`
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns
        WHERE table_name = 'user_relationships'
        ORDER BY ordinal_position;
      `);
      
      console.log('\nColumns:');
      schema.rows.forEach(col => {
        console.log(`  - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? 'NOT NULL' : ''}`);
      });
      
      const count = await pool.query('SELECT COUNT(*) FROM user_relationships');
      console.log(`\nRow count: ${count.rows[0].count}`);
    } else {
      console.log('user_relationships table does not exist');
    }
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    await pool.end();
  }
}

checkUserRelationships();
