#!/usr/bin/env ts-node

import { NestFactory } from '@nestjs/core';
import { Logger } from '@nestjs/common';
import { AppModule } from '../src/app.module';
import { DatabaseService } from '../src/core/database/database.service';
import { RedisDatabaseService } from '../src/core/database/redis/redis-database.service';
import { DistributedCacheService } from '../src/core/cache/distributed-cache.service';
import * as fs from 'fs/promises';
import * as path from 'path';

interface MigrationConfig {
  batchSize: number;
  backupPath: string;
  dryRun: boolean;
  skipTables: string[];
  onlyTables: string[];
}

class RedisMigrationService {
  private readonly logger = new Logger('RedisMigration');
  private migrationStats = {
    usersProcessed: 0,
    guildsProcessed: 0,
    sessionsProcessed: 0,
    eventsProcessed: 0,
    errorsEncountered: 0,
    startTime: Date.now(),
    endTime: 0,
  };

  constructor(
    private readonly postgresService: DatabaseService,
    private readonly redisService: RedisDatabaseService,
    private readonly config: MigrationConfig
  ) {}

  async migrate(): Promise<void> {
    this.logger.log('Starting Redis migration...');
    this.logger.log(`Configuration: ${JSON.stringify(this.config, null, 2)}`);

    try {
      // Create backup first
      if (!this.config.dryRun) {
        await this.createBackup();
      }

      // Verify connections
      await this.verifyConnections();

      // Run migrations in order (considering dependencies)
      await this.migrateUsers();
      await this.migrateGuilds();
      await this.migrateSessions();
      await this.migrateCommunityData();
      await this.migrateAIConfigs();
      await this.migrateChannelPanelData();
      await this.migrateDevOnDemandData();
      
      // Verify migration
      if (!this.config.dryRun) {
        await this.verifyMigration();
      }

      this.migrationStats.endTime = Date.now();
      await this.generateReport();

      this.logger.log('Redis migration completed successfully!');
    } catch (error) {
      this.logger.error('Migration failed:', error);
      
      if (!this.config.dryRun) {
        this.logger.log('Initiating rollback...');
        await this.rollback();
      }
      
      throw error;
    }
  }

  private async createBackup(): Promise<void> {
    this.logger.log('Creating PostgreSQL backup...');
    
    try {
      // Export current Redis data as backup
      const redisData = await this.redisService.exportData(['*']);
      const backupFile = path.join(this.config.backupPath, `redis-backup-${Date.now()}.json`);
      
      await fs.mkdir(this.config.backupPath, { recursive: true });
      await fs.writeFile(backupFile, JSON.stringify(redisData, null, 2));
      
      this.logger.log(`Redis backup created: ${backupFile}`);

      // Also backup current PostgreSQL data
      const pgBackupFile = path.join(this.config.backupPath, `postgres-backup-${Date.now()}.json`);
      const pgData = await this.exportPostgresData();
      await fs.writeFile(pgBackupFile, JSON.stringify(pgData, null, 2));
      
      this.logger.log(`PostgreSQL backup created: ${pgBackupFile}`);
    } catch (error) {
      this.logger.error('Backup creation failed:', error);
      throw error;
    }
  }

  private async verifyConnections(): Promise<void> {
    this.logger.log('Verifying database connections...');

    // Test PostgreSQL
    const pgHealth = await this.postgresService.healthCheck();
    if (!pgHealth) {
      throw new Error('PostgreSQL connection failed');
    }

    // Test Redis
    const redisHealth = await this.redisService.healthCheck();
    if (!redisHealth.connected) {
      throw new Error('Redis connection failed');
    }

    this.logger.log('Database connections verified');
  }

  private async migrateUsers(): Promise<void> {
    if (this.config.skipTables.includes('users') || 
        (this.config.onlyTables.length > 0 && !this.config.onlyTables.includes('users'))) {
      this.logger.log('Skipping users table');
      return;
    }

    this.logger.log('Migrating users...');
    
    try {
      const users = await this.postgresService.query(`
        SELECT * FROM users 
        WHERE deleted_at IS NULL 
        ORDER BY created_at ASC
      `);

      let processedCount = 0;
      const batches = this.createBatches(users, this.config.batchSize);

      for (const batch of batches) {
        await this.processBatch('users', batch, async (user) => {
          const redisUser = {
            discordId: user.discord_id,
            username: user.username,
            email: user.email,
            isActive: user.is_active,
            lastActivityAt: user.last_activity_at,
            preferences: user.preferences || {},
            profile: user.profile || {},
            accessToken: user.access_token,
            refreshToken: user.refresh_token,
            tokenExpiresAt: user.token_expires_at,
            experience: user.experience || 0,
            balance: user.balance || 0,
            userId: user.user_id,
            discriminator: user.discriminator,
            avatarUrl: user.avatar_url,
            createdAt: user.created_at,
            updatedAt: user.updated_at,
          };

          if (!this.config.dryRun) {
            await this.redisService.users.createUser(redisUser);

            // Migrate user-specific data
            await this.migrateUserRelationships(user.discord_id);
            await this.migrateUserAchievements(user);
            await this.migrateUserSessions(user.discord_id);
          }

          processedCount++;
          if (processedCount % 100 === 0) {
            this.logger.log(`Processed ${processedCount}/${users.length} users`);
          }
        });
      }

      this.migrationStats.usersProcessed = processedCount;
      this.logger.log(`Migrated ${processedCount} users`);
    } catch (error) {
      this.logger.error('User migration failed:', error);
      this.migrationStats.errorsEncountered++;
      throw error;
    }
  }

  private async migrateGuilds(): Promise<void> {
    if (this.config.skipTables.includes('guilds') || 
        (this.config.onlyTables.length > 0 && !this.config.onlyTables.includes('guilds'))) {
      this.logger.log('Skipping guilds table');
      return;
    }

    this.logger.log('Migrating guilds...');
    
    try {
      const guilds = await this.postgresService.query(`
        SELECT * FROM guilds 
        WHERE deleted_at IS NULL 
        ORDER BY created_at ASC
      `);

      let processedCount = 0;
      const batches = this.createBatches(guilds, this.config.batchSize);

      for (const batch of batches) {
        await this.processBatch('guilds', batch, async (guild) => {
          const redisGuild = {
            discordId: guild.discord_id,
            name: guild.name,
            icon: guild.icon,
            isActive: guild.is_active,
            settings: guild.settings || {},
            features: guild.features || {},
            lastActivityAt: guild.last_activity_at,
            ownerDiscordId: guild.owner_discord_id,
            welcomeEnabled: guild.welcome_enabled,
            welcomeChannelId: guild.welcome_channel_id,
            welcomeMessage: guild.welcome_message,
            welcomeRoles: guild.welcome_roles || {},
            starboardEnabled: guild.starboard_enabled,
            starboardChannelId: guild.starboard_channel_id,
            starboardThreshold: guild.starboard_threshold,
            guildId: guild.guild_id,
            createdAt: guild.created_at,
            updatedAt: guild.updated_at,
          };

          if (!this.config.dryRun) {
            await this.redisService.guilds.createGuild(redisGuild);

            // Migrate guild members
            await this.migrateGuildMembers(guild.discord_id);
            
            // Migrate AI agent configs for this guild
            await this.migrateGuildAIConfigs(guild.discord_id);
          }

          processedCount++;
        });
      }

      this.migrationStats.guildsProcessed = processedCount;
      this.logger.log(`Migrated ${processedCount} guilds`);
    } catch (error) {
      this.logger.error('Guild migration failed:', error);
      this.migrationStats.errorsEncountered++;
      throw error;
    }
  }

  private async migrateSessions(): Promise<void> {
    if (this.config.skipTables.includes('sessions') || 
        (this.config.onlyTables.length > 0 && !this.config.onlyTables.includes('sessions'))) {
      this.logger.log('Skipping sessions table');
      return;
    }

    this.logger.log('Migrating sessions...');
    
    try {
      // Only migrate non-expired sessions
      const sessions = await this.postgresService.query(`
        SELECT * FROM sessions 
        WHERE expires_at > NOW() 
        AND is_revoked = false
        ORDER BY created_at ASC
      `);

      let processedCount = 0;

      for (const session of sessions) {
        try {
          if (!this.config.dryRun) {
            const ttl = Math.max(0, Math.floor((new Date(session.expires_at).getTime() - Date.now()) / 1000));
            
            if (ttl > 0) {
              await this.redisService.getRedis().hset(
                `session:${session.session_id}`,
                {
                  sessionId: session.session_id,
                  userId: session.user_id,
                  encryptedData: session.encrypted_data || '',
                  expiresAt: session.expires_at.toISOString(),
                  ipAddress: session.ip_address || '',
                  userAgent: session.user_agent || '',
                  deviceFingerprint: session.device_fingerprint || '',
                  isRevoked: session.is_revoked ? '1' : '0',
                  lastAccessedAt: session.last_accessed_at?.toISOString() || '',
                  metadata: JSON.stringify(session.metadata || {}),
                  createdAt: session.created_at.toISOString(),
                  updatedAt: session.updated_at.toISOString(),
                }
              );

              // Set TTL
              await this.redisService.getRedis().expire(`session:${session.session_id}`, ttl);

              // Add to user's active sessions
              await this.redisService.getRedis().sadd(
                `user:${session.user_id}:active_sessions`,
                `session:${session.session_id}`
              );
              await this.redisService.getRedis().expire(
                `user:${session.user_id}:active_sessions`,
                ttl
              );
            }
          }

          processedCount++;
        } catch (error) {
          this.logger.warn(`Failed to migrate session ${session.session_id}:`, error);
          this.migrationStats.errorsEncountered++;
        }
      }

      this.migrationStats.sessionsProcessed = processedCount;
      this.logger.log(`Migrated ${processedCount} sessions`);
    } catch (error) {
      this.logger.error('Session migration failed:', error);
      this.migrationStats.errorsEncountered++;
      throw error;
    }
  }

  private async migrateCommunityData(): Promise<void> {
    this.logger.log('Migrating community data...');
    
    try {
      await Promise.all([
        this.migrateCommunityEvents(),
        this.migrateLeaderboards(),
        this.migrateFeedback(),
      ]);
    } catch (error) {
      this.logger.error('Community data migration failed:', error);
      this.migrationStats.errorsEncountered++;
      throw error;
    }
  }

  private async migrateCommunityEvents(): Promise<void> {
    try {
      const events = await this.postgresService.query(`
        SELECT * FROM community_events 
        WHERE deleted_at IS NULL 
        ORDER BY start_date ASC
      `);

      for (const event of events) {
        if (!this.config.dryRun) {
          const eventKey = `event:${event.id}`;
          
          await this.redisService.getRedis().hset(eventKey, {
            id: event.id.toString(),
            title: event.title,
            description: event.description || '',
            startDate: event.start_date.toISOString(),
            endDate: event.end_date?.toISOString() || '',
            type: event.type,
            maxParticipants: event.max_participants?.toString() || '0',
            currentParticipants: event.current_participants?.toString() || '0',
            tags: JSON.stringify(event.tags || []),
            guildId: event.guild_id,
            organizerId: event.organizer_id,
            isActive: event.is_active ? '1' : '0',
            createdAt: event.created_at.toISOString(),
            updatedAt: event.updated_at.toISOString(),
          });

          // Add to guild events index
          await this.redisService.getRedis().zadd(
            `guild:${event.guild_id}:events`,
            new Date(event.start_date).getTime(),
            eventKey
          );

          // Migrate event participants
          await this.migrateEventParticipants(event.id);
        }
      }

      this.migrationStats.eventsProcessed += events.length;
      this.logger.log(`Migrated ${events.length} community events`);
    } catch (error) {
      this.logger.error('Community events migration failed:', error);
      throw error;
    }
  }

  private async migrateLeaderboards(): Promise<void> {
    try {
      const entries = await this.postgresService.query(`
        SELECT * FROM leaderboard_entries 
        WHERE deleted_at IS NULL
      `);

      for (const entry of entries) {
        if (!this.config.dryRun) {
          // Add to guild leaderboard
          await this.redisService.getRedis().zadd(
            `leaderboard:points:${entry.guild_id}`,
            entry.points,
            entry.user_id
          );

          if (entry.level) {
            await this.redisService.getRedis().zadd(
              `leaderboard:level:${entry.guild_id}`,
              entry.level,
              entry.user_id
            );
          }

          // Store detailed entry data
          await this.redisService.getRedis().hset(
            `leaderboard:user:${entry.user_id}:${entry.guild_id}`,
            {
              points: entry.points.toString(),
              level: entry.level?.toString() || '0',
              badges: JSON.stringify(entry.badges || []),
              monthlyRank: entry.monthly_rank?.toString() || '0',
              allTimeRank: entry.all_time_rank?.toString() || '0',
              lastUpdated: entry.last_updated?.toISOString() || new Date().toISOString(),
              createdAt: entry.created_at.toISOString(),
              updatedAt: entry.updated_at.toISOString(),
            }
          );
        }
      }

      this.logger.log(`Migrated ${entries.length} leaderboard entries`);
    } catch (error) {
      this.logger.error('Leaderboards migration failed:', error);
      throw error;
    }
  }

  private async migrateFeedback(): Promise<void> {
    try {
      const feedback = await this.postgresService.query(`
        SELECT * FROM community_feedback 
        WHERE deleted_at IS NULL
      `);

      for (const item of feedback) {
        if (!this.config.dryRun) {
          const feedbackKey = `feedback:${item.id}`;
          
          await this.redisService.getRedis().hset(feedbackKey, {
            id: item.id.toString(),
            title: item.title,
            description: item.description || '',
            category: item.category,
            status: item.status,
            votes: item.votes?.toString() || '0',
            userId: item.user_id,
            guildId: item.guild_id,
            createdAt: item.created_at.toISOString(),
            updatedAt: item.updated_at.toISOString(),
          });

          // Add to guild feedback index
          await this.redisService.getRedis().zadd(
            `guild:${item.guild_id}:feedback`,
            new Date(item.created_at).getTime(),
            feedbackKey
          );
        }
      }

      this.logger.log(`Migrated ${feedback.length} feedback items`);
    } catch (error) {
      this.logger.error('Feedback migration failed:', error);
      throw error;
    }
  }

  private async migrateAIConfigs(): Promise<void> {
    this.logger.log('Migrating AI configurations...');
    
    try {
      const configs = await this.postgresService.query(`
        SELECT * FROM ai_agent_configs 
        WHERE deleted_at IS NULL
      `);

      for (const config of configs) {
        if (!this.config.dryRun) {
          await this.redisService.getRedis().hset(
            `agent:config:${config.guild_id}`,
            {
              guildId: config.guild_id,
              model: config.model || 'gpt-3.5-turbo',
              systemPrompt: config.system_prompt || '',
              maxTokens: config.max_tokens?.toString() || '2000',
              temperature: config.temperature?.toString() || '0.7',
              isEnabled: config.is_enabled ? '1' : '0',
              createdAt: config.created_at.toISOString(),
              updatedAt: config.updated_at.toISOString(),
            }
          );
        }
      }

      this.logger.log(`Migrated ${configs.length} AI configurations`);
    } catch (error) {
      this.logger.error('AI configurations migration failed:', error);
      throw error;
    }
  }

  private async migrateChannelPanelData(): Promise<void> {
    this.logger.log('Migrating channel panel data...');
    
    try {
      // Migrate panel deployments, analytics, cleanup data, etc.
      await Promise.all([
        this.migratePanelDeployments(),
        this.migratePanelAnalytics(),
        this.migrateUserPanelStates(),
      ]);
    } catch (error) {
      this.logger.error('Channel panel data migration failed:', error);
      this.migrationStats.errorsEncountered++;
    }
  }

  private async migrateDevOnDemandData(): Promise<void> {
    this.logger.log('Migrating dev-on-demand data...');
    
    try {
      // This would migrate dev requests, tier system, etc.
      // Implementation depends on the current schema
      this.logger.log('Dev-on-demand data migration completed (placeholder)');
    } catch (error) {
      this.logger.error('Dev-on-demand data migration failed:', error);
      this.migrationStats.errorsEncountered++;
    }
  }

  // Helper methods
  private async migrateUserRelationships(userId: string): Promise<void> {
    try {
      const relationships = await this.postgresService.query(`
        SELECT * FROM user_relationships 
        WHERE user_id = $1 OR target_user_id = $1
      `, [userId]);

      for (const rel of relationships) {
        if (!this.config.dryRun) {
          await this.redisService.getRedis().hset(
            `user:${rel.user_id}:relationships`,
            rel.target_user_id,
            rel.relationship_type
          );
        }
      }
    } catch (error) {
      this.logger.warn(`Failed to migrate relationships for user ${userId}:`, error);
    }
  }

  private async migrateUserAchievements(user: any): Promise<void> {
    try {
      if (user.profile?.achievements) {
        for (const achievement of user.profile.achievements) {
          if (!this.config.dryRun) {
            await this.redisService.users.addAchievement(user.discord_id, achievement.id);
          }
        }
      }
    } catch (error) {
      this.logger.warn(`Failed to migrate achievements for user ${user.discord_id}:`, error);
    }
  }

  private async migrateUserSessions(userId: string): Promise<void> {
    // Sessions are handled in the main session migration
    return;
  }

  private async migrateGuildMembers(guildId: string): Promise<void> {
    try {
      // In a real implementation, this would query guild memberships
      // For now, we'll infer from existing user guild associations
      const users = await this.postgresService.query(`
        SELECT discord_id FROM users 
        WHERE preferences->>'guilds' LIKE '%${guildId}%'
      `);

      for (const user of users) {
        if (!this.config.dryRun) {
          await this.redisService.guilds.addMember(guildId, user.discord_id);
        }
      }
    } catch (error) {
      this.logger.warn(`Failed to migrate members for guild ${guildId}:`, error);
    }
  }

  private async migrateGuildAIConfigs(guildId: string): Promise<void> {
    // Handled in migrateAIConfigs
    return;
  }

  private async migrateEventParticipants(eventId: number): Promise<void> {
    try {
      const participants = await this.postgresService.query(`
        SELECT * FROM event_participants 
        WHERE event_id = $1
      `, [eventId]);

      for (const participant of participants) {
        if (!this.config.dryRun) {
          await this.redisService.getRedis().sadd(
            `event:${eventId}:participants`,
            participant.user_id
          );
        }
      }
    } catch (error) {
      this.logger.warn(`Failed to migrate participants for event ${eventId}:`, error);
    }
  }

  private async migratePanelDeployments(): Promise<void> {
    // Implementation for panel deployments migration
    this.logger.log('Panel deployments migration completed (placeholder)');
  }

  private async migratePanelAnalytics(): Promise<void> {
    // Implementation for panel analytics migration
    this.logger.log('Panel analytics migration completed (placeholder)');
  }

  private async migrateUserPanelStates(): Promise<void> {
    // Implementation for user panel states migration
    this.logger.log('User panel states migration completed (placeholder)');
  }

  // Utility methods
  private createBatches<T>(items: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }
    return batches;
  }

  private async processBatch<T>(
    entityName: string,
    batch: T[],
    processor: (item: T) => Promise<void>
  ): Promise<void> {
    const promises = batch.map(async (item, index) => {
      try {
        await processor(item);
      } catch (error) {
        this.logger.error(`Failed to process ${entityName} item ${index}:`, error);
        this.migrationStats.errorsEncountered++;
        throw error;
      }
    });

    await Promise.all(promises);
  }

  private async exportPostgresData(): Promise<any> {
    const tables = [
      'users', 'guilds', 'sessions', 'community_events', 
      'leaderboard_entries', 'community_feedback', 'ai_agent_configs'
    ];

    const exportData: any = {};
    
    for (const table of tables) {
      try {
        const data = await this.postgresService.query(`SELECT * FROM ${table} WHERE deleted_at IS NULL`);
        exportData[table] = data;
      } catch (error) {
        this.logger.warn(`Failed to export table ${table}:`, error);
      }
    }

    return exportData;
  }

  private async verifyMigration(): Promise<void> {
    this.logger.log('Verifying migration...');
    
    try {
      // Basic counts verification
      const pgUserCount = await this.postgresService.query(`
        SELECT COUNT(*) as count FROM users WHERE deleted_at IS NULL
      `);
      const redisUserCount = await this.redisService.users.count();

      this.logger.log(`PostgreSQL users: ${pgUserCount[0].count}, Redis users: ${redisUserCount}`);

      const pgGuildCount = await this.postgresService.query(`
        SELECT COUNT(*) as count FROM guilds WHERE deleted_at IS NULL
      `);
      const redisGuildCount = await this.redisService.guilds.count();

      this.logger.log(`PostgreSQL guilds: ${pgGuildCount[0].count}, Redis guilds: ${redisGuildCount}`);

      // Sample data verification
      const sampleUsers = await this.postgresService.query(`
        SELECT discord_id, username FROM users 
        WHERE deleted_at IS NULL 
        ORDER BY created_at DESC 
        LIMIT 5
      `);

      for (const user of sampleUsers) {
        const redisUser = await this.redisService.users.findByDiscordId(user.discord_id);
        if (!redisUser) {
          throw new Error(`User ${user.discord_id} not found in Redis`);
        }
        if (redisUser.username !== user.username) {
          throw new Error(`Username mismatch for user ${user.discord_id}`);
        }
      }

      this.logger.log('Migration verification completed successfully');
    } catch (error) {
      this.logger.error('Migration verification failed:', error);
      throw error;
    }
  }

  private async rollback(): Promise<void> {
    this.logger.log('Rolling back migration...');
    
    try {
      // Clear all migrated Redis data
      await this.redisService.getRedis().flushdb();
      
      this.logger.log('Rollback completed');
    } catch (error) {
      this.logger.error('Rollback failed:', error);
      throw error;
    }
  }

  private async generateReport(): Promise<void> {
    const duration = this.migrationStats.endTime - this.migrationStats.startTime;
    const durationMinutes = Math.round(duration / 60000);

    const report = {
      ...this.migrationStats,
      durationMs: duration,
      durationMinutes,
      success: this.migrationStats.errorsEncountered === 0,
      timestamp: new Date().toISOString(),
    };

    const reportFile = path.join(this.config.backupPath, `migration-report-${Date.now()}.json`);
    await fs.writeFile(reportFile, JSON.stringify(report, null, 2));

    this.logger.log(`Migration report saved: ${reportFile}`);
    this.logger.log(`Migration Statistics:
      - Users: ${this.migrationStats.usersProcessed}
      - Guilds: ${this.migrationStats.guildsProcessed}
      - Sessions: ${this.migrationStats.sessionsProcessed}
      - Events: ${this.migrationStats.eventsProcessed}
      - Errors: ${this.migrationStats.errorsEncountered}
      - Duration: ${durationMinutes} minutes
    `);
  }
}

// CLI execution
async function main() {
  const args = process.argv.slice(2);
  const config: MigrationConfig = {
    batchSize: parseInt(args.find(arg => arg.startsWith('--batch-size='))?.split('=')[1] || '50'),
    backupPath: args.find(arg => arg.startsWith('--backup-path='))?.split('=')[1] || './backups',
    dryRun: args.includes('--dry-run'),
    skipTables: args.find(arg => arg.startsWith('--skip-tables='))?.split('=')[1]?.split(',') || [],
    onlyTables: args.find(arg => arg.startsWith('--only-tables='))?.split('=')[1]?.split(',') || [],
  };

  const logger = new Logger('RedisMigrationCLI');
  
  try {
    const app = await NestFactory.createApplicationContext(AppModule);
    const postgresService = app.get(DatabaseService);
    
    // Create Redis service manually since it's not yet in the main app
    const configService = app.get('ConfigService');
    const redisService = new RedisDatabaseService(configService);
    await redisService.onModuleInit();

    const migrationService = new RedisMigrationService(
      postgresService,
      redisService,
      config
    );

    await migrationService.migrate();
    
    await app.close();
    logger.log('Migration process completed successfully!');
    process.exit(0);
  } catch (error) {
    logger.error('Migration process failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch(console.error);
}

export { RedisMigrationService };