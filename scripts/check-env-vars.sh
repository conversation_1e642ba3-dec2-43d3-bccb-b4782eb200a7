#!/bin/bash

# Environment Variables Checker for Sevalla Deployment
# This script checks which environment variables are missing

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔍 Environment Variables Checker for Sevalla${NC}"
echo ""

# Critical environment variables
CRITICAL_VARS=(
    "DISCORD_TOKEN"
    "BOT_CLIENT_ID"
    "BOT_CLIENT_SECRET"
    "DISCORD_CLIENT_ID"
    "DISCORD_CLIENT_SECRET"
    "DATABASE_URL"
    "NODE_ENV"
    "PORT"
    "WEB_URL"
    "USER_ENCRYPTION_KEY"
    "SESSION_ENCRYPTION_KEY"
    "CSRF_ENCRYPTION_KEY"
)

# Optional but recommended variables
OPTIONAL_VARS=(
    "APP_URL"
    "NEXT_PUBLIC_API_ENDPOINT"
    "INTERNAL_API_ENDPOINT"
    "ANTHROPIC_API_KEY"
    "USE_MASTRA"
    "DEFAULT_AI_CHANNEL"
    "ENABLE_ENV_LOGIN"
    "SESSION_ISOLATION_ENABLED"
    "SESSION_FINGERPRINTING_ENABLED"
    "AUTOMATIC_TOKEN_ROTATION"
)

missing_critical=0
missing_optional=0

echo -e "${RED}🔴 CRITICAL VARIABLES:${NC}"
for var in "${CRITICAL_VARS[@]}"; do
    if [[ -z "${!var:-}" ]]; then
        echo -e "❌ ${var} - ${RED}MISSING${NC}"
        ((missing_critical++))
    else
        echo -e "✅ ${var} - ${GREEN}SET${NC}"
    fi
done

echo ""
echo -e "${YELLOW}🟡 OPTIONAL VARIABLES:${NC}"
for var in "${OPTIONAL_VARS[@]}"; do
    if [[ -z "${!var:-}" ]]; then
        echo -e "⚠️  ${var} - ${YELLOW}NOT SET${NC}"
        ((missing_optional++))
    else
        echo -e "✅ ${var} - ${GREEN}SET${NC}"
    fi
done

echo ""
echo -e "${BLUE}📊 SUMMARY:${NC}"
echo -e "Critical variables missing: ${RED}${missing_critical}${NC}"
echo -e "Optional variables missing: ${YELLOW}${missing_optional}${NC}"

if [[ $missing_critical -eq 0 ]]; then
    echo -e "${GREEN}🎉 All critical environment variables are set!${NC}"
    echo -e "${GREEN}Your application should start successfully.${NC}"
else
    echo -e "${RED}❌ ${missing_critical} critical environment variables are missing.${NC}"
    echo -e "${RED}Your application will fail to start.${NC}"
    echo ""
    echo -e "${BLUE}🔧 TO FIX:${NC}"
    echo "1. Go to your Sevalla dashboard"
    echo "2. Navigate to your backend project settings"
    echo "3. Add the missing environment variables"
    echo "4. Redeploy your application"
fi

echo ""
echo -e "${BLUE}💡 TIP:${NC} Copy the values from your local .env file to Sevalla dashboard"

exit $missing_critical
