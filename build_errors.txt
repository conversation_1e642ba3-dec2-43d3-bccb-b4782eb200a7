ERROR in ./src/agents/agents.service.ts:4:5
TS2305: Module '"@/core/database"' has no exported member 'AIAgentConfig'.

ERROR in ./src/agents/agents.service.ts:6:5
TS2305: Module '"@/core/database"' has no exported member 'AgentType'.

ERROR in ./src/agents/agents.service.ts:7:5
TS2305: Module '"@/core/database"' has no exported member 'InteractionType'.

ERROR in ./src/agents/agents.service.ts:8:5
TS2305: Module '"@/core/database"' has no exported member 'MemoryType'.

ERROR in ./src/agents/agents.service.ts:9:5
TS2724: '"@/core/database"' has no exported member named 'NewAgentMem<PERSON>'. Did you mean 'AgentMemory'?

ERROR in ./src/agents/types/intake-specialist.ts:271:21
TS2339: Property 'value' does not exist on type 'AgentMemory'.

ERROR in ./src/agents/types/intake-specialist.ts:333:37
TS2339: Property 'value' does not exist on type 'AgentMemory'.

ERROR in ./src/agents/types/personal-growth-coach.ts:223:35
TS2339: Property 'value' does not exist on type 'AgentMemory'.

ERROR in ./src/agents/types/personal-growth-coach.ts:224:35
TS2339: Property 'value' does not exist on type 'AgentMemory'.

ERROR in ./src/agents/types/personal-growth-coach.ts:225:35
TS2339: Property 'value' does not exist on type 'AgentMemory'.

ERROR in ./src/agents/types/progress-tracker.ts:293:23
TS2339: Property 'value' does not exist on type 'AgentMemory'.

ERROR in ./src/agents/types/progress-tracker.ts:294:35
TS2339: Property 'value' does not exist on type 'AgentMemory'.

ERROR in ./src/agents/types/progress-tracker.ts:295:32
TS2339: Property 'value' does not exist on type 'AgentMemory'.

ERROR in ./src/agents/types/progress-tracker.ts:296:31
TS2339: Property 'value' does not exist on type 'AgentMemory'.

ERROR in ./src/agents/types/progress-tracker.ts:297:35
TS2339: Property 'value' does not exist on type 'AgentMemory'.

ERROR in ./src/agents/types/progress-tracker.ts:298:36
TS2339: Property 'value' does not exist on type 'AgentMemory'.

ERROR in ./src/agents/types/progress-tracker.ts:299:25
TS2339: Property 'value' does not exist on type 'AgentMemory'.

ERROR in ./src/agents/types/progress-tracker.ts:344:26
TS2339: Property 'value' does not exist on type 'AgentMemory'.

ERROR in ./src/api/admin/admin.service.ts:3:10
TS2305: Module '"@/core/database"' has no exported member 'User'.

ERROR in ./src/api/admin/admin.service.ts:3:16
TS2305: Module '"@/core/database"' has no exported member 'Session'.

ERROR in ./src/api/admin/admin.service.ts:3:25
TS2305: Module '"@/core/database"' has no exported member 'Guild'.

ERROR in ./src/api/guilds/guilds.service.ts:3:10
TS2305: Module '"@/core/database"' has no exported member 'NewGuild'.

ERROR in ./src/core/agents/services/private-memory.service.ts:405:28
TS2769: No overload matches this call.

ERROR in ./src/core/agents/services/private-memory.service.ts:411:37
TS2769: No overload matches this call.

ERROR in ./src/core/agents/services/private-memory.service.ts:412:5
TS2322: Type 'string' is not assignable to type 'Buffer<ArrayBufferLike> & string'.

ERROR in ./src/core/database/query-builder.ts:306:7
TS2412: Type 'undefined' is not assignable to type 'number' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the type of the target.

ERROR in ./src/core/database/query-builder.ts:438:5
TS4104: The type 'readonly T[]' is 'readonly' and cannot be assigned to the mutable type 'T[]'.

ERROR in ./src/core/database/query-builder.ts:515:5
TS2412: Type 'undefined' is not assignable to type 'number' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the type of the target.

ERROR in ./src/core/database/query-builder.ts:516:5
TS2412: Type 'undefined' is not assignable to type 'number' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the type of the target.

ERROR in ./src/core/database/redis-database.service.ts:195:37
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/core/database/redis-database.service.ts:198:28
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/core/database/redis-database.service.ts:199:27
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/core/database/redis-database.service.ts:201:26
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/core/database/redis-database.service.ts:236:27
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/core/database/redis-database.service.ts:237:28
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/core/database/redis-database.service.ts:238:29
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/core/database/redis-database.service.ts:248:31
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/core/database/redis-database.service.ts:249:31
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/core/database/redis-database.service.ts:249:69
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/core/database/redis-database.service.ts:252:28
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/core/database/redis-database.service.ts:253:26
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/core/database/redis-database.service.ts:254:29
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/core/database/redis-entity-manager.service.ts:371:22
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/database/redis-entity-manager.service.ts:397:9
TS2322: Type 'string | undefined' is not assignable to type 'string'.

ERROR in ./src/core/database/redis-entity-manager.service.ts:401:9
TS2322: Type 'string | undefined' is not assignable to type 'string'.

ERROR in ./src/core/database/redis-entity-manager.service.ts:505:40
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/database/redis.service.ts:120:53
TS2345: Argument of type 'T' is not assignable to parameter of type 'Record<string, unknown>'.

ERROR in ./src/core/database/redis.service.ts:172:53
TS2345: Argument of type 'T' is not assignable to parameter of type 'Record<string, unknown>'.

ERROR in ./src/core/database/redis.service.ts:219:7
TS4104: The type 'readonly string[]' is 'readonly' and cannot be assigned to the mutable type 'string[]'.

ERROR in ./src/core/database/redis.service.ts:224:7
TS4104: The type 'readonly string[]' is 'readonly' and cannot be assigned to the mutable type 'string[]'.

ERROR in ./src/core/database/redis.service.ts:224:66
TS2345: Argument of type '{ readonly field: string; readonly direction: "ASC" | "DESC"; }' is not assignable to parameter of type '{ readonly field: keyof BaseEntity; readonly direction: "ASC" | "DESC"; }'.

ERROR in ./src/core/database/redis.service.ts:279:7
TS2740: Type 'ChainableCommander' is missing the following properties from type 'Pipeline': redis, isCluster, isPipeline, leftRedirections, and 19 more.

ERROR in ./src/core/database/redis.service.ts:411:36
TS2536: Type 'keyof T' cannot be used to index type 'BaseEntity'.

ERROR in ./src/core/database/redis.service.ts:504:29
TS2556: A spread argument must either have a tuple type or be passed to a rest parameter.

ERROR in ./src/core/database/redis/base-redis.repository.ts:84:12
TS2352: Conversion of type 'Omit<T, "id"> & { id: string; createdAt: string | NonNullable<T[string]>; updatedAt: string | NonNullable<T[string]>; }' to type 'T' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.

ERROR in ./src/core/database/redis/base-redis.repository.ts:113:33
TS2345: Argument of type '{}' is not assignable to parameter of type 'Record<string, string>'.

ERROR in ./src/core/database/redis/base-redis.repository.ts:283:29
TS2345: Argument of type 'ChainableCommander' is not assignable to parameter of type 'Redis'.

ERROR in ./src/core/database/redis/redis-database.service.ts:12:11
TS2564: Property 'redis' has no initializer and is not definitely assigned in the constructor.

ERROR in ./src/core/database/redis/redis-database.service.ts:23:47
TS2565: Property 'redis' is used before being assigned.

ERROR in ./src/core/database/redis/redis-database.service.ts:24:49
TS2565: Property 'redis' is used before being assigned.

ERROR in ./src/core/database/redis/redis-database.service.ts:53:24
TS2769: No overload matches this call.

ERROR in ./src/core/database/redis/redis-database.service.ts:58:9
TS1117: An object literal cannot have multiple properties with the same name.

ERROR in ./src/core/database/redis/redis-database.service.ts:98:36
TS7006: Parameter 'ms' implicitly has an 'any' type.

ERROR in ./src/core/database/redis/redis-database.service.ts:163:37
TS2345: Argument of type 'ChainableCommander' is not assignable to parameter of type 'Redis'.

ERROR in ./src/core/database/redis/redis-database.service.ts:177:37
TS2345: Argument of type 'ChainableCommander' is not assignable to parameter of type 'Redis'.

ERROR in ./src/core/database/redis/redis-database.service.ts:194:5
TS2322: Type 'unknown[]' is not assignable to type '(string | null)[]'.

ERROR in ./src/core/database/redis/redis-database.service.ts:372:26
TS2769: No overload matches this call.

ERROR in ./src/core/database/redis/redis-database.service.ts:384:7
TS2322: Type 'unknown' is not assignable to type 'any[]'.

ERROR in ./src/core/database/redis/redis-database.service.ts:397:27
TS2532: Object is possibly 'undefined'.

ERROR in ./src/core/database/redis/redis-database.service.ts:404:15
TS18048: 'line' is possibly 'undefined'.

ERROR in ./src/core/database/redis/redis-database.service.ts:405:34
TS18048: 'line' is possibly 'undefined'.

ERROR in ./src/core/database/redis/redis-database.service.ts:406:33
TS2538: Type 'undefined' cannot be used as an index type.

ERROR in ./src/core/database/redis/redis-database.service.ts:422:25
TS18048: 'cmd' is possibly 'undefined'.

ERROR in ./src/core/database/redis/redis-database.service.ts:424:25
TS18048: 'statsStr' is possibly 'undefined'.

ERROR in ./src/core/database/redis/redis-database.service.ts:427:29
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/core/database/redis/redis-database.service.ts:428:28
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/core/database/redis/redis-database.service.ts:429:39
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/core/database/redis/redis-database.service.ts:445:25
TS18048: 'info' is possibly 'undefined'.

ERROR in ./src/core/database/redis/redis-database.service.ts:448:18
TS2538: Type 'undefined' cannot be used as an index type.

ERROR in ./src/core/database/redis/redis-database.service.ts:449:28
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/core/database/redis/redis-database.service.ts:450:31
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/core/database/redis/redis-database.service.ts:451:31
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/core/database/redis/redis-database.service.ts:597:33
TS2769: No overload matches this call.

ERROR in ./src/core/database/redis/redis-monitoring.service.ts:86:74
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/database/redis/redis-monitoring.service.ts:113:47
TS2304: Cannot find name 'statsInfo'.

ERROR in ./src/core/database/redis/redis-monitoring.service.ts:254:30
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/core/database/redis/redis-monitoring.service.ts:255:18
TS2532: Object is possibly 'undefined'.

ERROR in ./src/core/database/redis/redis-monitoring.service.ts:356:21
TS2769: No overload matches this call.

ERROR in ./src/core/database/redis/redis-monitoring.service.ts:357:20
TS2769: No overload matches this call.

ERROR in ./src/core/database/redis/redis-monitoring.service.ts:358:20
TS2769: No overload matches this call.

ERROR in ./src/core/database/redis/redis-monitoring.service.ts:363:7
TS2322: Type 'unknown' is not assignable to type 'any[]'.

ERROR in ./src/core/database/redis/redis-monitoring.service.ts:377:25
TS2769: No overload matches this call.

ERROR in ./src/core/database/redis/redis.module.ts:30:27
TS2769: No overload matches this call.

ERROR in ./src/core/database/redis/redis.module.ts:35:11
TS1117: An object literal cannot have multiple properties with the same name.

ERROR in ./src/core/database/redis/repositories/guild-redis.repository.ts:7:18
TS2430: Interface 'RedisGuild' incorrectly extends interface 'Omit<Guild, "id">'.

ERROR in ./src/core/database/redis/repositories/guild-redis.repository.ts:17:13
TS2375: Type '{ keyPrefix: string; indexes: string[]; relationships: { ownerDiscordId: string; }; ttl: undefined; }' is not assignable to type 'RedisEntityConfig' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/database/redis/repositories/guild-redis.repository.ts:31:41
TS2379: Argument of type '{ id: undefined; isActive: boolean; lastActivityAt: Date; settings: any; features: GuildFeatures; name: string; deletedAt?: Date | null; discordId: string; metadata?: Record<...>; ... 11 more ...; __create?: never; }' is not assignable to parameter of type 'Omit<RedisGuild, "id"> & { id?: string; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/database/redis/repositories/guild-redis.repository.ts:36:27
TS2339: Property 'settings' does not exist on type 'CreateGuild'.

ERROR in ./src/core/database/redis/repositories/guild-redis.repository.ts:158:5
TS2322: Type 'false | { enabled: boolean; tiers: { id: string; name: string; roleIds: string[]; permissions: string[]; priority: number; }[]; defaultTier?: string; autoAssign?: boolean; logChannel?: string; restrictedChannels?: string[]; } | ... 9 more ... | { ...; }' is not assignable to type 'boolean'.

ERROR in ./src/core/database/redis/repositories/guild-redis.repository.ts:181:7
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string | Buffer<ArrayBufferLike>'.

ERROR in ./src/core/database/redis/repositories/guild-redis.repository.ts:202:14
TS2538: Type 'undefined' cannot be used as an index type.

ERROR in ./src/core/database/redis/repositories/guild-redis.repository.ts:202:43
TS2538: Type 'undefined' cannot be used as an index type.

ERROR in ./src/core/database/redis/repositories/guild-redis.repository.ts:397:12
TS2352: Conversion of type '{ prefix: string; language: string; timezone: string; moderation: { automod: boolean; logChannel: null; muteRole: null; }; welcome: { enabled: boolean; channel: null; message: string; }; leveling: { enabled: true; rewards: never[]; }; }' to type 'GuildSettings' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.

ERROR in ./src/core/database/redis/repositories/guild-redis.repository.ts:419:12
TS2352: Conversion of type '{ aiAgents: boolean; starboard: boolean; automod: boolean; leveling: boolean; music: boolean; tickets: boolean; polls: boolean; giveaways: boolean; }' to type 'GuildFeatures' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.

ERROR in ./src/core/database/redis/repositories/guild-redis.repository.ts:436:53
TS2769: No overload matches this call.

ERROR in ./src/core/database/redis/repositories/guild-redis.repository.ts:452:7
TS2322: Type 'string' is not assignable to type 'Date'.

ERROR in ./src/core/database/redis/repositories/guild-redis.repository.ts:461:7
TS2322: Type 'string' is not assignable to type 'Date'.

ERROR in ./src/core/database/redis/repositories/guild-redis.repository.ts:462:7
TS2322: Type 'string' is not assignable to type 'Date'.

ERROR in ./src/core/database/redis/repositories/user-redis.repository.ts:4:16
TS2305: Module '"../../entities/user.entity"' has no exported member 'NewUser'.

ERROR in ./src/core/database/redis/repositories/user-redis.repository.ts:13:13
TS2375: Type '{ keyPrefix: string; indexes: string[]; relationships: {}; ttl: undefined; }' is not assignable to type 'RedisEntityConfig' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/database/redis/repositories/user-redis.repository.ts:223:9
TS2322: Type 'string | undefined' is not assignable to type 'string'.

ERROR in ./src/core/database/redis/repositories/user-redis.repository.ts:224:25
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/core/database/redis/repositories/user-redis.repository.ts:325:7
TS2322: Type 'string' is not assignable to type 'Date'.

ERROR in ./src/core/database/redis/repositories/user-redis.repository.ts:341:7
TS2322: Type 'string' is not assignable to type 'Date'.

ERROR in ./src/core/database/redis/repositories/user-redis.repository.ts:342:7
TS2322: Type 'string' is not assignable to type 'Date'.

ERROR in ./src/core/database/repositories/agent.repository.ts:4:10
TS2305: Module '"../types"' has no exported member 'RepositoryOptions'.

ERROR in ./src/core/database/repositories/agent.repository.ts:74:7
TS4104: The type 'readonly Agent[]' is 'readonly' and cannot be assigned to the mutable type 'Agent[]'.

ERROR in ./src/core/database/repositories/agent.repository.ts:86:7
TS4104: The type 'readonly Agent[]' is 'readonly' and cannot be assigned to the mutable type 'Agent[]'.

ERROR in ./src/core/database/repositories/agent.repository.ts:98:7
TS4104: The type 'readonly Agent[]' is 'readonly' and cannot be assigned to the mutable type 'Agent[]'.

ERROR in ./src/core/database/repositories/agent.repository.ts:404:36
TS18048: 'key' is possibly 'undefined'.

ERROR in ./src/core/database/repositories/agent.repository.ts:409:29
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/core/database/repositories/base-redis.repository.ts:4:3
TS2305: Module '"../types"' has no exported member 'BaseEntity'.

ERROR in ./src/core/database/repositories/base-redis.repository.ts:5:3
TS2305: Module '"../types"' has no exported member 'CreateEntity'.

ERROR in ./src/core/database/repositories/base-redis.repository.ts:6:3
TS2305: Module '"../types"' has no exported member 'UpdateEntity'.

ERROR in ./src/core/database/repositories/base-redis.repository.ts:7:3
TS2305: Module '"../types"' has no exported member 'BaseRedisRepository'.

ERROR in ./src/core/database/repositories/base-redis.repository.ts:8:3
TS2305: Module '"../types"' has no exported member 'RedisOperationResult'.

ERROR in ./src/core/database/repositories/base-redis.repository.ts:9:3
TS2305: Module '"../types"' has no exported member 'RedisSearchOptions'.

ERROR in ./src/core/database/repositories/base-redis.repository.ts:10:3
TS2305: Module '"../types"' has no exported member 'RedisTransaction'.

ERROR in ./src/core/database/repositories/base-redis.repository.ts:11:3
TS2305: Module '"../types"' has no exported member 'EntityKeyGenerator'.

ERROR in ./src/core/database/repositories/base-redis.repository.ts:12:3
TS2305: Module '"../types"' has no exported member 'RepositoryOptions'.

ERROR in ./src/core/database/repositories/base-redis.repository.ts:13:3
TS2305: Module '"../types"' has no exported member 'RedisError'.

ERROR in ./src/core/database/repositories/base-redis.repository.ts:14:3
TS2305: Module '"../types"' has no exported member 'RedisSerializer'.

ERROR in ./src/core/database/repositories/base-redis.repository.ts:53:60
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/database/repositories/base-redis.repository.ts:101:67
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/database/repositories/base-redis.repository.ts:135:65
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/database/repositories/base-redis.repository.ts:177:56
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/database/repositories/base-redis.repository.ts:217:58
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/database/repositories/base-redis.repository.ts:255:56
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/database/repositories/base-redis.repository.ts:294:69
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/database/repositories/base-redis.repository.ts:334:56
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/database/repositories/base-redis.repository.ts:360:56
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/database/repositories/base-redis.repository.ts:377:61
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/database/repositories/base-redis.repository.ts:402:57
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/database/repositories/base-redis.repository.ts:422:69
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/database/repositories/base-redis.repository.ts:436:65
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/database/repositories/base-redis.repository.ts:450:57
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/database/repositories/base-redis.repository.ts:469:49
TS7006: Parameter 'field' implicitly has an 'any' type.

ERROR in ./src/core/database/repositories/base-redis.repository.ts:479:58
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/database/repositories/base.repository.ts:149:14
TS2352: Conversion of type '{ [x: string]: T[K]; }' to type 'Partial<T>' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.

ERROR in ./src/core/database/repositories/guild.repository.ts:34:5
TS4104: The type 'readonly Guild[]' is 'readonly' and cannot be assigned to the mutable type 'Guild[]'.

ERROR in ./src/core/database/repositories/guild.repository.ts:43:5
TS4104: The type 'readonly Guild[]' is 'readonly' and cannot be assigned to the mutable type 'Guild[]'.

ERROR in ./src/core/database/repositories/guild.repository.ts:52:5
TS4104: The type 'readonly Guild[]' is 'readonly' and cannot be assigned to the mutable type 'Guild[]'.

ERROR in ./src/core/database/repositories/guild.repository.ts:61:5
TS4104: The type 'readonly Guild[]' is 'readonly' and cannot be assigned to the mutable type 'Guild[]'.

ERROR in ./src/core/database/repositories/guild.repository.ts:70:24
TS2345: Argument of type 'string | number' is not assignable to parameter of type 'string'.

ERROR in ./src/core/database/repositories/guild.repository.ts:79:39
TS2345: Argument of type 'string | number' is not assignable to parameter of type 'string'.

ERROR in ./src/core/database/repositories/guild.repository.ts:83:16
TS2339: Property 'settings' does not exist on type 'Guild'.

ERROR in ./src/core/database/repositories/guild.repository.ts:87:24
TS2345: Argument of type 'string | number' is not assignable to parameter of type 'string'.

ERROR in ./src/core/database/repositories/guild.repository.ts:94:39
TS2345: Argument of type 'string | number' is not assignable to parameter of type 'string'.

ERROR in ./src/core/database/repositories/guild.repository.ts:102:24
TS2345: Argument of type 'string | number' is not assignable to parameter of type 'string'.

ERROR in ./src/core/database/repositories/guild.repository.ts:121:24
TS2345: Argument of type 'string | number' is not assignable to parameter of type 'string'.

ERROR in ./src/core/database/repositories/guild.repository.ts:138:24
TS2345: Argument of type 'string | number' is not assignable to parameter of type 'string'.

ERROR in ./src/core/database/repositories/index.ts:45:53
TS2694: Namespace '"/home/<USER>/Discordbot-EnergeX/src/core/database/types"' has no exported member 'BaseEntity'.

ERROR in ./src/core/database/repositories/index.ts:48:6
TS2304: Cannot find name 'EntityKeyGenerator'.

ERROR in ./src/core/database/repositories/index.ts:51:22
TS2304: Cannot find name 'EntityKeyGenerator'.

ERROR in ./src/core/database/repositories/index.ts:56:17
TS7006: Parameter 'field' implicitly has an 'any' type.

ERROR in ./src/core/database/repositories/index.ts:56:24
TS7006: Parameter 'value' implicitly has an 'any' type.

ERROR in ./src/core/database/repositories/index.ts:72:45
TS2552: Cannot find name 'RepositoryOptions'. Did you mean 'PositionOptions'?

ERROR in ./src/core/database/repositories/index.ts:72:71
TS2552: Cannot find name 'RepositoryOptions'. Did you mean 'PositionOptions'?

ERROR in ./src/core/database/repositories/index.ts:88:30
TS2552: Cannot find name 'RepositoryOptions'. Did you mean 'PositionOptions'?

ERROR in ./src/core/database/repositories/index.ts:105:48
TS2304: Cannot find name 'RepositoryFactory'.

ERROR in ./src/core/database/repositories/index.ts:106:32
TS2300: Duplicate identifier 'redis'.

ERROR in ./src/core/database/repositories/index.ts:111:39
TS2694: Namespace '"/home/<USER>/Discordbot-EnergeX/src/core/database/types"' has no exported member 'BaseEntity'.

ERROR in ./src/core/database/repositories/index.ts:113:19
TS2304: Cannot find name 'EntityKeyGenerator'.

ERROR in ./src/core/database/repositories/index.ts:114:14
TS2552: Cannot find name 'RepositoryOptions'. Did you mean 'PositionOptions'?

ERROR in ./src/core/database/repositories/index.ts:115:6
TS2304: Cannot find name 'BaseRedisRepository'.

ERROR in ./src/core/database/repositories/index.ts:116:65
TS2339: Property 'BaseRedisRepositoryImpl' does not exist on type 'Promise<typeof import("/home/<USER>/Discordbot-EnergeX/src/core/database/repositories/base-redis.repository")>'.

ERROR in ./src/core/database/repositories/index.ts:118:15
TS2304: Cannot find name 'redis'.

ERROR in ./src/core/database/repositories/index.ts:126:47
TS2694: Namespace '"/home/<USER>/Discordbot-EnergeX/src/core/database/types"' has no exported member 'BaseEntity'.

ERROR in ./src/core/database/repositories/index.ts:128:19
TS2304: Cannot find name 'EntityKeyGenerator'.

ERROR in ./src/core/database/repositories/index.ts:129:14
TS2552: Cannot find name 'RepositoryOptions'. Did you mean 'PositionOptions'?

ERROR in ./src/core/database/repositories/index.ts:130:6
TS2304: Cannot find name 'ExtendedRedisRepository'.

ERROR in ./src/core/database/repositories/index.ts:135:15
TS2300: Duplicate identifier 'redis'.

ERROR in ./src/core/database/repositories/session.repository.ts:25:5
TS4104: The type 'readonly Session[]' is 'readonly' and cannot be assigned to the mutable type 'Session[]'.

ERROR in ./src/core/database/repositories/session.repository.ts:105:7
TS2322: Type 'string' is not assignable to type 'Date'.

ERROR in ./src/core/database/repositories/session.repository.ts:157:7
TS2322: Type 'string' is not assignable to type 'Date'.

ERROR in ./src/core/database/repositories/session.repository.ts:165:5
TS4104: The type 'readonly Session[]' is 'readonly' and cannot be assigned to the mutable type 'Session[]'.

ERROR in ./src/core/database/repositories/session.repository.ts:174:5
TS4104: The type 'readonly Session[]' is 'readonly' and cannot be assigned to the mutable type 'Session[]'.

ERROR in ./src/core/database/repositories/user.repository.ts:5:10
TS2305: Module '"../types"' has no exported member 'RepositoryOptions'.

ERROR in ./src/core/database/repositories/user.repository.ts:35:7
TS2322: Type 'User | null | undefined' is not assignable to type 'User | null'.

ERROR in ./src/core/database/repositories/user.repository.ts:48:7
TS2322: Type 'User | null | undefined' is not assignable to type 'User | null'.

ERROR in ./src/core/database/repositories/user.repository.ts:61:7
TS2322: Type 'User | null | undefined' is not assignable to type 'User | null'.

ERROR in ./src/core/database/repositories/user.repository.ts:207:5
TS4104: The type 'readonly User[]' is 'readonly' and cannot be assigned to the mutable type 'User[]'.

ERROR in ./src/core/database/repositories/user.repository.ts:261:10
TS2339: Property 'sort' does not exist on type 'readonly User[]'.

ERROR in ./src/core/database/repositories/user.repository.ts:261:16
TS7006: Parameter 'a' implicitly has an 'any' type.

ERROR in ./src/core/database/repositories/user.repository.ts:261:19
TS7006: Parameter 'b' implicitly has an 'any' type.

ERROR in ./src/core/database/repositories/user.repository.ts:276:10
TS2339: Property 'sort' does not exist on type 'readonly User[]'.

ERROR in ./src/core/database/repositories/user.repository.ts:276:16
TS7006: Parameter 'a' implicitly has an 'any' type.

ERROR in ./src/core/database/repositories/user.repository.ts:276:19
TS7006: Parameter 'b' implicitly has an 'any' type.

ERROR in ./src/core/database/schema.ts:47:1
TS2308: Module './entities/agent-memory.entity' has already exported a member named 'MemoryValue'. Consider explicitly re-exporting to resolve the ambiguity.

ERROR in ./src/core/database/schema.ts:47:1
TS2308: Module './entities/ai-agent-config.entity' has already exported a member named 'AgentConfiguration'. Consider explicitly re-exporting to resolve the ambiguity.

ERROR in ./src/core/database/schema.ts:47:1
TS2308: Module './entities/ai-agent-config.entity' has already exported a member named 'AgentPermissions'. Consider explicitly re-exporting to resolve the ambiguity.

ERROR in ./src/core/database/schema.ts:47:1
TS2308: Module './entities/guild.entity' has already exported a member named 'GuildFeatures'. Consider explicitly re-exporting to resolve the ambiguity.

ERROR in ./src/core/database/schema.ts:47:1
TS2308: Module './entities/guild.entity' has already exported a member named 'GuildSettings'. Consider explicitly re-exporting to resolve the ambiguity.

ERROR in ./src/core/database/schema.ts:47:1
TS2308: Module './entities/guild.entity' has already exported a member named 'WelcomeRoles'. Consider explicitly re-exporting to resolve the ambiguity.

ERROR in ./src/core/database/schema.ts:47:1
TS2308: Module './entities/session.entity' has already exported a member named 'SessionMetadata'. Consider explicitly re-exporting to resolve the ambiguity.

ERROR in ./src/core/database/schema.ts:47:1
TS2308: Module './entities/user.entity' has already exported a member named 'UserPreferences'. Consider explicitly re-exporting to resolve the ambiguity.

ERROR in ./src/core/database/schema.ts:47:1
TS2308: Module './entities/user.entity' has already exported a member named 'UserProfile'. Consider explicitly re-exporting to resolve the ambiguity.

ERROR in ./src/core/database/types/index.ts:90:52
TS2304: Cannot find name 'BaseEntity'.

ERROR in ./src/core/database/types/index.ts:103:35
TS2304: Cannot find name 'BaseEntity'.

ERROR in ./src/core/database/types/index.ts:155:31
TS2304: Cannot find name 'BaseEntity'.

ERROR in ./src/core/database/types/index.ts:155:44
TS2304: Cannot find name 'BaseEntity'.

ERROR in ./src/core/database/utils/redis-migration.util.ts:143:5
TS2322: Type '{ version: string; description: string | undefined; executedAt: string | undefined; }[]' is not assignable to type '{ version: string; description: string; executedAt: string; }[]'.

ERROR in ./src/core/database/utils/redis-migration.util.ts:308:7
TS2322: Type 'string | undefined' is not assignable to type 'string'.

ERROR in ./src/core/database/utils/redis-migration.util.ts:372:7
TS2322: Type '{ key: string; issue: string; severity: string; }[]' is not assignable to type '{ key: string; issue: string; severity: "medium" | "low" | "high"; }[]'.

ERROR in ./src/core/monitoring/alerting.service.ts:30:11
TS2375: Type '{ id: string; level: "error" | "info" | "critical" | "warning"; title: string; message: string; source: string; timestamp: Date; metadata: Record<string, any> | undefined; resolved: false; }' is not assignable to type 'Alert' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/monitoring/health.service.ts:43:34
TS2339: Property 'query' does not exist on type 'DatabaseService'.

ERROR in ./src/core/monitoring/health.service.ts:55:56
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/monitoring/health.service.ts:98:20
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/monitoring/health.service.ts:117:22
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/monitoring/health.service.ts:149:20
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/monitoring/health.service.ts:193:27
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/monitoring/health.service.ts:227:27
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/monitoring/health.service.ts:247:27
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/monitoring/interceptors/audit.interceptor.ts:52:11
TS2375: Type '{ id: string; timestamp: Date; userId: any; action: string; resource: string | undefined; endpoint: any; method: any; ip: any; userAgent: any; }' is not assignable to type 'Partial<AuditEntry>' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/monitoring/interceptors/metrics.interceptor.ts:90:12
TS18048: 'path' is possibly 'undefined'.

ERROR in ./src/core/monitoring/interceptors/performance.interceptor.ts:117:12
TS18048: 'path' is possibly 'undefined'.

ERROR in ./src/core/monitoring/logging.service.ts:278:13
TS2375: Type '{ timestamp: string; level: LogLevel; message: string; context: string | undefined; }' is not assignable to type 'StructuredLog' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/monitoring/performance.service.ts:35:11
TS2375: Type '{ name: string; value: number; unit: string; timestamp: Date; tags: Record<string, string> | undefined; }' is not assignable to type 'PerformanceMetric' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/monitoring/tracing.service.ts:24:11
TS2375: Type '{ traceId: string; spanId: string; operation: string; startTime: number; metadata: Record<string, any> | undefined; }' is not assignable to type 'TraceContext' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/monitoring/tracing.service.ts:45:11
TS2375: Type '{ traceId: string; spanId: string; parentSpanId: string; operation: string; startTime: number; metadata: Record<string, any> | undefined; }' is not assignable to type 'TraceContext' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/multi-tenancy/organization.service.ts:17:10
TS2305: Module '"@/core/database"' has no exported member 'organizations'.

ERROR in ./src/core/multi-tenancy/organization.service.ts:91:41
TS2339: Property 'db' does not exist on type 'OrganizationService'.

ERROR in ./src/core/multi-tenancy/organization.service.ts:128:41
TS2339: Property 'db' does not exist on type 'OrganizationService'.

ERROR in ./src/core/multi-tenancy/organization.service.ts:156:41
TS2339: Property 'db' does not exist on type 'OrganizationService'.

ERROR in ./src/core/multi-tenancy/organization.service.ts:195:36
TS2339: Property 'db' does not exist on type 'OrganizationService'.

ERROR in ./src/core/multi-tenancy/organization.service.ts:245:18
TS2339: Property 'db' does not exist on type 'OrganizationService'.

ERROR in ./src/core/multi-tenancy/organization.service.ts:281:24
TS2339: Property 'db' does not exist on type 'OrganizationService'.

ERROR in ./src/core/multi-tenancy/organization.service.ts:282:29
TS2339: Property 'db' does not exist on type 'OrganizationService'.

ERROR in ./src/core/multi-tenancy/organization.service.ts:311:32
TS2339: Property 'db' does not exist on type 'OrganizationService'.

ERROR in ./src/core/multi-tenancy/organization.service.ts:317:21
TS7006: Parameter 'stat' implicitly has an 'any' type.

ERROR in ./src/core/multi-tenancy/organization.service.ts:344:5
TS2375: Type '{ planId: OrganizationTier; status: "active" | "trialing"; currentPeriodStart: Date; currentPeriodEnd: Date; trialEnd: Date | undefined; }' is not assignable to type 'BillingInfo' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/multi-tenancy/tenant.service.ts:22:7
TS2375: Type '{ organizationId: string; userId: string | undefined; permissions: string[]; tier: string; limits: { maxGuilds: number; maxUsers: number; maxApiCalls: number; }; }' is not assignable to type 'TenantContext' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/ngrok/config/ngrok.config.ts:84:54
TS18048: 'envConfig' is possibly 'undefined'.

ERROR in ./src/core/ngrok/config/ngrok.config.ts:91:72
TS18048: 'envConfig' is possibly 'undefined'.

ERROR in ./src/core/ngrok/config/ngrok.config.ts:93:85
TS18048: 'envConfig' is possibly 'undefined'.

ERROR in ./src/core/ngrok/config/ngrok.config.ts:94:63
TS18048: 'envConfig' is possibly 'undefined'.

ERROR in ./src/core/ngrok/config/ngrok.config.ts:95:69
TS18048: 'envConfig' is possibly 'undefined'.

ERROR in ./src/core/ngrok/config/ngrok.config.ts:96:46
TS18048: 'envConfig' is possibly 'undefined'.

ERROR in ./src/core/ngrok/config/ngrok.config.ts:200:16
TS18048: 'envConfig' is possibly 'undefined'.

ERROR in ./src/core/ngrok/controllers/ngrok.controller.ts:42:3
TS2564: Property 'id' has no initializer and is not definitely assigned in the constructor.

ERROR in ./src/core/ngrok/controllers/ngrok.controller.ts:44:3
TS2564: Property 'addr' has no initializer and is not definitely assigned in the constructor.

ERROR in ./src/core/ngrok/controllers/ngrok.controller.ts:191:11
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/ngrok/controllers/ngrok.controller.ts:193:44
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/ngrok/controllers/ngrok.controller.ts:197:11
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/ngrok/controllers/ngrok.controller.ts:198:33
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/ngrok/controllers/ngrok.controller.ts:228:11
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/ngrok/controllers/ngrok.controller.ts:250:11
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/ngrok/controllers/ngrok.controller.ts:453:11
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/ngrok/guards/discord-webhook.guard.ts:105:20
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/core/ngrok/guards/discord-webhook.guard.ts:105:29
TS18048: 'host' is possibly 'undefined'.

ERROR in ./src/core/ngrok/guards/discord-webhook.guard.ts:168:7
TS2322: Type 'string | undefined' is not assignable to type 'string'.

ERROR in ./src/core/ngrok/guards/discord-webhook.guard.ts:168:56
TS2532: Object is possibly 'undefined'.

ERROR in ./src/core/ngrok/guards/discord-webhook.guard.ts:174:7
TS2322: Type 'string | undefined' is not assignable to type 'string'.

ERROR in ./src/core/ngrok/middleware/discord-webhook.middleware.ts:103:7
TS2322: Type 'string | undefined' is not assignable to type 'string'.

ERROR in ./src/core/ngrok/middleware/discord-webhook.middleware.ts:103:56
TS2532: Object is possibly 'undefined'.

ERROR in ./src/core/ngrok/middleware/discord-webhook.middleware.ts:109:7
TS2322: Type 'string | undefined' is not assignable to type 'string'.

ERROR in ./src/core/ngrok/middleware/discord-webhook.middleware.ts:221:7
TS2322: Type 'string | undefined' is not assignable to type 'string'.

ERROR in ./src/core/ngrok/middleware/discord-webhook.middleware.ts:221:56
TS2532: Object is possibly 'undefined'.

ERROR in ./src/core/ngrok/middleware/discord-webhook.middleware.ts:226:7
TS2322: Type 'string | undefined' is not assignable to type 'string'.

ERROR in ./src/core/ngrok/services/ngrok-discord.service.ts:239:28
TS18048: 'network' is possibly 'undefined'.

ERROR in ./src/core/ngrok/services/ngrok-discord.service.ts:242:34
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/core/ngrok/services/ngrok-discord.service.ts:256:29
TS2532: Object is possibly 'undefined'.

ERROR in ./src/core/ngrok/services/ngrok-discord.service.ts:257:24
TS2532: Object is possibly 'undefined'.

ERROR in ./src/core/ngrok/services/ngrok-discord.service.ts:290:54
TS2345: Argument of type '{ id: string; proto: "http"; addr: number; domain: string | undefined; subdomain: string | undefined; region: any; basicAuth: string | undefined; ipRestrictions: string[]; metadata: { service: string; environment: any; version: string; }; }' is not assignable to parameter of type '{ id: string; proto: "http" | "https" | "tls" | "tcp"; addr: string | number; region: "in" | "us" | "eu" | "ap" | "au" | "sa" | "jp"; compression: boolean; websocketTcpConverter: boolean; ... 7 more ...; circuitBreaker?: number | undefined; }'.

ERROR in ./src/core/ngrok/services/ngrok-discord.service.ts:352:5
TS2375: Type '{ status: "error" | "healthy" | "warning"; tunnelAvailable: boolean; webhookUrl: string | undefined; lastUpdated: Date; errors: string[]; }' is not assignable to type '{ status: "error" | "healthy" | "warning"; tunnelAvailable: boolean; webhookUrl?: string; lastUpdated?: Date; errors: string[]; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/ngrok/services/ngrok-health-monitor.service.ts:131:7
TS2412: Type 'undefined' is not assignable to type 'Timeout' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the type of the target.

ERROR in ./src/core/ngrok/services/ngrok-health-monitor.service.ts:176:29
TS2379: Argument of type '{ id: string; status: "critical"; url: string | undefined; uptime: number; lastError: any; }' is not assignable to parameter of type 'TunnelHealthStatus' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/ngrok/services/ngrok-health-monitor.service.ts:181:22
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/ngrok/services/ngrok-health-monitor.service.ts:184:38
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/ngrok/services/ngrok-health-monitor.service.ts:243:5
TS2375: Type '{ id: string; status: "healthy" | "critical" | "warning"; url: string | undefined; responseTime: number; uptime: number; lastError: string | undefined; }' is not assignable to type 'TunnelHealthStatus' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/ngrok/services/ngrok-health-monitor.service.ts:292:19
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/ngrok/services/ngrok-health-monitor.service.ts:299:5
TS2412: Type 'string | undefined' is not assignable to type 'string' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the type of the target.

ERROR in ./src/core/ngrok/services/ngrok-health-monitor.service.ts:318:5
TS2375: Type '{ id: string; status: "healthy" | "critical" | "warning"; url: string; responseTime: number; uptime: number; lastError: string | undefined; }' is not assignable to type 'TunnelHealthStatus' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/ngrok/services/ngrok-health-monitor.service.ts:389:16
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/ngrok/services/ngrok-health-monitor.service.ts:410:11
TS2375: Type '{ tunnelId: string; url: string | undefined; status: "healthy"; uptime: number; lastCheck: Date; consecutiveFailures: number; totalChecks: number; successfulChecks: number; avgResponseTime: number; metrics: { ...; }; }' is not assignable to type 'TunnelHealthMetrics' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/ngrok/services/ngrok-logging.service.ts:108:7
TS2375: Type '{ enabled: boolean; endpoint: string | undefined; apiKey: string | undefined; bufferSize: number; flushInterval: number; }' is not assignable to type '{ enabled: boolean; endpoint?: string; apiKey?: string; bufferSize: number; flushInterval: number; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/ngrok/services/ngrok-logging.service.ts:204:11
TS2375: Type '{ timestamp: Date; level: "error" | "warn" | "info" | "debug"; category: string; message: string; data: Record<string, any> | undefined; correlationId: string | undefined; requestId: string; }' is not assignable to type 'LogEntry' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/ngrok/services/ngrok-logging.service.ts:317:5
TS2532: Object is possibly 'undefined'.

ERROR in ./src/core/ngrok/services/ngrok-logging.service.ts:322:5
TS2532: Object is possibly 'undefined'.

ERROR in ./src/core/ngrok/services/ngrok-logging.service.ts:327:7
TS2532: Object is possibly 'undefined'.

ERROR in ./src/core/ngrok/services/ngrok-persistence.service.ts:134:9
TS2412: Type 'undefined' is not assignable to type 'Timeout' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the type of the target.

ERROR in ./src/core/ngrok/services/ngrok-persistence.service.ts:169:16
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/ngrok/services/ngrok-persistence.service.ts:243:51
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/ngrok/services/ngrok-persistence.service.ts:256:54
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/ngrok/services/ngrok-persistence.service.ts:274:16
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/ngrok/services/ngrok-persistence.service.ts:314:11
TS2322: Type '{ id: any; config: { id: any; proto: any; addr: any; }; url: any; startedAt: any; isAutoRestart: true; metadata: { lastHealthCheck: any; metrics: any; }; }[]' is not assignable to type 'TunnelPersistenceData[]'.

ERROR in ./src/core/ngrok/services/ngrok-persistence.service.ts:330:5
TS2375: Type '{ version: string; savedAt: Date; tunnels: TunnelPersistenceData[]; discordIntegration: NgrokDiscordIntegration | undefined; serviceConfig: { ...; }; }' is not assignable to type 'NgrokPersistenceState' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/ngrok/services/ngrok.service.ts:87:5
TS2739: Type '{ id: string; proto: "http"; addr: number; domain: string | undefined; subdomain: string | undefined; region: "in" | "us" | "eu" | "ap" | "au" | "sa" | "jp"; basicAuth: string | undefined; oauthProvider: any; ipRestrictions: string[]; }' is missing the following properties from type '{ id: string; proto: "http" | "https" | "tls" | "tcp"; addr: string | number; region: "in" | "us" | "eu" | "ap" | "au" | "sa" | "jp"; compression: boolean; websocketTcpConverter: boolean; ... 7 more ...; circuitBreaker?: number | undefined; }': compression, websocketTcpConverter

ERROR in ./src/core/ngrok/services/ngrok.service.ts:198:26
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/ngrok/services/ngrok.service.ts:207:46
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/ngrok/services/ngrok.service.ts:208:36
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/ngrok/services/ngrok.service.ts:211:29
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/ngrok/services/ngrok.service.ts:242:9
TS2322: Type 'string | null' is not assignable to type 'string'.

ERROR in ./src/core/ngrok/services/ngrok.service.ts:309:29
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/ngrok/services/ngrok.service.ts:332:5
TS2375: Type '{ id: string; status: "error" | "connecting" | "connected" | "disconnected" | "retrying"; url: string | undefined; proto: "http" | "https" | "tls" | "tcp"; addr: string | number; startedAt: Date; lastHealthCheck: Date; metrics: any; errors: string[]; }' is not assignable to type 'TunnelStatus' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/ngrok/services/ngrok.service.ts:383:29
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/ngrok/services/ngrok.service.ts:413:20
TS2379: Argument of type '{ id: string; status: "healthy" | "critical" | "warning"; url: string | undefined; uptime: number; lastError: string | undefined; }' is not assignable to parameter of type 'TunnelHealthStatus' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/security/api-key.service.ts:69:13
TS2375: Type '{ id: string; organizationId: string; name: string; keyHash: string; permissions: string[]; rateLimit: { windowMs: number; maxRequests: number; skipSuccessfulRequests?: boolean; skipFailedRequests?: boolean; keyGenerator?: (context: RateLimitContext) => string; onLimitReached?: (context: RateLimitContext) => void; }...' is not assignable to type 'ApiKey' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/security/api-key.service.ts:304:37
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/core/security/api-key.service.ts:305:23
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/core/security/audit.service.ts:96:9
TS2375: Type '{ correlationId: string | undefined; requestId: string | undefined; source: string; version: string; environment: string; }' is not assignable to type 'AuditMetadata' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/security/audit.service.ts:308:45
TS2379: Argument of type '{ limit: undefined; offset: undefined; userId?: string; organizationId?: string; eventType?: string; action?: string; resource?: string; startDate?: Date; endDate?: Date; success?: boolean; }' is not assignable to parameter of type 'AuditQuery' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/security/encryption.service.ts:54:30
TS2769: No overload matches this call.

ERROR in ./src/core/security/encryption.service.ts:55:35
TS2769: No overload matches this call.

ERROR in ./src/core/security/encryption.service.ts:63:39
TS2769: No overload matches this call.

ERROR in ./src/core/security/encryption.service.ts:64:7
TS2322: Type 'string' is not assignable to type 'Buffer<ArrayBufferLike> & string'.

ERROR in ./src/core/security/encryption.service.ts:105:30
TS2769: No overload matches this call.

ERROR in ./src/core/security/encryption.service.ts:106:35
TS2769: No overload matches this call.

ERROR in ./src/core/security/encryption.service.ts:114:39
TS2769: No overload matches this call.

ERROR in ./src/core/security/encryption.service.ts:115:7
TS2322: Type 'string' is not assignable to type 'Buffer<ArrayBufferLike> & string'.

ERROR in ./src/core/security/encryption.service.ts:158:34
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/core/security/guards/api-key.guard.ts:110:9
TS2379: Argument of type '{ ipAddress: string; userAgent: string | undefined; }' is not assignable to parameter of type '{ ipAddress?: string; userAgent?: string; errorMessage?: string; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/security/guards/api-key.guard.ts:128:9
TS2379: Argument of type '{ userId: string; organizationId: string; ipAddress: string; userAgent: string | undefined; }' is not assignable to parameter of type '{ userId?: string; organizationId?: string; sessionId?: string; ipAddress?: string; userAgent?: string; resourceId?: string; correlationId?: string; requestId?: string; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/security/guards/api-key.guard.ts:227:9
TS2379: Argument of type '{ ipAddress: string; userAgent: string | undefined; endpoint: string; }' is not assignable to parameter of type '{ userId?: string; organizationId?: string; ipAddress?: string; userAgent?: string; endpoint?: string; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/security/guards/api-key.guard.ts:245:9
TS2379: Argument of type '{ userId: string; ipAddress: string; userAgent: string | undefined; }' is not assignable to parameter of type '{ userId?: string; organizationId?: string; sessionId?: string; ipAddress?: string; userAgent?: string; resourceId?: string; correlationId?: string; requestId?: string; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/security/guards/api-key.guard.ts:280:9
TS2379: Argument of type '{ userAgent: string | undefined; }' is not assignable to parameter of type '{ userId?: string; userAgent?: string; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/security/guards/api-key.guard.ts:291:9
TS2379: Argument of type '{ ipAddress: string; userAgent: string | undefined; errorMessage: string; }' is not assignable to parameter of type '{ ipAddress?: string; userAgent?: string; errorMessage?: string; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/security/guards/api-key.guard.ts:326:9
TS2379: Argument of type '{ organizationId: string; ipAddress: string; userAgent: string | undefined; endpoint: string; }' is not assignable to parameter of type '{ userId?: string; organizationId?: string; ipAddress?: string; userAgent?: string; endpoint?: string; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/security/guards/api-key.guard.ts:340:9
TS2379: Argument of type '{ ipAddress: string; userAgent: string | undefined; errorMessage: string; }' is not assignable to parameter of type '{ ipAddress?: string; userAgent?: string; errorMessage?: string; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/security/guards/api-key.guard.ts:358:9
TS2379: Argument of type '{ userId: string; organizationId: string; ipAddress: string; userAgent: string | undefined; }' is not assignable to parameter of type '{ userId?: string; organizationId?: string; sessionId?: string; ipAddress?: string; userAgent?: string; resourceId?: string; correlationId?: string; requestId?: string; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/security/guards/jwt-auth.guard.ts:70:81
TS2339: Property 'sub' does not exist on type 'never'.

ERROR in ./src/core/security/guards/jwt-auth.guard.ts:93:9
TS2379: Argument of type '{ userId: string; ipAddress: string; userAgent: string | undefined; }' is not assignable to parameter of type '{ userId?: string; organizationId?: string; sessionId?: string; ipAddress?: string; userAgent?: string; resourceId?: string; correlationId?: string; requestId?: string; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/security/guards/jwt-auth.guard.ts:157:53
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/security/guards/jwt-auth.guard.ts:198:9
TS2379: Argument of type '{ userId: string | undefined; ipAddress: string; userAgent: string | undefined; endpoint: string; }' is not assignable to parameter of type '{ userId?: string; organizationId?: string; ipAddress?: string; userAgent?: string; endpoint?: string; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/security/guards/jwt-auth.guard.ts:217:9
TS2379: Argument of type '{ userId: string; ipAddress: string; userAgent: string | undefined; }' is not assignable to parameter of type '{ userId?: string; organizationId?: string; sessionId?: string; ipAddress?: string; userAgent?: string; resourceId?: string; correlationId?: string; requestId?: string; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/security/guards/rate-limit.guard.ts:144:5
TS2375: Type '{ windowMs: number; maxRequests: number; skipSuccessfulRequests: boolean | undefined; skipFailedRequests: boolean | undefined; }' is not assignable to type 'RateLimitConfig' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/security/guards/rate-limit.guard.ts:251:9
TS2379: Argument of type '{ userId: any; userAgent: string | undefined; }' is not assignable to parameter of type '{ userId?: string; userAgent?: string; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/security/guards/rbac.guard.ts:78:13
TS2375: Type '{ user: JwtPayload; requiredRoles: string[]; requiredPermissions: string[]; organizationRequired: boolean; resourceId: string | undefined; endpoint: string; method: string; }' is not assignable to type 'RbacContext' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/security/guards/rbac.guard.ts:107:9
TS2379: Argument of type '{ userId: string; organizationId: string | undefined; resourceId: string | undefined; ipAddress: string; userAgent: string | undefined; }' is not assignable to parameter of type '{ userId?: string; organizationId?: string; sessionId?: string; ipAddress?: string; userAgent?: string; resourceId?: string; correlationId?: string; requestId?: string; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/security/guards/rbac.guard.ts:309:9
TS2379: Argument of type '{ userId: string | undefined; organizationId: string | undefined; ipAddress: string; userAgent: string | undefined; endpoint: string; }' is not assignable to parameter of type '{ userId?: string; organizationId?: string; ipAddress?: string; userAgent?: string; endpoint?: string; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/security/guards/rbac.guard.ts:329:9
TS2379: Argument of type '{ userId: string; organizationId: string | undefined; resourceId: string | undefined; ipAddress: string; userAgent: string | undefined; }' is not assignable to parameter of type '{ userId?: string; organizationId?: string; sessionId?: string; ipAddress?: string; userAgent?: string; resourceId?: string; correlationId?: string; requestId?: string; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/security/interceptors/audit.interceptor.ts:158:9
TS2379: Argument of type '{ userId: any; organizationId: any; ipAddress: string; userAgent: string | undefined; correlationId: string | undefined; requestId: string | undefined; }' is not assignable to parameter of type '{ userId?: string; organizationId?: string; sessionId?: string; ipAddress?: string; userAgent?: string; resourceId?: string; correlationId?: string; requestId?: string; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/security/interceptors/audit.interceptor.ts:214:9
TS2379: Argument of type '{ userId: any; organizationId: any; ipAddress: string; userAgent: string | undefined; correlationId: string | undefined; requestId: string | undefined; }' is not assignable to parameter of type '{ userId?: string; organizationId?: string; sessionId?: string; ipAddress?: string; userAgent?: string; resourceId?: string; correlationId?: string; requestId?: string; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/security/interceptors/audit.interceptor.ts:266:9
TS2379: Argument of type '{ userId: any; organizationId: any; ipAddress: string; userAgent: string | undefined; correlationId: string | undefined; requestId: string | undefined; }' is not assignable to parameter of type '{ userId?: string; organizationId?: string; sessionId?: string; ipAddress?: string; userAgent?: string; resourceId?: string; correlationId?: string; requestId?: string; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/security/interceptors/security.interceptor.ts:323:9
TS2379: Argument of type '{ userId: string; ipAddress: string; userAgent: string | undefined; endpoint: string; }' is not assignable to parameter of type '{ userId?: string; ipAddress?: string; userAgent?: string; endpoint?: string; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/security/rate-limiting.service.ts:115:32
TS2379: Argument of type '{ userId: string; organizationId: string | undefined; }' is not assignable to parameter of type 'RateLimitContext' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/security/rbac.service.ts:328:5
TS2375: Type '{ userId: string; organizationId: string | undefined; roles: never[]; directPermissions: never[]; effectivePermissions: never[]; lastUpdated: Date; }' is not assignable to type 'UserPermissions' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/security/security-enhanced.module.ts:43:7
TS2322: Type '(configService: ConfigService) => Promise<{ secret: string | undefined; signOptions: { expiresIn: string; issuer: string; audience: string; }; }>' is not assignable to type '(...args: any[]) => JwtModuleOptions | Promise<JwtModuleOptions>'.

ERROR in ./src/core/security/security-event.service.ts:134:9
TS2375: Type '{ correlationId: string | undefined; requestId: string | undefined; sessionId: string | undefined; source: string; version: string; environment: string; }' is not assignable to type 'SecurityEventMetadata' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/security/session.service.ts:6:10
TS2305: Module '"@/core/database"' has no exported member 'Session'.

ERROR in ./src/core/security/session.service.ts:6:19
TS2305: Module '"@/core/database"' has no exported member 'sessions'.

ERROR in ./src/core/security/session.service.ts:7:10
TS2305: Module '"@/core/database"' has no exported member 'users'.

ERROR in ./src/core/security/strategies/api-key.strategy.ts:85:9
TS2379: Argument of type '{ userAgent?: string | undefined; ipAddress: string; }' is not assignable to parameter of type '{ ipAddress?: string; userAgent?: string; errorMessage?: string; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/security/strategies/api-key.strategy.ts:103:9
TS2379: Argument of type '{ userId: string; organizationId: string; ipAddress: string; userAgent: string | undefined; }' is not assignable to parameter of type '{ userId?: string; organizationId?: string; sessionId?: string; ipAddress?: string; userAgent?: string; resourceId?: string; correlationId?: string; requestId?: string; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/security/strategies/api-key.strategy.ts:204:9
TS2379: Argument of type '{ ipAddress: string; userAgent: string | undefined; endpoint: string; }' is not assignable to parameter of type '{ userId?: string; organizationId?: string; ipAddress?: string; userAgent?: string; endpoint?: string; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/security/strategies/api-key.strategy.ts:222:9
TS2379: Argument of type '{ userId: string; ipAddress: string; userAgent: string | undefined; }' is not assignable to parameter of type '{ userId?: string; organizationId?: string; sessionId?: string; ipAddress?: string; userAgent?: string; resourceId?: string; correlationId?: string; requestId?: string; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/security/strategies/api-key.strategy.ts:257:9
TS2379: Argument of type '{ userAgent: string | undefined; }' is not assignable to parameter of type '{ userId?: string; userAgent?: string; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/security/strategies/api-key.strategy.ts:268:9
TS2379: Argument of type '{ ipAddress: string; userAgent: string | undefined; errorMessage: string; }' is not assignable to parameter of type '{ ipAddress?: string; userAgent?: string; errorMessage?: string; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/security/strategies/api-key.strategy.ts:338:7
TS2322: Type 'string | null | undefined' is not assignable to type 'string | null'.

ERROR in ./src/core/security/strategies/api-key.strategy.ts:346:68
TS2379: Argument of type '{ headerName: string; queryParam: undefined; allowMultipleSources: false; }' is not assignable to parameter of type 'ApiKeyStrategyOptions' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/security/strategies/api-key.strategy.ts:352:67
TS2379: Argument of type '{ headerName: undefined; queryParam: string; allowMultipleSources: false; }' is not assignable to parameter of type 'ApiKeyStrategyOptions' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/security/strategies/api-key.strategy.ts:358:88
TS2379: Argument of type '{ headerName: string; queryParam: undefined; allowMultipleSources: false; caseSensitive: true; }' is not assignable to parameter of type 'ApiKeyStrategyOptions' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/security/strategies/jwt.strategy.ts:82:7
TS2375: Type '{ id: string; email: any; username: any; organizationId: string | undefined; roles: any[]; permissions: any[]; isActive: any; lastLoginAt: any; sessionId: string | undefined; }' is not assignable to type 'ValidatedUser' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/security/strategies/jwt.strategy.ts:216:9
TS2379: Argument of type '{ userId: string; ipAddress: string; userAgent: string | undefined; }' is not assignable to parameter of type '{ userId?: string; organizationId?: string; sessionId?: string; ipAddress?: string; userAgent?: string; resourceId?: string; correlationId?: string; requestId?: string; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/security/user.service.ts:3:10
TS2305: Module '"@/core/database"' has no exported member 'User'.

ERROR in ./src/core/security/user.service.ts:3:16
TS2305: Module '"@/core/database"' has no exported member 'UserKeys'.

ERROR in ./src/core/security/user.service.ts:3:26
TS2305: Module '"@/core/database"' has no exported member 'CreateUser'.

ERROR in ./src/core/security/user.service.ts:4:10
TS2305: Module '"@/core/database"' has no exported member 'Organization'.

ERROR in ./src/core/security/user.service.ts:4:24
TS2305: Module '"@/core/database"' has no exported member 'OrganizationMember'.

ERROR in ./src/core/security/user.service.ts:4:44
TS2305: Module '"@/core/database"' has no exported member 'OrganizationKeys'.

ERROR in ./src/core/security/user.service.ts:4:62
TS2305: Module '"@/core/database"' has no exported member 'OrganizationMemberKeys'.

ERROR in ./src/core/security/user.service.ts:6:10
TS2305: Module '"@/core/database"' has no exported member 'generateId'.

ERROR in ./src/core/services/ai-agent.service.ts:178:5
TS2375: Type '{ content: string; shouldEscalate: true; nextAgent: AgentType | undefined; }' is not assignable to type 'AgentResponse' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/services/channel.service.ts:46:78
TS2379: Argument of type '{ name: string; type: "text"; parent: string | undefined; permissions: ChannelPermission[]; }' is not assignable to parameter of type 'ChannelConfig' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/services/discord.service.ts:116:42
TS2345: Argument of type '{ content: string | undefined; embeds: any[] | undefined; components: any[] | undefined; }' is not assignable to parameter of type 'string | MessagePayload | MessageCreateOptions'.

ERROR in ./src/core/services/discord.service.ts:132:23
TS2345: Argument of type '{ content: string | undefined; embeds: any[] | undefined; components: any[] | undefined; }' is not assignable to parameter of type 'string | MessagePayload | MessageCreateOptions'.

ERROR in ./src/core/services/membership.service.ts:5:10
TS2305: Module '"@/core/database"' has no exported member 'users'.

ERROR in ./src/core/services/membership.service.ts:52:21
TS18048: 'user' is possibly 'undefined'.

ERROR in ./src/core/services/membership.service.ts:53:23
TS18048: 'user' is possibly 'undefined'.

ERROR in ./src/core/services/membership.service.ts:53:41
TS18048: 'user' is possibly 'undefined'.

ERROR in ./src/core/services/whop.service.ts:130:5
TS2375: Type '{ id: any; userId: any; tier: MembershipTier; status: any; features: string[]; limits: { aiModels: string[]; dailyTokens: number; requestsPerHour: number; } | { aiModels: string[]; dailyTokens: number; requestsPerHour: number; } | { ...; }; expiresAt: Date | undefined; }' is not assignable to type 'WhopMembership' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/core/services/whop.service.ts:239:61
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/core/services/whop.service.ts:257:58
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/discord/discord.service.ts:7:10
TS2305: Module '"@/core/database"' has no exported member 'aiAgentConfigs'.

ERROR in ./src/discord/discord.service.ts:7:26
TS2305: Module '"@/core/database"' has no exported member 'guilds'.

ERROR in ./src/discord/discord.service.ts:19:39
TS2304: Cannot find name 'DatabaseService'.

ERROR in ./src/discord/discord.service.ts:71:39
TS2339: Property 'db' does not exist on type 'DiscordService'.

ERROR in ./src/discord/discord.service.ts:79:47
TS2339: Property 'db' does not exist on type 'DiscordService'.

ERROR in ./src/discord/discord.service.ts:190:5
TS2322: Type 'Client<boolean> | null' is not assignable to type 'Client<boolean>'.

ERROR in ./src/discord/events/discord-events.service.ts:8:10
TS2305: Module '"@/core/database"' has no exported member 'guilds'.

ERROR in ./src/discord/events/discord-events.service.ts:8:18
TS2305: Module '"@/core/database"' has no exported member 'users'.

ERROR in ./src/discord/events/discord-events.service.ts:8:30
TS2305: Module '"@/core/database"' has no exported member 'NewUser'.

ERROR in ./src/discord/events/discord-events.service.ts:8:44
TS2305: Module '"@/core/database"' has no exported member 'NewGuild'.

ERROR in ./src/discord/events/discord-events.service.ts:127:18
TS2339: Property 'db' does not exist on type 'DiscordEventsService'.

ERROR in ./src/discord/events/discord-events.service.ts:148:18
TS2339: Property 'db' does not exist on type 'DiscordEventsService'.

ERROR in ./src/discord/events/discord-events.service.ts:162:18
TS2339: Property 'db' does not exist on type 'DiscordEventsService'.

ERROR in ./src/discord/events/discord-events.service.ts:188:18
TS2339: Property 'db' does not exist on type 'DiscordEventsService'.

ERROR in ./src/discord/events/discord-events.service.ts:207:18
TS2339: Property 'db' does not exist on type 'DiscordEventsService'.

ERROR in ./src/features/ai-agents/ai-agents.commands.ts:21:3
TS2564: Property 'agent' has no initializer and is not definitely assigned in the constructor.

ERROR in ./src/features/ai-agents/ai-agents.commands.ts:28:3
TS2564: Property 'question' has no initializer and is not definitely assigned in the constructor.

ERROR in ./src/features/ai-agents/ai-agents.commands.ts:132:25
TS2345: Argument of type '[{ name: string; value: string | undefined; inline: true; }[]]' is not assignable to parameter of type 'RestOrArray<APIEmbedField>'.

ERROR in ./src/features/ai-agents/ai-agents.commands.ts:339:23
TS7053: Element implicitly has an 'any' type because expression of type 'any' can't be used to index type 'Record<AgentSpecialty, AgentPersonality>'.

ERROR in ./src/features/ai-agents/specialized-agents.service.ts:182:21
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/features/ai-agents/specialized-agents.service.ts:211:81
TS2532: Object is possibly 'undefined'.

ERROR in ./src/features/ai-agents/specialized-agents.service.ts:239:11
TS2375: Type '{ content: string; actionItems: string[] | undefined; resources: { title: string; url: string; description: string; }[] | undefined; followUpQuestions: string[] | undefined; nextSteps: string[] | undefined; }' is not assignable to type 'AgentResponse' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/features/ai-agents/specialized-agents.service.ts:240:39
TS18048: 'baseResponse' is possibly 'undefined'.

ERROR in ./src/features/ai-agents/specialized-agents.service.ts:241:20
TS18048: 'baseResponse' is possibly 'undefined'.

ERROR in ./src/features/ai-agents/specialized-agents.service.ts:242:18
TS18048: 'baseResponse' is possibly 'undefined'.

ERROR in ./src/features/ai-agents/specialized-agents.service.ts:243:26
TS18048: 'baseResponse' is possibly 'undefined'.

ERROR in ./src/features/ai-agents/specialized-agents.service.ts:244:18
TS18048: 'baseResponse' is possibly 'undefined'.

ERROR in ./src/features/ai-agents/specialized-agents.service.ts:253:32
TS2769: No overload matches this call.

ERROR in ./src/features/ai-agents/specialized-agents.service.ts:254:31
TS2769: No overload matches this call.

ERROR in ./src/features/ai-automation/ai-automation.service.ts:15:3
TS2564: Property 'feature' has no initializer and is not definitely assigned in the constructor.

ERROR in ./src/features/ai-automation/ai-automation.service.ts:22:3
TS2564: Property 'enabled' has no initializer and is not definitely assigned in the constructor.

ERROR in ./src/features/ai-automation/ai-automation.service.ts:62:26
TS7053: Element implicitly has an 'any' type because expression of type '"ai-automation"' can't be used to index type 'GuildFeatures'.

ERROR in ./src/features/ai-automation/ai-automation.service.ts:107:39
TS2339: Property 'db' does not exist on type 'AIAutomationService'.

ERROR in ./src/features/ai-automation/ai-automation.service.ts:107:56
TS2304: Cannot find name 'guilds'.

ERROR in ./src/features/ai-automation/ai-automation.service.ts:107:73
TS2304: Cannot find name 'guilds'.

ERROR in ./src/features/ai-automation/ai-automation.service.ts:135:39
TS2339: Property 'db' does not exist on type 'AIAutomationService'.

ERROR in ./src/features/ai-automation/ai-automation.service.ts:135:56
TS2304: Cannot find name 'guilds'.

ERROR in ./src/features/ai-automation/ai-automation.service.ts:135:73
TS2304: Cannot find name 'guilds'.

ERROR in ./src/features/ai-automation/ai-automation.service.ts:161:39
TS2339: Property 'db' does not exist on type 'AIAutomationService'.

ERROR in ./src/features/ai-automation/ai-automation.service.ts:161:56
TS2304: Cannot find name 'guilds'.

ERROR in ./src/features/ai-automation/ai-automation.service.ts:161:73
TS2304: Cannot find name 'guilds'.

ERROR in ./src/features/ai-automation/ai-automation.service.ts:182:39
TS2339: Property 'db' does not exist on type 'AIAutomationService'.

ERROR in ./src/features/ai-automation/ai-automation.service.ts:182:56
TS2304: Cannot find name 'guilds'.

ERROR in ./src/features/ai-automation/ai-automation.service.ts:182:73
TS2304: Cannot find name 'guilds'.

ERROR in ./src/features/ai-automation/ai-automation.service.ts:212:7
TS2322: Type 'string | undefined' is not assignable to type 'string | null'.

ERROR in ./src/features/ai-automation/ai-automation.service.ts:229:7
TS2322: Type 'string | undefined' is not assignable to type 'string'.

ERROR in ./src/features/ai-automation/ai-automation.service.ts:256:5
TS2322: Type 'string | undefined' is not assignable to type 'string | null'.

ERROR in ./src/features/ai-automation/auto-moderation.service.ts:8:10
TS2305: Module '"@/core/database"' has no exported member 'guilds'.

ERROR in ./src/features/ai-automation/auto-moderation.service.ts:8:18
TS2305: Module '"@/core/database"' has no exported member 'users'.

ERROR in ./src/features/ai-automation/auto-moderation.service.ts:8:25
TS2305: Module '"@/core/database"' has no exported member 'Guild'.

ERROR in ./src/features/ai-automation/auto-moderation.service.ts:8:32
TS2305: Module '"@/core/database"' has no exported member 'User'.

ERROR in ./src/features/ai-automation/auto-moderation.service.ts:35:39
TS2339: Property 'db' does not exist on type 'AutoModerationService'.

ERROR in ./src/features/ai-automation/auto-moderation.service.ts:35:70
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/ai-automation/auto-moderation.service.ts:109:36
TS2339: Property 'db' does not exist on type 'AutoModerationService'.

ERROR in ./src/features/ai-automation/auto-moderation.service.ts:109:66
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/ai-automation/auto-moderation.service.ts:240:39
TS2339: Property 'db' does not exist on type 'AutoModerationService'.

ERROR in ./src/features/ai-automation/auto-moderation.service.ts:240:70
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/ai-automation/smart-notification.service.ts:7:10
TS2305: Module '"@/core/database"' has no exported member 'guilds'.

ERROR in ./src/features/ai-automation/smart-notification.service.ts:7:18
TS2305: Module '"@/core/database"' has no exported member 'users'.

ERROR in ./src/features/ai-automation/smart-notification.service.ts:7:25
TS2305: Module '"@/core/database"' has no exported member 'agentInteractions'.

ERROR in ./src/features/ai-automation/smart-notification.service.ts:7:44
TS2305: Module '"@/core/database"' has no exported member 'Guild'.

ERROR in ./src/features/ai-automation/smart-notification.service.ts:7:51
TS2305: Module '"@/core/database"' has no exported member 'User'.

ERROR in ./src/features/ai-automation/smart-notification.service.ts:7:57
TS2305: Module '"@/core/database"' has no exported member 'AgentInteraction'.

ERROR in ./src/features/ai-automation/smart-notification.service.ts:20:39
TS2339: Property 'db' does not exist on type 'SmartNotificationService'.

ERROR in ./src/features/ai-automation/smart-notification.service.ts:20:70
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/ai-automation/smart-notification.service.ts:37:39
TS2339: Property 'db' does not exist on type 'SmartNotificationService'.

ERROR in ./src/features/ai-automation/smart-notification.service.ts:37:70
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/ai-automation/smart-notification.service.ts:54:39
TS2339: Property 'db' does not exist on type 'SmartNotificationService'.

ERROR in ./src/features/ai-automation/smart-notification.service.ts:54:70
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/ai-automation/smart-notification.service.ts:73:40
TS2339: Property 'db' does not exist on type 'SmartNotificationService'.

ERROR in ./src/features/ai-automation/smart-notification.service.ts:74:16
TS2304: Cannot find name 'and'.

ERROR in ./src/features/ai-automation/smart-notification.service.ts:75:11
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/ai-automation/smart-notification.service.ts:76:11
TS2304: Cannot find name 'lt'.

ERROR in ./src/features/ai-automation/smart-notification.service.ts:105:14
TS2339: Property 'db' does not exist on type 'SmartNotificationService'.

ERROR in ./src/features/ai-automation/smart-notification.service.ts:105:44
TS2304: Cannot find name 'gte'.

ERROR in ./src/features/ai-automation/smart-notification.service.ts:106:14
TS2339: Property 'db' does not exist on type 'SmartNotificationService'.

ERROR in ./src/features/ai-automation/smart-notification.service.ts:106:44
TS2304: Cannot find name 'and'.

ERROR in ./src/features/ai-automation/smart-notification.service.ts:107:11
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/ai-automation/smart-notification.service.ts:108:11
TS2304: Cannot find name 'gte'.

ERROR in ./src/features/ai-automation/smart-notification.service.ts:110:14
TS2339: Property 'db' does not exist on type 'SmartNotificationService'.

ERROR in ./src/features/ai-automation/smart-notification.service.ts:110:56
TS2304: Cannot find name 'gte'.

ERROR in ./src/features/ai-automation/smart-notification.service.ts:178:41
TS2339: Property 'db' does not exist on type 'SmartNotificationService'.

ERROR in ./src/features/ai-automation/smart-notification.service.ts:179:14
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/ai-automation/smart-notification.service.ts:180:16
TS2304: Cannot find name 'desc'.

ERROR in ./src/features/ai-automation/smart-notification.service.ts:188:5
TS2322: Type 'string | undefined' is not assignable to type 'string | null'.

ERROR in ./src/features/ai-channel/ai-channel-auto-setup.service.ts:15:10
TS2305: Module '"@/core/database"' has no exported member 'aiChannelConfigs'.

ERROR in ./src/features/ai-channel/ai-channel-auto-setup.service.ts:250:53
TS2769: No overload matches this call.

ERROR in ./src/features/ai-channel/ai-channel-auto-setup.service.ts:303:33
TS2339: Property 'db' does not exist on type 'AIChannelAutoSetupService'.

ERROR in ./src/features/ai-channel/ai-channel-auto-setup.service.ts:304:16
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/ai-channel/ai-channel-auto-setup.service.ts:439:38
TS2339: Property 'db' does not exist on type 'AIChannelAutoSetupService'.

ERROR in ./src/features/ai-channel/ai-channel-panel.service.ts:24:3
TS2305: Module '"@/core/database"' has no exported member 'AIChannelConfig'.

ERROR in ./src/features/ai-channel/ai-channel-panel.service.ts:25:3
TS2305: Module '"@/core/database"' has no exported member 'NewAIChannelConfig'.

ERROR in ./src/features/ai-channel/ai-channel-panel.service.ts:26:3
TS2305: Module '"@/core/database"' has no exported member 'aiChannelConfigs'.

ERROR in ./src/features/ai-channel/ai-channel-panel.service.ts:27:3
TS2305: Module '"@/core/database"' has no exported member 'AIChannelSettings'.

ERROR in ./src/features/ai-channel/ai-channel-panel.service.ts:28:3
TS2305: Module '"@/core/database"' has no exported member 'AIProvider'.

ERROR in ./src/features/ai-channel/ai-channel-panel.service.ts:283:59
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/features/ai-channel/ai-channel-panel.service.ts:288:61
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/features/ai-channel/ai-channel-panel.service.ts:676:50
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/features/ai-channel/ai-channel-panel.service.ts:816:9
TS2532: Object is possibly 'undefined'.

ERROR in ./src/features/ai-channel/ai-channel-panel.service.ts:959:21
TS2345: Argument of type '[{ label: any; description: string; value: any; emoji: string | undefined; }[]]' is not assignable to parameter of type 'RestOrArray<StringSelectMenuOptionBuilder | SelectMenuComponentOptionData | APISelectMenuOption>'.

ERROR in ./src/features/ai-channel/ai-channel-panel.service.ts:1312:18
TS2339: Property 'db' does not exist on type 'AIChannelPanelService'.

ERROR in ./src/features/ai-channel/ai-channel-panel.service.ts:1361:18
TS2339: Property 'db' does not exist on type 'AIChannelPanelService'.

ERROR in ./src/features/ai-channel/ai-channel-panel.service.ts:1363:16
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/ai-channel/ai-channel-panel.service.ts:1379:33
TS2339: Property 'db' does not exist on type 'AIChannelPanelService'.

ERROR in ./src/features/ai-channel/ai-channel-panel.service.ts:1380:16
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/ai-channel/ai-channel-panel.service.ts:1412:18
TS2339: Property 'db' does not exist on type 'AIChannelPanelService'.

ERROR in ./src/features/ai-channel/ai-channel-panel.service.ts:1418:16
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/ai-channel/ai-channel-panel.service.ts:1472:26
TS18048: 'embed' is possibly 'undefined'.

ERROR in ./src/features/ai-channel/ai-channel.commands.ts:53:3
TS2564: Property 'userId' has no initializer and is not definitely assigned in the constructor.

ERROR in ./src/features/ai-channel/ai-channel.commands.ts:65:3
TS2564: Property 'action' has no initializer and is not definitely assigned in the constructor.

ERROR in ./src/features/ai-channel/ai-channel.commands.ts:657:13
TS18046: 'timeoutError' is of type 'unknown'.

ERROR in ./src/features/ai-channel/ai-channel.commands.ts:748:48
TS2532: Object is possibly 'undefined'.

ERROR in ./src/features/ai-channel/api-key-manager.service.ts:8:3
TS2305: Module '"@/core/database"' has no exported member 'userApiKeys'.

ERROR in ./src/features/ai-channel/api-key-manager.service.ts:9:3
TS2305: Module '"@/core/database"' has no exported member 'UserApiKey'.

ERROR in ./src/features/ai-channel/api-key-manager.service.ts:10:3
TS2305: Module '"@/core/database"' has no exported member 'NewUserApiKey'.

ERROR in ./src/features/ai-channel/api-key-manager.service.ts:11:3
TS2305: Module '"@/core/database"' has no exported member 'AIProvider'.

ERROR in ./src/features/ai-channel/api-key-manager.service.ts:12:3
TS2305: Module '"@/core/database"' has no exported member 'APIKeyConfig'.

ERROR in ./src/features/ai-channel/api-key-manager.service.ts:13:3
TS2305: Module '"@/core/database"' has no exported member 'APIKeyValidation'.

ERROR in ./src/features/ai-channel/api-key-manager.service.ts:87:32
TS2769: No overload matches this call.

ERROR in ./src/features/ai-channel/api-key-manager.service.ts:88:37
TS2769: No overload matches this call.

ERROR in ./src/features/ai-channel/api-key-manager.service.ts:96:41
TS2769: No overload matches this call.

ERROR in ./src/features/ai-channel/api-key-manager.service.ts:97:9
TS2322: Type 'string' is not assignable to type 'Buffer<ArrayBufferLike> & string'.

ERROR in ./src/features/ai-channel/api-key-manager.service.ts:179:18
TS2339: Property 'db' does not exist on type 'ApiKeyManagerService'.

ERROR in ./src/features/ai-channel/api-key-manager.service.ts:203:33
TS2339: Property 'db' does not exist on type 'ApiKeyManagerService'.

ERROR in ./src/features/ai-channel/api-key-manager.service.ts:225:25
TS2339: Property 'db' does not exist on type 'ApiKeyManagerService'.

ERROR in ./src/features/ai-channel/api-key-manager.service.ts:242:33
TS2339: Property 'db' does not exist on type 'ApiKeyManagerService'.

ERROR in ./src/features/ai-channel/api-key-manager.service.ts:264:33
TS2339: Property 'db' does not exist on type 'ApiKeyManagerService'.

ERROR in ./src/features/ai-channel/api-key-manager.service.ts:285:30
TS2339: Property 'db' does not exist on type 'ApiKeyManagerService'.

ERROR in ./src/features/ai-channel/api-key-manager.service.ts:295:20
TS2339: Property 'db' does not exist on type 'ApiKeyManagerService'.

ERROR in ./src/features/ai-channel/api-key-manager.service.ts:313:33
TS2339: Property 'db' does not exist on type 'ApiKeyManagerService'.

ERROR in ./src/features/ai-channel/api-key-manager.service.ts:377:33
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/ai-channel/api-key-manager.service.ts:388:18
TS2339: Property 'db' does not exist on type 'ApiKeyManagerService'.

ERROR in ./src/features/ai-channel/api-key-manager.service.ts:410:30
TS2339: Property 'db' does not exist on type 'ApiKeyManagerService'.

ERROR in ./src/features/ai-channel/api-key-manager.service.ts:420:18
TS2339: Property 'db' does not exist on type 'ApiKeyManagerService'.

ERROR in ./src/features/ai-channel/api-key-manager.service.ts:440:18
TS2339: Property 'db' does not exist on type 'ApiKeyManagerService'.

ERROR in ./src/features/ai-channel/api-key.commands.ts:21:10
TS2305: Module '"@/core/database"' has no exported member 'AIProvider'.

ERROR in ./src/features/ai-channel/api-key.commands.ts:36:3
TS2564: Property 'action' has no initializer and is not definitely assigned in the constructor.

ERROR in ./src/features/ai-channel/exa-byok.service.ts:4:10
TS2305: Module '"@/core/database"' has no exported member 'AIProvider'.

ERROR in ./src/features/ai-channel/model-selection.service.ts:2:10
TS2305: Module '"@/core/database"' has no exported member 'AIProvider'.

ERROR in ./src/features/ai-channel/model-selection.service.ts:156:11
TS2322: Type 'ModelConfig | undefined' is not assignable to type 'ModelConfig'.

ERROR in ./src/features/ai-channel/model-selection.service.ts:164:11
TS2322: Type 'ModelConfig | undefined' is not assignable to type 'ModelConfig'.

ERROR in ./src/features/ai-channel/model-selection.service.ts:172:11
TS2322: Type 'ModelConfig | undefined' is not assignable to type 'ModelConfig'.

ERROR in ./src/features/ai-channel/model-selection.service.ts:178:11
TS2322: Type 'ModelConfig | undefined' is not assignable to type 'ModelConfig'.

ERROR in ./src/features/ai-channel/model-selection.service.ts:183:11
TS2322: Type 'ModelConfig | undefined' is not assignable to type 'ModelConfig'.

ERROR in ./src/features/ai-channel/private-chat-manager.service.ts:17:3
TS2305: Module '"@/core/database"' has no exported member 'AIChatSession'.

ERROR in ./src/features/ai-channel/private-chat-manager.service.ts:18:3
TS2305: Module '"@/core/database"' has no exported member 'NewAIChatSession'.

ERROR in ./src/features/ai-channel/private-chat-manager.service.ts:19:3
TS2305: Module '"@/core/database"' has no exported member 'aiChatSessions'.

ERROR in ./src/features/ai-channel/private-chat-manager.service.ts:20:3
TS2305: Module '"@/core/database"' has no exported member 'AIChannelConfig'.

ERROR in ./src/features/ai-channel/private-chat-manager.service.ts:21:3
TS2305: Module '"@/core/database"' has no exported member 'aiChannelConfigs'.

ERROR in ./src/features/ai-channel/private-chat-manager.service.ts:22:3
TS2305: Module '"@/core/database"' has no exported member 'AIChatSessionStatus'.

ERROR in ./src/features/ai-channel/private-chat-manager.service.ts:23:3
TS2305: Module '"@/core/database"' has no exported member 'AIAccessLevel'.

ERROR in ./src/features/ai-channel/private-chat-manager.service.ts:109:18
TS2339: Property 'db' does not exist on type 'PrivateChatManagerService'.

ERROR in ./src/features/ai-channel/private-chat-manager.service.ts:238:33
TS2339: Property 'db' does not exist on type 'PrivateChatManagerService'.

ERROR in ./src/features/ai-channel/private-chat-manager.service.ts:239:16
TS2304: Cannot find name 'and'.

ERROR in ./src/features/ai-channel/private-chat-manager.service.ts:240:11
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/ai-channel/private-chat-manager.service.ts:241:11
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/ai-channel/private-chat-manager.service.ts:242:11
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/ai-channel/private-chat-manager.service.ts:243:11
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/ai-channel/private-chat-manager.service.ts:259:33
TS2339: Property 'db' does not exist on type 'PrivateChatManagerService'.

ERROR in ./src/features/ai-channel/private-chat-manager.service.ts:260:16
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/ai-channel/private-chat-manager.service.ts:300:18
TS2339: Property 'db' does not exist on type 'PrivateChatManagerService'.

ERROR in ./src/features/ai-channel/private-chat-manager.service.ts:306:16
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/ai-channel/private-chat-manager.service.ts:326:43
TS2339: Property 'db' does not exist on type 'PrivateChatManagerService'.

ERROR in ./src/features/ai-channel/private-chat-manager.service.ts:327:16
TS2304: Cannot find name 'and'.

ERROR in ./src/features/ai-channel/private-chat-manager.service.ts:328:11
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/ai-channel/private-chat-manager.service.ts:354:18
TS2339: Property 'db' does not exist on type 'PrivateChatManagerService'.

ERROR in ./src/features/ai-channel/private-chat-manager.service.ts:360:16
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/ai-channel/private-chat-manager.service.ts:372:33
TS2339: Property 'db' does not exist on type 'PrivateChatManagerService'.

ERROR in ./src/features/ai-channel/private-chat-manager.service.ts:373:16
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/ai-channel/private-chat-manager.service.ts:411:25
TS2339: Property 'db' does not exist on type 'PrivateChatManagerService'.

ERROR in ./src/features/ai-channel/private-chat-manager.service.ts:412:16
TS2304: Cannot find name 'and'.

ERROR in ./src/features/ai-channel/private-chat-manager.service.ts:413:11
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/ai-channel/private-chat-manager.service.ts:414:11
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/ai-channel/private-chat-manager.service.ts:415:11
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/ai-channel/providers/model-config.ts:1:10
TS2305: Module '"@/core/database"' has no exported member 'AIProvider'.

ERROR in ./src/features/ai-channel/providers/provider-config.ts:1:10
TS2305: Module '"@/core/database"' has no exported member 'AIProvider'.

ERROR in ./src/features/ai-channel/providers/provider-config.ts:256:3
TS2322: Type 'ProviderConfig | undefined' is not assignable to type 'ProviderConfig'.

ERROR in ./src/features/ai-channel/providers/provider-config.ts:272:3
TS2375: Type '{ isValid: boolean; message: string | undefined; }' is not assignable to type '{ isValid: boolean; message?: string; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/features/automation/automation.commands.ts:58:3
TS2564: Property 'feature' has no initializer and is not definitely assigned in the constructor.

ERROR in ./src/features/automation/comprehensive-automation.service.ts:15:10
TS2305: Module '"@/core/database"' has no exported member 'guilds'.

ERROR in ./src/features/automation/comprehensive-automation.service.ts:135:39
TS2339: Property 'db' does not exist on type 'ComprehensiveAutomationService'.

ERROR in ./src/features/automation/comprehensive-automation.service.ts:135:70
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/automation/comprehensive-automation.service.ts:209:13
TS2412: Type 'string | undefined' is not assignable to type 'string' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the type of the target.

ERROR in ./src/features/automation/comprehensive-automation.service.ts:220:20
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/automation/comprehensive-automation.service.ts:225:35
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/automation/comprehensive-automation.service.ts:258:31
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/automation/comprehensive-automation.service.ts:319:16
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/automation/comprehensive-automation.service.ts:344:9
TS2322: Type 'null' is not assignable to type 'string'.

ERROR in ./src/features/automation/comprehensive-automation.service.ts:359:16
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/automation/comprehensive-automation.service.ts:379:16
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/automation/comprehensive-automation.service.ts:387:39
TS2339: Property 'db' does not exist on type 'ComprehensiveAutomationService'.

ERROR in ./src/features/automation/comprehensive-automation.service.ts:387:70
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/automation/comprehensive-automation.service.ts:417:20
TS2339: Property 'db' does not exist on type 'ComprehensiveAutomationService'.

ERROR in ./src/features/automation/comprehensive-automation.service.ts:417:86
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/automation/comprehensive-automation.service.ts:430:16
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/automation/comprehensive-automation.service.ts:468:16
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/automation/comprehensive-automation.service.ts:476:39
TS2339: Property 'db' does not exist on type 'ComprehensiveAutomationService'.

ERROR in ./src/features/automation/comprehensive-automation.service.ts:476:70
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/automation/comprehensive-automation.service.ts:496:16
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/automation/comprehensive-automation.service.ts:528:16
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/automation/comprehensive-automation.service.ts:536:39
TS2339: Property 'db' does not exist on type 'ComprehensiveAutomationService'.

ERROR in ./src/features/automation/comprehensive-automation.service.ts:536:70
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/automation/comprehensive-automation.service.ts:550:20
TS2339: Property 'db' does not exist on type 'ComprehensiveAutomationService'.

ERROR in ./src/features/automation/comprehensive-automation.service.ts:550:86
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/automation/comprehensive-automation.service.ts:563:16
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/automation/comprehensive-automation.service.ts:586:16
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/automation/comprehensive-automation.service.ts:618:16
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/automation/comprehensive-automation.service.ts:625:39
TS2339: Property 'db' does not exist on type 'ComprehensiveAutomationService'.

ERROR in ./src/features/automation/comprehensive-automation.service.ts:625:70
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/automation/comprehensive-automation.service.ts:628:20
TS2339: Property 'db' does not exist on type 'ComprehensiveAutomationService'.

ERROR in ./src/features/automation/comprehensive-automation.service.ts:725:39
TS2339: Property 'db' does not exist on type 'ComprehensiveAutomationService'.

ERROR in ./src/features/automation/comprehensive-automation.service.ts:725:70
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/automation/comprehensive-automation.service.ts:754:20
TS2339: Property 'db' does not exist on type 'ComprehensiveAutomationService'.

ERROR in ./src/features/automation/comprehensive-automation.service.ts:754:86
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/automation/comprehensive-automation.service.ts:763:39
TS2339: Property 'db' does not exist on type 'ComprehensiveAutomationService'.

ERROR in ./src/features/automation/comprehensive-automation.service.ts:763:70
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/automation/comprehensive-automation.service.ts:815:36
TS2339: Property 'db' does not exist on type 'ComprehensiveAutomationService'.

ERROR in ./src/features/automation/comprehensive-automation.service.ts:836:43
TS7006: Parameter 'sum' implicitly has an 'any' type.

ERROR in ./src/features/automation/comprehensive-automation.service.ts:836:48
TS7006: Parameter 'setup' implicitly has an 'any' type.

ERROR in ./src/features/channel-management/community-channels.service.ts:350:54
TS2769: No overload matches this call.

ERROR in ./src/features/channel-management/community-channels.service.ts:366:57
TS2769: No overload matches this call.

ERROR in ./src/features/channel-management/community-channels.service.ts:795:12
TS7053: Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{ free: string[]; ai_explorer: string[]; wealth_builder: string[]; dev_premium: string[]; }'.

ERROR in ./src/features/channel-panels/base/unified-panel.base.ts:216:9
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/features/channel-panels/base/unified-panel.base.ts:221:25
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/base/unified-panel.base.ts:222:23
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/base/unified-panel.base.ts:306:27
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/base/unified-panel.base.ts:307:25
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/core/actions/base-action-handler.ts:71:45
TS2345: Argument of type 'unknown' is not assignable to parameter of type 'Error'.

ERROR in ./src/features/channel-panels/core/actions/base-action-handler.ts:74:36
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/core/actions/base-action-handler.ts:78:9
TS2345: Argument of type 'unknown' is not assignable to parameter of type 'Error | undefined'.

ERROR in ./src/features/channel-panels/core/actions/base-action-handler.ts:166:5
TS2375: Type '{ success: true; renderData: PanelRenderData; newState: { sessionData: Record<string, unknown>; } | undefined; shouldUpdatePanel: boolean; }' is not assignable to type 'InteractionResult' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/features/channel-panels/core/actions/community-hub-actions-database.handler.ts:4:10
TS2724: '"../../../../core/database/entities/community-events.entity"' has no exported member named 'CommunityFeedback'. Did you mean 'communityFeedback'?

ERROR in ./src/features/channel-panels/core/actions/community-hub-actions-database.handler.ts:112:52
TS2339: Property 'toLocaleDateString' does not exist on type 'string'.

ERROR in ./src/features/channel-panels/core/actions/community-hub-actions-database.handler.ts:112:95
TS2339: Property 'toLocaleTimeString' does not exist on type 'string'.

ERROR in ./src/features/channel-panels/core/actions/community-hub-actions-database.handler.ts:181:26
TS18048: 'user' is possibly 'undefined'.

ERROR in ./src/features/channel-panels/core/actions/community-hub-actions-database.handler.ts:182:48
TS18048: 'user' is possibly 'undefined'.

ERROR in ./src/features/channel-panels/core/actions/community-hub-actions-database.handler.ts:182:87
TS18048: 'user' is possibly 'undefined'.

ERROR in ./src/features/channel-panels/core/actions/community-hub-actions-database.handler.ts:182:111
TS18048: 'user' is possibly 'undefined'.

ERROR in ./src/features/channel-panels/core/actions/community-hub-actions-database.handler.ts:376:55
TS18048: 'eventToJoin' is possibly 'undefined'.

ERROR in ./src/features/channel-panels/core/actions/community-hub-actions-database.handler.ts:388:59
TS18048: 'eventToJoin' is possibly 'undefined'.

ERROR in ./src/features/channel-panels/core/actions/community-hub-actions-database.handler.ts:390:43
TS18048: 'eventToJoin' is possibly 'undefined'.

ERROR in ./src/features/channel-panels/core/actions/community-hub-actions-database.handler.ts:390:65
TS2339: Property 'toLocaleDateString' does not exist on type 'string'.

ERROR in ./src/features/channel-panels/core/actions/community-hub-actions-database.handler.ts:391:42
TS18048: 'eventToJoin' is possibly 'undefined'.

ERROR in ./src/features/channel-panels/core/actions/community-hub-actions-database.handler.ts:391:64
TS2339: Property 'toLocaleTimeString' does not exist on type 'string'.

ERROR in ./src/features/channel-panels/core/actions/community-hub-actions-database.handler.ts:392:48
TS18048: 'eventToJoin' is possibly 'undefined'.

ERROR in ./src/features/channel-panels/core/actions/community-hub-actions-database.handler.ts:392:86
TS18048: 'eventToJoin' is possibly 'undefined'.

ERROR in ./src/features/channel-panels/core/actions/community-hub-actions-database.handler.ts:392:120
TS18048: 'eventToJoin' is possibly 'undefined'.

ERROR in ./src/features/channel-panels/core/actions/community-hub-actions-database.handler.ts:422:54
TS18048: 'eventToJoin' is possibly 'undefined'.

ERROR in ./src/features/channel-panels/core/actions/community-hub-actions-database.handler.ts:448:9
TS2353: Object literal may only specify known properties, and 'type' does not exist in type 'Omit<CommunityFeedback, "id" | "createdAt" | "updatedAt">'.

ERROR in ./src/features/channel-panels/core/actions/community-hub-actions.handler.ts:208:26
TS18048: 'user' is possibly 'undefined'.

ERROR in ./src/features/channel-panels/core/actions/community-hub-actions.handler.ts:209:48
TS18048: 'user' is possibly 'undefined'.

ERROR in ./src/features/channel-panels/core/actions/community-hub-actions.handler.ts:209:69
TS18048: 'user' is possibly 'undefined'.

ERROR in ./src/features/channel-panels/core/actions/community-hub-actions.handler.ts:209:93
TS18048: 'user' is possibly 'undefined'.

ERROR in ./src/features/channel-panels/core/actions/community-hub-actions.handler.ts:394:39
TS18048: 'eventToJoin' is possibly 'undefined'.

ERROR in ./src/features/channel-panels/core/actions/community-hub-actions.handler.ts:396:57
TS18048: 'eventToJoin' is possibly 'undefined'.

ERROR in ./src/features/channel-panels/core/actions/community-hub-actions.handler.ts:401:11
TS18048: 'eventToJoin' is possibly 'undefined'.

ERROR in ./src/features/channel-panels/core/actions/community-hub-actions.handler.ts:401:42
TS18048: 'eventToJoin' is possibly 'undefined'.

ERROR in ./src/features/channel-panels/core/actions/community-hub-actions.handler.ts:401:77
TS18048: 'eventToJoin' is possibly 'undefined'.

ERROR in ./src/features/channel-panels/core/actions/community-hub-actions.handler.ts:403:33
TS18048: 'eventToJoin' is possibly 'undefined'.

ERROR in ./src/features/channel-panels/core/actions/community-hub-actions.handler.ts:409:31
TS18048: 'eventToJoin' is possibly 'undefined'.

ERROR in ./src/features/channel-panels/core/actions/community-hub-actions.handler.ts:411:7
TS18048: 'eventToJoin' is possibly 'undefined'.

ERROR in ./src/features/channel-panels/core/actions/community-hub-actions.handler.ts:416:59
TS18048: 'eventToJoin' is possibly 'undefined'.

ERROR in ./src/features/channel-panels/core/actions/community-hub-actions.handler.ts:418:43
TS18048: 'eventToJoin' is possibly 'undefined'.

ERROR in ./src/features/channel-panels/core/actions/community-hub-actions.handler.ts:419:42
TS18048: 'eventToJoin' is possibly 'undefined'.

ERROR in ./src/features/channel-panels/core/actions/community-hub-actions.handler.ts:420:48
TS18048: 'eventToJoin' is possibly 'undefined'.

ERROR in ./src/features/channel-panels/core/actions/community-hub-actions.handler.ts:420:82
TS18048: 'eventToJoin' is possibly 'undefined'.

ERROR in ./src/features/channel-panels/core/actions/community-hub-actions.handler.ts:420:116
TS18048: 'eventToJoin' is possibly 'undefined'.

ERROR in ./src/features/channel-panels/core/actions/community-hub-actions.handler.ts:447:54
TS18048: 'eventToJoin' is possibly 'undefined'.

ERROR in ./src/features/channel-panels/core/actions/enhanced-dev-on-demand-actions.handler.ts:557:12
TS7053: Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{ draft: string; open: string; matching: string; assigned: string; in_progress: string; completed: string; cancelled: string; }'.

ERROR in ./src/features/channel-panels/core/actions/gaming-entertainment-actions.handler.ts:862:5
TS2322: Type 'string | undefined' is not assignable to type 'string'.

ERROR in ./src/features/channel-panels/core/actions/goal-tracking-actions.handler.ts:611:5
TS2322: Type 'string | undefined' is not assignable to type 'string'.

ERROR in ./src/features/channel-panels/core/actions/premium-community-actions.handler.ts:99:9
TS2532: Object is possibly 'undefined'.

ERROR in ./src/features/channel-panels/core/actions/premium-community-actions.handler.ts:414:9
TS2532: Object is possibly 'undefined'.

ERROR in ./src/features/channel-panels/core/actions/technical-support-actions-database.handler.ts:4:10
TS2724: '"../../../../core/database/entities/support-tickets.entity"' has no exported member named 'KnowledgeBaseArticle'. Did you mean 'knowledgeBaseArticles'?

ERROR in ./src/features/channel-panels/core/actions/technical-support-actions-database.handler.ts:4:32
TS2724: '"../../../../core/database/entities/support-tickets.entity"' has no exported member named 'TroubleshootingGuide'. Did you mean 'troubleshootingGuides'?

ERROR in ./src/features/channel-panels/core/actions/technical-support-actions-database.handler.ts:4:54
TS2724: '"../../../../core/database/entities/support-tickets.entity"' has no exported member named 'SystemStatus'. Did you mean 'systemStatus'?

ERROR in ./src/features/channel-panels/core/actions/technical-support-actions-database.handler.ts:167:67
TS2339: Property 'difficulty' does not exist on type 'KnowledgeBaseArticle'.

ERROR in ./src/features/channel-panels/core/actions/technical-support-actions-database.handler.ts:174:67
TS2551: Property 'subcategory' does not exist on type 'KnowledgeBaseArticle'. Did you mean 'category'?

ERROR in ./src/features/channel-panels/core/actions/technical-support-actions-database.handler.ts:176:57
TS2339: Property 'toLocaleDateString' does not exist on type 'string'.

ERROR in ./src/features/channel-panels/core/actions/technical-support-actions-database.handler.ts:317:84
TS2339: Property 'solutions' does not exist on type 'TroubleshootingGuide'.

ERROR in ./src/features/channel-panels/core/actions/technical-support-actions-database.handler.ts:333:74
TS2339: Property 'solutions' does not exist on type 'TroubleshootingGuide'.

ERROR in ./src/features/channel-panels/core/actions/technical-support-actions-database.handler.ts:477:69
TS18048: 's.responseTime' is possibly 'undefined'.

ERROR in ./src/features/channel-panels/core/actions/technical-support-actions-database.handler.ts:478:70
TS2339: Property 'incidents' does not exist on type 'SystemStatus'.

ERROR in ./src/features/channel-panels/core/actions/technical-support-actions-database.handler.ts:534:56
TS2345: Argument of type '{ userId: string; guildId: string; title: string; description: string; category: string; priority: string; }' is not assignable to parameter of type 'Omit<SupportTicket, "id" | "createdAt" | "updatedAt" | "ticketNumber">'.

ERROR in ./src/features/channel-panels/core/actions/technical-support-actions-database.handler.ts:549:57
TS2339: Property 'toLocaleDateString' does not exist on type 'string'.

ERROR in ./src/features/channel-panels/core/content/base-content-provider.ts:87:37
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/core/content/base-content-provider.ts:90:9
TS2345: Argument of type 'unknown' is not assignable to parameter of type 'Error | undefined'.

ERROR in ./src/features/channel-panels/core/content/base-content-provider.ts:200:5
TS2375: Type '{ data: T; source: string; timestamp: Date; expiresAt: Date | undefined; metadata: { providerId: string; fetchedAt: string; }; }' is not assignable to type 'ContentResponse<T>' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/features/channel-panels/core/content/base-content-provider.ts:360:5
TS2375: Type '{ totalEntries: number; expiredEntries: number; cacheHitRate: number; oldestEntry: Date | undefined; }' is not assignable to type '{ totalEntries: number; expiredEntries: number; cacheHitRate: number; oldestEntry?: Date; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/features/channel-panels/core/orchestration/panel-orchestrator.ts:127:26
TS2345: Argument of type '{ embeds: EmbedBuilder[]; components: any; content: string | undefined; }' is not assignable to parameter of type 'string | MessagePayload | MessageCreateOptions'.

ERROR in ./src/features/channel-panels/core/orchestration/panel-orchestrator.ts:381:29
TS18048: 'panelActionPart' is possibly 'undefined'.

ERROR in ./src/features/channel-panels/core/orchestration/panel-orchestrator.ts:386:23
TS18048: 'panelActionPart' is possibly 'undefined'.

ERROR in ./src/features/channel-panels/core/orchestration/panel-orchestrator.ts:387:22
TS18048: 'panelActionPart' is possibly 'undefined'.

ERROR in ./src/features/channel-panels/core/orchestration/panel-orchestrator.ts:400:5
TS2375: Type '{ panelType: string; actionId: string; actionData: Record<string, unknown> | undefined; }' is not assignable to type '{ panelType: string | null; actionId: string; actionData?: Record<string, unknown>; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/features/channel-panels/core/state/panel-state-manager.ts:129:13
TS2375: Type '{ panelId: string; userId: string; currentView: string; previousView: undefined; sessionData: { userContext: { username: string; guildId: string; channelId: string; permissions: string[]; preferredLanguage: string | undefined; timezone: string | undefined; }; createdAt: string; preferences: {}; interactions: never[]...' is not assignable to type 'PanelState' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/features/channel-panels/core/state/panel-state-manager.ts:380:5
TS2375: Type '{ panelId: string; userId: string; currentView: string; previousView: string | undefined; sessionData: any; lastInteraction: Date; viewCount: number; isActive: boolean; }' is not assignable to type 'PanelState' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/features/channel-panels/enhanced-channel-panel-orchestrator.service.ts:205:15
TS18048: 'channelResult' is possibly 'undefined'.

ERROR in ./src/features/channel-panels/enhanced-channel-panel-orchestrator.service.ts:209:27
TS18048: 'channelResult' is possibly 'undefined'.

ERROR in ./src/features/channel-panels/enhanced-channel-panel-orchestrator.service.ts:214:22
TS18048: 'channelResult' is possibly 'undefined'.

ERROR in ./src/features/channel-panels/enhanced-channel-panel-orchestrator.service.ts:222:18
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/enhanced-channel-panel-orchestrator.service.ts:284:15
TS18046: 'deferError' is of type 'unknown'.

ERROR in ./src/features/channel-panels/enhanced-channel-panel-orchestrator.service.ts:288:15
TS18046: 'deferError' is of type 'unknown'.

ERROR in ./src/features/channel-panels/enhanced-channel-panel-orchestrator.service.ts:1139:15
TS18046: 'followUpError' is of type 'unknown'.

ERROR in ./src/features/channel-panels/enhanced-channel-panel-orchestrator.service.ts:1139:47
TS18046: 'followUpError' is of type 'unknown'.

ERROR in ./src/features/channel-panels/enhanced-channel-panel-orchestrator.service.ts:1148:11
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/enhanced-channel-panel-orchestrator.service.ts:1150:18
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/enhanced-channel-panel-orchestrator.service.ts:1239:38
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/enhanced-channel-panel-orchestrator.service.ts:1255:46
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/enhanced-channel-panel-orchestrator.service.ts:1283:9
TS2322: Type 'null' is not assignable to type 'Date'.

ERROR in ./src/features/channel-panels/enhanced-channel-panel-orchestrator.service.ts:1302:66
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/factories/unified-panel.factory.ts:345:15
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/features/channel-panels/factories/unified-panel.factory.ts:363:15
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/features/channel-panels/factories/unified-panel.factory.ts:369:15
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/features/channel-panels/factories/unified-panel.factory.ts:386:51
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/features/channel-panels/factories/unified-panel.factory.ts:457:9
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/features/channel-panels/factories/unified-panel.factory.ts:538:11
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/features/channel-panels/handlers/guidelines-command.handler.ts:12:3
TS2564: Property 'query' has no initializer and is not definitely assigned in the constructor.

ERROR in ./src/features/channel-panels/handlers/guidelines-command.handler.ts:28:3
TS2564: Property 'type' has no initializer and is not definitely assigned in the constructor.

ERROR in ./src/features/channel-panels/handlers/guidelines-command.handler.ts:49:62
TS2345: Argument of type 'string | null' is not assignable to parameter of type 'string'.

ERROR in ./src/features/channel-panels/handlers/guidelines-command.handler.ts:72:20
TS2379: Argument of type '{ text: string; iconURL: string | undefined; }' is not assignable to parameter of type 'Omit<EmbedFooterData, "proxyIconURL">' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/features/channel-panels/handlers/guidelines-command.handler.ts:190:62
TS2345: Argument of type 'string | null' is not assignable to parameter of type 'string'.

ERROR in ./src/features/channel-panels/handlers/guidelines-command.handler.ts:223:20
TS2379: Argument of type '{ text: string; iconURL: string | undefined; }' is not assignable to parameter of type 'Omit<EmbedFooterData, "proxyIconURL">' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/features/channel-panels/handlers/guidelines-command.handler.ts:276:23
TS2379: Argument of type '{ text: string; iconURL: undefined; }' is not assignable to parameter of type 'Omit<EmbedFooterData, "proxyIconURL">' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/features/channel-panels/handlers/guidelines-command.handler.ts:327:20
TS2379: Argument of type '{ text: string; iconURL: undefined; }' is not assignable to parameter of type 'Omit<EmbedFooterData, "proxyIconURL">' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/features/channel-panels/handlers/ticket-reaction.handler.ts:61:60
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:195:39
TS2339: Property 'create' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:219:36
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:226:43
TS2339: Property 'findMany' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:246:13
TS2322: Type '{ isBookmarked: boolean; userRating: number | undefined; id: string; name: string; description: string; category: string; rating: number; pricing: string; features: string[]; url: string; tags: string[]; ... 6 more ...; updatedAt: string; }[]' is not assignable to type 'AIToolWithBookmark[]'.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:252:54
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:258:49
TS7006: Parameter 'b' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:280:40
TS2339: Property 'search' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:290:16
TS7006: Parameter 'a' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:290:19
TS7006: Parameter 'b' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:294:32
TS7006: Parameter 'tool' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:299:54
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:305:49
TS7006: Parameter 'b' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:328:48
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:334:51
TS7006: Parameter 'b' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:339:26
TS2339: Property 'create' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:361:43
TS2339: Property 'create' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:385:40
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:392:43
TS2339: Property 'findMany' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:412:13
TS2322: Type '{ userProgress: TutorialProgress | undefined; isCompleted: boolean; progressPercentage: number; id: string; title: string; description: string; content: string; difficulty: string; ... 11 more ...; updatedAt: string; }[]' is not assignable to type 'AITutorialWithProgress[]'.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:419:57
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:425:50
TS7006: Parameter 'p' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:451:51
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:457:54
TS7006: Parameter 'p' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:462:26
TS2339: Property 'create' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:477:26
TS2339: Property 'increment' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:498:51
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:504:54
TS7006: Parameter 'p' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:521:26
TS2339: Property 'update' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:530:28
TS2339: Property 'increment' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:543:39
TS2339: Property 'create' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:568:49
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:576:35
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:583:35
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:590:43
TS2339: Property 'findMany' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:614:50
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:632:28
TS2339: Property 'update' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:642:28
TS2339: Property 'create' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:671:46
TS2339: Property 'findMany' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:678:48
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:686:50
TS2339: Property 'findMany' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:692:47
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:699:51
TS7006: Parameter 'sum' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:699:56
TS7006: Parameter 'p' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:723:48
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:730:28
TS2339: Property 'create' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/ai-mastery-database.service.ts:745:28
TS2339: Property 'update' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:201:47
TS2339: Property 'create' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:239:46
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:261:27
TS7006: Parameter 'a' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:261:30
TS7006: Parameter 'b' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:270:41
TS7006: Parameter 'announcement' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:271:49
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:302:26
TS2339: Property 'update' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:323:26
TS2339: Property 'update' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:345:43
TS2339: Property 'create' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:368:45
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:397:52
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:403:47
TS7006: Parameter 's' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:407:44
TS2339: Property 'update' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:420:47
TS2339: Property 'create' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:439:52
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:445:51
TS7006: Parameter 's' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:457:56
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:463:51
TS7006: Parameter 'r' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:471:26
TS2339: Property 'create' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:478:26
TS2339: Property 'increment' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:495:42
TS2339: Property 'create' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:505:26
TS2339: Property 'increment' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:522:46
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:532:28
TS7006: Parameter 'a' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:532:31
TS7006: Parameter 'b' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:552:50
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:558:45
TS7006: Parameter 'a' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:563:26
TS2339: Property 'create' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:576:26
TS2339: Property 'increment' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:593:43
TS2339: Property 'create' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:613:42
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:629:23
TS7006: Parameter 'a' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:629:26
TS7006: Parameter 'b' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:644:51
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:660:59
TS7006: Parameter 'sum' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:660:64
TS7006: Parameter 'a' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:661:60
TS7006: Parameter 'sum' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:661:65
TS7006: Parameter 'a' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:667:35
TS7006: Parameter 'a' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:699:48
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:706:28
TS2339: Property 'create' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/announcement-database.service.ts:721:28
TS2339: Property 'update' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/community-database.service.ts:120:40
TS2339: Property 'create' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/community-database.service.ts:140:46
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/community-database.service.ts:155:28
TS7006: Parameter 'a' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/community-database.service.ts:155:31
TS7006: Parameter 'b' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/community-database.service.ts:164:34
TS7006: Parameter 'event' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/community-database.service.ts:165:51
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/community-database.service.ts:189:55
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/community-database.service.ts:195:57
TS7006: Parameter 'p' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/community-database.service.ts:201:40
TS2339: Property 'findById' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/community-database.service.ts:219:11
TS2353: Object literal may only specify known properties, and 'createdAt' does not exist in type 'EventParticipant'.

ERROR in ./src/features/channel-panels/services/community-database.service.ts:225:40
TS2345: Argument of type 'Record<string, string>' is not assignable to parameter of type 'string | number | Buffer<ArrayBufferLike>'.

ERROR in ./src/features/channel-panels/services/community-database.service.ts:255:42
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/community-database.service.ts:263:21
TS7006: Parameter 'a' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/community-database.service.ts:263:24
TS7006: Parameter 'b' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/community-database.service.ts:270:35
TS7006: Parameter 'entry' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/community-database.service.ts:271:43
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/community-database.service.ts:294:47
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/community-database.service.ts:300:47
TS7006: Parameter 'entry' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/community-database.service.ts:306:28
TS2339: Property 'update' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/community-database.service.ts:319:28
TS2339: Property 'create' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/community-database.service.ts:345:43
TS2339: Property 'create' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/community-database.service.ts:366:43
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/community-database.service.ts:386:43
TS2339: Property 'findById' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/community-database.service.ts:393:26
TS2339: Property 'increment' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/community-database.service.ts:396:26
TS2339: Property 'update' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/community-database.service.ts:415:48
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/community-database.service.ts:423:28
TS2339: Property 'create' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/community-database.service.ts:439:28
TS2339: Property 'update' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/consolidated-panel-core.service.ts:75:43
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/features/channel-panels/services/consolidated-panel-core.service.ts:109:34
TS2339: Property 'query' does not exist on type 'DatabaseService'.

ERROR in ./src/features/channel-panels/services/consolidated-panel-core.service.ts:130:9
TS2322: Type 'PanelState | undefined' is not assignable to type 'PanelState | null'.

ERROR in ./src/features/channel-panels/services/consolidated-panel-core.service.ts:134:49
TS2339: Property 'query' does not exist on type 'DatabaseService'.

ERROR in ./src/features/channel-panels/services/consolidated-panel-core.service.ts:173:34
TS2339: Property 'query' does not exist on type 'DatabaseService'.

ERROR in ./src/features/channel-panels/services/consolidated-panel-core.service.ts:196:49
TS2339: Property 'query' does not exist on type 'DatabaseService'.

ERROR in ./src/features/channel-panels/services/consolidated-panel-core.service.ts:205:37
TS7006: Parameter 'sum' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/consolidated-panel-core.service.ts:205:42
TS7006: Parameter 'row' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/consolidated-panel-core.service.ts:206:38
TS7006: Parameter 'acc' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/consolidated-panel-core.service.ts:206:43
TS7006: Parameter 'row' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/consolidated-panel-core.service.ts:265:34
TS2339: Property 'query' does not exist on type 'DatabaseService'.

ERROR in ./src/features/channel-panels/services/consolidated-panel-core.service.ts:348:43
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/features/channel-panels/services/consolidated-panel-core.service.ts:460:49
TS2339: Property 'query' does not exist on type 'DatabaseService'.

ERROR in ./src/features/channel-panels/services/consolidated-panel-core.service.ts:475:39
TS7006: Parameter 'sum' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/consolidated-panel-core.service.ts:475:44
TS7006: Parameter 'row' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/consolidated-panel-core.service.ts:476:40
TS7006: Parameter 'acc' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/consolidated-panel-core.service.ts:476:45
TS7006: Parameter 'row' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/consolidated-panel-lifecycle.service.ts:122:77
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/services/consolidated-panel-lifecycle.service.ts:126:20
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/services/consolidated-panel-lifecycle.service.ts:201:49
TS2339: Property 'query' does not exist on type 'DatabaseService'.

ERROR in ./src/features/channel-panels/services/consolidated-panel-lifecycle.service.ts:318:42
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/services/consolidated-panel-lifecycle.service.ts:370:18
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/services/consolidated-panel-lifecycle.service.ts:412:73
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/services/consolidated-panel-lifecycle.service.ts:416:18
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/services/consolidated-panel-lifecycle.service.ts:526:47
TS2339: Property 'query' does not exist on type 'DatabaseService'.

ERROR in ./src/features/channel-panels/services/consolidated-panel-lifecycle.service.ts:553:36
TS2339: Property 'query' does not exist on type 'DatabaseService'.

ERROR in ./src/features/channel-panels/services/consolidated-panel-lifecycle.service.ts:567:36
TS2339: Property 'query' does not exist on type 'DatabaseService'.

ERROR in ./src/features/channel-panels/services/consolidated-panel-lifecycle.service.ts:568:36
TS2339: Property 'query' does not exist on type 'DatabaseService'.

ERROR in ./src/features/channel-panels/services/consolidated-panel-lifecycle.service.ts:569:36
TS2339: Property 'query' does not exist on type 'DatabaseService'.

ERROR in ./src/features/channel-panels/services/consolidated-panel-lifecycle.service.ts:588:16
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/services/consolidated-panel-lifecycle.service.ts:607:5
TS2532: Object is possibly 'undefined'.

ERROR in ./src/features/channel-panels/services/consolidated-panel-lifecycle.service.ts:613:32
TS2339: Property 'query' does not exist on type 'DatabaseService'.

ERROR in ./src/features/channel-panels/services/consolidated-panel-lifecycle.service.ts:631:32
TS2339: Property 'query' does not exist on type 'DatabaseService'.

ERROR in ./src/features/channel-panels/services/consolidated-panel-lifecycle.service.ts:656:47
TS2339: Property 'query' does not exist on type 'DatabaseService'.

ERROR in ./src/features/channel-panels/services/consolidated-panel-lifecycle.service.ts:683:32
TS2339: Property 'query' does not exist on type 'DatabaseService'.

ERROR in ./src/features/channel-panels/services/consolidated-panel-lifecycle.service.ts:691:32
TS2339: Property 'query' does not exist on type 'DatabaseService'.

ERROR in ./src/features/channel-panels/services/consolidated-panel-lifecycle.service.ts:699:47
TS2339: Property 'query' does not exist on type 'DatabaseService'.

ERROR in ./src/features/channel-panels/services/consolidated-panel-lifecycle.service.ts:711:49
TS2339: Property 'query' does not exist on type 'DatabaseService'.

ERROR in ./src/features/channel-panels/services/consolidated-panel-lifecycle.service.ts:733:11
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/services/consolidated-panel-lifecycle.service.ts:737:65
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/services/consolidated-panel-lifecycle.service.ts:744:49
TS2339: Property 'query' does not exist on type 'DatabaseService'.

ERROR in ./src/features/channel-panels/services/consolidated-panel-lifecycle.service.ts:754:11
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/services/consolidated-panel-lifecycle.service.ts:758:64
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/services/dynamic-content.service.ts:10:3
TS2305: Module '"@/core/database"' has no exported member 'dynamicContentSources'.

ERROR in ./src/features/channel-panels/services/dynamic-content.service.ts:11:3
TS2305: Module '"@/core/database"' has no exported member 'dynamicContentCache'.

ERROR in ./src/features/channel-panels/services/dynamic-content.service.ts:12:3
TS2305: Module '"@/core/database"' has no exported member 'panelContentMappings'.

ERROR in ./src/features/channel-panels/services/dynamic-content.service.ts:13:3
TS2305: Module '"@/core/database"' has no exported member 'contentFreshnessTracking'.

ERROR in ./src/features/channel-panels/services/dynamic-content.service.ts:14:3
TS2305: Module '"@/core/database"' has no exported member 'DynamicContentSource'.

ERROR in ./src/features/channel-panels/services/dynamic-content.service.ts:15:3
TS2305: Module '"@/core/database"' has no exported member 'NewDynamicContentSource'.

ERROR in ./src/features/channel-panels/services/dynamic-content.service.ts:16:3
TS2305: Module '"@/core/database"' has no exported member 'DynamicContentCache'.

ERROR in ./src/features/channel-panels/services/dynamic-content.service.ts:17:3
TS2305: Module '"@/core/database"' has no exported member 'NewDynamicContentCache'.

ERROR in ./src/features/channel-panels/services/dynamic-content.service.ts:18:3
TS2305: Module '"@/core/database"' has no exported member 'PanelContentMapping'.

ERROR in ./src/features/channel-panels/services/dynamic-content.service.ts:19:3
TS2305: Module '"@/core/database"' has no exported member 'ContentFreshnessTracking'.

ERROR in ./src/features/channel-panels/services/dynamic-content.service.ts:55:39
TS2551: Property 'getDb' does not exist on type 'DatabaseService'. Did you mean 'get'?

ERROR in ./src/features/channel-panels/services/dynamic-content.service.ts:67:63
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/services/dynamic-content.service.ts:67:80
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/services/dynamic-content.service.ts:77:39
TS2551: Property 'getDb' does not exist on type 'DatabaseService'. Did you mean 'get'?

ERROR in ./src/features/channel-panels/services/dynamic-content.service.ts:107:72
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/services/dynamic-content.service.ts:107:89
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/services/dynamic-content.service.ts:123:39
TS2551: Property 'getDb' does not exist on type 'DatabaseService'. Did you mean 'get'?

ERROR in ./src/features/channel-panels/services/dynamic-content.service.ts:181:75
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/services/dynamic-content.service.ts:181:92
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/services/dynamic-content.service.ts:243:77
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/services/dynamic-content.service.ts:243:94
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/services/dynamic-content.service.ts:246:52
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/services/dynamic-content.service.ts:250:16
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/services/dynamic-content.service.ts:371:39
TS2551: Property 'getDb' does not exist on type 'DatabaseService'. Did you mean 'get'?

ERROR in ./src/features/channel-panels/services/dynamic-content.service.ts:424:68
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/services/dynamic-content.service.ts:424:85
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/services/dynamic-content.service.ts:433:39
TS2551: Property 'getDb' does not exist on type 'DatabaseService'. Did you mean 'get'?

ERROR in ./src/features/channel-panels/services/dynamic-content.service.ts:464:74
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/services/dynamic-content.service.ts:464:91
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/services/dynamic-content.service.ts:474:39
TS2551: Property 'getDb' does not exist on type 'DatabaseService'. Did you mean 'get'?

ERROR in ./src/features/channel-panels/services/dynamic-content.service.ts:508:79
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/services/dynamic-content.service.ts:508:96
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/services/dynamic-content.service.ts:519:39
TS2551: Property 'getDb' does not exist on type 'DatabaseService'. Did you mean 'get'?

ERROR in ./src/features/channel-panels/services/dynamic-content.service.ts:529:13
TS2304: Cannot find name 'lt'.

ERROR in ./src/features/channel-panels/services/dynamic-content.service.ts:552:61
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/services/dynamic-content.service.ts:552:78
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/services/dynamic-content.service.ts:686:39
TS2551: Property 'getDb' does not exist on type 'DatabaseService'. Did you mean 'get'?

ERROR in ./src/features/channel-panels/services/dynamic-content.service.ts:703:58
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/services/dynamic-content.service.ts:703:75
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/services/guidelines.service.ts:370:7
TS2412: Type 'string | undefined' is not assignable to type 'string' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the type of the target.

ERROR in ./src/features/channel-panels/services/networking-database.service.ts:5:5
TS2305: Module '"@/core/database"' has no exported member 'businessOpportunities'.

ERROR in ./src/features/channel-panels/services/networking-database.service.ts:6:5
TS2305: Module '"@/core/database"' has no exported member 'jobApplications'.

ERROR in ./src/features/channel-panels/services/networking-database.service.ts:7:5
TS2305: Module '"@/core/database"' has no exported member 'jobPostings'.

ERROR in ./src/features/channel-panels/services/networking-database.service.ts:8:5
TS2305: Module '"@/core/database"' has no exported member 'opportunityInterests'.

ERROR in ./src/features/channel-panels/services/networking-database.service.ts:9:5
TS2305: Module '"@/core/database"' has no exported member 'professionalConnections'.

ERROR in ./src/features/channel-panels/services/networking-database.service.ts:10:5
TS2305: Module '"@/core/database"' has no exported member 'professionalProfiles'.

ERROR in ./src/features/channel-panels/services/networking-database.service.ts:11:5
TS2305: Module '"@/core/database"' has no exported member 'skillEndorsements'.

ERROR in ./src/features/channel-panels/services/networking-database.service.ts:12:10
TS2305: Module '"@/core/database"' has no exported member 'BusinessOpportunity'.

ERROR in ./src/features/channel-panels/services/networking-database.service.ts:13:10
TS2305: Module '"@/core/database"' has no exported member 'JobApplication'.

ERROR in ./src/features/channel-panels/services/networking-database.service.ts:14:10
TS2305: Module '"@/core/database"' has no exported member 'JobPosting'.

ERROR in ./src/features/channel-panels/services/networking-database.service.ts:15:10
TS2305: Module '"@/core/database"' has no exported member 'NewBusinessOpportunity'.

ERROR in ./src/features/channel-panels/services/networking-database.service.ts:16:10
TS2305: Module '"@/core/database"' has no exported member 'NewJobPosting'.

ERROR in ./src/features/channel-panels/services/networking-database.service.ts:17:10
TS2305: Module '"@/core/database"' has no exported member 'NewProfessionalProfile'.

ERROR in ./src/features/channel-panels/services/networking-database.service.ts:18:10
TS2305: Module '"@/core/database"' has no exported member 'ProfessionalConnection'.

ERROR in ./src/features/channel-panels/services/networking-database.service.ts:19:10
TS2305: Module '"@/core/database"' has no exported member 'ProfessionalProfile'.

ERROR in ./src/features/channel-panels/services/networking-database.service.ts:20:10
TS2305: Module '"@/core/database"' has no exported member 'SkillEndorsement'.

ERROR in ./src/features/channel-panels/services/networking-database.service.ts:22:10
TS2305: Module '"@/core/database"' has no exported member 'users'.

ERROR in ./src/features/channel-panels/services/networking-database.service.ts:55:33
TS2551: Property 'getDb' does not exist on type 'DatabaseService'. Did you mean 'get'?

ERROR in ./src/features/channel-panels/services/networking-database.service.ts:108:59
TS7006: Parameter 'acc' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/networking-database.service.ts:108:64
TS7006: Parameter 'e' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/networking-database.service.ts:154:30
TS2304: Cannot find name 'ilike'.

ERROR in ./src/features/channel-panels/services/networking-database.service.ts:250:20
TS2304: Cannot find name 'inArray'.

ERROR in ./src/features/channel-panels/services/networking-database.service.ts:362:9
TS2304: Cannot find name 'gte'.

ERROR in ./src/features/channel-panels/services/networking-database.service.ts:378:30
TS2304: Cannot find name 'ilike'.

ERROR in ./src/features/channel-panels/services/networking-database.service.ts:534:9
TS2304: Cannot find name 'gte'.

ERROR in ./src/features/channel-panels/services/networking-database.service.ts:544:30
TS2304: Cannot find name 'ilike'.

ERROR in ./src/features/channel-panels/services/panel-features.service.ts:60:49
TS2339: Property 'query' does not exist on type 'DatabaseService'.

ERROR in ./src/features/channel-panels/services/panel-features.service.ts:91:54
TS2339: Property 'query' does not exist on type 'DatabaseService'.

ERROR in ./src/features/channel-panels/services/panel-features.service.ts:179:35
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/features/channel-panels/services/panel-features.service.ts:245:13
TS2375: Type '{ date: Date; completed: boolean; notes: string | undefined; value: number | undefined; }' is not assignable to type 'HabitEntry' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/features/channel-panels/services/panel-features.service.ts:282:34
TS2339: Property 'query' does not exist on type 'DatabaseService'.

ERROR in ./src/features/channel-panels/services/panel-features.service.ts:310:49
TS2339: Property 'query' does not exist on type 'DatabaseService'.

ERROR in ./src/features/channel-panels/services/panel-features.service.ts:317:47
TS7006: Parameter 'sum' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/panel-features.service.ts:317:52
TS7006: Parameter 'h' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/panel-features.service.ts:343:49
TS2339: Property 'query' does not exist on type 'DatabaseService'.

ERROR in ./src/features/channel-panels/services/panel-features.service.ts:386:49
TS2339: Property 'query' does not exist on type 'DatabaseService'.

ERROR in ./src/features/channel-panels/services/panel-features.service.ts:482:32
TS2339: Property 'query' does not exist on type 'DatabaseService'.

ERROR in ./src/features/channel-panels/services/panel-features.service.ts:498:5
TS2375: Type '{ id: string; title: string; category: string; subCategory: string | undefined; symptoms: string[]; diagnosticSteps: string[]; solutions: string[]; commonCauses: string[]; preventionTips: string[]; relatedIssues: never[]; difficulty: "medium"; estimatedTime: string; createdAt: Date; updatedAt: Date; }' is not assignable to type 'TroubleshootingGuide' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/features/channel-panels/services/support-database.service.ts:154:46
TS2339: Property 'increment' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/support-database.service.ts:158:41
TS2339: Property 'create' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/support-database.service.ts:179:42
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/support-database.service.ts:191:28
TS7006: Parameter 'ticket' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/support-database.service.ts:192:48
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/support-database.service.ts:215:41
TS2339: Property 'findById' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/support-database.service.ts:220:44
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/support-database.service.ts:227:23
TS7006: Parameter 'a' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/support-database.service.ts:227:26
TS7006: Parameter 'b' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/support-database.service.ts:240:43
TS2339: Property 'create' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/support-database.service.ts:251:28
TS2339: Property 'update' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/support-database.service.ts:281:26
TS2339: Property 'update' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/support-database.service.ts:297:42
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/support-database.service.ts:315:46
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/support-database.service.ts:322:49
TS7006: Parameter 'a' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/support-database.service.ts:322:52
TS7006: Parameter 'b' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/support-database.service.ts:355:39
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/support-database.service.ts:362:43
TS2339: Property 'findMany' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/support-database.service.ts:384:43
TS2339: Property 'search' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/support-database.service.ts:394:16
TS7006: Parameter 'a' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/support-database.service.ts:394:19
TS7006: Parameter 'b' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/support-database.service.ts:405:26
TS2339: Property 'increment' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/support-database.service.ts:415:26
TS2339: Property 'increment' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/support-database.service.ts:430:37
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/support-database.service.ts:437:43
TS2339: Property 'findMany' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/support-database.service.ts:459:40
TS2339: Property 'findById' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/support-database.service.ts:479:41
TS2339: Property 'findMany' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/support-database.service.ts:487:24
TS7006: Parameter 'a' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/support-database.service.ts:487:27
TS7006: Parameter 'b' implicitly has an 'any' type.

ERROR in ./src/features/channel-panels/services/support-database.service.ts:499:51
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/support-database.service.ts:513:28
TS2339: Property 'update' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/support-database.service.ts:521:28
TS2339: Property 'create' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/support-database.service.ts:541:48
TS2339: Property 'findByIndex' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/support-database.service.ts:548:28
TS2339: Property 'create' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/support-database.service.ts:563:28
TS2339: Property 'update' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/channel-panels/services/ticket-system.service.ts:103:53
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/services/ticket-system.service.ts:144:58
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/services/ticket-system.service.ts:149:52
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:145:49
TS2304: Cannot find name 'DatabaseService'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:152:53
TS2304: Cannot find name 'NewMarketData'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:157:15
TS2304: Cannot find name 'marketData'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:158:19
TS2304: Cannot find name 'marketData'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:163:19
TS2304: Cannot find name 'marketData'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:169:21
TS2304: Cannot find name 'marketData'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:175:46
TS2304: Cannot find name 'marketData'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:189:33
TS2304: Cannot find name 'marketData'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:192:30
TS2304: Cannot find name 'inArray'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:192:38
TS2304: Cannot find name 'marketData'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:196:33
TS2304: Cannot find name 'marketData'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:201:15
TS2304: Cannot find name 'marketData'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:203:23
TS2304: Cannot find name 'marketData'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:215:15
TS2304: Cannot find name 'marketData'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:217:14
TS2304: Cannot find name 'marketData'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:219:15
TS2304: Cannot find name 'marketData'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:220:15
TS2304: Cannot find name 'marketData'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:223:23
TS2304: Cannot find name 'marketData'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:232:45
TS2304: Cannot find name 'NewTradingPortfolio'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:234:48
TS2304: Cannot find name 'tradingPortfolios'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:250:15
TS2304: Cannot find name 'tradingPortfolios'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:251:19
TS2304: Cannot find name 'tradingPortfolios'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:252:17
TS2304: Cannot find name 'tradingPortfolios'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:253:24
TS2304: Cannot find name 'tradingPortfolios'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:254:23
TS2304: Cannot find name 'tradingPortfolios'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:255:22
TS2304: Cannot find name 'tradingPortfolios'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:256:26
TS2304: Cannot find name 'tradingPortfolios'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:257:33
TS2304: Cannot find name 'tradingPortfolios'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:258:22
TS2304: Cannot find name 'tradingPortfolios'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:259:21
TS2304: Cannot find name 'tradingPortfolios'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:260:22
TS2304: Cannot find name 'tradingPortfolios'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:261:21
TS2304: Cannot find name 'tradingPortfolios'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:262:20
TS2304: Cannot find name 'tradingPortfolios'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:263:24
TS2304: Cannot find name 'tradingPortfolios'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:264:22
TS2304: Cannot find name 'tradingPortfolios'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:265:22
TS2304: Cannot find name 'tradingPortfolios'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:266:22
TS2304: Cannot find name 'tradingPortfolios'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:267:55
TS2304: Cannot find name 'portfolioHoldings'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:268:58
TS2304: Cannot find name 'tradingTransactions'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:270:15
TS2304: Cannot find name 'tradingPortfolios'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:271:19
TS2304: Cannot find name 'portfolioHoldings'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:271:41
TS2304: Cannot find name 'portfolioHoldings'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:271:72
TS2304: Cannot find name 'tradingPortfolios'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:272:19
TS2304: Cannot find name 'tradingTransactions'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:272:43
TS2304: Cannot find name 'tradingTransactions'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:272:76
TS2304: Cannot find name 'tradingPortfolios'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:274:14
TS2304: Cannot find name 'tradingPortfolios'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:275:14
TS2304: Cannot find name 'tradingPortfolios'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:277:18
TS2304: Cannot find name 'tradingPortfolios'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:278:23
TS2304: Cannot find name 'tradingPortfolios'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:278:58
TS2304: Cannot find name 'tradingPortfolios'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:291:15
TS2304: Cannot find name 'portfolioHoldings'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:292:19
TS2304: Cannot find name 'portfolioHoldings'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:306:17
TS2304: Cannot find name 'tradingPortfolios'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:315:19
TS2304: Cannot find name 'tradingPortfolios'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:325:38
TS2304: Cannot find name 'NewPortfolioHolding'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:330:15
TS2304: Cannot find name 'portfolioHoldings'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:332:14
TS2304: Cannot find name 'portfolioHoldings'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:333:14
TS2304: Cannot find name 'portfolioHoldings'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:346:19
TS2304: Cannot find name 'portfolioHoldings'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:357:21
TS2304: Cannot find name 'portfolioHoldings'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:363:46
TS2304: Cannot find name 'portfolioHoldings'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:382:15
TS2304: Cannot find name 'portfolioHoldings'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:383:19
TS2304: Cannot find name 'portfolioHoldings'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:384:23
TS2304: Cannot find name 'portfolioHoldings'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:392:46
TS2304: Cannot find name 'NewTradingTransaction'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:394:50
TS2304: Cannot find name 'tradingTransactions'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:417:17
TS2304: Cannot find name 'portfolioHoldings'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:419:16
TS2304: Cannot find name 'portfolioHoldings'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:420:16
TS2304: Cannot find name 'portfolioHoldings'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:432:23
TS2304: Cannot find name 'portfolioHoldings'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:433:25
TS2304: Cannot find name 'portfolioHoldings'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:441:23
TS2304: Cannot find name 'portfolioHoldings'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:449:25
TS2304: Cannot find name 'portfolioHoldings'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:469:15
TS2304: Cannot find name 'tradingTransactions'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:470:19
TS2304: Cannot find name 'tradingTransactions'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:471:23
TS2304: Cannot find name 'tradingTransactions'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:480:37
TS2304: Cannot find name 'NewTradingAlert'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:482:44
TS2304: Cannot find name 'tradingAlerts'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:497:12
TS2304: Cannot find name 'tradingAlerts'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:498:12
TS2304: Cannot find name 'tradingAlerts'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:502:33
TS2304: Cannot find name 'tradingAlerts'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:507:15
TS2304: Cannot find name 'tradingAlerts'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:509:23
TS2304: Cannot find name 'tradingAlerts'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:521:15
TS2304: Cannot find name 'tradingAlerts'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:523:14
TS2304: Cannot find name 'tradingAlerts'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:524:14
TS2304: Cannot find name 'tradingAlerts'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:533:17
TS2304: Cannot find name 'marketData'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:534:21
TS2304: Cannot find name 'marketData'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:557:21
TS2304: Cannot find name 'tradingAlerts'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:563:23
TS2304: Cannot find name 'tradingAlerts'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:581:43
TS2304: Cannot find name 'NewTradingStrategy'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:583:47
TS2304: Cannot find name 'tradingStrategies'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:599:15
TS2304: Cannot find name 'tradingStrategies'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:601:14
TS2304: Cannot find name 'tradingStrategies'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:602:14
TS2304: Cannot find name 'tradingStrategies'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:604:23
TS2304: Cannot find name 'tradingStrategies'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:612:45
TS2304: Cannot find name 'NewWatchlist'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:614:48
TS2304: Cannot find name 'watchlists'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:630:15
TS2304: Cannot find name 'watchlists'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:632:14
TS2304: Cannot find name 'watchlists'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:633:14
TS2304: Cannot find name 'watchlists'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:635:23
TS2304: Cannot find name 'watchlists'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:646:15
TS2304: Cannot find name 'watchlists'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:647:19
TS2304: Cannot find name 'watchlists'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:660:17
TS2304: Cannot find name 'watchlists'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:665:19
TS2304: Cannot find name 'watchlists'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:683:15
TS2304: Cannot find name 'tradingPortfolios'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:684:19
TS2304: Cannot find name 'tradingPortfolios'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:690:15
TS2304: Cannot find name 'tradingTransactions'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:691:20
TS2304: Cannot find name 'tradingPortfolios'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:691:42
TS2304: Cannot find name 'tradingTransactions'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:691:75
TS2304: Cannot find name 'tradingPortfolios'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:692:19
TS2304: Cannot find name 'tradingPortfolios'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:696:19
TS2304: Cannot find name 'tradingTransactions'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:699:15
TS2304: Cannot find name 'tradingTransactions'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:700:20
TS2304: Cannot find name 'tradingPortfolios'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:700:42
TS2304: Cannot find name 'tradingTransactions'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:700:75
TS2304: Cannot find name 'tradingPortfolios'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:701:19
TS2304: Cannot find name 'tradingPortfolios'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:702:18
TS2304: Cannot find name 'tradingTransactions'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:730:15
TS2304: Cannot find name 'users'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:731:19
TS2304: Cannot find name 'users'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:735:30
TS2304: Cannot find name 'users'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:747:19
TS2304: Cannot find name 'users'.

ERROR in ./src/features/channel-panels/services/trading-database.service.ts:752:21
TS2304: Cannot find name 'users'.

ERROR in ./src/features/channel-panels/services/user-management.service.ts:40:49
TS2339: Property 'query' does not exist on type 'DatabaseService'.

ERROR in ./src/features/channel-panels/services/user-management.service.ts:95:34
TS2339: Property 'query' does not exist on type 'DatabaseService'.

ERROR in ./src/features/channel-panels/services/user-management.service.ts:140:9
TS2322: Type '{ lastInteraction: Date; interactionCount: number; panelId?: string; channelId?: string; startedAt?: Date; }' is not assignable to type 'ActiveInteraction'.

ERROR in ./src/features/channel-panels/services/user-management.service.ts:143:29
TS2532: Object is possibly 'undefined'.

ERROR in ./src/features/channel-panels/services/user-management.service.ts:198:49
TS2339: Property 'query' does not exist on type 'DatabaseService'.

ERROR in ./src/features/channel-panels/services/user-management.service.ts:320:18
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/channel-panels/services/user-management.service.ts:331:34
TS2339: Property 'query' does not exist on type 'DatabaseService'.

ERROR in ./src/features/channel-panels/services/user-management.service.ts:378:32
TS2339: Property 'query' does not exist on type 'DatabaseService'.

ERROR in ./src/features/channel-panels/services/user-management.service.ts:420:47
TS2339: Property 'query' does not exist on type 'DatabaseService'.

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:6:10
TS2305: Module '"@/core/database"' has no exported member 'User'.

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:15:3
TS2564: Property 'description' has no initializer and is not definitely assigned in the constructor.

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:45:3
TS2564: Property 'requestId' has no initializer and is not definitely assigned in the constructor.

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:54:3
TS2564: Property 'action' has no initializer and is not definitely assigned in the constructor.

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:77:3
TS2564: Property 'setting' has no initializer and is not definitely assigned in the constructor.

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:84:3
TS2564: Property 'value' has no initializer and is not definitely assigned in the constructor.

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:93:3
TS2564: Property 'requestId' has no initializer and is not definitely assigned in the constructor.

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:100:3
TS2564: Property 'amount' has no initializer and is not definitely assigned in the constructor.

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:171:11
TS2353: Object literal may only specify known properties, and 'settings' does not exist in type 'CreateEntity<Guild>'.

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:214:18
TS2339: Property 'settings' does not exist on type 'Guild'.

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:215:15
TS2339: Property 'settings' does not exist on type 'Guild'.

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:216:20
TS2339: Property 'settings' does not exist on type 'Guild'.

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:228:79
TS2339: Property 'settings' does not exist on type 'Guild'.

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:258:34
TS2339: Property 'settings' does not exist on type 'Guild'.

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:297:13
TS2375: Type '{ id: string; clientId: string; clientTag: string; description: string; budget: string | undefined; timeline: string | undefined; skills: any[]; status: "open"; createdAt: string; }' is not assignable to type 'DevRequest' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:381:34
TS2339: Property 'settings' does not exist on type 'Guild'.

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:391:62
TS2339: Property 'find' does not exist on type 'UserRepository'.

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:394:24
TS7006: Parameter 'user' implicitly has an 'any' type.

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:465:62
TS2339: Property 'find' does not exist on type 'UserRepository'.

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:741:62
TS2339: Property 'find' does not exist on type 'UserRepository'.

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:868:74
TS2345: Argument of type '{ devProfile: DevProfile | { isDeveloper: false; skills: never[]; portfolio: null; rating: number; completedProjects: number; }; notifications?: { enabled: boolean; types: string[]; frequency: "immediate" | "daily" | "weekly"; }; ... 8 more ...; warnings?: UserWarning[]; }' is not assignable to parameter of type 'Partial<UserPreferences | undefined>'.

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:893:74
TS2345: Argument of type '{ devProfile: DevProfile | { isDeveloper: false; skills: never[]; portfolio: null; rating: number; completedProjects: number; }; notifications?: { enabled: boolean; types: string[]; frequency: "immediate" | "daily" | "weekly"; }; ... 8 more ...; warnings?: UserWarning[]; }' is not assignable to parameter of type 'Partial<UserPreferences | undefined>'.

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:942:33
TS2339: Property 'settings' does not exist on type 'Guild'.

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:993:42
TS2339: Property 'settings' does not exist on type 'Guild'.

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:1024:62
TS2339: Property 'find' does not exist on type 'UserRepository'.

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:1033:24
TS7006: Parameter 'user' implicitly has an 'any' type.

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:1081:62
TS2339: Property 'find' does not exist on type 'UserRepository'.

ERROR in ./src/features/dev-on-demand/services/tier-management.service.ts:290:11
TS2375: Type '{ id: string; guildId: string; level: TierLevel; name: string; description: string; monthlyPrice: number; yearlyPrice: number | undefined; features: TierFeatures; ... 7 more ...; updatedAt: Date; }' is not assignable to type 'CommunityTier' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/features/dev-on-demand/services/tier-management.service.ts:325:9
TS2322: Type 'string | null' is not assignable to type 'string | undefined'.

ERROR in ./src/features/dev-on-demand/services/tier-management.service.ts:330:13
TS2375: Type '{ id: string; userId: string; guildId: string; tierId: string; whopSubscriptionId: string | undefined; subscriptionStartDate: Date; subscriptionEndDate: Date | undefined; status: "active"; usageStats: { ...; }; createdAt: Date; updatedAt: Date; }' is not assignable to type 'UserMembership' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/features/dynamic-panels/commands/panel-management.commands.ts:20:3
TS2564: Property 'action' has no initializer and is not definitely assigned in the constructor.

ERROR in ./src/features/dynamic-panels/commands/panel-management.commands.ts:100:60
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/dynamic-panels/commands/panel-management.commands.ts:152:47
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/dynamic-panels/commands/panel-management.commands.ts:176:48
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/dynamic-panels/commands/panel-management.commands.ts:213:15
TS7022: 'typeEmoji' implicitly has type 'any' because it does not have a type annotation and is referenced directly or indirectly in its own initializer.

ERROR in ./src/features/dynamic-panels/commands/panel-management.commands.ts:213:27
TS7053: Element implicitly has an 'any' type because expression of type 'string | number | symbol' can't be used to index type '{ ai_mastery: string; wealth_building: string; dev_on_demand: string; personal_growth: string; enterprise_support: string; }'.

ERROR in ./src/features/dynamic-panels/commands/panel-management.commands.ts:242:46
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/dynamic-panels/commands/panel-management.commands.ts:266:50
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/dynamic-panels/commands/panel-management.commands.ts:299:11
TS7022: 'typeEmoji' implicitly has type 'any' because it does not have a type annotation and is referenced directly or indirectly in its own initializer.

ERROR in ./src/features/dynamic-panels/commands/panel-management.commands.ts:299:23
TS7053: Element implicitly has an 'any' type because expression of type 'string | number | symbol' can't be used to index type '{ ai_mastery: string; wealth_building: string; dev_on_demand: string; personal_growth: string; enterprise_support: string; }'.

ERROR in ./src/features/dynamic-panels/handlers/panel-interaction.handler.ts:42:54
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/features/dynamic-panels/handlers/panel-interaction.handler.ts:51:48
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/features/dynamic-panels/handlers/panel-interaction.handler.ts:130:63
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/dynamic-panels/handlers/panel-interaction.handler.ts:161:59
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/dynamic-panels/handlers/panel-interaction.handler.ts:226:48
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/dynamic-panels/handlers/panel-interaction.handler.ts:242:48
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/features/dynamic-panels/handlers/panel-interaction.handler.ts:246:52
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/features/dynamic-panels/handlers/panel-interaction.handler.ts:250:49
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/features/dynamic-panels/handlers/panel-interaction.handler.ts:254:52
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/features/dynamic-panels/handlers/panel-interaction.handler.ts:258:56
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/features/dynamic-panels/handlers/panel-interaction.handler.ts:262:54
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/features/dynamic-panels/handlers/panel-interaction.handler.ts:266:53
TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.

ERROR in ./src/features/dynamic-panels/listeners/dynamic-panels.listener.ts:40:66
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/dynamic-panels/listeners/dynamic-panels.listener.ts:68:70
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/dynamic-panels/listeners/dynamic-panels.listener.ts:82:64
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/dynamic-panels/listeners/dynamic-panels.listener.ts:97:68
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/dynamic-panels/listeners/dynamic-panels.listener.ts:121:72
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/dynamic-panels/listeners/dynamic-panels.listener.ts:133:62
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/dynamic-panels/listeners/dynamic-panels.listener.ts:143:56
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/dynamic-panels/services/panel-config.service.ts:601:30
TS7053: Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ basic: number; premium: number; enterprise: number; }'.

ERROR in ./src/features/dynamic-panels/services/panel-orchestrator.service.ts:112:64
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/dynamic-panels/services/panel-orchestrator.service.ts:173:65
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/dynamic-panels/services/panel-orchestrator.service.ts:207:74
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/dynamic-panels/services/panel-orchestrator.service.ts:239:13
TS2375: Type '{ id: string; panelId: string; channelId: string; messageId: string; userId: string; deployedAt: Date; expiresAt: Date | undefined; usageCount: number; }' is not assignable to type 'PanelDeployment' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/features/dynamic-panels/services/panel-orchestrator.service.ts:264:52
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/dynamic-panels/services/panel-orchestrator.service.ts:282:61
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/dynamic-panels/services/panel-orchestrator.service.ts:289:52
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/dynamic-panels/services/panel-orchestrator.service.ts:305:62
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/dynamic-panels/services/panel-orchestrator.service.ts:323:62
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/dynamic-panels/services/panel-orchestrator.service.ts:333:61
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/dynamic-panels/services/panel-orchestrator.service.ts:342:62
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/dynamic-panels/services/panel-orchestrator.service.ts:361:58
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/dynamic-panels/services/panel-orchestrator.service.ts:374:61
TS18046: 'error' is of type 'unknown'.

ERROR in ./src/features/dynamic-panels/services/panel-orchestrator.service.ts:487:12
TS7053: Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{ '1396352166536548407': string; '1396352189206757376': string; '1396594612654702744': string; '1396594632699416757': string; '1396594648335777803': string; '1396594691818127380': string; '1398998360605855827': string; '1396594670179586218': string; '1394355427612823725': string; '1396352214796079115': string; }'.

ERROR in ./src/features/economy/economy.service.ts:24:52
TS2339: Property 'getUser' does not exist on type 'RedisDatabaseService'.

ERROR in ./src/features/economy/economy.service.ts:46:38
TS2339: Property 'db' does not exist on type 'EconomyService'.

ERROR in ./src/features/economy/economy.service.ts:46:55
TS2304: Cannot find name 'users'.

ERROR in ./src/features/economy/economy.service.ts:47:12
TS2304: Cannot find name 'users'.

ERROR in ./src/features/economy/economy.service.ts:53:43
TS2339: Property 'db' does not exist on type 'EconomyService'.

ERROR in ./src/features/economy/economy.service.ts:53:53
TS2304: Cannot find name 'users'.

ERROR in ./src/features/economy/economy.service.ts:101:18
TS2339: Property 'db' does not exist on type 'EconomyService'.

ERROR in ./src/features/economy/economy.service.ts:101:28
TS2304: Cannot find name 'users'.

ERROR in ./src/features/economy/economy.service.ts:103:19
TS2304: Cannot find name 'users'.

ERROR in ./src/features/leveling/leveling.service.ts:5:15
TS2305: Module '"@/core/database"' has no exported member 'NewUser'.

ERROR in ./src/features/moderation/moderation.service.ts:9:10
TS2305: Module '"@/core/database"' has no exported member 'users'.

ERROR in ./src/features/moderation/moderation.service.ts:17:3
TS2564: Property 'user' has no initializer and is not definitely assigned in the constructor.

ERROR in ./src/features/moderation/moderation.service.ts:33:3
TS2564: Property 'user' has no initializer and is not definitely assigned in the constructor.

ERROR in ./src/features/moderation/moderation.service.ts:56:3
TS2564: Property 'user' has no initializer and is not definitely assigned in the constructor.

ERROR in ./src/features/moderation/moderation.service.ts:63:3
TS2564: Property 'duration' has no initializer and is not definitely assigned in the constructor.

ERROR in ./src/features/moderation/moderation.service.ts:79:3
TS2564: Property 'user' has no initializer and is not definitely assigned in the constructor.

ERROR in ./src/features/moderation/moderation.service.ts:86:3
TS2564: Property 'reason' has no initializer and is not definitely assigned in the constructor.

ERROR in ./src/features/moderation/moderation.service.ts:95:3
TS2564: Property 'user' has no initializer and is not definitely assigned in the constructor.

ERROR in ./src/features/moderation/moderation.service.ts:111:9
TS7030: Not all code paths return a value.

ERROR in ./src/features/moderation/moderation.service.ts:149:9
TS7030: Not all code paths return a value.

ERROR in ./src/features/moderation/moderation.service.ts:199:9
TS7030: Not all code paths return a value.

ERROR in ./src/features/moderation/moderation.service.ts:270:38
TS2339: Property 'db' does not exist on type 'ModerationService'.

ERROR in ./src/features/moderation/moderation.service.ts:270:68
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/moderation/moderation.service.ts:274:43
TS2339: Property 'db' does not exist on type 'ModerationService'.

ERROR in ./src/features/moderation/moderation.service.ts:299:18
TS2339: Property 'db' does not exist on type 'ModerationService'.

ERROR in ./src/features/moderation/moderation.service.ts:299:89
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/moderation/moderation.service.ts:352:38
TS2339: Property 'db' does not exist on type 'ModerationService'.

ERROR in ./src/features/moderation/moderation.service.ts:352:68
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/music/music.service.ts:15:3
TS2564: Property 'query' has no initializer and is not definitely assigned in the constructor.

ERROR in ./src/features/project-tracking/project-tracking.service.ts:213:13
TS2375: Type '{ id: string; projectId: string; authorId: string; authorTag: string; type: "progress" | "milestone" | "completion" | "communication" | "issue" | "payment"; title: string; content: string; metadata: Record<...> | undefined; createdAt: Date; }' is not assignable to type 'ProjectUpdate' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.

ERROR in ./src/features/reaction-role/reaction-role.service.ts:8:10
TS2305: Module '"@/core/database"' has no exported member 'guilds'.

ERROR in ./src/features/reaction-role/reaction-role.service.ts:27:39
TS2339: Property 'db' does not exist on type 'ReactionRoleService'.

ERROR in ./src/features/reaction-role/reaction-role.service.ts:27:70
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/reaction-role/reaction-role.service.ts:33:47
TS7006: Parameter 'rule' implicitly has an 'any' type.

ERROR in ./src/features/reaction-role/reaction-role.service.ts:67:39
TS2339: Property 'db' does not exist on type 'ReactionRoleService'.

ERROR in ./src/features/reaction-role/reaction-role.service.ts:67:70
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/reaction-role/reaction-role.service.ts:73:47
TS7006: Parameter 'rule' implicitly has an 'any' type.

ERROR in ./src/features/reaction-role/reaction-role.service.ts:109:39
TS2339: Property 'db' does not exist on type 'ReactionRoleService'.

ERROR in ./src/features/reaction-role/reaction-role.service.ts:109:70
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/reaction-role/reaction-role.service.ts:126:18
TS2339: Property 'db' does not exist on type 'ReactionRoleService'.

ERROR in ./src/features/reaction-role/reaction-role.service.ts:126:84
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/reaction-role/reaction-role.service.ts:140:39
TS2339: Property 'db' does not exist on type 'ReactionRoleService'.

ERROR in ./src/features/reaction-role/reaction-role.service.ts:140:70
TS2304: Cannot find name 'eq'.

ERROR in ./src/features/reaction-role/reaction-role.service.ts:165:9
TS7006: Parameter 'r' implicitly has an 'any' type.

ERROR in ./src/features/reaction-role/reaction-role.service.ts:191:9
TS7006: Parameter 'r' implicitly has an 'any' type.

ERROR in ./src/features/welcome/welcome.service.ts:4:10
TS2305: Module '"@/core/database"' has no exported member 'Guild'.

ERROR in ./src/features/welcome/welcome.service.ts:4:17
TS2305: Module '"@/core/database"' has no exported member 'User'.

