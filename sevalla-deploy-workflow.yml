# GitHub Actions Workflow for Sevalla Deployment
# This file needs to be manually moved to .github/workflows/sevalla-deploy.yml
# due to GitHub App permissions restrictions

name: Deploy to Seval<PERSON>

on:
  push:
    branches: [ master, staging, develop ]
  pull_request:
    branches: [ master ]

jobs:
  build-and-test:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: 'lts/*'
        cache: 'pnpm'
    
    - name: Install pnpm
      uses: pnpm/action-setup@v2
      with:
        version: 8
        
    - name: Install dependencies
      run: pnpm install
      
    - name: Build application
      run: pnpm run build
      
    - name: Test health endpoint
      run: |
        pnpm start &
        sleep 5
        curl -f http://localhost:3000/api/health
        
  deploy:
    needs: build-and-test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/master' || github.ref == 'refs/heads/staging' || github.ref == 'refs/heads/develop'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Deploy to <PERSON><PERSON><PERSON>
      uses: sevalla/github-action@v1
      with:
        api-key: ${{ secrets.SEVALLA_API_KEY }}
        project-id: ${{ secrets.SEVALLA_PROJECT_ID }}
        environment: ${{ github.ref == 'refs/heads/master' && 'production' || github.ref == 'refs/heads/staging' && 'staging' || 'development' }}
        
    - name: Verify deployment
      run: |
        sleep 30
        curl -f ${{ secrets.SEVALLA_DEPLOYMENT_URL }}/api/health