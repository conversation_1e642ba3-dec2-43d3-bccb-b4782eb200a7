#!/bin/bash

# Fix all relations in entity files to be commented out for Redis migration
echo "Fixing Drizzle relations in entity files..."

# Find all entity files that contain 'relations('
find src/core/database/entities -name "*.entity.ts" -exec grep -l "relations(" {} \; | while read file; do
    echo "Processing: $file"
    
    # Comment out relations import if it exists
    sed -i 's/^import.*relations.*from.*drizzle-orm.*;$/\/\/ &/' "$file"
    
    # Comment out relations export lines but preserve them for reference
    sed -i '/^export const.*Relations = relations(/,/^});$/ {
        s/^/\/\/ /
    }' "$file"
    
    # Alternative pattern for single-line relations
    sed -i 's/^export const.*Relations = relations.*/\/\/ &/' "$file"
    
    echo "Fixed relations in: $file"
done

# Fix imports of 'relations' function
echo "Commenting out relations imports..."
find src/core/database/entities -name "*.entity.ts" -exec sed -i 's/import.*relations.*from.*drizzle-orm\/relations.*/\/\/ &/' {} \;

echo "Relations fix completed!"